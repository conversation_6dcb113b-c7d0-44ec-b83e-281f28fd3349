"""
知识库管理API
"""
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from pydantic import BaseModel, Field

from app.core.database import get_async_session
from app.models.knowledge_base import (
    KnowledgeBase, KnowledgeBaseDocument, KnowledgeBaseVector, 
    KnowledgeBaseConfig, KnowledgeBaseAccessLog
)
from app.models.document_segment import DocumentSegment, DocumentSegmentTask

router = APIRouter()


# Pydantic模型
class KnowledgeBaseCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    visibility: str = Field(default='private', pattern='^(private|public|shared)$')
    file_ids: List[str] = Field(..., min_items=1)
    vector_config: dict = Field(default_factory=dict)


class KnowledgeBaseUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    visibility: Optional[str] = Field(None, pattern='^(private|public|shared)$')
    status: Optional[str] = Field(None, pattern='^(active|inactive)$')


class KnowledgeBaseResponse(BaseModel):
    id: int
    kb_id: str
    name: str
    description: Optional[str]
    avatar_url: Optional[str]
    status: str
    visibility: str
    owner_id: Optional[str]
    document_count: int
    segment_count: int
    vector_count: int
    total_tokens: int
    embedding_model: Optional[str]
    vector_dimension: Optional[int]
    chunk_size: int
    chunk_overlap: int
    created_at: str
    updated_at: str
    last_indexed_at: Optional[str]

    class Config:
        from_attributes = True


class EmbeddingModelResponse(BaseModel):
    name: str
    display_name: str
    dimension: int
    description: str
    provider: str


@router.get("/embedding-models", response_model=List[EmbeddingModelResponse])
async def get_embedding_models(db: AsyncSession = Depends(get_async_session)):
    """获取可用的嵌入模型列表"""
    try:
        # 从配置表获取可用模型
        stmt = select(KnowledgeBaseConfig).where(
            and_(
                KnowledgeBaseConfig.kb_id == 'default',
                KnowledgeBaseConfig.config_type == 'embedding',
                KnowledgeBaseConfig.config_key == 'available_models'
            )
        )
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()
        
        if config and config.config_value:
            import json
            model_names = json.loads(config.config_value)
            
            # 返回模型信息
            models = []
            model_info = {
                'text-embedding-ada-002': {
                    'display_name': 'OpenAI Ada-002',
                    'dimension': 1536,
                    'description': 'OpenAI的高性能嵌入模型',
                    'provider': 'OpenAI'
                },
                'text-embedding-3-small': {
                    'display_name': 'OpenAI Embedding-3-Small',
                    'dimension': 1536,
                    'description': 'OpenAI最新的小型嵌入模型',
                    'provider': 'OpenAI'
                },
                'text-embedding-3-large': {
                    'display_name': 'OpenAI Embedding-3-Large',
                    'dimension': 3072,
                    'description': 'OpenAI最新的大型嵌入模型',
                    'provider': 'OpenAI'
                },
                'bge-large-zh-v1.5': {
                    'display_name': 'BGE Large Chinese',
                    'dimension': 1024,
                    'description': '专为中文优化的嵌入模型',
                    'provider': 'BAAI'
                },
                'm3e-base': {
                    'display_name': 'M3E Base',
                    'dimension': 768,
                    'description': '中文多模态嵌入模型',
                    'provider': 'Moka'
                }
            }
            
            for model_name in model_names:
                if model_name in model_info:
                    info = model_info[model_name]
                    models.append(EmbeddingModelResponse(
                        name=model_name,
                        display_name=info['display_name'],
                        dimension=info['dimension'],
                        description=info['description'],
                        provider=info['provider']
                    ))
            
            return models
        
        # 返回默认模型
        return [
            EmbeddingModelResponse(
                name='text-embedding-ada-002',
                display_name='OpenAI Ada-002',
                dimension=1536,
                description='OpenAI的高性能嵌入模型',
                provider='OpenAI'
            )
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取嵌入模型失败: {str(e)}")


@router.get("", response_model=dict)
async def list_knowledge_bases(
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[str] = Query(None, description="状态过滤"),
    visibility: Optional[str] = Query(None, description="可见性过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_async_session)
):
    """获取知识库列表"""
    try:
        # 构建查询条件
        conditions = [KnowledgeBase.status != 'deleted']
        
        if search:
            conditions.append(
                or_(
                    KnowledgeBase.name.ilike(f"%{search}%"),
                    KnowledgeBase.description.ilike(f"%{search}%")
                )
            )
        
        if status:
            conditions.append(KnowledgeBase.status == status)
            
        if visibility:
            conditions.append(KnowledgeBase.visibility == visibility)
        
        # 查询总数
        count_stmt = select(func.count(KnowledgeBase.id)).where(and_(*conditions))
        count_result = await db.execute(count_stmt)
        total_count = count_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        stmt = (
            select(KnowledgeBase)
            .where(and_(*conditions))
            .order_by(KnowledgeBase.updated_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(stmt)
        knowledge_bases = result.scalars().all()
        
        # 转换为响应格式
        kb_list = []
        for kb in knowledge_bases:
            kb_dict = {
                'id': kb.id,
                'kb_id': kb.kb_id,
                'name': kb.name,
                'description': kb.description,
                'avatar_url': kb.avatar_url,
                'status': kb.status,
                'visibility': kb.visibility,
                'owner_id': kb.owner_id,
                'document_count': kb.document_count,
                'segment_count': kb.segment_count,
                'vector_count': kb.vector_count,
                'total_tokens': kb.total_tokens,
                'embedding_model': kb.embedding_model,
                'vector_dimension': kb.vector_dimension,
                'chunk_size': kb.chunk_size,
                'chunk_overlap': kb.chunk_overlap,
                'created_at': kb.created_at.isoformat() if kb.created_at else None,
                'updated_at': kb.updated_at.isoformat() if kb.updated_at else None,
                'last_indexed_at': kb.last_indexed_at.isoformat() if kb.last_indexed_at else None
            }
            kb_list.append(kb_dict)
        
        return {
            'data': kb_list,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size,
                'has_next': page * page_size < total_count,
                'has_prev': page > 1
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识库列表失败: {str(e)}")


@router.post("", response_model=dict)
async def create_knowledge_base(
    kb_data: KnowledgeBaseCreate,
    db: AsyncSession = Depends(get_async_session)
):
    """创建知识库"""
    try:
        # 生成知识库ID
        kb_id = f"kb_{uuid.uuid4().hex[:8]}"
        
        # 获取向量化配置
        vector_config = kb_data.vector_config
        embedding_model = vector_config.get('embedding_model', 'text-embedding-ada-002')
        vector_dimension = vector_config.get('vector_dimension', 1536)
        chunk_size = vector_config.get('chunk_size', 1000)
        chunk_overlap = vector_config.get('chunk_overlap', 200)
        
        # 创建知识库记录
        kb = KnowledgeBase(
            kb_id=kb_id,
            name=kb_data.name,
            description=kb_data.description,
            visibility=kb_data.visibility,
            owner_id='admin',  # TODO: 从认证信息获取
            embedding_model=embedding_model,
            vector_dimension=vector_dimension,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        db.add(kb)
        await db.flush()
        
        # 创建向量化任务
        # TODO: 实现向量化任务创建逻辑
        task_id = f"task_{uuid.uuid4().hex[:8]}"
        
        await db.commit()
        
        return {
            'success': True,
            'message': '知识库创建成功',
            'data': {
                'kb_id': kb_id,
                'task_id': task_id
            }
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建知识库失败: {str(e)}")


@router.get("/{kb_id}", response_model=KnowledgeBaseResponse)
async def get_knowledge_base(
    kb_id: str,
    db: AsyncSession = Depends(get_async_session)
):
    """获取知识库详情"""
    try:
        stmt = select(KnowledgeBase).where(KnowledgeBase.kb_id == kb_id)
        result = await db.execute(stmt)
        kb = result.scalar_one_or_none()
        
        if not kb:
            raise HTTPException(status_code=404, detail="知识库不存在")
        
        return KnowledgeBaseResponse.from_orm(kb)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识库详情失败: {str(e)}")


@router.put("/{kb_id}", response_model=dict)
async def update_knowledge_base(
    kb_id: str,
    kb_data: KnowledgeBaseUpdate,
    db: AsyncSession = Depends(get_async_session)
):
    """更新知识库"""
    try:
        stmt = select(KnowledgeBase).where(KnowledgeBase.kb_id == kb_id)
        result = await db.execute(stmt)
        kb = result.scalar_one_or_none()
        
        if not kb:
            raise HTTPException(status_code=404, detail="知识库不存在")
        
        # 更新字段
        update_data = kb_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(kb, field, value)
        
        await db.commit()
        
        return {
            'success': True,
            'message': '知识库更新成功'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新知识库失败: {str(e)}")


@router.delete("/{kb_id}", response_model=dict)
async def delete_knowledge_base(
    kb_id: str,
    db: AsyncSession = Depends(get_async_session)
):
    """删除知识库"""
    try:
        stmt = select(KnowledgeBase).where(KnowledgeBase.kb_id == kb_id)
        result = await db.execute(stmt)
        kb = result.scalar_one_or_none()
        
        if not kb:
            raise HTTPException(status_code=404, detail="知识库不存在")
        
        # 软删除
        kb.status = 'deleted'
        await db.commit()
        
        return {
            'success': True,
            'message': '知识库删除成功'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除知识库失败: {str(e)}")
