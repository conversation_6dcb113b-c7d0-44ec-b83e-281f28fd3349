# 知识库智能平台 API

基于FastAPI的多环境、模块化、插件化Python应用，支持智能知识管理与检索功能。

## 特性

- 🚀 **FastAPI框架** - 高性能异步Web框架
- 🔧 **多环境支持** - 开发、测试、生产环境配置
- 📦 **模块化设计** - 清晰的项目结构和模块划分
- 🔌 **插件系统** - 即插即用的插件架构
- 🛡️ **安全认证** - JWT令牌认证和授权
- 📁 **文件管理** - 文件上传、下载和管理
- 🧠 **知识引擎** - 智能知识检索与管理功能
- 📊 **监控日志** - 结构化日志和健康检查
- 🐍 **uv管理** - 现代Python包管理工具

## 项目结构

```
api/
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── api/               # API路由
│   │   ├── __init__.py
│   │   ├── router.py      # 路由汇总
│   │   └── v1/           # API v1版本
│   │       ├── auth.py    # 认证端点
│   │       ├── users.py   # 用户管理
│   │       ├── files.py   # 文件管理
│   │       └── system.py  # 系统管理
│   ├── core/              # 核心模块
│   │   ├── __init__.py
│   │   ├── config.py      # 配置管理
│   │   ├── app_factory.py # 应用工厂
│   │   ├── logging.py     # 日志配置
│   │   ├── exceptions.py  # 异常处理
│   │   └── middleware.py  # 中间件
│   ├── plugins/           # 插件系统
│   │   ├── __init__.py
│   │   ├── base.py        # 插件基类
│   │   ├── manager.py     # 插件管理器
│   │   ├── auth/          # 认证插件
│   │   ├── file_manager/  # 文件管理插件
│   │   └── rag_engine/    # RAG引擎插件
│   └── cli.py             # 命令行工具
├── main.py                # 应用入口
├── pyproject.toml         # 项目配置
├── .env.example           # 环境配置示例
├── .env.development       # 开发环境配置
├── .env.testing           # 测试环境配置
├── .env.production        # 生产环境配置
└── README.md              # 项目文档
```

## 快速开始

### 1. 安装依赖

确保已安装uv包管理器：

```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用pip安装
pip install uv
```

安装项目依赖：

```bash
cd api

# 设置链接模式避免警告（可选）
export UV_LINK_MODE=copy

# 同步依赖
uv sync
```

### 2. 环境配置

复制环境配置文件：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置数据库、Redis等服务连接信息。

### 3. 运行应用

**推荐方式：使用uvicorn直接运行**

```bash
cd api

# 开发模式运行（自动重载）
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 生产模式运行
uv run uvicorn main:app --host 0.0.0.0 --port 8000

# 指定日志级别
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload --log-level debug
```

**其他运行方式：**

```bash
# 使用内置CLI工具运行
uv run knowledge-api run

# 使用简化启动脚本
uv run python run.py

# 直接运行主文件
uv run python main.py
```

### 4. 访问API

启动成功后，你会看到类似输出：

```
🚀 XHC RAG API v0.1.0 starting...
📍 Environment: development
🌐 Server: http://127.0.0.1:8000
📚 Docs: http://127.0.0.1:8000/docs
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [xxxxx] using StatReload
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

然后可以访问：

- **API文档**: http://127.0.0.1:8000/docs （Swagger UI）
- **ReDoc文档**: http://127.0.0.1:8000/redoc （ReDoc界面）
- **健康检查**: http://127.0.0.1:8000/health
- **API根路径**: http://127.0.0.1:8000/api/v1/
- **根路径**: http://127.0.0.1:8000/

## 环境配置

### 开发环境

```bash
# 方式1：设置环境变量
export ENVIRONMENT=development
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 方式2：使用.env.development文件（自动加载）
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

### 测试环境

```bash
# 运行测试
export ENVIRONMENT=testing
uv run python -m pytest

# 测试环境服务器
export ENVIRONMENT=testing
uv run uvicorn main:app --host 127.0.0.1 --port 8001
```

### 生产环境

```bash
export ENVIRONMENT=production
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

## CLI工具

项目提供了丰富的CLI命令：

```bash
# 显示应用信息
uv run knowledge-api info

# 显示插件信息
uv run knowledge-api plugins

# 健康检查
uv run knowledge-api health

# 创建环境配置文件
uv run knowledge-api create-env development

# 初始化数据库
uv run knowledge-api init-db

# 重置数据库
uv run knowledge-api reset-db
```

## 插件系统

### 内置插件

1. **认证插件** (`auth`) - 提供JWT认证和授权功能
2. **文件管理插件** (`file_manager`) - 文件上传、下载和管理
3. **RAG引擎插件** (`rag_engine`) - 检索增强生成功能

### 创建自定义插件

1. 在 `app/plugins/` 目录下创建插件目录
2. 实现插件类，继承 `BasePlugin`
3. 在配置中启用插件

示例插件结构：

```python
from app.plugins.base import BasePlugin, PluginMetadata

class MyPlugin(BasePlugin):
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="my_plugin",
            version="1.0.0",
            description="My custom plugin",
            author="Your Name"
        )
    
    async def initialize(self, app_settings):
        # 初始化逻辑
        pass
    
    async def startup(self):
        # 启动逻辑
        pass
    
    async def shutdown(self):
        # 关闭逻辑
        pass
```

## API端点

### 认证

- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/me` - 获取当前用户信息

### 用户管理

- `GET /api/v1/users/` - 获取用户列表
- `GET /api/v1/users/{user_id}` - 获取用户详情
- `POST /api/v1/users/` - 创建用户
- `PUT /api/v1/users/{user_id}` - 更新用户
- `DELETE /api/v1/users/{user_id}` - 删除用户

### 文件管理

- `POST /api/v1/files/upload` - 上传文件
- `GET /api/v1/files/` - 获取文件列表
- `GET /api/v1/files/{file_id}` - 获取文件信息
- `GET /api/v1/files/{file_id}/download` - 下载文件
- `DELETE /api/v1/files/{file_id}` - 删除文件

### RAG引擎

- `POST /api/v1/rag/query` - 查询文档
- `POST /api/v1/rag/index` - 索引文档
- `DELETE /api/v1/rag/index/{document_id}` - 移除文档索引
- `GET /api/v1/rag/stats` - 获取RAG统计信息

### 系统管理

- `GET /api/v1/system/info` - 获取系统信息
- `GET /api/v1/system/health` - 获取系统健康状态
- `GET /api/v1/system/plugins` - 获取插件列表
- `POST /api/v1/system/plugins/{plugin_name}/enable` - 启用插件
- `POST /api/v1/system/plugins/{plugin_name}/disable` - 禁用插件

## 开发指南

### 代码规范

项目使用以下工具确保代码质量：

- **Black** - 代码格式化
- **isort** - 导入排序
- **flake8** - 代码检查
- **mypy** - 类型检查

运行代码检查：

```bash
uv run black .
uv run isort .
uv run flake8 .
uv run mypy .
```

### 测试

运行测试：

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/test_auth.py

# 生成覆盖率报告
uv run pytest --cov=app --cov-report=html
```

## 部署

### Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装uv
RUN pip install uv

# 复制项目文件
COPY . .

# 安装依赖
RUN uv sync --frozen

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["uv", "run", "python", "main.py"]
```

### 生产环境配置

1. 设置环境变量
2. 配置数据库和Redis
3. 设置反向代理(Nginx)
4. 配置SSL证书
5. 设置监控和日志收集

## 开发模式详细说明

### 推荐的开发启动方式

**最佳实践命令：**

```bash
cd api

# 基础开发模式（推荐）
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 详细日志模式
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload --log-level debug

# 指定环境变量
export ENVIRONMENT=development
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 其他端口
uv run uvicorn main:app --host 127.0.0.1 --port 8080 --reload
```

### 常见问题解决

1. **链接模式警告**：
```bash
export UV_LINK_MODE=copy
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

2. **依赖问题**：
```bash
uv sync  # 重新同步依赖
```

3. **端口占用**：
```bash
# 更换端口
uv run uvicorn main:app --host 127.0.0.1 --port 8080 --reload
```

4. **email-validator错误**：
```bash
uv add email-validator
# 或者
uv sync
```

### 项目特性

这个FastAPI项目的主要优势：

- 🚀 **高性能** - 基于Starlette和Pydantic，性能优异
- 📚 **自动文档** - 自动生成OpenAPI/Swagger文档
- 🔍 **类型检查** - 完整的类型提示和验证
- ⚡ **异步支持** - 原生异步/await支持
- 🔌 **插件化** - 灵活的插件系统架构
- 🌍 **多环境** - 开发、测试、生产环境配置
- 🛡️ **安全性** - 内置安全中间件和认证
- 📊 **监控** - 健康检查和系统监控
- 🐍 **现代工具** - 使用uv进行依赖管理

### 启动成功示例

运行成功后，你会看到类似输出：

```
🚀 XHC RAG API v0.1.0 starting...
📍 Environment: development
🌐 Server: http://127.0.0.1:8000
📚 Docs: http://127.0.0.1:8000/docs
INFO:     Will watch for changes in these directories: ['f:\\workspace\\xhc-rag\\api']
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [xxxxx] using StatReload
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
