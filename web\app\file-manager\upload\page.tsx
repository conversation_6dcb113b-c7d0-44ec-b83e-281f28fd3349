'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Upload,
  X,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Trash,
  Plus,
  Pause,
  Play,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  Sparkles,
  Zap,
  Brain,
  Cpu,
  FileText,
  Square,
  CheckSquare,
  RotateCcw
} from 'lucide-react';
import FileTypeIcon from '../../../components/FileManager/FileTypeIcon';
import { uploadTaskManager, UploadTask } from '../../../lib/uploadTaskManager';

const FileUploadPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const storageId = searchParams.get('storageId') ? Number(searchParams.get('storageId')) : null;
  const currentPath = searchParams.get('path') || '/';

  const [tasks, setTasks] = useState<UploadTask[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTasks, setTotalTasks] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 更新任务列表
  const updateTasks = useCallback(async () => {
    try {
      const paginatedData = await uploadTaskManager.getTasksPaginated(currentPage, pageSize);
      setTasks(paginatedData.tasks);
      setTotalPages(paginatedData.pages);
      setTotalTasks(paginatedData.total);

      // 清理已删除任务的选择状态
      setSelectedTasks(prev => {
        const newSelected = new Set(prev);
        const currentTaskIds = new Set(paginatedData.tasks.map(t => t.task_id));
        prev.forEach(taskId => {
          if (!currentTaskIds.has(taskId)) {
            newSelected.delete(taskId);
          }
        });
        return newSelected;
      });
    } catch (error) {
      console.error('更新任务列表失败:', error);
    }
  }, [currentPage, pageSize]);

  // 监听任务变化
  useEffect(() => {
    updateTasks();
    uploadTaskManager.addListener(updateTasks);

    // 设置定时更新，用于实时显示进度
    const interval = setInterval(() => {
      updateTasks();
    }, 1000); // 每秒更新一次

    return () => {
      uploadTaskManager.removeListener(updateTasks);
      clearInterval(interval);
    };
  }, [updateTasks]);

  // 添加文件到上传队列
  const addFiles = useCallback(async (files: FileList | File[]) => {
    if (!storageId) {
      alert('请先选择存储配置');
      return;
    }

    try {
      const fileArray = Array.from(files);
      await uploadTaskManager.addTasks(fileArray, storageId, currentPath);
      updateTasks();
    } catch (error) {
      console.error('添加文件失败:', error);
      alert('添加文件失败，请重试');
    }
  }, [storageId, currentPath, updateTasks]);

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      addFiles(files);
    }
    event.target.value = '';
  };

  // 处理拖拽
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      addFiles(files);
    }
  };

  // 移除任务
  const removeTask = async (taskId: string) => {
    try {
      await uploadTaskManager.removeTask(taskId);
      updateTasks();
    } catch (error) {
      console.error('删除任务失败:', error);
    }
  };

  // 重试任务
  const retryTask = async (taskId: string) => {
    try {
      await uploadTaskManager.retryTask(taskId);
      updateTasks();
    } catch (error) {
      console.error('重试任务失败:', error);
    }
  };

  // 暂停/恢复上传
  const togglePause = async () => {
    try {
      if (isPaused) {
        await uploadTaskManager.resumeAll();
        setIsPaused(false);
      } else {
        await uploadTaskManager.pauseAll();
        setIsPaused(true);
      }
      updateTasks();
    } catch (error) {
      console.error('暂停/恢复操作失败:', error);
    }
  };

  // 清空已完成的任务
  const clearCompleted = async () => {
    try {
      await uploadTaskManager.clearCompletedTasks();
      updateTasks();
    } catch (error) {
      console.error('清理任务失败:', error);
    }
  };

  // 分页控制
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // 选择控制
  const toggleSelectTask = (taskId: string) => {
    setSelectedTasks(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(taskId)) {
        newSelected.delete(taskId);
      } else {
        newSelected.add(taskId);
      }
      return newSelected;
    });
  };

  const toggleSelectAll = () => {
    if (selectedTasks.size === tasks.length && tasks.length > 0) {
      setSelectedTasks(new Set());
    } else {
      setSelectedTasks(new Set(tasks.map(t => t.task_id)));
    }
  };

  // 批量操作
  const handleBatchPause = async () => {
    try {
      const selectedArray = Array.from(selectedTasks);
      await uploadTaskManager.pauseTasks(selectedArray);
      updateTasks();
    } catch (error) {
      console.error('批量暂停失败:', error);
    }
  };

  const handleBatchResume = async () => {
    try {
      const selectedArray = Array.from(selectedTasks);
      await uploadTaskManager.resumeTasks(selectedArray);
      updateTasks();
    } catch (error) {
      console.error('批量恢复失败:', error);
    }
  };

  const handleBatchDelete = async () => {
    if (confirm(`确定要删除选中的 ${selectedTasks.size} 个任务吗？`)) {
      try {
        const selectedArray = Array.from(selectedTasks);
        await uploadTaskManager.removeTasks(selectedArray);
        setSelectedTasks(new Set());
        updateTasks();
      } catch (error) {
        console.error('批量删除失败:', error);
      }
    }
  };

  // 获取状态统计
  const getStats = () => {
    const pending = tasks.filter(task => task.status === 'pending').length;
    const uploading = tasks.filter(task => task.status === 'uploading').length;
    const error = tasks.filter(task => task.status === 'error').length;
    const paused = tasks.filter(task => task.status === 'paused').length;

    return {
      total: totalTasks,
      pending,
      uploading,
      error,
      paused,
      selected: selectedTasks.size,
      active: pending + uploading
    };
  };

  const stats = getStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* AI风格背景装饰 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-32 w-96 h-96 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-cyan-400/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      <div className="relative z-10">
        {/* 头部导航 */}
        <div className="bg-white/80 backdrop-blur-lg border-b border-gray-200/50 sticky top-0 z-20">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.back()}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                    <Brain className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      AI 智能文件上传
                    </h1>
                    <p className="text-sm text-gray-600">
                      上传到: {currentPath || '/'}
                    </p>
                  </div>
                </div>
              </div>

              {/* 控制按钮 */}
              <div className="flex items-center space-x-3">
                {/* 批量操作按钮 */}
                {selectedTasks.size > 0 && (
                  <>
                    <button
                      onClick={handleBatchPause}
                      className="flex items-center space-x-2 px-3 py-2 bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors"
                    >
                      <Pause className="w-4 h-4" />
                      <span>暂停选中</span>
                    </button>
                    <button
                      onClick={handleBatchResume}
                      className="flex items-center space-x-2 px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                    >
                      <Play className="w-4 h-4" />
                      <span>恢复选中</span>
                    </button>
                    <button
                      onClick={handleBatchDelete}
                      className="flex items-center space-x-2 px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                    >
                      <Trash className="w-4 h-4" />
                      <span>删除选中</span>
                    </button>
                  </>
                )}

                {/* 全局控制按钮 */}
                {stats.total > 0 && (
                  <>
                    <button
                      onClick={togglePause}
                      className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      {isPaused ? (
                        <>
                          <Play className="w-4 h-4 text-green-600" />
                          <span>恢复全部</span>
                        </>
                      ) : (
                        <>
                          <Pause className="w-4 h-4 text-orange-600" />
                          <span>暂停全部</span>
                        </>
                      )}
                    </button>
                    <button
                      onClick={clearCompleted}
                      className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Trash className="w-4 h-4 text-red-600" />
                      <span>清理完成</span>
                    </button>
                  </>
                )}



                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all"
                >
                  <Plus className="w-4 h-4" />
                  <span>添加文件</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* 统计卡片 */}
          {stats.total > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <Cpu className="w-5 h-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">总任务</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <RefreshCw className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">待处理</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.pending}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Zap className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">上传中</p>
                    <p className="text-2xl font-bold text-green-600">{stats.uploading}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">失败</p>
                    <p className="text-2xl font-bold text-red-600">{stats.error}</p>
                  </div>
                </div>
              </div>



              {stats.selected > 0 && (
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <CheckSquare className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">已选中</p>
                      <p className="text-2xl font-bold text-purple-600">{stats.selected}</p>
                    </div>
                  </div>
                </div>
              )}

              {stats.paused > 0 && (
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Pause className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">暂停</p>
                      <p className="text-2xl font-bold text-orange-600">{stats.paused}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 主要内容 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 overflow-hidden">
            {tasks.length === 0 ? (
              // 拖拽上传区域
              <div
                className={`h-96 flex items-center justify-center border-2 border-dashed transition-all duration-300 ${
                  isDragging 
                    ? 'border-blue-400 bg-blue-50/50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="text-center">
                  <motion.div
                    animate={{ 
                      scale: isDragging ? 1.1 : 1,
                      rotate: isDragging ? 5 : 0
                    }}
                    className="mb-6"
                  >
                    <div className="relative">
                      <Upload className="w-16 h-16 text-gray-400 mx-auto" />
                      <Sparkles className="w-6 h-6 text-blue-500 absolute -top-2 -right-2 animate-pulse" />
                    </div>
                  </motion.div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    AI 智能文件上传
                  </h3>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    拖拽文件到这里，或点击下方按钮选择文件。支持批量上传，智能队列管理。
                  </p>
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105"
                  >
                    <Plus className="w-5 h-5 inline mr-2" />
                    选择文件
                  </button>
                </div>
              </div>
            ) : (
              // 任务列表
              <div className="p-6">
                {/* 全选控制 */}
                {tasks.length > 0 && (
                  <div className="flex items-center justify-between mb-4 p-3 bg-gray-50/50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={toggleSelectAll}
                        className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900"
                      >
                        {selectedTasks.size === tasks.length && tasks.length > 0 ? (
                          <CheckSquare className="w-4 h-4 text-blue-600" />
                        ) : (
                          <Square className="w-4 h-4" />
                        )}
                        <span>
                          {selectedTasks.size === tasks.length && tasks.length > 0 ? '取消全选' : '全选'}
                          {selectedTasks.size > 0 && ` (${selectedTasks.size}/${tasks.length})`}
                        </span>
                      </button>
                    </div>


                  </div>
                )}

                <div className="space-y-4">
                  <AnimatePresence>
                    {tasks.map((task) => (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className={`bg-white/80 backdrop-blur-sm border rounded-xl p-4 hover:shadow-lg transition-all duration-300 ${
                          selectedTasks.has(task.task_id)
                            ? 'border-blue-300 bg-blue-50/50 shadow-md'
                            : 'border-gray-200/50'
                        } ${
                          task.status === 'uploading'
                            ? 'border-green-300 bg-green-50/30 shadow-lg'
                            : ''
                        } ${
                          task.status === 'error'
                            ? 'border-red-300 bg-red-50/30'
                            : ''
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 flex-1">
                            {/* 选择框 */}
                            <button
                              onClick={() => toggleSelectTask(task.task_id)}
                              className="flex-shrink-0"
                            >
                              {selectedTasks.has(task.task_id) ? (
                                <CheckSquare className="w-5 h-5 text-blue-600" />
                              ) : (
                                <Square className="w-5 h-5 text-gray-400 hover:text-gray-600" />
                              )}
                            </button>

                            <FileTypeIcon
                              fileName={task.file_name}
                              isDirectory={false}
                              className="w-10 h-10"
                              size={40}
                            />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {task.file_name}
                              </p>
                              <div className="flex items-center space-x-3 text-xs text-gray-500 mt-1">
                                <span>{formatFileSize(task.file_size)}</span>
                                {task.retry_count > 0 && (
                                  <span className="text-orange-600 bg-orange-100 px-2 py-1 rounded">
                                    重试 {task.retry_count} 次
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-3">
                            {/* 状态指示器 */}
                            {task.status === 'pending' && (
                              <div className="flex items-center space-x-2">
                                <div className="w-6 h-6 rounded-full border-2 border-gray-300 animate-pulse" />
                                <span className="text-xs text-gray-500">等待中</span>
                              </div>
                            )}
                            {task.status === 'uploading' && (
                              <div className="flex items-center space-x-2">
                                <div className="w-6 h-6 relative">
                                  <RefreshCw className="w-6 h-6 text-green-500 animate-spin" />
                                  <div className="absolute inset-0 rounded-full border-2 border-green-200 animate-ping"></div>
                                </div>
                                <span className="text-xs text-green-600 font-medium">上传中</span>
                              </div>
                            )}
                            {task.status === 'paused' && (
                              <div className="flex items-center space-x-2">
                                <Pause className="w-6 h-6 text-orange-500" />
                                <span className="text-xs text-orange-600">已暂停</span>
                              </div>
                            )}
                            {task.status === 'success' && (
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-6 h-6 text-green-500" />
                                <span className="text-xs text-green-600 font-medium">已完成</span>
                              </div>
                            )}
                            {task.status === 'error' && (
                              <div className="flex items-center space-x-2">
                                <AlertCircle className="w-6 h-6 text-red-500" />
                                <button
                                  onClick={() => retryTask(task.task_id)}
                                  className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm"
                                >
                                  重试
                                </button>
                              </div>
                            )}

                            {/* 删除按钮 */}
                            <button
                              onClick={() => removeTask(task.task_id)}
                              className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
                            >
                              <X className="w-5 h-5 text-gray-400" />
                            </button>
                          </div>
                        </div>

                        {/* 进度条 */}
                        {task.status === 'uploading' && (
                          <div className="mt-4">
                            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                              <span className="flex items-center space-x-2">
                                <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />
                                <span>上传中...</span>
                              </span>
                              <span className="font-medium text-blue-600">{task.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                              <div
                                className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out relative"
                                style={{ width: `${task.progress}%` }}
                              >
                                {/* 进度条动画效果 */}
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                              </div>
                            </div>
                            {/* 上传速度和剩余时间 */}
                            <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                              <span>
                                {task.uploaded_bytes > 0 && (
                                  `已上传: ${formatFileSize(task.uploaded_bytes)} / ${formatFileSize(task.file_size)}`
                                )}
                              </span>
                              <span>
                                {task.progress > 0 && task.progress < 100 && (
                                  `剩余: ${Math.round((100 - task.progress) / task.progress * 100) / 100}%`
                                )}
                              </span>
                            </div>
                          </div>
                        )}

                        {/* 暂停状态的进度条 */}
                        {task.status === 'paused' && task.progress > 0 && (
                          <div className="mt-4">
                            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                              <span className="flex items-center space-x-2">
                                <Pause className="w-4 h-4 text-orange-500" />
                                <span>已暂停</span>
                              </span>
                              <span className="font-medium text-orange-600">{task.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <div
                                className="bg-gradient-to-r from-orange-400 to-orange-600 h-3 rounded-full"
                                style={{ width: `${task.progress}%` }}
                              />
                            </div>
                          </div>
                        )}

                        {/* 成功状态的进度条 */}
                        {task.status === 'success' && (
                          <div className="mt-4">
                            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                              <span className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-500" />
                                <span>上传完成</span>
                              </span>
                              <span className="font-medium text-green-600">100%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <div className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full w-full relative overflow-hidden">
                                {/* 成功动画效果 */}
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* 错误信息 */}
                        {task.status === 'error' && task.error_message && (
                          <div className="mt-3 text-sm text-red-600 bg-red-50 p-3 rounded-lg border border-red-200">
                            {task.error_message}
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>

                {/* 分页控制 */}
                {totalPages > 1 && (
                  <div className="mt-8 flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      显示 {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, totalTasks)} 
                      ，共 {totalTasks} 个任务
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2 text-sm">
                        <span className="text-gray-600">每页显示:</span>
                        <select
                          value={pageSize}
                          onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                          className="border border-gray-300 rounded-lg px-3 py-1 text-sm bg-white"
                        >
                          <option value={10}>10</option>
                          <option value={20}>20</option>
                          <option value={50}>50</option>
                          <option value={100}>100</option>
                        </select>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronLeft className="w-4 h-4" />
                        </button>
                        
                        <span className="text-sm text-gray-600 px-3">
                          {currentPage} / {totalPages}
                        </span>
                        
                        <button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronRight className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={handleFileSelect}
      />
    </div>
  );
};

export default FileUploadPage;
