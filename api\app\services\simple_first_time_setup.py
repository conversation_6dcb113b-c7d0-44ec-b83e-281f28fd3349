"""
简化的首次设置服务
使用更直接的数据库连接方式
"""
import json
import os
import asyncio
from typing import Dict, Any, Optional
from sqlalchemy import text
from loguru import logger

from app.core.database import get_async_session


class SimpleFirstTimeSetupService:
    """简化的首次设置服务"""
    
    async def check_system_status(self) -> Dict[str, Any]:
        """
        检查系统状态，判断是否需要首次设置
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
        try:
            # 使用依赖注入的方式获取数据库会话
            async for session in get_async_session():
                # 检查数据库表是否存在
                tables_status = await self._check_tables_exist(session)
                
                # 检查是否有管理员用户
                has_admin = await self._check_admin_user_exists(session)
                
                # 检查是否有存储配置
                has_storage = await self._check_storage_config_exists(session)
                
                # 判断是否需要首次设置
                needs_setup = not (tables_status['all_exist'] and has_admin and has_storage)
                
                return {
                    "needs_setup": needs_setup,
                    "tables": tables_status,
                    "has_admin_user": has_admin,
                    "has_storage_config": has_storage,
                    "setup_steps": self._get_setup_steps(tables_status, has_admin, has_storage)
                }
                
        except Exception as e:
            logger.error("检查系统状态失败: {}".format(str(e)))
            # 返回默认状态，假设需要设置
            return {
                "needs_setup": True,
                "tables": {"all_exist": False, "existing": [], "missing": ["unknown"]},
                "has_admin_user": False,
                "has_storage_config": False,
                "setup_steps": ["数据库连接", "表结构创建", "管理员创建", "存储配置"],
                "error": str(e)
            }
    
    async def _check_tables_exist(self, session) -> Dict[str, Any]:
        """检查数据库表是否存在"""
        try:
            # 检查主要表是否存在
            tables_to_check = [
                'users',
                'storage_configs'
            ]
            
            existing_tables = []
            missing_tables = []
            
            for table_name in tables_to_check:
                try:
                    result = await session.execute(text(
                        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = :table_name)"
                    ), {"table_name": table_name})
                    
                    exists = result.scalar()
                    if exists:
                        existing_tables.append(table_name)
                    else:
                        missing_tables.append(table_name)
                        
                except Exception as e:
                    logger.warning("检查表 {} 失败: {}".format(table_name, str(e)))
                    missing_tables.append(table_name)
            
            return {
                "all_exist": len(missing_tables) == 0,
                "existing": existing_tables,
                "missing": missing_tables,
                "total": len(tables_to_check)
            }
            
        except Exception as e:
            logger.error("检查数据库表失败: {}".format(str(e)))
            return {
                "all_exist": False,
                "existing": [],
                "missing": ["unknown"],
                "total": 0,
                "error": str(e)
            }
    
    async def _check_admin_user_exists(self, session) -> bool:
        """检查是否存在管理员用户"""
        try:
            result = await session.execute(text(
                "SELECT EXISTS (SELECT 1 FROM users WHERE is_superuser = true LIMIT 1)"
            ))
            return result.scalar() or False
            
        except Exception as e:
            logger.warning("检查管理员用户失败: {}".format(str(e)))
            return False
    
    async def _check_storage_config_exists(self, session) -> bool:
        """检查是否存在存储配置"""
        try:
            result = await session.execute(text(
                "SELECT EXISTS (SELECT 1 FROM storage_configs LIMIT 1)"
            ))
            return result.scalar() or False
            
        except Exception as e:
            logger.warning("检查存储配置失败: {}".format(str(e)))
            return False
    
    def _get_setup_steps(self, tables_status: Dict, has_admin: bool, has_storage: bool) -> list:
        """获取需要执行的设置步骤"""
        steps = []
        
        if not tables_status.get('all_exist', False):
            steps.append("创建数据库表结构")
        
        if not has_admin:
            steps.append("创建管理员账户")
        
        if not has_storage:
            steps.append("配置默认存储")
        
        if not steps:
            steps.append("系统已完成初始化")
        
        return steps
    
    async def initialize_system(self, admin_data: Optional[Dict] = None, storage_path: Optional[str] = None) -> Dict[str, Any]:
        """
        初始化系统

        Args:
            admin_data: 管理员账户数据
            storage_path: 自定义存储路径

        Returns:
            Dict[str, Any]: 初始化结果
        """
        try:
            logger.info("开始系统初始化，存储路径: {}".format(storage_path))
            results = {
                "success": True,
                "steps_completed": [],
                "errors": []
            }
            
            async for session in get_async_session():
                # 1. 创建数据库表（如果需要）
                tables_result = await self._ensure_tables_exist(session)
                if tables_result["success"]:
                    results["steps_completed"].append("数据库表结构")
                else:
                    results["errors"].append(f"数据库表创建失败: {tables_result['error']}")
                
                # 2. 创建管理员用户（如果需要）
                admin_result = await self._ensure_admin_user(session, admin_data)
                if admin_result["success"]:
                    results["steps_completed"].append("管理员账户")
                else:
                    results["errors"].append(f"管理员创建失败: {admin_result['error']}")
                
                # 3. 创建默认存储配置（如果需要）
                logger.info("传递给_ensure_default_storage的存储路径: {}".format(storage_path))
                storage_result = await self._ensure_default_storage(session, storage_path)
                if storage_result["success"]:
                    results["steps_completed"].append("默认存储配置")
                else:
                    results["errors"].append(f"存储配置失败: {storage_result['error']}")
                
                # 提交所有更改
                await session.commit()
                
                results["success"] = len(results["errors"]) == 0
                return results
            
        except Exception as e:
            logger.error("系统初始化失败: {}".format(str(e)))
            return {
                "success": False,
                "steps_completed": [],
                "errors": [str(e)]
            }
    
    async def _ensure_tables_exist(self, session) -> Dict[str, Any]:
        """确保数据库表存在"""
        try:
            # 这里可以运行数据库迁移或创建表的逻辑
            # 暂时返回成功，实际实现时可以调用 Alembic 或直接创建表
            return {"success": True, "message": "数据库表检查完成"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _ensure_admin_user(self, session, admin_data: Optional[Dict]) -> Dict[str, Any]:
        """确保存在管理员用户"""
        try:
            # 检查是否已有管理员
            result = await session.execute(text(
                "SELECT EXISTS (SELECT 1 FROM users WHERE is_superuser = true LIMIT 1)"
            ))
            has_admin = result.scalar()

            if has_admin:
                return {"success": True, "message": "管理员用户已存在"}

            # 创建默认管理员
            if not admin_data:
                admin_data = {
                    "username": "admin",
                    "password": "password",
                    "email": "<EMAIL>"
                }

            # 实际创建管理员用户
            from app.core.security import get_password_hash

            # 加密密码
            password_hash = get_password_hash(admin_data["password"])

            # 插入管理员用户
            await session.execute(text("""
                INSERT INTO users (
                    username, email, full_name, hashed_password,
                    is_active, is_superuser, created_at, updated_at
                ) VALUES (
                    :username, :email, :full_name, :hashed_password,
                    :is_active, :is_superuser, NOW(), NOW()
                )
            """), {
                "username": admin_data["username"],
                "email": admin_data["email"],
                "full_name": admin_data.get("full_name", "系统管理员"),
                "hashed_password": password_hash,
                "is_active": True,
                "is_superuser": True
            })

            logger.info("管理员用户创建成功: {}".format(admin_data["username"]))
            return {"success": True, "message": "管理员用户创建成功"}

        except Exception as e:
            logger.error("创建管理员用户失败: {}".format(str(e)))
            return {"success": False, "error": str(e)}
    
    async def _ensure_default_storage(self, session, storage_path: Optional[str] = None) -> Dict[str, Any]:
        """确保存在默认存储配置"""
        try:
            logger.info("_ensure_default_storage接收到的存储路径: {}".format(storage_path))

            # 检查是否已有存储配置
            result = await session.execute(text(
                "SELECT id, config FROM storage_configs LIMIT 1"
            ))
            existing_storage = result.fetchone()

            if existing_storage and storage_path:
                # 如果存储配置已存在但用户指定了新路径，更新配置
                logger.info("存储配置已存在，但用户指定了新路径，更新配置")

                # 解析现有配置
                existing_config = json.loads(existing_storage.config) if isinstance(existing_storage.config, str) else existing_storage.config

                # 更新路径
                existing_config["base_path"] = storage_path

                # 更新数据库
                await session.execute(text("""
                    UPDATE storage_configs
                    SET config = :config, updated_at = NOW()
                    WHERE id = :id
                """), {
                    "config": json.dumps(existing_config),
                    "id": existing_storage.id
                })

                # 确保新目录存在
                if not os.path.exists(storage_path):
                    os.makedirs(storage_path, exist_ok=True)
                    logger.info("创建存储目录: {}".format(storage_path))

                return {"success": True, "message": "存储配置已更新，路径: {}".format(storage_path)}

            elif existing_storage:
                logger.info("存储配置已存在，跳过创建")
                return {"success": True, "message": "存储配置已存在"}

            # 创建默认本地存储配置
            base_path = storage_path if storage_path else "./storage"
            config_data = {
                "base_path": base_path,
                "max_file_size": 100 * 1024 * 1024  # 100MB
            }

            logger.info("创建默认存储配置，最终路径: {}".format(base_path))
            logger.info("配置数据: {}".format(config_data))
            
            # 使用原生SQL插入，避免枚举问题
            await session.execute(text("""
                INSERT INTO storage_configs (name, storage_type, is_default, is_active, config, created_at, updated_at)
                VALUES (:name, :storage_type, :is_default, :is_active, :config, NOW(), NOW())
            """), {
                "name": "默认本地存储",
                "storage_type": "LOCAL",
                "is_default": True,
                "is_active": True,
                "config": json.dumps(config_data)
            })
            
            # 确保存储目录存在
            storage_dir = config_data["base_path"]
            if not os.path.exists(storage_dir):
                os.makedirs(storage_dir, exist_ok=True)
                logger.info("创建存储目录: {}".format(storage_dir))

            return {"success": True, "message": "默认存储配置创建成功，路径: {}".format(base_path)}
            
        except Exception as e:
            return {"success": False, "error": str(e)}


# 全局实例
simple_first_time_setup_service = SimpleFirstTimeSetupService()
