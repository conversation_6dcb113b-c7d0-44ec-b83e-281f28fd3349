#!/usr/bin/env python3
"""
创建默认存储配置
"""
import asyncio
import asyncpg
import json
from loguru import logger
from app.core.config import get_settings

settings = get_settings()

async def create_default_storage():
    """创建默认的本地存储配置"""
    
    # 解析数据库URL
    db_url = settings.DATABASE_URL
    if db_url.startswith("postgresql+asyncpg://"):
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(db_url)
        logger.info("Connected to database")
        
        # 检查是否已有存储配置
        existing = await conn.fetchval("SELECT COUNT(*) FROM storage_configs")
        
        if existing > 0:
            logger.info(f"Already have {existing} storage configs, skipping creation")
            await conn.close()
            return
        
        # 创建默认本地存储配置
        config = {
            "base_path": "./storage",
            "max_file_size": 100 * 1024 * 1024  # 100MB
        }
        
        await conn.execute("""
            INSERT INTO storage_configs (name, storage_type, is_default, is_active, config, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        """, "默认本地存储", "local", True, True, json.dumps(config))
        
        logger.info("✅ Created default local storage config")
        
        # 验证创建结果
        result = await conn.fetchrow("SELECT * FROM storage_configs WHERE name = $1", "默认本地存储")
        if result:
            logger.info(f"Created storage config: ID={result['id']}, Type={result['storage_type']}")
        
        await conn.close()
        
    except Exception as e:
        logger.error(f"Error creating default storage: {e}")

if __name__ == "__main__":
    asyncio.run(create_default_storage())
