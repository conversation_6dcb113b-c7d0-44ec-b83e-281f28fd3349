/**
 * English translation file
 */

import { Translations } from '../index';

const en: Translations = {
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    submit: 'Submit',
    retry: 'Retry',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    import: 'Import',
    refresh: 'Refresh',
  },
  
  login: {
    title: 'AI Knowledge Base',
    subtitle: 'AI-Powered Intelligent Knowledge Management & Retrieval System',
    welcomeBack: 'Welcome Back',
    username: '<PERSON>rna<PERSON>',
    password: 'Password',
    rememberMe: 'Remember me',
    forgotPassword: 'Forgot password?',
    loginButton: 'Sign In',
    noAccount: "Don't have an account?",
    signUp: 'Sign Up',
    loginSuccess: 'Login successful!',
    loginError: 'Login failed',
    invalidCredentials: 'Invalid username or password',
    networkError: 'Network error, please try again later',
    usernameRequired: 'Username is required',
    passwordRequired: 'Password is required',
    usernameMinLength: 'Username must be at least 3 characters',
    passwordMinLength: 'Password must be at least 6 characters',
  },
  
  ai: {
    poweredBy: 'Powered by AI',
    intelligentSystem: 'Intelligent System',
    secureLogin: 'Secure Login',
    aiAssistant: 'AI Assistant',
    smartAnalysis: 'Smart Analysis',
    dataProtection: 'Data Protection',
    knowledgeBase: 'Knowledge Base',
    intelligentRetrieval: 'Intelligent Retrieval',
    documentProcessing: 'Document Processing',
  },
  
  language: {
    current: 'English',
    switch: 'Switch Language',
    chinese: '中文简体',
    traditionalChinese: '中文繁體',
    english: 'English',
    japanese: '日本語',
  },
  
  navigation: {
    home: 'Home',
    dashboard: 'Dashboard',
    documents: 'File Manager',
    knowledge: 'Knowledge Base',
    search: 'Smart Search',
    analytics: 'Analytics',
    settings: 'Settings',
    profile: 'Profile',
    logout: 'Logout',
    admin: 'Administration',
  },
  
  dashboard: {
    welcome: 'Welcome back',
    subtitle: 'Manage your AI-powered document processing workflow',
    overview: 'Overview',
    statistics: 'Statistics',
    recentActivity: 'Recent Activity',
    quickActions: 'Quick Actions',
    systemStatus: 'System Status',
    userInfo: 'User Information',
    totalDocuments: 'Total Documents',
    totalQueries: 'Total Queries',
    totalUsers: 'Total Users',
    systemSettings: 'System Settings',
    stats: {
      documents: 'Documents',
      queries: 'Queries',
      users: 'Users',
      settings: 'Settings'
    },
    fileManager: {
      description: 'Upload, organize and manage your file library',
      stats: '125 files'
    },
    knowledge: {
      description: 'Build and query your knowledge base',
      stats: '89 entries'
    },
    search: {
      description: 'Use AI for intelligent content search',
      stats: '1,234 queries'
    },
    activities: {
      documentUploaded: 'Document uploaded',
      queryProcessed: 'Query processed',
      userLoggedIn: 'User logged in',
      settingsUpdated: 'Settings updated'
    },
    actions: {
      uploadDocument: 'Upload Document',
      runQuery: 'Run Query',
      manageUsers: 'Manage Users',
      systemSettings: 'System Settings'
    }
  },
  
  documents: {
    title: 'Document Management',
    upload: 'Upload Document',
    download: 'Download',
    delete: 'Delete',
    preview: 'Preview',
    details: 'Details',
    fileName: 'File Name',
    fileSize: 'File Size',
    uploadTime: 'Upload Time',
    fileType: 'File Type',
    status: 'Status',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
    uploadSuccess: 'Upload successful',
    uploadError: 'Upload failed',
    deleteConfirm: 'Are you sure you want to delete this document?',
  },
  
  search: {
    title: 'Smart Search',
    placeholder: 'Enter search keywords...',
    results: 'Search Results',
    noResults: 'No results found',
    searching: 'Searching...',
    advanced: 'Advanced Search',
    filters: 'Filters',
    sortBy: 'Sort By',
    relevance: 'Relevance',
    date: 'Date',
    size: 'Size',
  },
  
  settings: {
    title: 'Settings',
    general: 'General',
    account: 'Account',
    security: 'Security',
    notifications: 'Notifications',
    language: 'Language',
    theme: 'Theme',
    privacy: 'Privacy',
    about: 'About',
  },
  
  errors: {
    pageNotFound: 'Page Not Found',
    serverError: 'Internal Server Error',
    networkError: 'Network Connection Error',
    unauthorized: 'Unauthorized Access',
    forbidden: 'Access Forbidden',
    validationError: 'Validation Error',
    unknownError: 'Unknown Error',
  },
};

export default en;
