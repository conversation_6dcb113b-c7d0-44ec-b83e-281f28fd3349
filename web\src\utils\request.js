/**
 * HTTP请求工具
 * 基于axios封装，包含请求/响应拦截器
 */

import axios from 'axios'
import { authApi } from '@/api/auth'

// 创建axios实例
const request = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://127.0.0.1:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    console.log(`API请求耗时: ${duration}ms - ${response.config.method?.toUpperCase()} ${response.config.url}`)
    
    // 检查响应状态
    if (response.data && response.data.code !== undefined) {
      // 如果后端返回了自定义状态码
      if (response.data.code !== 200) {
        const error = new Error(response.data.message || '请求失败')
        error.code = response.data.code
        error.response = response
        return Promise.reject(error)
      }
    }
    
    return response
  },
  async (error) => {
    const { response, config } = error
    
    // 网络错误
    if (!response) {
      console.error('网络错误:', error.message)
      return Promise.reject(new Error('网络连接失败，请检查网络设置'))
    }
    
    // 根据状态码处理不同错误
    switch (response.status) {
      case 401:
        // 未授权，尝试刷新token
        if (!config._retry) {
          config._retry = true
          
          try {
            const refreshToken = localStorage.getItem('refresh_token')
            if (refreshToken) {
              const refreshResponse = await authApi.refreshToken(refreshToken)
              const { access_token, refresh_token: newRefreshToken } = refreshResponse.data
              
              // 更新token
              localStorage.setItem('access_token', access_token)
              if (newRefreshToken) {
                localStorage.setItem('refresh_token', newRefreshToken)
              }
              
              // 重新发送原请求
              config.headers.Authorization = `Bearer ${access_token}`
              return request(config)
            }
          } catch (refreshError) {
            console.error('刷新token失败:', refreshError)
          }
        }
        
        // 刷新失败或没有refresh token，清除认证信息并跳转登录
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_info')
        
        // 如果不是在登录页面，跳转到登录页
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
        break
        
      case 403:
        console.error('权限不足')
        break
        
      case 404:
        console.error('请求的资源不存在')
        break
        
      case 422:
        // 验证错误，通常包含详细的字段错误信息
        if (response.data && response.data.detail) {
          const detail = response.data.detail
          if (Array.isArray(detail)) {
            // FastAPI验证错误格式
            const errorMessages = detail.map(err => `${err.loc.join('.')}: ${err.msg}`).join('; ')
            error.message = errorMessages
          } else if (typeof detail === 'string') {
            error.message = detail
          }
        }
        break
        
      case 500:
        console.error('服务器内部错误')
        error.message = '服务器内部错误，请稍后重试'
        break
        
      default:
        console.error(`HTTP错误 ${response.status}:`, response.data)
    }
    
    return Promise.reject(error)
  }
)

// 请求方法封装
const http = {
  get(url, config = {}) {
    return request.get(url, config)
  },
  
  post(url, data = {}, config = {}) {
    return request.post(url, data, config)
  },
  
  put(url, data = {}, config = {}) {
    return request.put(url, data, config)
  },
  
  patch(url, data = {}, config = {}) {
    return request.patch(url, data, config)
  },
  
  delete(url, config = {}) {
    return request.delete(url, config)
  },
  
  upload(url, formData, config = {}) {
    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers
      }
    })
  }
}

export default request
export { http }
