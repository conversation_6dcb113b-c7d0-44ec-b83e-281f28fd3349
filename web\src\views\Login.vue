<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>🤖 AI知识库</h1>
        <p>智能知识管理平台</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label>用户名/邮箱</label>
          <input 
            v-model="loginForm.username" 
            type="text" 
            placeholder="请输入用户名或邮箱"
            required
            :disabled="loading"
          />
        </div>
        
        <div class="form-group">
          <label>密码</label>
          <input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            required
            :disabled="loading"
          />
        </div>
        
        <div class="form-options">
          <label class="checkbox-label">
            <input v-model="loginForm.rememberMe" type="checkbox" />
            <span>记住我</span>
          </label>
          <a href="#" class="forgot-password">忘记密码？</a>
        </div>
        
        <button type="submit" class="btn-login" :disabled="loading">
          <i v-if="loading" class="fas fa-spinner fa-spin"></i>
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </form>

      <div class="login-footer">
        <p>还没有账户？ <router-link to="/register">立即注册</router-link></p>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <span>{{ error }}</span>
      </div>

      <!-- 成功信息 -->
      <div v-if="success" class="success-message">
        <i class="fas fa-check-circle"></i>
        <span>{{ success }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { authApi } from '@/api/auth'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    
    const loginForm = reactive({
      username: '',
      password: '',
      rememberMe: false
    })
    
    // 处理登录
    const handleLogin = async () => {
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        // 调用登录API
        const response = await authApi.login({
          username: loginForm.username,
          password: loginForm.password
        })
        
        const { access_token, refresh_token, user } = response.data
        
        // 保存token和用户信息
        localStorage.setItem('access_token', access_token)
        localStorage.setItem('refresh_token', refresh_token)
        localStorage.setItem('user_info', JSON.stringify(user))
        
        // 如果选择记住我，设置更长的过期时间
        if (loginForm.rememberMe) {
          const expireTime = new Date().getTime() + 7 * 24 * 60 * 60 * 1000 // 7天
          localStorage.setItem('token_expire', expireTime.toString())
        }
        
        success.value = '登录成功，正在跳转...'
        
        // 延迟跳转，让用户看到成功信息
        setTimeout(() => {
          router.push('/dashboard')
        }, 1000)
        
      } catch (err) {
        console.error('登录失败:', err)
        error.value = err.response?.data?.detail || '登录失败，请检查用户名和密码'
      } finally {
        loading.value = false
      }
    }
    
    return {
      loading,
      error,
      success,
      loginForm,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 400px;
  width: 100%;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.login-header p {
  color: #666;
  font-size: 16px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.forgot-password {
  color: #2196f3;
  text-decoration: none;
  font-size: 14px;
}

.forgot-password:hover {
  text-decoration: underline;
}

.btn-login {
  background: #2196f3;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-login:hover:not(:disabled) {
  background: #1976d2;
}

.btn-login:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.login-footer p {
  color: #666;
  font-size: 14px;
}

.login-footer a {
  color: #2196f3;
  text-decoration: none;
}

.login-footer a:hover {
  text-decoration: underline;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.success-message {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}
</style>
