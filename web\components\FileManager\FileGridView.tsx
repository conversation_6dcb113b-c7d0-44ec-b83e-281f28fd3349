'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Check,
  Download,
  Eye,
  Edit3,
  MoreHorizontal,
  Folder,
  File
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import FileTypeIcon, { getFileTypeDescription, getFileCategory } from './FileTypeIcon';

interface FileItem {
  file_id: string;
  file_path: string;
  file_name: string;
  file_type: 'file' | 'directory';
  file_size: number;
  file_size_formatted: string;
  mime_type?: string;
  file_extension: string;
  created_at: string;
  modified_at: string;
  is_directory: boolean;
  is_image: boolean;
  is_video: boolean;
  is_document: boolean;
}

interface FileGridViewProps {
  files: FileItem[];
  selectedFiles: string[];
  onFileSelect: (file: FileItem, isSelected: boolean) => void;
  onFileDoubleClick: (file: FileItem) => void;
  getFileIcon: (file: FileItem) => React.ReactNode;
  onFileDownload: (file: FileItem) => void;
  onFilePreview: (file: FileItem) => void;
}

const FileGridView: React.FC<FileGridViewProps> = ({
  files,
  selectedFiles,
  onFileSelect,
  onFileDoubleClick,
  getFileIcon,
  onFileDownload,
  onFilePreview
}) => {
  const router = useRouter();
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    file: FileItem;
  } | null>(null);

  // 判断是否为可编辑文件
  const isEditableFile = (file: FileItem) => {
    const editableExtensions = ['.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt', '.pdf', '.txt', '.md', '.markdown'];
    return editableExtensions.includes(file.file_extension?.toLowerCase());
  };

  // 判断是否为Office文件
  const isOfficeFile = (file: FileItem) => {
    const officeExtensions = ['.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt'];
    return officeExtensions.includes(file.file_extension?.toLowerCase());
  };

  // 处理文件编辑 - 在新浏览器标签页中打开
  const handleFileEdit = (file: FileItem) => {
    const encodedFileId = btoa(file.file_id);

    // 构建编辑URL
    let editUrl;
    if (isOfficeFile(file)) {
      editUrl = `/file-manager/office/edit/${encodedFileId}`;
    } else {
      editUrl = `/file-manager/edit/${encodedFileId}`;
    }

    // 在新浏览器标签页中打开编辑器
    window.open(editUrl, '_blank', 'noopener,noreferrer');
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, file: FileItem) => {
    e.preventDefault();
    e.stopPropagation();

    // 获取鼠标位置
    const mouseX = e.clientX;
    const mouseY = e.clientY;

    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = 192; // 菜单实际宽度 (min-w-48 = 12rem = 192px)
    const menuHeight = 200; // 菜单预估高度

    // 计算菜单位置，确保不超出视口
    let x = mouseX;
    let y = mouseY;

    // 右侧边界检查
    if (x + menuWidth > viewportWidth) {
      x = mouseX - menuWidth; // 显示在鼠标左侧
    }

    // 底部边界检查
    if (y + menuHeight > viewportHeight) {
      y = mouseY - menuHeight; // 显示在鼠标上方
    }

    // 确保菜单不会超出屏幕边界
    x = Math.max(8, Math.min(x, viewportWidth - menuWidth - 8));
    y = Math.max(8, Math.min(y, viewportHeight - menuHeight - 8));

    setContextMenu({
      x,
      y,
      file
    });
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // 处理文件选择
  const handleFileClick = (file: FileItem, e: React.MouseEvent) => {
    const isSelected = selectedFiles.includes(file.file_path);
    
    if (e.ctrlKey || e.metaKey) {
      // Ctrl/Cmd + 点击：切换选择状态
      onFileSelect(file, !isSelected);
    } else if (e.shiftKey && selectedFiles.length > 0) {
      // Shift + 点击：范围选择
      onFileSelect(file, !isSelected);
    } else {
      // 普通点击：单选
      if (!isSelected) {
        onFileSelect(file, true);
      }
    }
  };

  // 获取文件缩略图
  const getFileThumbnail = (file: FileItem) => {
    const category = getFileCategory(file.file_name, file.is_directory);

    // 根据文件类别设置背景色
    const getBgColor = (category: string) => {
      switch (category) {
        case 'directory': return 'bg-blue-50';
        case 'image': return 'bg-green-50';
        case 'video': return 'bg-red-50';
        case 'audio': return 'bg-purple-50';
        case 'document': return 'bg-blue-50';
        case 'spreadsheet': return 'bg-green-50';
        case 'presentation': return 'bg-orange-50';
        case 'code': return 'bg-indigo-50';
        case 'archive': return 'bg-yellow-50';
        default: return 'bg-gray-50';
      }
    };

    return (
      <div className={`w-16 h-16 flex items-center justify-center ${getBgColor(category)} rounded-lg`}>
        <FileTypeIcon
          fileName={file.file_name}
          fileType={file.file_type}
          isDirectory={file.is_directory}
          className="w-8 h-8"
          size={32}
        />
      </div>
    );
  };

  return (
    <>
      <div className="p-4 overflow-y-auto h-full">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
          {files.map((file) => {
            const isSelected = selectedFiles.includes(file.file_path);
            
            return (
              <motion.div
                key={file.file_id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{ scale: 1.02 }}
                className={`relative group p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  isSelected 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-transparent hover:border-gray-200 hover:bg-gray-50'
                }`}
                onClick={(e) => handleFileClick(file, e)}
                onDoubleClick={() => onFileDoubleClick(file)}
                onContextMenu={(e) => handleContextMenu(e, file)}
              >
                {/* 选择指示器 */}
                <div className={`absolute top-2 left-2 z-10 ${isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} transition-opacity`}>
                  <div
                    className={`flex items-center justify-center w-5 h-5 border rounded transition-colors ${
                      isSelected 
                        ? 'bg-blue-600 border-blue-600' 
                        : 'bg-white border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    {isSelected && <Check className="w-3 h-3 text-white" />}
                  </div>
                </div>

                {/* 操作按钮 */}
                {!file.is_directory && (
                  <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onFilePreview(file);
                        }}
                        className="p-1 bg-white rounded shadow-sm text-gray-600 hover:text-gray-800"
                        title="预览"
                      >
                        <Eye className="w-3 h-3" />
                      </button>

                      {/* 编辑按钮 - 支持可编辑文件 */}
                      {isEditableFile(file) && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleFileEdit(file);
                          }}
                          className="p-1 bg-white rounded shadow-sm text-green-600 hover:text-green-800"
                          title="编辑"
                        >
                          <Edit3 className="w-3 h-3" />
                        </button>
                      )}

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onFileDownload(file);
                        }}
                        className="p-1 bg-white rounded shadow-sm text-gray-600 hover:text-gray-800"
                        title="下载"
                      >
                        <Download className="w-3 h-3" />
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleContextMenu(e, file);
                        }}
                        className="p-1 bg-white rounded shadow-sm text-gray-600 hover:text-gray-800"
                        title="更多"
                      >
                        <MoreHorizontal className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )}

                {/* 文件缩略图 */}
                <div className="flex flex-col items-center space-y-2">
                  {getFileThumbnail(file)}
                  
                  {/* 文件名 */}
                  <div className="w-full text-center">
                    <div 
                      className="text-xs text-gray-900 truncate px-1" 
                      title={file.file_name}
                    >
                      {file.file_name}
                    </div>
                    
                    {/* 文件信息 */}
                    <div className="text-xs text-gray-500 mt-1">
                      {file.is_directory ? '文件夹' : file.file_size_formatted}
                    </div>

                    {/* 文件类型 */}
                    <div className="text-xs text-gray-400 truncate px-1" title={getFileTypeDescription(file.file_name, file.is_directory)}>
                      {getFileTypeDescription(file.file_name, file.is_directory)}
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* 右键菜单 */}
      {contextMenu && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={closeContextMenu}
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className="fixed z-50 bg-white rounded-lg shadow-xl border border-gray-200 py-2 min-w-48"
            style={{
              left: `${contextMenu.x}px`,
              top: `${contextMenu.y}px`
            }}
          >
            {!contextMenu.file.is_directory && (
              <>
                <button
                  onClick={() => {
                    onFilePreview(contextMenu.file);
                    closeContextMenu();
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Eye className="w-4 h-4" />
                  <span>查看</span>
                </button>

                {/* 右键菜单编辑按钮 */}
                {isEditableFile(contextMenu.file) && (
                  <button
                    onClick={() => {
                      handleFileEdit(contextMenu.file);
                      closeContextMenu();
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                  >
                    <Edit3 className="w-4 h-4" />
                    <span>编辑</span>
                  </button>
                )}

                <button
                  onClick={() => {
                    onFileDownload(contextMenu.file);
                    closeContextMenu();
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>下载</span>
                </button>

                <div className="border-t border-gray-200 my-1"></div>
              </>
            )}
            
            <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
              重命名
            </button>
            
            <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
              复制
            </button>
            
            <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
              移动
            </button>
            
            <div className="border-t border-gray-200 my-1"></div>
            
            <button className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
              删除
            </button>
          </motion.div>
        </>
      )}
    </>
  );
};

export default FileGridView;
