"""
存储统计响应模式
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel


class StorageStatsData(BaseModel):
    """存储统计数据"""
    id: int
    storage_id: int
    stats_date: Optional[str]
    total_files: int
    total_folders: int
    total_size: int
    used_size: int
    available_size: int
    document_count: int
    image_count: int
    video_count: int
    audio_count: int
    archive_count: int
    other_count: int
    document_size: int
    image_size: int
    video_size: int
    audio_size: int
    archive_size: int
    other_size: int
    avg_file_size: float
    largest_file_size: int
    smallest_file_size: int
    read_operations: int
    write_operations: int
    delete_operations: int
    error_count: int
    last_error: Optional[str]
    health_score: float
    is_healthy: bool
    created_at: Optional[str]
    updated_at: Optional[str]


class StorageStatsResponse(BaseModel):
    """存储统计响应"""
    success: bool
    message: str
    data: Dict[str, Any]


class StorageStatsHistoryResponse(BaseModel):
    """存储统计历史响应"""
    success: bool
    message: str
    data: List[Dict[str, Any]]


class StorageOverviewData(BaseModel):
    """存储概览数据"""
    storage_id: int
    storage_name: str
    storage_type: str
    total_files: int
    total_size: int
    health_score: float
    is_healthy: bool
    last_updated: Optional[str]


class StorageOverviewSummary(BaseModel):
    """存储概览汇总"""
    total_storages: int
    healthy_storages: int
    total_files: int
    total_size: int
    health_rate: float


class StorageOverviewResponse(BaseModel):
    """存储概览响应"""
    success: bool
    message: str
    data: Dict[str, Any]


class StorageHealthDetail(BaseModel):
    """存储健康详情"""
    storage_id: int
    storage_name: str
    storage_type: str
    status: str
    health_score: float
    last_error: Optional[str]


class StorageHealthReport(BaseModel):
    """存储健康报告"""
    total_storages: int
    healthy_storages: int
    warning_storages: int
    critical_storages: int
    details: List[StorageHealthDetail]


class StorageHealthResponse(BaseModel):
    """存储健康响应"""
    success: bool
    message: str
    data: StorageHealthReport
