'use client';

import React, { useState } from 'react';
import { checkHealth, login, testCors } from '@/lib/api';

const TestApiPage: React.FC = () => {
  const [healthStatus, setHealthStatus] = useState<string>('');
  const [loginStatus, setLoginStatus] = useState<string>('');
  const [corsStatus, setCorsStatus] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testHealth = async () => {
    setLoading(true);
    try {
      const result = await checkHealth();
      setHealthStatus(`✅ API健康检查成功: ${JSON.stringify(result)}`);
    } catch (error) {
      setHealthStatus(`❌ API健康检查失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    try {
      console.log('🧪 Starting login test...');
      const result = await login({ username: 'admin', password: 'password' });
      setLoginStatus(`✅ 登录成功: ${JSON.stringify(result)}`);
    } catch (error) {
      console.error('🧪 Login test failed:', error);
      setLoginStatus(`❌ 登录失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch = async () => {
    setLoading(true);
    try {
      console.log('🧪 Testing direct fetch...');
      const response = await fetch('http://127.0.0.1:8000/health');
      const data = await response.json();
      setHealthStatus(`✅ 直接请求成功: ${JSON.stringify(data)}`);
    } catch (error) {
      console.error('🧪 Direct fetch failed:', error);
      setHealthStatus(`❌ 直接请求失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testCorsConfig = async () => {
    setLoading(true);
    try {
      console.log('🧪 Testing CORS configuration...');
      const result = await testCors();
      setCorsStatus(`✅ CORS测试成功: ${JSON.stringify(result)}`);
    } catch (error) {
      console.error('🧪 CORS test failed:', error);
      setCorsStatus(`❌ CORS测试失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">API连接测试</h1>
        
        <div className="space-y-6">
          {/* 健康检查测试 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">健康检查测试</h2>
            <div className="space-x-2">
              <button
                onClick={testHealth}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? '测试中...' : '测试健康检查 (Axios)'}
              </button>
              <button
                onClick={testDirectFetch}
                disabled={loading}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? '测试中...' : '测试健康检查 (Fetch)'}
              </button>
            </div>
            {healthStatus && (
              <div className="mt-4 p-4 bg-gray-100 rounded">
                <pre className="text-sm">{healthStatus}</pre>
              </div>
            )}
          </div>

          {/* CORS测试 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">CORS配置测试</h2>
            <p className="text-gray-600 mb-4">
              测试跨域请求配置是否正确
            </p>
            <button
              onClick={testCorsConfig}
              disabled={loading}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? '测试中...' : '测试CORS'}
            </button>
            {corsStatus && (
              <div className="mt-4 p-4 bg-gray-100 rounded">
                <pre className="text-sm">{corsStatus}</pre>
              </div>
            )}
          </div>

          {/* 登录测试 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">登录测试</h2>
            <p className="text-gray-600 mb-4">
              测试用户名: admin, 密码: password
            </p>
            <button
              onClick={testLogin}
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              {loading ? '测试中...' : '测试登录'}
            </button>
            {loginStatus && (
              <div className="mt-4 p-4 bg-gray-100 rounded">
                <pre className="text-sm">{loginStatus}</pre>
              </div>
            )}
          </div>

          {/* API配置信息 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">API配置信息</h2>
            <div className="space-y-2 text-sm">
              <p><strong>API Base URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000'}</p>
              <p><strong>健康检查端点:</strong> /health</p>
              <p><strong>登录端点:</strong> /api/v1/auth/login</p>
            </div>
          </div>

          {/* 网络诊断 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">网络诊断</h2>
            <div className="space-y-2 text-sm">
              <p><strong>当前域名:</strong> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}</p>
              <p><strong>用户代理:</strong> {typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestApiPage;
