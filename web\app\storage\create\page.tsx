'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/Auth/AuthGuard';
import MainLayout from '@/components/Layout/MainLayout';
import { 
  ArrowLeft, 
  Save, 
  HardDrive, 
  Server, 
  Cloud, 
  Database,
  FolderOpen,
  TestTube,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

interface StorageFormData {
  name: string;
  storage_type: string;
  is_default: boolean;
  is_active: boolean;
  config: {
    base_path?: string;
    host?: string;
    port?: number;
    username?: string;
    password?: string;
    endpoint?: string;
    access_key?: string;
    secret_key?: string;
    bucket?: string;
    region?: string;
  };
}

export default function CreateStoragePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  
  const [formData, setFormData] = useState<StorageFormData>({
    name: '',
    storage_type: 'LOCAL',
    is_default: false,
    is_active: true,
    config: {
      base_path: './storage'
    }
  });

  // 存储类型选项
  const storageTypes = [
    { value: 'LOCAL', label: '本地存储', icon: HardDrive, description: '存储在本地文件系统' },
    { value: 'FTP', label: 'FTP存储', icon: Server, description: '通过FTP协议存储' },
    { value: 'MINIO', label: 'MinIO存储', icon: Cloud, description: '兼容S3的对象存储' }
  ];

  // 更新表单数据
  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 更新配置数据
  const updateConfig = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [field]: value
      }
    }));
  };

  // 测试连接
  const testConnection = async () => {
    setTesting(true);
    setTestResult(null);
    
    try {
      // 这里可以添加测试连接的逻辑
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟测试
      
      if (formData.storage_type === 'LOCAL') {
        setTestResult({ success: true, message: '本地存储路径可访问' });
      } else {
        setTestResult({ success: true, message: '连接测试成功' });
      }
    } catch (error) {
      setTestResult({ success: false, message: '连接测试失败' });
    } finally {
      setTesting(false);
    }
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/storage/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          ...formData,
          config: formData.config
        }),
      });

      if (response.ok) {
        alert('存储配置创建成功！');
        router.push('/storage');
      } else {
        const error = await response.json();
        alert(`创建失败: ${error.detail}`);
      }
    } catch (error) {
      alert('创建失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染配置表单
  const renderConfigForm = () => {
    switch (formData.storage_type) {
      case 'LOCAL':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                存储路径
              </label>
              <div className="relative">
                <FolderOpen className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  value={formData.config.base_path || ''}
                  onChange={(e) => updateConfig('base_path', e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="./storage"
                  required
                />
              </div>
              <p className="text-sm text-gray-500 mt-1">文件存储的本地路径</p>
            </div>
          </div>
        );

      case 'FTP':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  FTP服务器
                </label>
                <input
                  type="text"
                  value={formData.config.host || ''}
                  onChange={(e) => updateConfig('host', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="ftp.example.com"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  端口
                </label>
                <input
                  type="number"
                  value={formData.config.port || 21}
                  onChange={(e) => updateConfig('port', parseInt(e.target.value))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="21"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户名
                </label>
                <input
                  type="text"
                  value={formData.config.username || ''}
                  onChange={(e) => updateConfig('username', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="username"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  密码
                </label>
                <input
                  type="password"
                  value={formData.config.password || ''}
                  onChange={(e) => updateConfig('password', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="password"
                  required
                />
              </div>
            </div>
          </div>
        );

      case 'MINIO':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                MinIO端点
              </label>
              <input
                type="text"
                value={formData.config.endpoint || ''}
                onChange={(e) => updateConfig('endpoint', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="http://localhost:9000"
                required
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Access Key
                </label>
                <input
                  type="text"
                  value={formData.config.access_key || ''}
                  onChange={(e) => updateConfig('access_key', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="access_key"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Secret Key
                </label>
                <input
                  type="password"
                  value={formData.config.secret_key || ''}
                  onChange={(e) => updateConfig('secret_key', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="secret_key"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  存储桶
                </label>
                <input
                  type="text"
                  value={formData.config.bucket || ''}
                  onChange={(e) => updateConfig('bucket', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="my-bucket"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  区域
                </label>
                <input
                  type="text"
                  value={formData.config.region || ''}
                  onChange={(e) => updateConfig('region', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="us-east-1"
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <AuthGuard>
      <MainLayout>
        <div className="min-h-full bg-gradient-to-br from-blue-50 via-white to-purple-50">
          {/* 页面头部 */}
          <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50">
            <div className="mx-auto px-6 lg:px-12">
              <div className="flex items-center justify-between h-16">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => router.back()}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5 text-gray-600" />
                  </button>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                      <Database className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h1 className="text-xl font-bold text-gray-900">添加存储配置</h1>
                      <p className="text-sm text-gray-500">创建新的文件存储配置</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 主要内容 */}
          <div className="max-w-6xl mx-auto px-6 lg:px-12 py-8">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* 基本信息 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">基本信息</h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  配置名称
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="输入存储配置名称"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  存储类型
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {storageTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <div
                        key={type.value}
                        className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all ${
                          formData.storage_type === type.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => updateFormData('storage_type', type.value)}
                      >
                        <div className="flex items-center space-x-3">
                          <Icon className={`w-6 h-6 ${
                            formData.storage_type === type.value ? 'text-blue-600' : 'text-gray-400'
                          }`} />
                          <div>
                            <h3 className="font-medium text-gray-900">{type.label}</h3>
                            <p className="text-sm text-gray-500">{type.description}</p>
                          </div>
                        </div>
                        {formData.storage_type === type.value && (
                          <CheckCircle className="absolute top-2 right-2 w-5 h-5 text-blue-600" />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.is_default}
                    onChange={(e) => updateFormData('is_default', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">设为默认存储</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => updateFormData('is_active', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">启用存储</span>
                </label>
              </div>
            </div>
          </div>

          {/* 存储配置 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">存储配置</h2>
              <button
                type="button"
                onClick={testConnection}
                disabled={testing}
                className="flex items-center space-x-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors disabled:opacity-50"
              >
                {testing ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <TestTube className="w-4 h-4" />
                )}
                <span>{testing ? '测试中...' : '测试连接'}</span>
              </button>
            </div>
            
            {renderConfigForm()}
            
            {/* 测试结果 */}
            {testResult && (
              <div className={`mt-4 p-4 rounded-lg flex items-center space-x-2 ${
                testResult.success 
                  ? 'bg-green-50 text-green-700 border border-green-200' 
                  : 'bg-red-50 text-red-700 border border-red-200'
              }`}>
                {testResult.success ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <XCircle className="w-5 h-5" />
                )}
                <span>{testResult.message}</span>
              </div>
            )}
          </div>

          {/* 提交按钮 */}
          <div className="flex items-center justify-end space-x-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-3 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl disabled:opacity-50"
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>{loading ? '创建中...' : '创建存储'}</span>
            </button>
          </div>
        </form>
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
