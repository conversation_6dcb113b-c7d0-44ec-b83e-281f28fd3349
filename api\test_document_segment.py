#!/usr/bin/env python3
"""
文档分段功能测试脚本
"""

import asyncio
import json
from datetime import datetime
from sqlalchemy.orm import Session

from app.core.database import get_sync_session
from app.models.document_segment import DocumentSegmentTask, DocumentSegment, SegmentTemplate
from app.core.init_segment_templates import init_default_segment_templates


def test_database_models():
    """测试数据库模型"""
    print("🔍 测试数据库模型...")
    
    db = next(get_sync_session())
    try:
        # 初始化默认模板
        print("📋 初始化默认分段模板...")
        init_default_segment_templates(db)
        
        # 查询模板
        templates = db.query(SegmentTemplate).all()
        print(f"✅ 成功创建 {len(templates)} 个分段模板")
        
        for template in templates:
            print(f"   - {template.template_name}: {template.description}")
        
        # 创建测试任务
        print("\n📝 创建测试分段任务...")
        task = DocumentSegmentTask(
            task_name="测试分段任务",
            description="这是一个测试任务",
            file_ids=["test_file_1", "test_file_2"],
            total_files=2,
            segment_method="paragraph",
            max_length=500,
            overlap=50,
            preserve_formatting=True,
            enable_vectorization=True,
            embedding_model="text-embedding-ada-002",
            vector_dimension=1536,
            chunk_size=1000,
            language="zh",
            remove_stopwords=False,
            normalize_text=True,
            extract_keywords=True
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 成功创建任务: {task.task_name} (ID: {task.task_id})")
        
        # 创建测试分段
        print("\n📄 创建测试分段...")
        for i in range(3):
            segment = DocumentSegment(
                task_id=task.id,
                file_id="test_file_1",
                content=f"这是测试文件1的第{i+1}个分段内容。包含一些示例文本用于测试分段功能。",
                segment_index=i,
                start_position=i * 100,
                end_position=(i + 1) * 100,
                word_count=50,
                sentence_count=2,
                vectorize_status="completed",
                quality_score=0.8,
                readability_score=0.7,
                keywords=["测试", "分段", "内容"]
            )
            db.add(segment)
        
        db.commit()
        
        # 查询结果
        segments = db.query(DocumentSegment).filter(
            DocumentSegment.task_id == task.id
        ).all()
        
        print(f"✅ 成功创建 {len(segments)} 个分段")
        
        # 更新任务统计
        task.total_segments = len(segments)
        task.total_vectors = len([s for s in segments if s.vectorize_status == "completed"])
        task.processed_files = 1
        task.progress = 50.0
        task.status = "processing"
        
        db.commit()
        
        print(f"✅ 任务统计更新完成")
        print(f"   - 总分段数: {task.total_segments}")
        print(f"   - 总向量数: {task.total_vectors}")
        print(f"   - 进度: {task.progress}%")
        
        return task.task_id
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        db.rollback()
        return None
    finally:
        db.close()


def test_api_models():
    """测试API模型"""
    print("\n🔌 测试API模型...")
    
    try:
        from app.api.v1.document_segment import (
            SegmentConfigRequest,
            CreateTaskRequest,
            CreateTemplateRequest
        )
        
        # 测试分段配置模型
        config = SegmentConfigRequest(
            method="paragraph",
            max_length=500,
            overlap=50,
            preserve_formatting=True,
            enable_vectorization=True,
            embedding_model="text-embedding-ada-002",
            vector_dimension=1536,
            chunk_size=1000,
            language="zh",
            remove_stopwords=False,
            normalize_text=True,
            extract_keywords=True
        )
        
        print("✅ 分段配置模型验证通过")
        
        # 测试任务创建模型
        task_request = CreateTaskRequest(
            task_name="API测试任务",
            description="通过API创建的测试任务",
            file_ids=["file1", "file2", "file3"],
            config=config
        )
        
        print("✅ 任务创建模型验证通过")
        
        # 测试模板创建模型
        template_request = CreateTemplateRequest(
            template_name="API测试模板",
            description="通过API创建的测试模板",
            config=config,
            is_default=False
        )
        
        print("✅ 模板创建模型验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ API模型测试失败: {str(e)}")
        return False


def test_template_functionality():
    """测试模板功能"""
    print("\n📋 测试模板功能...")
    
    db = next(get_sync_session())
    try:
        # 查询所有模板
        templates = db.query(SegmentTemplate).all()
        print(f"📊 当前共有 {len(templates)} 个模板")
        
        # 查询默认模板
        default_template = db.query(SegmentTemplate).filter(
            SegmentTemplate.is_default == True
        ).first()
        
        if default_template:
            print(f"✅ 默认模板: {default_template.template_name}")
        else:
            print("❌ 未找到默认模板")
        
        # 查询系统模板
        system_templates = db.query(SegmentTemplate).filter(
            SegmentTemplate.is_system == True
        ).all()
        
        print(f"🔧 系统模板数量: {len(system_templates)}")
        
        # 显示模板详情
        print("\n📋 模板列表:")
        for template in templates:
            print(f"   - {template.template_name}")
            print(f"     方法: {template.segment_method}")
            print(f"     长度: {template.max_length}")
            print(f"     向量化: {'是' if template.enable_vectorization else '否'}")
            print(f"     默认: {'是' if template.is_default else '否'}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 模板功能测试失败: {str(e)}")
        return False
    finally:
        db.close()


def generate_test_report(task_id):
    """生成测试报告"""
    print("\n📊 生成测试报告...")
    
    report = {
        "test_time": datetime.now().isoformat(),
        "test_results": {
            "database_models": "✅ 通过",
            "api_models": "✅ 通过", 
            "template_functionality": "✅ 通过"
        },
        "created_task_id": task_id,
        "test_summary": {
            "total_tests": 3,
            "passed_tests": 3,
            "failed_tests": 0,
            "success_rate": "100%"
        },
        "next_steps": [
            "启动前端服务测试UI界面",
            "启动后端服务测试API接口",
            "测试完整的分段流程",
            "验证任务监控功能"
        ]
    }
    
    # 保存报告
    with open("document_segment_test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("✅ 测试报告已保存到 document_segment_test_report.json")
    
    return report


def main():
    """主测试函数"""
    print("🚀 开始文档分段功能测试")
    print("=" * 50)
    
    # 测试数据库模型
    task_id = test_database_models()
    
    # 测试API模型
    api_test_result = test_api_models()
    
    # 测试模板功能
    template_test_result = test_template_functionality()
    
    # 生成测试报告
    if task_id and api_test_result and template_test_result:
        report = generate_test_report(task_id)
        
        print("\n🎉 所有测试通过！")
        print("=" * 50)
        print("📋 测试总结:")
        print(f"   - 数据库模型: ✅ 通过")
        print(f"   - API模型: ✅ 通过")
        print(f"   - 模板功能: ✅ 通过")
        print(f"   - 创建的测试任务ID: {task_id}")
        print("\n🚀 下一步:")
        print("   1. 启动前端服务: cd web && pnpm dev")
        print("   2. 启动后端服务: cd api && python -m uvicorn main:app --reload")
        print("   3. 访问文件管理页面测试UI功能")
        print("   4. 测试批量分段和任务监控功能")
        
    else:
        print("\n❌ 部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
