"""
存储接口基类
定义统一的存储操作接口
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any, BinaryIO, AsyncGenerator
from dataclasses import dataclass
from pathlib import Path


class StorageException(Exception):
    """存储操作异常"""
    pass


@dataclass
class FileInfo:
    """文件信息数据类"""
    path: str
    name: str
    size: int
    is_directory: bool
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    mime_type: Optional[str] = None
    extension: Optional[str] = None
    hash_value: Optional[str] = None
    file_metadata: Optional[Dict[str, Any]] = None
    
    @property
    def parent_path(self) -> str:
        """获取父目录路径"""
        return str(Path(self.path).parent)
    
    @property
    def file_extension(self) -> str:
        """获取文件扩展名"""
        if self.is_directory:
            return ""
        return Path(self.name).suffix.lower()


class StorageInterface(ABC):
    """存储接口抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化存储接口
        
        Args:
            config: 存储配置参数
        """
        self.config = config
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        连接到存储服务
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """断开存储服务连接"""
        pass
    
    @abstractmethod
    async def list_files(self, path: str = "/", recursive: bool = False) -> List[FileInfo]:
        """
        列出指定路径下的文件和目录
        
        Args:
            path: 目录路径
            recursive: 是否递归列出子目录
            
        Returns:
            List[FileInfo]: 文件信息列表
        """
        pass
    
    @abstractmethod
    async def get_file_info(self, path: str) -> Optional[FileInfo]:
        """
        获取文件信息
        
        Args:
            path: 文件路径
            
        Returns:
            Optional[FileInfo]: 文件信息，不存在则返回None
        """
        pass
    
    @abstractmethod
    async def file_exists(self, path: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            path: 文件路径
            
        Returns:
            bool: 文件是否存在
        """
        pass
    
    @abstractmethod
    async def create_directory(self, path: str) -> bool:
        """
        创建目录
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 创建是否成功
        """
        pass
    
    @abstractmethod
    async def delete_file(self, path: str) -> bool:
        """
        删除文件或目录
        
        Args:
            path: 文件路径
            
        Returns:
            bool: 删除是否成功
        """
        pass
    
    @abstractmethod
    async def move_file(self, source_path: str, target_path: str) -> bool:
        """
        移动/重命名文件
        
        Args:
            source_path: 源文件路径
            target_path: 目标文件路径
            
        Returns:
            bool: 移动是否成功
        """
        pass
    
    @abstractmethod
    async def copy_file(self, source_path: str, target_path: str) -> bool:
        """
        复制文件
        
        Args:
            source_path: 源文件路径
            target_path: 目标文件路径
            
        Returns:
            bool: 复制是否成功
        """
        pass
    
    @abstractmethod
    async def upload_file(self, local_path: str, remote_path: str) -> bool:
        """
        上传文件
        
        Args:
            local_path: 本地文件路径
            remote_path: 远程文件路径
            
        Returns:
            bool: 上传是否成功
        """
        pass
    
    @abstractmethod
    async def download_file(self, remote_path: str, local_path: str) -> bool:
        """
        下载文件
        
        Args:
            remote_path: 远程文件路径
            local_path: 本地文件路径
            
        Returns:
            bool: 下载是否成功
        """
        pass
    
    @abstractmethod
    async def read_file(self, path: str) -> bytes:
        """
        读取文件内容
        
        Args:
            path: 文件路径
            
        Returns:
            bytes: 文件内容
        """
        pass
    
    @abstractmethod
    async def write_file(self, path: str, content: bytes) -> bool:
        """
        写入文件内容
        
        Args:
            path: 文件路径
            content: 文件内容
            
        Returns:
            bool: 写入是否成功
        """
        pass
    
    @abstractmethod
    async def get_file_stream(self, path: str) -> AsyncGenerator[bytes, None]:
        """
        获取文件流
        
        Args:
            path: 文件路径
            
        Yields:
            bytes: 文件数据块
        """
        pass
    
    async def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        Returns:
            Dict[str, Any]: 存储信息
        """
        return {
            "type": self.__class__.__name__,
            "config": self.config,
            "connected": await self.is_connected()
        }
    
    async def is_connected(self) -> bool:
        """
        检查是否已连接
        
        Returns:
            bool: 是否已连接
        """
        try:
            # 尝试列出根目录来检查连接状态
            await self.list_files("/")
            return True
        except Exception:
            return False
    
    def normalize_path(self, path: str) -> str:
        """
        标准化路径格式
        
        Args:
            path: 原始路径
            
        Returns:
            str: 标准化后的路径
        """
        # 统一使用正斜杠
        path = path.replace("\\", "/")
        
        # 确保以斜杠开头
        if not path.startswith("/"):
            path = "/" + path
        
        # 移除重复的斜杠
        while "//" in path:
            path = path.replace("//", "/")
        
        # 移除末尾的斜杠（除非是根目录）
        if path != "/" and path.endswith("/"):
            path = path[:-1]
        
        return path

    async def test_connection(self) -> Dict[str, Any]:
        """测试存储连接"""
        result = {
            'success': False,
            'message': '',
            'details': {}
        }

        try:
            # 尝试连接
            connected = await self.connect()
            if not connected:
                result['message'] = 'Failed to connect to storage'
                return result

            # 测试基本操作
            try:
                # 尝试列出根目录
                files = await self.list_files('/')
                result['details']['root_files_count'] = len(files)

                # 获取存储信息
                storage_info = await self.get_storage_info()
                result['details']['storage_info'] = storage_info

                result['success'] = True
                result['message'] = 'Connection test successful'

            except Exception as e:
                result['message'] = f'Connection established but operation failed: {str(e)}'

            finally:
                await self.disconnect()

        except Exception as e:
            result['message'] = f'Connection test failed: {str(e)}'

        return result
