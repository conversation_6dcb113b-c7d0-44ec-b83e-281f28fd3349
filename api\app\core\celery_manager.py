"""
Celery服务管理器
通过Python代码管理Celery Worker、<PERSON>和Flower服务
"""
import os
import sys
import time
import signal
import subprocess
import threading
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
from loguru import logger

class CeleryManager:
    """Celery服务管理器"""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.status_file = Path("celery_status.json")
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        self._shutdown_requested = False
        self._last_redis_check = 0
        self._redis_check_interval = 30  # 30秒检查一次Redis连接
        self._last_redis_status = False

        # 在FastAPI环境中不设置信号处理器，避免冲突
        # 应用关闭时会通过shutdown_tasks()正确清理
        logger.debug("Celery管理器初始化完成")
    
    def _signal_handler(self, signum, frame):
        """信号处理器，确保优雅退出"""
        if self._shutdown_requested:
            return

        self._shutdown_requested = True
        logger.info("接收到退出信号，正在停止Celery服务...")

        try:
            # 在信号处理器中使用同步版本
            self._stop_all_sync()
        except Exception as e:
            logger.error(f"停止Celery服务失败: {e}")

        logger.info("Celery服务已停止")
    
    def _get_python_executable(self) -> str:
        """获取Python可执行文件路径"""
        return sys.executable
    
    def _get_celery_command(self, service_type: str) -> List[str]:
        """获取Celery命令"""
        python_exe = self._get_python_executable()
        base_cmd = [
            python_exe, "-m", "celery",
            "-A", "app.core.celery_config:celery_app"
        ]
        
        if service_type == "worker":
            return base_cmd + [
                "worker",
                "--loglevel=info",
                "--concurrency=4",
                "--queues=default,upload_queue,file_queue",
                "--hostname=worker@%h",
                f"--logfile={self.log_dir}/celery_worker.log"
            ]
        elif service_type == "beat":
            return base_cmd + [
                "beat",
                "--loglevel=info",
                f"--logfile={self.log_dir}/celery_beat.log"
            ]
        elif service_type == "flower":
            # 检查端口是否可用，如果不可用则使用其他端口
            flower_port = self._find_available_port(5555)
            return base_cmd + [
                "flower",
                f"--port={flower_port}",
                "--basic_auth=admin:password",
                f"--logfile={self.log_dir}/celery_flower.log"
            ]
        else:
            raise ValueError(f"未知的服务类型: {service_type}")

    def _find_available_port(self, start_port: int) -> int:
        """查找可用端口"""
        import socket

        for port in range(start_port, start_port + 10):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    logger.info(f"找到可用端口: {port}")
                    return port
            except OSError:
                continue

        # 如果都不可用，返回原始端口
        logger.warning(f"未找到可用端口，使用默认端口: {start_port}")
        return start_port

    def _check_redis_connection(self) -> bool:
        """检查Redis连接（带缓存机制）"""
        current_time = time.time()

        # 如果距离上次检查时间不足间隔，返回缓存的结果
        if current_time - self._last_redis_check < self._redis_check_interval:
            return self._last_redis_status

        try:
            import redis
            redis_host = os.getenv('REDIS_HOST', '**************')
            redis_port = int(os.getenv('REDIS_PORT', '6379'))
            redis_db = int(os.getenv('REDIS_DB', '10'))
            redis_password = os.getenv('REDIS_PASSWORD', '')

            # 创建Redis连接，包含密码
            r = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password if redis_password else None,
                decode_responses=False,
                socket_timeout=5,  # 5秒超时
                socket_connect_timeout=5
            )
            r.ping()

            # 更新缓存
            self._last_redis_check = current_time
            self._last_redis_status = True

            # 只在状态改变时记录日志
            if not hasattr(self, '_redis_was_connected') or not self._redis_was_connected:
                logger.info("Redis连接成功")
                self._redis_was_connected = True

            return True

        except Exception as e:
            # 更新缓存
            self._last_redis_check = current_time
            self._last_redis_status = False

            # 只在状态改变时记录错误日志
            if not hasattr(self, '_redis_was_connected') or self._redis_was_connected:
                logger.warning(f"Redis连接失败: {e}")
                self._redis_was_connected = False

            return False
    
    def start_service(self, service_type: str) -> bool:
        """启动指定服务"""
        if service_type in self.processes:
            if self.processes[service_type].poll() is None:
                logger.info(f"{service_type} 服务已在运行")
                return True
        
        try:
            # 检查Redis连接
            if not self._check_redis_connection():
                logger.error("Redis连接失败，无法启动Celery服务")
                return False
            
            cmd = self._get_celery_command(service_type)
            logger.info(f"启动 {service_type} 服务: {' '.join(cmd)}")
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONPATH'] = f"{os.getcwd()}{os.pathsep}{env.get('PYTHONPATH', '')}"
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                cwd=os.getcwd(),
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            self.processes[service_type] = process
            
            # 等待一下确保启动成功
            time.sleep(2)
            
            if process.poll() is None:
                logger.info(f"{service_type} 服务启动成功 (PID: {process.pid})")
                self._save_status()
                return True
            else:
                # 获取错误输出
                try:
                    stdout, stderr = process.communicate(timeout=1)
                    error_msg = stderr.decode('utf-8') if stderr else stdout.decode('utf-8')
                    logger.error(f"{service_type} 服务启动失败，错误信息: {error_msg}")
                except:
                    logger.error(f"{service_type} 服务启动失败")
                return False
                
        except Exception as e:
            logger.error(f"启动 {service_type} 服务失败: {e}")
            return False
    
    def stop_service(self, service_type: str) -> bool:
        """停止指定服务"""
        if service_type not in self.processes:
            logger.info(f"{service_type} 服务未运行")
            return True
        
        try:
            process = self.processes[service_type]
            
            if process.poll() is not None:
                logger.info(f"{service_type} 服务已停止")
                del self.processes[service_type]
                self._save_status()
                return True
            
            logger.info(f"正在停止 {service_type} 服务 (PID: {process.pid})")
            
            # 优雅停止
            if os.name == 'nt':
                # Windows
                process.terminate()
            else:
                # Unix/Linux
                process.send_signal(signal.SIGTERM)
            
            # 等待进程结束
            try:
                process.wait(timeout=10)
                logger.info(f"{service_type} 服务已停止")
            except subprocess.TimeoutExpired:
                # 强制杀死
                logger.warning(f"{service_type} 服务未响应，强制停止")
                process.kill()
                process.wait()
            
            del self.processes[service_type]
            self._save_status()
            return True
            
        except Exception as e:
            logger.error(f"停止 {service_type} 服务失败: {e}")
            return False
    
    def start_all(self) -> Dict[str, bool]:
        """启动所有服务"""
        results = {}
        services = ["worker", "beat", "flower"]
        
        for service in services:
            results[service] = self.start_service(service)
            if results[service]:
                time.sleep(1)  # 服务间启动间隔
        
        return results
    
    def stop_all(self) -> Dict[str, bool]:
        """停止所有服务"""
        results = {}
        services = ["flower", "beat", "worker"]  # 反向停止
        
        for service in services:
            results[service] = self.stop_service(service)
        
        return results

    def _stop_all_sync(self) -> Dict[str, bool]:
        """同步版本的停止所有服务（用于信号处理器）"""
        results = {}
        services = ["flower", "beat", "worker"]  # 反向停止

        for service in services:
            try:
                if service in self.processes:
                    process = self.processes[service]

                    if process.poll() is not None:
                        logger.info(f"{service} 服务已停止")
                        del self.processes[service]
                        results[service] = True
                        continue

                    logger.info(f"正在停止 {service} 服务 (PID: {process.pid})")

                    # 优雅停止
                    if os.name == 'nt':
                        process.terminate()
                    else:
                        process.send_signal(signal.SIGTERM)

                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        logger.info(f"{service} 服务已停止")
                    except subprocess.TimeoutExpired:
                        logger.warning(f"{service} 服务未响应，强制停止")
                        process.kill()
                        process.wait()

                    del self.processes[service]
                    results[service] = True
                else:
                    results[service] = True

            except Exception as e:
                logger.error(f"停止 {service} 服务失败: {e}")
                results[service] = False

        return results

    def _get_real_process_info(self, service_type: str) -> Dict[str, Any]:
        """获取真实的进程信息"""
        import psutil

        # 根据服务类型构建进程名称模式
        process_patterns = {
            "worker": ["celery", "worker"],
            "beat": ["celery", "beat"],
            "flower": ["celery", "flower"]
        }

        pattern = process_patterns.get(service_type, [])

        try:
            # 遍历所有进程查找匹配的Celery进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline and len(cmdline) > 1:
                        cmdline_str = ' '.join(cmdline).lower()

                        # 检查是否包含所有模式关键词
                        if all(p in cmdline_str for p in pattern):
                            return {
                                "running": True,
                                "pid": proc.info['pid'],
                                "name": proc.info['name'],
                                "cmdline": cmdline_str
                            }
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

        except Exception as e:
            logger.warning(f"获取 {service_type} 真实进程信息失败: {e}")

        return {
            "running": False,
            "pid": None,
            "name": None,
            "cmdline": None
        }

    def get_status(self) -> Dict[str, Any]:
        """获取所有服务状态"""
        status = {
            "redis_connected": self._check_redis_connection(),
            "services": {}
        }

        for service_type in ["worker", "beat", "flower"]:
            # 首先尝试从进程对象获取信息
            process_info = None
            if service_type in self.processes:
                process = self.processes[service_type]
                try:
                    is_running = process.poll() is None
                    if is_running:
                        process_info = {
                            "running": True,
                            "pid": process.pid,
                            "source": "process_object"
                        }
                    else:
                        # 进程已结束，清理进程对象
                        logger.info(f"{service_type} 进程对象已失效，清理中...")
                        del self.processes[service_type]
                except Exception as e:
                    logger.warning(f"检查 {service_type} 进程对象失败: {e}")
                    # 进程对象无效，清理它
                    try:
                        del self.processes[service_type]
                    except:
                        pass

            # 如果进程对象无效或不存在，尝试通过系统进程查找
            if not process_info or not process_info["running"]:
                real_info = self._get_real_process_info(service_type)
                if real_info["running"]:
                    process_info = {
                        "running": True,
                        "pid": real_info["pid"],
                        "source": "system_scan",
                        "cmdline": real_info["cmdline"]
                    }
                    logger.info(f"通过系统扫描找到 {service_type} 进程 (PID: {real_info['pid']})")

            # 设置最终状态
            if process_info and process_info["running"]:
                status["services"][service_type] = {
                    "running": True,
                    "pid": process_info["pid"],
                    "source": process_info.get("source", "unknown")
                }
            else:
                status["services"][service_type] = {
                    "running": False,
                    "pid": None,
                    "source": "not_found"
                }

        return status
    
    def restart_service(self, service_type: str) -> bool:
        """重启指定服务"""
        logger.info(f"重启 {service_type} 服务")
        self.stop_service(service_type)
        time.sleep(2)
        return self.start_service(service_type)
    
    def restart_all(self) -> Dict[str, bool]:
        """重启所有服务"""
        logger.info("重启所有Celery服务")
        self.stop_all()
        time.sleep(3)
        return self.start_all()
    
    def _save_status(self):
        """保存状态到文件"""
        try:
            status = self.get_status()
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"保存状态文件失败: {e}")
    
    def load_status(self) -> Dict[str, Any]:
        """从文件加载状态"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载状态文件失败: {e}")
        
        return self.get_status()


# 全局管理器实例
celery_manager = CeleryManager()
