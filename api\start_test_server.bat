@echo off
echo 启动测试API服务器...
echo.

cd /d "%~dp0"

echo 检查Python环境...
if not exist ".venv\Scripts\python.exe" (
    echo 错误: 找不到Python虚拟环境
    echo 请确保已创建虚拟环境: .venv\Scripts\python.exe
    pause
    exit /b 1
)

echo 检查依赖...
.venv\Scripts\python.exe -c "import psutil; print('psutil 可用')" 2>nul
if errorlevel 1 (
    echo 安装psutil依赖...
    .venv\Scripts\python.exe -m pip install psutil
)

echo.
echo 启动测试API服务器 (端口: 8001)...
echo 访问地址: http://127.0.0.1:8001
echo 按 Ctrl+C 停止服务器
echo.

.venv\Scripts\python.exe test_apis.py

pause
