'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Search, Zap, Target, Brain } from 'lucide-react';
import MainLayout from '@/components/Layout/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';

const SearchPage: React.FC = () => {
  const { t, isLoading: langLoading } = useLanguage();

  if (langLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <Search className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t?.navigation?.search || '智能搜索'}
          </h1>
          <p className="text-gray-600 mb-8">
            使用AI技术进行智能内容搜索和分析
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <Zap className="w-8 h-8 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">快速搜索</h3>
              <p className="text-gray-600 text-sm">毫秒级响应的智能搜索体验</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <Target className="w-8 h-8 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">精准匹配</h3>
              <p className="text-gray-600 text-sm">基于语义理解的精准内容匹配</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <Brain className="w-8 h-8 text-purple-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">AI分析</h3>
              <p className="text-gray-600 text-sm">智能分析和推荐相关内容</p>
            </motion.div>
          </div>
          
          <div className="mt-12 text-gray-500">
            <p>智能搜索功能正在开发中，敬请期待...</p>
          </div>
        </motion.div>
        </div>
      </div>
    </MainLayout>
  );
};

export default SearchPage;
