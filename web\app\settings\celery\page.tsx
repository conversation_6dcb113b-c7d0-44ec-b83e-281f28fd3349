'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Activity } from 'lucide-react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/Layout/MainLayout';
import CeleryControl from '@/components/Settings/CeleryControl';
import { useLanguage } from '@/contexts/LanguageContext';

const CeleryManagementPage: React.FC = () => {
  const router = useRouter();
  const { t, isLoading: langLoading } = useLanguage();

  if (langLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面头部 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            {/* 返回按钮 */}
            <div className="flex items-center mb-6">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.push('/settings')}
                className="inline-flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回设置
              </motion.button>
            </div>

            {/* 页面标题 */}
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl mb-4">
                <Activity className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Celery任务队列管理
              </h1>
              <p className="text-gray-600 max-w-2xl mx-auto">
                管理和监控异步任务处理服务，包括Worker进程、Beat调度器和Flower监控界面的完整控制
              </p>
            </div>
          </motion.div>

          {/* Celery控制组件 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <CeleryControl />
          </motion.div>



          {/* 帮助信息 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mt-8"
          >
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">💡 使用说明</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div>
                  <h4 className="font-medium mb-2">服务状态</h4>
                  <ul className="space-y-1 text-blue-700">
                    <li>• Worker: 处理异步任务的工作进程</li>
                    <li>• Beat: 定时任务调度器</li>
                    <li>• Flower: Web监控界面</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">操作说明</h4>
                  <ul className="space-y-1 text-blue-700">
                    <li>• 启动: 启动所有Celery服务</li>
                    <li>• 重启: 重新启动服务以应用配置</li>
                    <li>• 停止: 安全停止所有服务</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </MainLayout>
  );
};

export default CeleryManagementPage;
