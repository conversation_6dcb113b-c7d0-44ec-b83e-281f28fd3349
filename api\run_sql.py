#!/usr/bin/env python3
"""
执行SQL文件创建表
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import asyncpg

async def run_sql():
    """执行SQL文件"""
    try:
        print("开始连接数据库...")
        
        # 数据库连接参数
        conn = await asyncpg.connect(
            host='**************',
            port=5432,
            user='postgres',
            password='XHC12345',
            database='xhc_rag'
        )
        
        print("数据库连接成功")
        
        # 读取SQL文件
        with open('create_segment_tables.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("开始执行SQL...")
        
        # 执行SQL
        await conn.execute(sql_content)
        
        print("SQL执行成功")
        
        # 验证表是否创建
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%segment%'
            ORDER BY table_name
        """)
        
        print("创建的表:")
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # 检查模板数量
        template_count = await conn.fetchval("SELECT COUNT(*) FROM segment_templates")
        print(f"分段模板数量: {template_count}")
        
        await conn.close()
        print("✅ 数据库表创建完成")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_sql())
