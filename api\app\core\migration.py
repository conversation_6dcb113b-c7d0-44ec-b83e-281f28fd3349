"""
数据库迁移管理器
自动检测和同步模型与数据库表结构
"""

import asyncio
from typing import Dict, List, Any, Optional, Set
from sqlalchemy import text, inspect, MetaData, Table, Column
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine
from loguru import logger

from app.models.base import BaseModel

# 导入所有模型以确保它们被注册到metadata中
from app.models.user import User
from app.models.file_management import StorageConfig, FileRecord, SyncLog


class DatabaseMigrationManager:
    """数据库迁移管理器"""

    def __init__(self):
        # 延迟导入数据库引擎
        from app.core.database import async_engine, sync_engine
        self.async_engine = async_engine
        self.sync_engine = sync_engine
        self.metadata = BaseModel.metadata

        if not self.async_engine or not self.sync_engine:
            raise RuntimeError("Database engines not initialized")
        
    async def check_and_migrate(self) -> Dict[str, Any]:
        """检查并执行数据库迁移"""
        result = {
            "status": "success",
            "actions": [],
            "errors": [],
            "summary": {}
        }
        
        try:
            # 1. 检查数据库连接
            await self._check_connection()
            result["actions"].append("Database connection verified")
            
            # 2. 获取数据库类型
            db_type = self._get_database_type()
            result["database_type"] = db_type
            result["actions"].append(f"Database type detected: {db_type}")
            
            # 3. 检查表结构差异
            differences = await self._compare_schemas()
            result["differences"] = differences
            
            # 4. 执行迁移
            if differences["missing_tables"] or differences["schema_changes"]:
                migration_result = await self._execute_migrations(differences)
                result["actions"].extend(migration_result["actions"])
                result["errors"].extend(migration_result["errors"])
            else:
                result["actions"].append("No migrations needed - database is up to date")
            
            # 5. 生成摘要
            result["summary"] = {
                "total_tables": len(self.metadata.tables),
                "missing_tables": len(differences["missing_tables"]),
                "modified_tables": len(differences["schema_changes"]),
                "migrations_executed": len([a for a in result["actions"] if "Created" in a or "Modified" in a])
            }
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            result["status"] = "error"
            result["errors"].append(str(e))
            
        return result
    
    async def _check_connection(self):
        """检查数据库连接"""
        async with self.async_engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
    
    def _get_database_type(self) -> str:
        """获取数据库类型"""
        dialect = self.async_engine.dialect.name
        return dialect
    
    async def _compare_schemas(self) -> Dict[str, Any]:
        """比较模型定义与数据库实际结构"""
        differences = {
            "missing_tables": [],
            "extra_tables": [],
            "schema_changes": {}
        }
        
        try:
            # 获取数据库中现有的表
            async with self.async_engine.begin() as conn:
                # 使用同步方式获取表信息
                def get_existing_tables(sync_conn):
                    inspector = inspect(sync_conn)
                    return inspector.get_table_names()

                existing_tables = await conn.run_sync(get_existing_tables)
            
            # 获取模型定义的表
            model_tables = set(self.metadata.tables.keys())
            existing_tables_set = set(existing_tables)
            
            # 找出缺失的表
            differences["missing_tables"] = list(model_tables - existing_tables_set)
            
            # 找出多余的表（数据库中有但模型中没有的）
            differences["extra_tables"] = list(existing_tables_set - model_tables)
            
            # 检查现有表的结构差异
            for table_name in model_tables & existing_tables_set:
                table_diff = await self._compare_table_structure(table_name)
                if table_diff:
                    differences["schema_changes"][table_name] = table_diff
            
        except Exception as e:
            logger.error(f"Schema comparison failed: {e}")
            raise
            
        return differences
    
    async def _compare_table_structure(self, table_name: str) -> Optional[Dict[str, Any]]:
        """比较单个表的结构"""
        try:
            # 获取模型定义的表结构
            model_table = self.metadata.tables[table_name]
            
            # 获取数据库中的表结构
            async with self.async_engine.begin() as conn:
                def get_table_info(sync_conn):
                    inspector = inspect(sync_conn)
                    columns = inspector.get_columns(table_name)
                    indexes = inspector.get_indexes(table_name)
                    foreign_keys = inspector.get_foreign_keys(table_name)
                    return columns, indexes, foreign_keys

                db_columns, db_indexes, db_foreign_keys = await conn.run_sync(get_table_info)
            
            differences = {}
            
            # 比较列
            model_columns = {col.name: col for col in model_table.columns}
            db_columns_dict = {col['name']: col for col in db_columns}
            
            missing_columns = set(model_columns.keys()) - set(db_columns_dict.keys())
            extra_columns = set(db_columns_dict.keys()) - set(model_columns.keys())
            
            if missing_columns:
                differences["missing_columns"] = list(missing_columns)
            if extra_columns:
                differences["extra_columns"] = list(extra_columns)
            
            # 检查列类型差异（简化版本）
            type_differences = []
            for col_name in set(model_columns.keys()) & set(db_columns_dict.keys()):
                model_col = model_columns[col_name]
                db_col = db_columns_dict[col_name]
                
                # 这里可以添加更详细的类型比较逻辑
                # 目前只做基本检查
                if str(model_col.type) != str(db_col.get('type', '')):
                    type_differences.append({
                        "column": col_name,
                        "model_type": str(model_col.type),
                        "db_type": str(db_col.get('type', ''))
                    })
            
            if type_differences:
                differences["type_differences"] = type_differences
            
            return differences if differences else None
            
        except Exception as e:
            logger.error(f"Table structure comparison failed for {table_name}: {e}")
            return None
    
    async def _execute_migrations(self, differences: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据库迁移"""
        result = {
            "actions": [],
            "errors": []
        }
        
        try:
            # 创建缺失的表
            if differences["missing_tables"]:
                await self._create_missing_tables(differences["missing_tables"], result)
            
            # 处理表结构变更（目前只支持添加列）
            if differences["schema_changes"]:
                await self._handle_schema_changes(differences["schema_changes"], result)
                
        except Exception as e:
            logger.error(f"Migration execution failed: {e}")
            result["errors"].append(str(e))
            
        return result
    
    async def _create_missing_tables(self, missing_tables: List[str], result: Dict[str, Any]):
        """创建缺失的表"""
        try:
            # 只创建缺失的表
            tables_to_create = [self.metadata.tables[name] for name in missing_tables]
            
            async with self.async_engine.begin() as conn:
                for table in tables_to_create:
                    await conn.run_sync(table.create)
                    result["actions"].append(f"Created table: {table.name}")
                    logger.info(f"Created table: {table.name}")
                    
        except Exception as e:
            error_msg = f"Failed to create tables: {e}"
            result["errors"].append(error_msg)
            logger.error(error_msg)
            raise
    
    async def _handle_schema_changes(self, schema_changes: Dict[str, Any], result: Dict[str, Any]):
        """处理表结构变更"""
        for table_name, changes in schema_changes.items():
            try:
                # 目前只支持添加新列
                if "missing_columns" in changes:
                    await self._add_missing_columns(table_name, changes["missing_columns"], result)
                
                # 其他变更类型可以在这里添加
                if "type_differences" in changes:
                    result["actions"].append(f"Warning: Type differences detected in table {table_name} (manual review required)")
                    
            except Exception as e:
                error_msg = f"Failed to modify table {table_name}: {e}"
                result["errors"].append(error_msg)
                logger.error(error_msg)
    
    async def _add_missing_columns(self, table_name: str, missing_columns: List[str], result: Dict[str, Any]):
        """添加缺失的列"""
        try:
            model_table = self.metadata.tables[table_name]
            db_type = self._get_database_type()
            
            async with self.async_engine.begin() as conn:
                for col_name in missing_columns:
                    column = model_table.columns[col_name]
                    
                    # 生成适合不同数据库的ALTER TABLE语句
                    alter_sql = self._generate_add_column_sql(table_name, column, db_type)
                    
                    await conn.execute(text(alter_sql))
                    result["actions"].append(f"Added column {col_name} to table {table_name}")
                    logger.info(f"Added column {col_name} to table {table_name}")
                    
        except Exception as e:
            error_msg = f"Failed to add columns to table {table_name}: {e}"
            result["errors"].append(error_msg)
            logger.error(error_msg)
            raise
    
    def _generate_add_column_sql(self, table_name: str, column: Column, db_type: str) -> str:
        """生成添加列的SQL语句，适配不同数据库类型"""
        column_def = f"{column.name} {column.type}"
        
        # 处理NOT NULL约束
        if not column.nullable:
            if column.default is not None:
                column_def += f" DEFAULT {column.default.arg if hasattr(column.default, 'arg') else column.default}"
            column_def += " NOT NULL"
        
        # 根据数据库类型调整语法
        if db_type == "postgresql":
            return f"ALTER TABLE {table_name} ADD COLUMN {column_def}"
        elif db_type == "mysql":
            return f"ALTER TABLE {table_name} ADD COLUMN {column_def}"
        elif db_type == "sqlite":
            return f"ALTER TABLE {table_name} ADD COLUMN {column_def}"
        elif db_type == "oracle":
            return f"ALTER TABLE {table_name} ADD ({column_def})"
        else:
            # 默认使用标准SQL
            return f"ALTER TABLE {table_name} ADD COLUMN {column_def}"


# 全局迁移管理器实例
migration_manager: Optional[DatabaseMigrationManager] = None


async def init_migration_manager():
    """初始化迁移管理器"""
    global migration_manager

    try:
        migration_manager = DatabaseMigrationManager()
        logger.info("Migration manager initialized")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize migration manager: {e}")
        return False


async def run_migrations() -> Dict[str, Any]:
    """运行数据库迁移"""
    global migration_manager

    if migration_manager is None:
        success = await init_migration_manager()
        if not success:
            return {
                "status": "error",
                "message": "Migration manager initialization failed"
            }

    if migration_manager is None:
        return {
            "status": "error",
            "message": "Migration manager not available"
        }

    logger.info("Starting database migration check...")
    result = await migration_manager.check_and_migrate()
    logger.info(f"Migration completed with status: {result['status']}")

    return result
