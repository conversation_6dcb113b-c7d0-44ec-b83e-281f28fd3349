"""
测试模型修复
验证SQLAlchemy模型关系是否正确配置
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_model_imports():
    """测试模型导入"""
    try:
        print("测试模型导入...")
        
        # 测试基础模型导入
        from app.models.base import BaseModel
        print("✓ BaseModel 导入成功")
        
        # 测试用户模型导入
        from app.models.user import User
        print("✓ User 模型导入成功")
        
        # 测试存储配置模型导入
        from app.models.file_management import StorageConfig
        print("✓ StorageConfig 模型导入成功")
        
        # 测试存储统计模型导入
        from app.models.storage_stats import StorageStats, StorageStatsHistory
        print("✓ StorageStats 和 StorageStatsHistory 模型导入成功")
        
        # 测试模型初始化
        from app.models import *
        print("✓ 所有模型初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型导入失败: {e}")
        return False

def test_model_relationships():
    """测试模型关系"""
    try:
        print("\n测试模型关系...")
        
        from app.models.file_management import StorageConfig
        from app.models.storage_stats import StorageStats
        
        # 检查关系是否正确配置
        if hasattr(StorageConfig, 'stats'):
            print("✓ StorageConfig.stats 关系已配置")
        else:
            print("✗ StorageConfig.stats 关系未配置")
            
        if hasattr(StorageStats, 'storage'):
            print("✓ StorageStats.storage 关系已配置")
        else:
            print("✗ StorageStats.storage 关系未配置")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型关系测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        print("\n测试数据库连接...")
        
        from app.core.database import get_db
        from app.models.file_management import StorageConfig
        
        # 获取数据库会话
        db = next(get_db())
        
        # 尝试查询存储配置
        storages = db.query(StorageConfig).limit(1).all()
        print(f"✓ 数据库连接成功，找到 {len(storages)} 个存储配置")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("模型修复验证测试")
    print("=" * 50)
    
    tests = [
        ("模型导入测试", test_model_imports),
        ("模型关系测试", test_model_relationships),
        ("数据库连接测试", test_database_connection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模型修复成功！")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
