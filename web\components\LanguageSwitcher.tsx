'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Globe, ChevronDown } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Language, languageConfig } from '@/lib/i18n';
import LoadingSpinner from './LoadingSpinner';

const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage, t, isLoading } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);

  // 如果翻译还未加载，显示加载状态
  if (isLoading || !t) {
    return (
      <div className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white">
        <LoadingSpinner size="sm" />
        <span className="text-sm">Loading...</span>
      </div>
    );
  }

  const languages: { code: Language; name: string; flag: string }[] = [
    { code: 'zh-CN', name: t.language.chinese, flag: languageConfig['zh-CN'].flag },
    { code: 'zh-TW', name: t.language.traditionalChinese, flag: languageConfig['zh-TW'].flag },
    { code: 'en', name: t.language.english, flag: languageConfig['en'].flag },
    { code: 'ja', name: t.language.japanese, flag: languageConfig['ja'].flag },
  ];

  const currentLanguage = languages.find(lang => lang.code === language);

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-200"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Globe size={16} />
        <span className="text-sm font-medium">
          {currentLanguage?.flag} {currentLanguage?.name}
        </span>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown size={14} />
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* 下拉菜单 */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full mt-2 right-0 z-50 min-w-[200px] bg-white/95 backdrop-blur-md rounded-xl border border-white/20 shadow-2xl overflow-hidden"
            >
              <div className="py-2">
                {languages.map((lang) => (
                  <motion.button
                    key={lang.code}
                    onClick={() => handleLanguageChange(lang.code)}
                    className={`w-full px-4 py-3 text-left flex items-center space-x-3 hover:bg-blue-50 transition-colors duration-150 ${
                      language === lang.code 
                        ? 'bg-blue-100 text-blue-700 font-medium' 
                        : 'text-gray-700'
                    }`}
                    whileHover={{ x: 4 }}
                    transition={{ duration: 0.1 }}
                  >
                    <span className="text-lg">{lang.flag}</span>
                    <span className="text-sm">{lang.name}</span>
                    {language === lang.code && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="ml-auto w-2 h-2 bg-blue-500 rounded-full"
                      />
                    )}
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSwitcher;
