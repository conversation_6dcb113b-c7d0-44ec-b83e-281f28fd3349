'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, Scissors, Brain, Zap, Loader2, CheckCircle, 
  AlertCircle, FileText, Settings,
  ChevronLeft, ChevronRight
} from 'lucide-react';

// 测试数据
const mockFiles = [
  {
    file_id: 'file_1',
    file_name: '深圳市政务云服务申请表.docx',
    file_size: 1024000,
    file_size_formatted: '1.0 MB',
    file_extension: 'docx'
  },
  {
    file_id: 'file_2', 
    file_name: '民生警务平台汇总.xlsx',
    file_size: 2048000,
    file_size_formatted: '2.0 MB',
    file_extension: 'xlsx'
  },
  {
    file_id: 'file_3',
    file_name: '技术文档说明.pdf',
    file_size: 512000,
    file_size_formatted: '512 KB',
    file_extension: 'pdf'
  }
];

const mockSegments = [
  {
    id: 1,
    segment_id: 'seg_001',
    content: '深圳市政务云服务是面向全市各级政府部门和事业单位提供的统一云计算服务平台。该平台整合了计算、存储、网络等基础设施资源，为政务应用提供安全、可靠、高效的云服务支撑。',
    segment_index: 0,
    word_count: 85,
    sentence_count: 2,
    quality_score: 0.92,
    vectorize_status: 'COMPLETED',
    keywords: ['政务云', '云计算', '基础设施', '政府部门'],
    created_at: '2025-06-16T14:30:00Z'
  },
  {
    id: 2,
    segment_id: 'seg_002', 
    content: '申请单位需要填写详细的资源需求信息，包括CPU核数、内存大小、存储容量、网络带宽等技术参数。同时需要说明应用场景、预期用户数量、数据安全等级等业务需求。',
    segment_index: 1,
    word_count: 78,
    sentence_count: 2,
    quality_score: 0.88,
    vectorize_status: 'PROCESSING',
    keywords: ['资源需求', 'CPU', '内存', '存储', '网络带宽'],
    created_at: '2025-06-16T14:31:00Z'
  },
  {
    id: 3,
    segment_id: 'seg_003',
    content: '审批流程包括初审、技术评估、安全评估、领导审批等环节。整个流程预计需要5-10个工作日完成。申请单位可通过系统实时查看审批进度。',
    segment_index: 2,
    word_count: 65,
    sentence_count: 3,
    quality_score: 0.85,
    vectorize_status: 'PENDING',
    keywords: ['审批流程', '技术评估', '安全评估', '工作日'],
    created_at: '2025-06-16T14:32:00Z'
  }
];

const TestLayoutPage: React.FC = () => {
  const [selectedFileId, setSelectedFileId] = useState('file_1');
  const [taskStatus, setTaskStatus] = useState<'idle' | 'running' | 'completed'>('running');
  const [taskName, setTaskName] = useState('批量分段任务 - 2025/6/16 22:46:35');
  
  const fileProgress = {
    'file_1': { status: 'completed', progress: 100, segments: 15 },
    'file_2': { status: 'processing', progress: 60, segments: 8 },
    'file_3': { status: 'pending', progress: 0, segments: 0 }
  };

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex flex-col">
      {/* 头部导航 */}
      <div className="bg-white/60 backdrop-blur-lg shadow-sm border-b border-white/30 flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200">
                <ArrowLeft className="w-5 h-5" />
              </button>

              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Scissors className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    AI智能批量分段
                  </h1>
                  <p className="text-sm text-gray-500 flex items-center space-x-1">
                    <Brain className="w-3 h-3 text-purple-500" />
                    <span>{mockFiles.length} 个文件待处理</span>
                    <span>•</span>
                    <span className="text-blue-600 font-medium">处理中...</span>
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 px-4 py-2 bg-blue-100/50 rounded-xl">
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                <span className="text-sm font-medium text-blue-700">AI处理中</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：文件列表和配置 */}
        <div className="w-96 flex-shrink-0 bg-white/60 backdrop-blur-lg border-r border-white/30 overflow-y-auto">
          {/* 任务配置区域 */}
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Settings className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">任务配置</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">任务名称</label>
                <input
                  type="text"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">分段方式</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                  <option value="paragraph">按段落分段</option>
                  <option value="sentence">按句子分段</option>
                  <option value="fixed_length">固定长度分段</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">最大长度</label>
                  <input type="number" defaultValue={500} className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">重叠长度</label>
                  <input type="number" defaultValue={50} className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" />
                </div>
              </div>
            </div>
          </div>

          {/* 文件列表区域 */}
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <FileText className="w-3 h-3 text-white" />
                </div>
                <h3 className="text-md font-bold text-gray-900">待处理文件</h3>
              </div>
              <span className="text-xs text-gray-500 bg-gray-100/50 px-2 py-1 rounded-full">
                {mockFiles.length} 个
              </span>
            </div>

            <div className="space-y-3">
              {mockFiles.map((file) => {
                const progress = fileProgress[file.file_id];
                const isSelected = selectedFileId === file.file_id;
                
                return (
                  <div
                    key={file.file_id}
                    onClick={() => setSelectedFileId(file.file_id)}
                    className={`p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50/50 shadow-md' 
                        : 'border-gray-200 bg-white/50 hover:border-gray-300 hover:bg-white/70'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2 flex-1 min-w-0">
                        <FileText className={`w-4 h-4 ${isSelected ? 'text-blue-500' : 'text-gray-400'}`} />
                        <div className="min-w-0 flex-1">
                          <p className={`text-sm font-medium truncate ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                            {file.file_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {file.file_size_formatted} • {file.file_extension.toUpperCase()}
                          </p>
                        </div>
                      </div>
                    </div>

                    {progress && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-600">
                            {progress.status === 'pending' && '等待处理'}
                            {progress.status === 'processing' && '处理中...'}
                            {progress.status === 'completed' && `已完成 (${progress.segments} 段)`}
                          </span>
                          <span className="text-xs text-gray-500">{progress.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1.5">
                          <div 
                            className={`h-1.5 rounded-full transition-all duration-300 ${
                              progress.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${progress.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 右侧：分段结果列表 */}
        <div className="flex-1 flex flex-col bg-white/40 backdrop-blur-lg overflow-hidden">
          {/* 分段列表头部 */}
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                  <Scissors className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">分段结果</h3>
                  <p className="text-sm text-gray-500">
                    {mockFiles.find(f => f.file_id === selectedFileId)?.file_name}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-500 bg-gray-100/50 px-3 py-1 rounded-full">
                  共 {mockSegments.length} 个分段
                </span>
              </div>
            </div>
          </div>

          {/* 分段列表内容 */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="space-y-4">
              {mockSegments.map((segment, index) => (
                <motion.div
                  key={segment.segment_id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-xs font-bold">
                        {segment.segment_index + 1}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          分段 #{segment.segment_index + 1}
                        </p>
                        <div className="flex items-center space-x-3 text-xs text-gray-500">
                          <span>{segment.word_count} 字符</span>
                          <span>•</span>
                          <span>{segment.sentence_count} 句</span>
                          <span>•</span>
                          <span>质量: {(segment.quality_score * 100).toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {segment.vectorize_status === 'COMPLETED' && (
                        <div className="w-2 h-2 bg-green-500 rounded-full" title="已向量化" />
                      )}
                      {segment.vectorize_status === 'PROCESSING' && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" title="向量化中" />
                      )}
                      {segment.vectorize_status === 'PENDING' && (
                        <div className="w-2 h-2 bg-gray-400 rounded-full" title="待向量化" />
                      )}
                    </div>
                  </div>

                  <div className="mb-3">
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {segment.content}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {segment.keywords.map((keyword, idx) => (
                      <span
                        key={idx}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>

                  <div className="pt-3 border-t border-gray-200/50 flex items-center justify-between text-xs text-gray-500">
                    <span>ID: {segment.segment_id}</span>
                    <span>{new Date(segment.created_at).toLocaleString('zh-CN')}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLayoutPage;
