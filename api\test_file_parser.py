#!/usr/bin/env python3
"""
测试文件解析服务
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.file_parser_service import file_parser_service

def test_file_parser():
    # 测试文件路径
    test_file = "storage/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print(f"测试文件: {test_file}")
    print(f"文件大小: {os.path.getsize(test_file)} bytes")
    
    # 检查是否支持解析
    file_extension = os.path.splitext(test_file)[1]
    print(f"文件扩展名: {file_extension}")
    print(f"是否支持解析: {file_parser_service.is_supported(file_extension)}")
    
    # 尝试解析文件
    try:
        result = file_parser_service.parse_file(test_file)
        print(f"解析结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  格式: {result.get('format', 'unknown')}")
        print(f"  错误: {result.get('error', 'None')}")
        
        if result.get('success'):
            content = result.get('content', '')
            print(f"  内容长度: {len(content)} 字符")
            print(f"  内容预览: {content[:200]}...")
            
            metadata = result.get('metadata', {})
            print(f"  元数据: {metadata}")
        
    except Exception as e:
        print(f"解析失败: {e}")

if __name__ == '__main__':
    test_file_parser()
