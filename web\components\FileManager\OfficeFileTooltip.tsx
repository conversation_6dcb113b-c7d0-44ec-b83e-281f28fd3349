'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Eye,
  Edit3,
  Scissors,
  Download,
  Share2,
  Star,
  Clock,
  User,
  FileText,
  Presentation,
  FileSpreadsheet,
  File
} from 'lucide-react';

interface OfficeFileTooltipProps {
  file: {
    file_id: string;
    file_name: string;
    file_extension: string;
    file_size_formatted: string;
    modified_at: string;
    is_document: boolean;
  };
  children: React.ReactNode;
  onView: () => void;
  onEdit: () => void;
  onSegment?: () => void;
  onDownload: () => void;
}

const OfficeFileTooltip: React.FC<OfficeFileTooltipProps> = ({
  file,
  children,
  onView,
  onEdit,
  onSegment,
  onDownload
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const handleMouseEnter = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  const getFileIcon = () => {
    const extension = file.file_extension.toLowerCase();
    switch (extension) {
      case 'doc':
      case 'docx':
        return <FileText className="w-6 h-6 text-blue-500" />;
      case 'ppt':
      case 'pptx':
        return <Presentation className="w-6 h-6 text-orange-500" />;
      case 'xls':
      case 'xlsx':
        return <FileSpreadsheet className="w-6 h-6 text-green-500" />;
      case 'pdf':
        return <File className="w-6 h-6 text-red-500" />;
      default:
        return <FileText className="w-6 h-6 text-gray-500" />;
    }
  };

  const getFileTypeLabel = () => {
    const extension = file.file_extension.toLowerCase();
    switch (extension) {
      case 'doc':
      case 'docx':
        return 'Word 文档';
      case 'ppt':
      case 'pptx':
        return 'PowerPoint 演示文稿';
      case 'xls':
      case 'xlsx':
        return 'Excel 表格';
      case 'pdf':
        return 'PDF 文档';
      case 'txt':
        return '文本文档';
      case 'md':
      case 'markdown':
        return 'Markdown 文档';
      default:
        return '文档';
    }
  };

  const isSegmentable = () => {
    const segmentableExtensions = ['txt', 'md', 'markdown', 'doc', 'docx', 'pdf', 'ppt', 'pptx'];
    return segmentableExtensions.includes(file.file_extension.toLowerCase());
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="relative"
      >
        {children}
      </div>

      <AnimatePresence>
        {isVisible && (
          <>
            {/* 背景遮罩 */}
            <div className="fixed inset-0 z-40" />
            
            {/* 工具提示 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 10 }}
              transition={{ duration: 0.2 }}
              className="fixed z-50 bg-white rounded-xl shadow-2xl border border-gray-200 p-4 max-w-sm"
              style={{
                left: position.x - 150, // 居中显示
                top: position.y - 10,
                transform: 'translateY(-100%)'
              }}
            >
              {/* 文件信息头部 */}
              <div className="flex items-start space-x-3 mb-4">
                <div className="flex-shrink-0">
                  {getFileIcon()}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {file.file_name}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1">
                    {getFileTypeLabel()}
                  </p>
                  <div className="flex items-center space-x-3 mt-2 text-xs text-gray-400">
                    <div className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatDate(file.modified_at)}
                    </div>
                    <div className="flex items-center">
                      <FileText className="w-3 h-3 mr-1" />
                      {file.file_size_formatted}
                    </div>
                  </div>
                </div>
              </div>

              {/* Office功能标识 */}
              <div className="mb-4 p-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                  <span className="text-xs font-medium text-blue-700">
                    支持 Office 在线编辑
                  </span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  类似 OnlyOffice 的完整编辑体验
                </p>
              </div>

              {/* 操作按钮 */}
              <div className="grid grid-cols-2 gap-2">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onView();
                    setIsVisible(false);
                  }}
                  className="flex items-center justify-center space-x-1 px-3 py-2 bg-blue-500 text-white rounded-lg text-xs font-medium hover:bg-blue-600 transition-colors"
                >
                  <Eye className="w-3 h-3" />
                  <span>查看</span>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit();
                    setIsVisible(false);
                  }}
                  className="flex items-center justify-center space-x-1 px-3 py-2 bg-green-500 text-white rounded-lg text-xs font-medium hover:bg-green-600 transition-colors"
                >
                  <Edit3 className="w-3 h-3" />
                  <span>编辑</span>
                </motion.button>

                {isSegmentable() && onSegment && (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onSegment();
                      setIsVisible(false);
                    }}
                    className="flex items-center justify-center space-x-1 px-3 py-2 bg-purple-500 text-white rounded-lg text-xs font-medium hover:bg-purple-600 transition-colors"
                  >
                    <Scissors className="w-3 h-3" />
                    <span>分段</span>
                  </motion.button>
                )}

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownload();
                    setIsVisible(false);
                  }}
                  className="flex items-center justify-center space-x-1 px-3 py-2 bg-gray-500 text-white rounded-lg text-xs font-medium hover:bg-gray-600 transition-colors"
                >
                  <Download className="w-3 h-3" />
                  <span>下载</span>
                </motion.button>
              </div>

              {/* 箭头指示器 */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                <div className="w-3 h-3 bg-white border-r border-b border-gray-200 transform rotate-45"></div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default OfficeFileTooltip;
