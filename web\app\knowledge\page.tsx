'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Database, Plus, Search, BookOpen } from 'lucide-react';
import MainLayout from '@/components/Layout/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';

const KnowledgePage: React.FC = () => {
  const { t, isLoading: langLoading } = useLanguage();

  if (langLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <Database className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t?.navigation?.knowledge || '知识库'}
          </h1>
          <p className="text-gray-600 mb-8">
            构建和管理您的AI知识库，让信息更智能、更易获取
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <Plus className="w-8 h-8 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">创建知识条目</h3>
              <p className="text-gray-600 text-sm">添加新的知识内容到您的知识库</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <Search className="w-8 h-8 text-blue-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">智能搜索</h3>
              <p className="text-gray-600 text-sm">使用AI技术快速找到相关信息</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <BookOpen className="w-8 h-8 text-purple-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">知识管理</h3>
              <p className="text-gray-600 text-sm">组织和维护您的知识库内容</p>
            </motion.div>
          </div>
          
          <div className="mt-12 text-gray-500">
            <p>知识库功能正在开发中，敬请期待...</p>
          </div>
        </motion.div>
        </div>
      </div>
    </MainLayout>
  );
};

export default KnowledgePage;
