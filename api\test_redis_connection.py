#!/usr/bin/env python3
"""
测试Redis连接的脚本
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

import redis
from loguru import logger


def test_redis_connection():
    """测试Redis连接"""
    redis_host = os.getenv('REDIS_HOST', '**************')
    redis_port = int(os.getenv('REDIS_PORT', '6379'))
    redis_db = int(os.getenv('REDIS_DB', '10'))
    redis_password = os.getenv('REDIS_PASSWORD', '')
    
    logger.info(f"测试Redis连接:")
    logger.info(f"  主机: {redis_host}")
    logger.info(f"  端口: {redis_port}")
    logger.info(f"  数据库: {redis_db}")
    logger.info(f"  密码: {'***' if redis_password else '(无)'}")
    
    try:
        # 测试1: 不带密码连接
        logger.info("测试1: 不带密码连接...")
        r1 = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=False,
            socket_timeout=5,
            socket_connect_timeout=5
        )
        r1.ping()
        logger.info("✅ 不带密码连接成功")
        return True
        
    except Exception as e1:
        logger.warning(f"❌ 不带密码连接失败: {e1}")
        
        try:
            # 测试2: 带密码连接
            logger.info("测试2: 带密码连接...")
            r2 = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password if redis_password else None,
                decode_responses=False,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            r2.ping()
            logger.info("✅ 带密码连接成功")
            return True
            
        except Exception as e2:
            logger.error(f"❌ 带密码连接失败: {e2}")
            
            try:
                # 测试3: 使用AUTH命令
                logger.info("测试3: 使用AUTH命令...")
                r3 = redis.Redis(
                    host=redis_host,
                    port=redis_port,
                    db=redis_db,
                    decode_responses=False,
                    socket_timeout=5,
                    socket_connect_timeout=5
                )
                if redis_password:
                    r3.auth(redis_password)
                r3.ping()
                logger.info("✅ AUTH命令连接成功")
                return True
                
            except Exception as e3:
                logger.error(f"❌ AUTH命令连接失败: {e3}")
                
                # 测试4: 连接到默认数据库
                try:
                    logger.info("测试4: 连接到默认数据库(db=0)...")
                    r4 = redis.Redis(
                        host=redis_host,
                        port=redis_port,
                        db=0,
                        password=redis_password if redis_password else None,
                        decode_responses=False,
                        socket_timeout=5,
                        socket_connect_timeout=5
                    )
                    r4.ping()
                    logger.info("✅ 默认数据库连接成功")
                    logger.warning(f"注意: 数据库{redis_db}可能不存在，但数据库0可用")
                    return True
                    
                except Exception as e4:
                    logger.error(f"❌ 默认数据库连接失败: {e4}")
    
    logger.error("所有Redis连接测试都失败了")
    return False


def test_redis_info():
    """获取Redis服务器信息"""
    try:
        redis_host = os.getenv('REDIS_HOST', '**************')
        redis_port = int(os.getenv('REDIS_PORT', '6379'))
        redis_password = os.getenv('REDIS_PASSWORD', '')
        
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=0,  # 使用默认数据库
            password=redis_password if redis_password else None,
            decode_responses=True,
            socket_timeout=5,
            socket_connect_timeout=5
        )
        
        info = r.info()
        logger.info("Redis服务器信息:")
        logger.info(f"  版本: {info.get('redis_version', 'unknown')}")
        logger.info(f"  模式: {info.get('redis_mode', 'unknown')}")
        logger.info(f"  已用内存: {info.get('used_memory_human', 'unknown')}")
        logger.info(f"  连接数: {info.get('connected_clients', 'unknown')}")
        logger.info(f"  数据库数量: {info.get('databases', 'unknown')}")
        
        # 检查可用的数据库
        logger.info("检查数据库...")
        for db_num in range(16):  # Redis默认有16个数据库
            try:
                r.select(db_num)
                logger.info(f"  数据库{db_num}: 可用")
            except:
                logger.debug(f"  数据库{db_num}: 不可用")
                
    except Exception as e:
        logger.error(f"获取Redis信息失败: {e}")


if __name__ == "__main__":
    logger.info("开始Redis连接测试...")
    
    success = test_redis_connection()
    
    if success:
        test_redis_info()
    
    logger.info("测试完成")
    sys.exit(0 if success else 1)
