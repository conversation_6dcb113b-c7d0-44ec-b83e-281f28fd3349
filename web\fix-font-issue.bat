@echo off
echo 修复字体问题...
echo.

cd /d "%~dp0"

echo 问题: Next.js 14.0.0 不支持 Geist 字体
echo 解决方案: 替换为 Inter 和 JetBrains Mono 字体
echo.

echo 1. 清理缓存...
if exist .next (
    echo 删除 .next 目录...
    rmdir /s /q .next
)

echo.
echo 2. 字体修复已完成:
echo ✅ layout.tsx - 替换 Geist 为 Inter
echo ✅ globals.css - 更新字体变量
echo ✅ tailwind.config.js - 添加字体配置
echo.

echo 3. 重新启动开发服务器...
echo 运行: pnpm dev
echo.

pnpm dev

pause
