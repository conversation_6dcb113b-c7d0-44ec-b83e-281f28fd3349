'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Folder,
  File,
  Upload,
  Download,
  Trash2,
  Copy,
  Move,
  Search,
  Filter,
  Grid,
  List,
  ArrowUp,
  Home,
  RefreshCw,
  Plus,
  Settings,
  Brain,
  Sparkles,
  Zap,
  Cloud,
  HardDrive,
  Database,
  Activity,
  Users,
  Clock,
  TrendingUp,
  Shield,
  Cpu,
  MemoryStick,
  Wifi,
  FolderCog,
  Scissors
} from 'lucide-react';

import FileExplorer from '@/components/FileManager/FileExplorer';
import StorageSelector from '@/components/FileManager/StorageSelector';

import CreateFileDialog from '@/components/FileManager/CreateFileDialog';

import MainLayout from '@/components/Layout/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useRouter } from 'next/navigation';
import { useStorageInfo, formatStoragePath, getStorageTypeName } from '@/hooks/useStorageInfo';

interface FileManagerPageProps {}

const FileManagerPage: React.FC<FileManagerPageProps> = () => {
  const { t, isLoading: langLoading } = useLanguage();
  const router = useRouter();

  // 状态管理
  const [currentStorage, setCurrentStorage] = useState<number | null>(null);
  const [currentPath, setCurrentPath] = useState('/');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const [showCreateFileDialog, setShowCreateFileDialog] = useState(false);

  // 获取存储信息
  const { storageInfo, loading: storageLoading } = useStorageInfo(currentStorage);

  // 刷新文件列表
  const handleRefresh = () => {
    setIsLoading(true);
    setRefreshTrigger(prev => prev + 1);
  };

  // 获取真实路径
  const getRealPath = () => {
    return formatStoragePath(storageInfo, currentPath);
  };

  // 处理批量分段
  const handleBatchSegment = () => {
    if (selectedFiles.length === 0) {
      alert('请先选择要分段的文件');
      return;
    }

    if (!currentStorage) {
      alert('请先选择存储配置');
      return;
    }

    // 生成正确的文件ID格式：storage_type_storage_id_encoded_path
    const fileIds = selectedFiles.map(filePath => {
      // 确保路径以 / 开头
      const normalizedPath = filePath.startsWith('/') ? filePath : '/' + filePath;
      // URL编码路径
      const encodedPath = encodeURIComponent(normalizedPath);
      // 生成文件ID：local_storageId_encodedPath
      return `local_${currentStorage}_${encodedPath}`;
    });

    console.log('批量分段 - 选择的文件路径:', selectedFiles);
    console.log('批量分段 - 生成的文件IDs:', fileIds);

    // 跳转到批量分段页面 - 移除双重编码
    const fileIdsParam = fileIds.join(',');
    console.log('批量分段 - URL参数:', fileIdsParam);
    router.push(`/file-manager/segment/batch?files=${fileIdsParam}`);
  };

  // 如果语言还在加载中，显示加载状态
  if (langLoading || !t) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="h-[calc(100vh-4rem)] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex">
        {/* 主要内容区域 */}
        <div className="flex flex-1 h-full">
          {/* AI现代风格侧边栏 */}
          <div className="w-80 bg-white/60 backdrop-blur-lg border-r border-white/30 flex flex-col flex-shrink-0 h-full">
            {/* AI智能文件管理标题 */}
            <div className="p-6 border-b border-white/30">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    AI智能文件管理
                  </h1>
                  <p className="text-xs text-gray-500 flex items-center space-x-1">
                    <Sparkles className="w-3 h-3 text-blue-500" />
                    <span>智能化文件处理平台</span>
                  </p>
                </div>
              </div>

              {/* 快速操作按钮 */}
              <div className="flex space-x-2">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowCreateFileDialog(true)}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md text-sm"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  新建
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    if (currentStorage) {
                      router.push(`/file-manager/upload?storageId=${currentStorage}&path=${encodeURIComponent(currentPath)}`);
                    } else {
                      alert('请先选择存储配置');
                    }
                  }}
                  disabled={!currentStorage}
                  className={`flex-1 inline-flex items-center justify-center px-3 py-2 bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-lg text-gray-700 hover:bg-white/90 hover:border-gray-300/50 transition-all duration-200 shadow-sm hover:shadow-md text-sm ${
                    !currentStorage ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <Upload className="w-4 h-4 mr-1" />
                  上传
                </motion.button>
              </div>
            </div>

            {/* 存储信息增强版 */}
            <div className="p-6 flex-1 overflow-y-auto">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Database className="w-5 h-5 mr-2 text-blue-500" />
                  存储概览
                </h3>

                {/* 存储使用情况卡片 */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-4 border border-blue-100">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700">存储使用率</span>
                    <span className="text-sm text-blue-600 font-semibold">45.2%</span>
                  </div>
                  <div className="w-full bg-white/50 rounded-full h-3 mb-3">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full shadow-sm" style={{ width: '45%' }}></div>
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">已使用:</span>
                      <span className="font-medium text-gray-900">45.2 GB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">可用:</span>
                      <span className="font-medium text-gray-900">54.8 GB</span>
                    </div>
                  </div>
                </div>

                {/* 系统信息卡片 */}
                <div className="space-y-3">
                  <div className="bg-white/70 rounded-lg p-3 border border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <HardDrive className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-gray-700">总容量</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">100 GB</span>
                    </div>
                  </div>

                  <div className="bg-white/70 rounded-lg p-3 border border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Activity className="w-4 h-4 text-orange-500" />
                        <span className="text-sm text-gray-700">文件总数</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">1,247</span>
                    </div>
                  </div>

                  <div className="bg-white/70 rounded-lg p-3 border border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4 text-purple-500" />
                        <span className="text-sm text-gray-700">共享文件</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">89</span>
                    </div>
                  </div>

                  <div className="bg-white/70 rounded-lg p-3 border border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-blue-500" />
                        <span className="text-sm text-gray-700">最近访问</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">2小时前</span>
                    </div>
                  </div>
                </div>

                {/* AI功能卡片 */}
                <div className="mt-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <Zap className="w-4 h-4 mr-2 text-yellow-500" />
                    AI智能功能
                  </h4>

                  <div className="space-y-2">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-100 rounded-lg p-3 text-left hover:from-purple-100 hover:to-pink-100 transition-all duration-200"
                    >
                      <div className="flex items-center space-x-2">
                        <Brain className="w-4 h-4 text-purple-500" />
                        <span className="text-sm font-medium text-gray-900">智能分类</span>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">自动整理文件</p>
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-lg p-3 text-left hover:from-green-100 hover:to-emerald-100 transition-all duration-200"
                    >
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="w-4 h-4 text-green-500" />
                        <span className="text-sm font-medium text-gray-900">使用分析</span>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">文件使用统计</p>
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-100 rounded-lg p-3 text-left hover:from-blue-100 hover:to-cyan-100 transition-all duration-200"
                    >
                      <div className="flex items-center space-x-2">
                        <Shield className="w-4 h-4 text-blue-500" />
                        <span className="text-sm font-medium text-gray-900">安全检测</span>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">文件安全扫描</p>
                    </motion.button>
                  </div>
                </div>

                {/* 系统状态 */}
                <div className="mt-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <Cpu className="w-4 h-4 mr-2 text-red-500" />
                    系统状态
                  </h4>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-gray-700">CPU使用率</span>
                      </div>
                      <span className="font-medium text-gray-900">23%</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-gray-700">内存使用</span>
                      </div>
                      <span className="font-medium text-gray-900">67%</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-gray-700">网络状态</span>
                      </div>
                      <span className="font-medium text-gray-900">正常</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI现代风格主文件区域 */}
          <div className="flex-1 flex flex-col bg-white/40 backdrop-blur-sm h-full">
            {/* 现代化工具栏 */}
            <div className="flex-shrink-0 bg-white/60 backdrop-blur-lg border-b border-white/30">
              <div className="px-6 py-4">
                <div className="flex items-center justify-between">
                  {/* 存储选择器和路径导航 */}
                  <div className="flex items-center space-x-4 flex-1 min-w-0">
                    {/* 存储选择器 */}
                    <div className="flex-shrink-0 min-w-48">
                      <StorageSelector
                        currentStorage={currentStorage}
                        onStorageChange={setCurrentStorage}
                        showPath={true}
                      />
                    </div>

                    {/* 分隔线 */}
                    <div className="w-px h-6 bg-gray-300 flex-shrink-0"></div>

                    {/* 路径导航 */}
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setCurrentPath('/')}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200 flex-shrink-0"
                        title="返回根目录"
                      >
                        <Home className="w-4 h-4" />
                      </motion.button>

                      <div className="flex items-center space-x-1 text-sm text-gray-600 min-w-0 overflow-hidden">
                        {/* 显示真实路径 */}
                        <div className="flex items-center space-x-1 bg-gray-100/50 px-2 py-1 rounded text-xs font-mono">
                          <HardDrive className="w-3 h-3 text-gray-500" />
                          <span className="text-gray-700 truncate" title={formatStoragePath(storageInfo, currentPath)}>
                            {storageInfo ? (
                              storageInfo.storage_type === 'local'
                                ? (storageInfo.config?.base_path || '/') + (currentPath === '/' ? '' : currentPath)
                                : formatStoragePath(storageInfo, currentPath)
                            ) : currentPath}
                          </span>
                        </div>

                        {/* 分隔符 */}
                        <span className="text-gray-400">→</span>

                        {/* 相对路径导航 */}
                        <span className="flex-shrink-0 text-gray-500">相对路径:</span>
                        <span className="flex-shrink-0">/</span>
                        {currentPath.split('/').filter(Boolean).map((segment, index, array) => (
                          <React.Fragment key={index}>
                            <motion.button
                              whileHover={{ scale: 1.02 }}
                              className="px-2 py-1 rounded hover:bg-blue-50 hover:text-blue-600 transition-colors truncate"
                              onClick={() => setCurrentPath('/' + array.slice(0, index + 1).join('/'))}
                              title={segment}
                            >
                              {segment}
                            </motion.button>
                            {index < array.length - 1 && <span className="flex-shrink-0">/</span>}
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 工具栏操作 */}
                  <div className="flex items-center space-x-3">
                    {/* 搜索框 */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="搜索文件..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 pr-4 py-2 bg-white/70 border border-gray-200/50 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
                      />
                    </div>

                    {/* 视图切换 */}
                    <div className="flex bg-white/70 rounded-lg p-1 border border-gray-200/50">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setViewMode('list')}
                        className={`p-2 rounded transition-colors ${
                          viewMode === 'list'
                            ? 'bg-blue-500 text-white shadow-sm'
                            : 'text-gray-600 hover:text-blue-600'
                        }`}
                      >
                        <List className="w-4 h-4" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setViewMode('grid')}
                        className={`p-2 rounded transition-colors ${
                          viewMode === 'grid'
                            ? 'bg-blue-500 text-white shadow-sm'
                            : 'text-gray-600 hover:text-blue-600'
                        }`}
                      >
                        <Grid className="w-4 h-4" />
                      </motion.button>
                    </div>

                    {/* 刷新按钮 */}
                    <motion.button
                      whileHover={{ scale: 1.05, rotate: 90 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleRefresh}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200"
                      title="刷新文件列表"
                    >
                      <RefreshCw className="w-4 h-4" />
                    </motion.button>

                    {/* 存储管理按钮 */}
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => router.push('/storage')}
                      className="inline-flex items-center px-3 py-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50/50 rounded-lg transition-all duration-200"
                      title="存储管理"
                    >
                      <FolderCog className="w-4 h-4 mr-2" />
                      <span className="text-sm">存储管理</span>
                    </motion.button>

                    {/* 批量操作按钮 */}
                    {selectedFiles.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-400 text-sm">|</span>

                        {/* 批量分段 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleBatchSegment}
                          className="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs hover:bg-purple-200 transition-colors"
                          title="批量分段"
                        >
                          <Scissors className="w-3 h-3 mr-1" />
                          分段
                        </motion.button>

                        {/* 批量下载 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            // TODO: 实现批量下载逻辑
                            alert(`批量下载 ${selectedFiles.length} 个文件`);
                          }}
                          className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200 transition-colors"
                          title="批量下载"
                        >
                          <Download className="w-3 h-3 mr-1" />
                          下载
                        </motion.button>

                        {/* 批量迁移 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            // TODO: 实现批量迁移逻辑
                            alert(`批量迁移 ${selectedFiles.length} 个文件至其它存储`);
                          }}
                          className="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 transition-colors"
                          title="批量迁移至其它存储"
                        >
                          <Move className="w-3 h-3 mr-1" />
                          迁移
                        </motion.button>

                        {/* 批量删除 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            if (confirm(`确定要删除选中的 ${selectedFiles.length} 个文件吗？`)) {
                              // TODO: 实现批量删除逻辑
                              alert(`批量删除 ${selectedFiles.length} 个文件`);
                            }
                          }}
                          className="inline-flex items-center px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200 transition-colors"
                          title="批量删除"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          删除
                        </motion.button>
                      </div>
                    )}


                  </div>
                </div>
              </div>
            </div>

            {/* 文件浏览器 */}
            <div className="flex-1 overflow-hidden">
              <FileExplorer
                storageId={currentStorage}
                currentPath={currentPath}
                onPathChange={setCurrentPath}
                viewMode={viewMode}
                selectedFiles={selectedFiles}
                onSelectionChange={setSelectedFiles}
                searchQuery={searchQuery}
                sortBy={sortBy}
                sortOrder={sortOrder}
                isLoading={isLoading}
                onLoadingChange={setIsLoading}
                refreshTrigger={refreshTrigger}
              />
            </div>

            {/* AI现代风格状态栏 */}
            <div className="flex-shrink-0 bg-white/60 backdrop-blur-lg border-t border-white/30">
              <div className="px-6 py-3">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4 text-gray-600">
                    <span>已选择 {selectedFiles.length} 个项目</span>
                    <span>•</span>
                    <span>相对路径: {currentPath}</span>
                    <span>•</span>
                    <span className="font-mono text-xs" title={formatStoragePath(storageInfo, currentPath)}>
                      真实路径: {storageInfo ? formatStoragePath(storageInfo, currentPath) : currentPath}
                    </span>

                    {/* 批量操作按钮 */}
                    {selectedFiles.length > 0 && (
                      <div className="flex items-center space-x-2 ml-4">
                        <span className="text-gray-400">|</span>

                        {/* 批量分段 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleBatchSegment}
                          className="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs hover:bg-purple-200 transition-colors"
                          title="批量分段"
                        >
                          <Scissors className="w-3 h-3 mr-1" />
                          分段
                        </motion.button>

                        {/* 批量下载 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            // TODO: 实现批量下载逻辑
                            alert(`批量下载 ${selectedFiles.length} 个文件`);
                          }}
                          className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200 transition-colors"
                          title="批量下载"
                        >
                          <Download className="w-3 h-3 mr-1" />
                          下载
                        </motion.button>

                        {/* 批量迁移 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            // TODO: 实现批量迁移逻辑
                            alert(`批量迁移 ${selectedFiles.length} 个文件至其它存储`);
                          }}
                          className="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 transition-colors"
                          title="批量迁移至其它存储"
                        >
                          <Move className="w-3 h-3 mr-1" />
                          迁移
                        </motion.button>

                        {/* 批量删除 */}
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            if (confirm(`确定要删除选中的 ${selectedFiles.length} 个文件吗？`)) {
                              // TODO: 实现批量删除逻辑
                              alert(`批量删除 ${selectedFiles.length} 个文件`);
                            }
                          }}
                          className="inline-flex items-center px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200 transition-colors"
                          title="批量删除"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          删除
                        </motion.button>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-4 text-gray-600">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>在线</span>
                    </div>
                    <span>•</span>
                    <span>最后同步: 刚刚</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 新建文件对话框 */}
        <CreateFileDialog
          isOpen={showCreateFileDialog}
          onClose={() => setShowCreateFileDialog(false)}
          currentPath={currentPath}
          storageId={currentStorage || 0}
          onFileCreated={(_file) => {
            // 文件创建成功后刷新列表
            setIsLoading(true);
            setShowCreateFileDialog(false);
          }}
        />
      </div>
    </MainLayout>
  );
};

export default FileManagerPage;
