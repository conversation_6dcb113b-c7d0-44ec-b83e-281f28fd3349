# 字体问题修复指南

## 🚨 问题描述

遇到以下错误：
```
`next/font` error:
Unknown font `<PERSON>eist`
```

## 🔍 问题原因

1. **Geist 字体不兼容**
   - `Geist` 字体在 Next.js 14.0.0 中不可用
   - 这是较新版本的 Next.js 才支持的字体

2. **版本兼容性问题**
   - 我们使用的是 Next.js 14.0.0 (稳定版本)
   - Geist 字体需要更新的 Next.js 版本

## ✅ 修复方案

### 1. 字体替换

将 `Geist` 字体替换为兼容的字体：
- `Geist` → `Inter` (无衬线字体)
- `Geist_Mono` → `JetBrains_Mono` (等宽字体)

### 2. 修复的文件

#### app/layout.tsx
```typescript
// 修复前
import { Geist, Geist_Mono } from "next/font/google";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// 修复后
import { Inter, JetBrains_Mono } from "next/font/google";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});
```

#### app/globals.css
```css
/* 修复前 */
@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

/* 修复后 */
@theme inline {
  --font-sans: var(--font-inter);
  --font-mono: var(--font-jetbrains-mono);
}

body {
  font-family: var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
```

#### tailwind.config.js
```javascript
// 添加字体配置
theme: {
  extend: {
    fontFamily: {
      sans: ["var(--font-inter)", "system-ui", "sans-serif"],
      mono: ["var(--font-jetbrains-mono)", "monospace"],
    },
  },
}
```

## 🚀 修复步骤

### 方法1：使用修复脚本（推荐）
```cmd
cd web
双击运行: fix-font-issue.bat
```

### 方法2：手动修复
```cmd
cd web

# 1. 清理缓存
rmdir /s /q .next

# 2. 文件已自动修复，直接启动
pnpm dev
```

## 🔍 验证修复

### 1. 检查修复状态

确保以下文件已正确修复：
- ✅ `app/layout.tsx` - 使用 Inter 和 JetBrains_Mono
- ✅ `app/globals.css` - 更新字体变量
- ✅ `tailwind.config.js` - 添加字体配置

### 2. 启动测试

运行以下命令：
```cmd
cd web
pnpm dev
```

成功的输出应该不再包含字体错误：
```
○ Compiling /file-manager ...
✓ Compiled successfully
- ready started server on 0.0.0.0:3000
```

### 3. 浏览器验证

访问 `http://localhost:3000/file-manager` 应该能正常加载页面。

## 📋 字体对比

| 原字体 | 替换字体 | 类型 | 兼容性 |
|--------|----------|------|--------|
| Geist | Inter | 无衬线 | ✅ 广泛支持 |
| Geist_Mono | JetBrains_Mono | 等宽 | ✅ 广泛支持 |

## 🎨 字体特性

### Inter 字体
- **类型**: 无衬线字体
- **特点**: 现代、清晰、易读
- **用途**: 界面文本、正文内容
- **兼容性**: Next.js 12+ 全面支持

### JetBrains Mono 字体
- **类型**: 等宽字体
- **特点**: 代码友好、字符区分度高
- **用途**: 代码显示、终端文本
- **兼容性**: Next.js 12+ 全面支持

## 🐛 常见问题

### 问题1：仍然报字体错误
**解决方案**：
```cmd
# 清理所有缓存
rmdir /s /q .next
rmdir /s /q node_modules\.cache
pnpm dev
```

### 问题2：字体显示异常
**解决方案**：
```cmd
# 检查浏览器缓存
# 按 Ctrl+Shift+R 强制刷新
# 或者清理浏览器缓存
```

### 问题3：CSS 变量未生效
**解决方案**：
检查 `globals.css` 中的字体变量是否正确：
```css
--font-sans: var(--font-inter);
--font-mono: var(--font-jetbrains-mono);
```

## 📈 性能影响

### 字体加载优化
- Inter 和 JetBrains Mono 都是 Google Fonts
- 自动优化字体加载性能
- 支持字体预加载和缓存

### 文件大小
- Inter: 约 400KB (完整字符集)
- JetBrains Mono: 约 300KB (完整字符集)
- 总体影响: 最小化，因为只加载需要的字符子集

## ✅ 修复确认

修复成功后，您应该能够：
- ✅ 启动开发服务器无字体错误
- ✅ 访问所有页面正常显示
- ✅ 字体渲染清晰美观
- ✅ 代码区域使用等宽字体
- ✅ 界面文本使用无衬线字体

## 🔄 回滚方案

如果需要回滚到其他字体：

```typescript
// 使用系统字体
const systemFont = {
  variable: "--font-system",
  // 不需要从 Google Fonts 加载
};

// 在 CSS 中使用
font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
```

## 📞 进一步支持

如果仍然遇到字体问题：

1. **检查 Next.js 版本兼容性**
2. **查看 Google Fonts 可用字体列表**
3. **考虑使用本地字体文件**
4. **检查网络连接（Google Fonts 加载）**

修复完成后，前端应用应该能够正常启动和运行！
