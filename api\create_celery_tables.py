#!/usr/bin/env python3
"""
创建Celery表的简化脚本
通过FastAPI应用上下文运行
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.models.celery_config import CeleryConfiguration, CeleryMetrics
from app.core.database import Base, engine
from loguru import logger


async def create_tables():
    """创建Celery相关表"""
    try:
        # 使用SQLAlchemy的create_all方法
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Celery表创建成功")
        
        # 创建默认配置
        from app.services.celery_config_service import celery_config_service
        config = await celery_config_service.get_active_config()
        if config:
            logger.info("默认Celery配置已存在")
        else:
            logger.info("创建默认Celery配置")
        
    except Exception as e:
        logger.error(f"创建Celery表失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        # 初始化数据库引擎
        from app.core.database import init_database
        from app.core.config import get_settings

        settings = get_settings()
        await init_database(settings)

        # 创建表
        await create_tables()

        logger.info("Celery表创建完成")

    except Exception as e:
        logger.error(f"操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
