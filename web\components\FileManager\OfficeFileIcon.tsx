'use client';

import React from 'react';
import {
  FileText,
  Presentation,
  FileSpreadsheet,
  File,
  FileImage,
  FileVideo,
  FileAudio,
  Archive,
  Code,
  Database
} from 'lucide-react';

interface OfficeFileIconProps {
  fileExtension: string;
  className?: string;
}

const OfficeFileIcon: React.FC<OfficeFileIconProps> = ({ fileExtension, className = "w-5 h-5" }) => {
  const extension = fileExtension.toLowerCase();

  // 根据文件扩展名返回对应的图标和颜色
  const getIconAndColor = () => {
    switch (extension) {
      // Word文档
      case 'doc':
      case 'docx':
        return { icon: FileText, color: 'text-blue-600' };
      
      // PowerPoint演示文稿
      case 'ppt':
      case 'pptx':
        return { icon: Presentation, color: 'text-orange-600' };
      
      // Excel表格
      case 'xls':
      case 'xlsx':
        return { icon: FileSpreadsheet, color: 'text-green-600' };
      
      // PDF文档
      case 'pdf':
        return { icon: File, color: 'text-red-600' };
      
      // 文本文件
      case 'txt':
      case 'md':
      case 'markdown':
        return { icon: FileText, color: 'text-gray-600' };
      
      // 图片文件
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
      case 'webp':
        return { icon: FileImage, color: 'text-purple-600' };
      
      // 视频文件
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return { icon: FileVideo, color: 'text-pink-600' };
      
      // 音频文件
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
      case 'ogg':
        return { icon: FileAudio, color: 'text-indigo-600' };
      
      // 压缩文件
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return { icon: Archive, color: 'text-yellow-600' };
      
      // 代码文件
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
      case 'html':
      case 'css':
      case 'json':
      case 'xml':
      case 'py':
      case 'java':
      case 'cpp':
      case 'c':
      case 'php':
      case 'rb':
      case 'go':
      case 'rs':
        return { icon: Code, color: 'text-teal-600' };
      
      // 数据库文件
      case 'sql':
      case 'db':
      case 'sqlite':
        return { icon: Database, color: 'text-cyan-600' };
      
      // 默认文件
      default:
        return { icon: File, color: 'text-gray-500' };
    }
  };

  const { icon: IconComponent, color } = getIconAndColor();

  return (
    <div className="relative">
      <IconComponent className={`${className} ${color}`} />
      
      {/* Office文件特殊标识 */}
      {['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf'].includes(extension) && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
        </div>
      )}
    </div>
  );
};

export default OfficeFileIcon;
