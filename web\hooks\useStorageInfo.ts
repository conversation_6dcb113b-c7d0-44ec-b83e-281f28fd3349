/**
 * 存储信息Hook
 * 用于获取存储配置信息，包括真实路径
 */
import { useState, useEffect } from 'react';
import apiClient from '@/lib/api';

interface StorageInfo {
  id: number;
  name: string;
  storage_type: string;
  config: {
    base_path?: string;
    endpoint?: string;
    bucket?: string;
    host?: string;
    port?: number;
    [key: string]: any;
  };
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface UseStorageInfoReturn {
  storageInfo: StorageInfo | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useStorageInfo = (storageId: number | null): UseStorageInfoReturn => {
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStorageInfo = async () => {
    if (!storageId) {
      setStorageInfo(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get(`/api/v1/file-management/storages/${storageId}`);
      
      if (response.data.success) {
        setStorageInfo(response.data.data);
      } else {
        setError(response.data.message || '获取存储信息失败');
      }
    } catch (err) {
      console.error('Failed to fetch storage info:', err);
      setError(err instanceof Error ? err.message : '获取存储信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStorageInfo();
  }, [storageId]);

  return {
    storageInfo,
    loading,
    error,
    refetch: fetchStorageInfo
  };
};

/**
 * 格式化存储路径显示
 */
export const formatStoragePath = (storageInfo: StorageInfo | null, currentPath: string): string => {
  if (!storageInfo) {
    return currentPath;
  }

  const config = storageInfo.config || {};
  
  switch (storageInfo.storage_type) {
    case 'local':
      const basePath = config.base_path || '/';
      // 组合基础路径和当前路径
      const fullPath = basePath.endsWith('/') ? basePath.slice(0, -1) : basePath;
      const relativePath = currentPath === '/' ? '' : currentPath;
      return fullPath + relativePath;
      
    case 'minio':
      const endpoint = config.endpoint || '';
      const bucket = config.bucket || '';
      return `${endpoint}/${bucket}${currentPath}`;
      
    case 'ftp':
    case 'sftp':
      const host = config.host || '';
      const port = config.port || (storageInfo.storage_type === 'ftp' ? 21 : 22);
      const ftpBasePath = config.base_path || '/';
      const ftpFullPath = ftpBasePath.endsWith('/') ? ftpBasePath.slice(0, -1) : ftpBasePath;
      return `${host}:${port}${ftpFullPath}${currentPath}`;
      
    default:
      return currentPath;
  }
};

/**
 * 获取存储类型显示名称
 */
export const getStorageTypeName = (type: string): string => {
  switch (type) {
    case 'local':
      return '本地存储';
    case 'minio':
      return 'MinIO';
    case 'ftp':
      return 'FTP';
    case 'sftp':
      return 'SFTP';
    default:
      return type.toUpperCase();
  }
};
