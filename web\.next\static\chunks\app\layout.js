/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$":
/*!**************************************************************!*\
  !*** ./lib/i18n/locales/ lazy ^\.\/.*\.ts$ namespace object ***!
  \**************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./en.ts": [
		"(app-pages-browser)/./lib/i18n/locales/en.ts",
		"_app-pages-browser_lib_i18n_locales_en_ts"
	],
	"./ja.ts": [
		"(app-pages-browser)/./lib/i18n/locales/ja.ts",
		"_app-pages-browser_lib_i18n_locales_ja_ts"
	],
	"./zh-CN.ts": [
		"(app-pages-browser)/./lib/i18n/locales/zh-CN.ts",
		"_app-pages-browser_lib_i18n_locales_zh-CN_ts"
	],
	"./zh-TW.ts": [
		"(app-pages-browser)/./lib/i18n/locales/zh-TW.ts",
		"_app-pages-browser_lib_i18n_locales_zh-TW_ts"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(function() {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(function() {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = function() { return Object.keys(map); };
webpackAsyncContext.id = "(app-pages-browser)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/LanguageContext.tsx */ \"(app-pages-browser)/./contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs */ \"(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={592:(e,r,t)=>{var n=t(722);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},722:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(592);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"850ffaee15ee\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzPzc4NWMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NTBmZmFlZTE1ZWVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: function() { return /* binding */ LanguageProvider; },\n/* harmony export */   useLanguage: function() { return /* binding */ useLanguage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(app-pages-browser)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n    const [t, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化语言设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeLanguage = async ()=>{\n            setIsLoading(true);\n            try {\n                // 预加载所有语言\n                await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.preloadAllLanguages)();\n                // 从localStorage加载语言设置\n                const savedLanguage = localStorage.getItem(\"language\");\n                let targetLanguage;\n                if (savedLanguage && [\n                    \"zh-CN\",\n                    \"zh-TW\",\n                    \"en\",\n                    \"ja\"\n                ].includes(savedLanguage)) {\n                    targetLanguage = savedLanguage;\n                } else {\n                    // 检测浏览器语言\n                    targetLanguage = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.detectBrowserLanguage)();\n                }\n                setLanguageState(targetLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(targetLanguage);\n                setTranslations(translations);\n            } catch (error) {\n                console.error(\"Failed to initialize language:\", error);\n                // 回退到默认语言\n                setLanguageState(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                setTranslations(translations);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeLanguage();\n    }, []);\n    const setLanguage = async (newLanguage)=>{\n        setIsLoading(true);\n        try {\n            setLanguageState(newLanguage);\n            const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(newLanguage);\n            setTranslations(translations);\n            localStorage.setItem(\"language\", newLanguage);\n        } catch (error) {\n            console.error(\"Failed to set language:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage,\n            t,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LanguageProvider, \"qWsdG9m0vWt2q9EkW6Pe6qQABlw=\");\n_c = LanguageProvider;\nconst useLanguage = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/LanguageContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.defaultLanguage; },\n/* harmony export */   detectBrowserLanguage: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.detectBrowserLanguage; },\n/* harmony export */   formatDate: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatDate; },\n/* harmony export */   formatNumber: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatNumber; },\n/* harmony export */   getTranslation: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslation; },\n/* harmony export */   getTranslationSync: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslationSync; },\n/* harmony export */   languageConfig: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.languageConfig; },\n/* harmony export */   preloadAllLanguages: function() { return /* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.preloadAllLanguages; }\n/* harmony export */ });\n/* harmony import */ var _i18n_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./i18n/index */ \"(app-pages-browser)/./lib/i18n/index.ts\");\n/**\n * 多语言支持 - 兼容性导出文件\n * 重新导出新架构中的所有功能\n */ // 重新导出所有类型和函数\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9pMThuLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBOzs7Q0FHQyxHQUVELGNBQWM7QUFZUSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9saWIvaTE4bi50cz80OWFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5aSa6K+t6KiA5pSv5oyBIC0g5YW85a655oCn5a+85Ye65paH5Lu2XG4gKiDph43mlrDlr7zlh7rmlrDmnrbmnoTkuK3nmoTmiYDmnInlip/og71cbiAqL1xuXG4vLyDph43mlrDlr7zlh7rmiYDmnInnsbvlnovlkozlh73mlbBcbmV4cG9ydCB0eXBlIHsgTGFuZ3VhZ2UsIFRyYW5zbGF0aW9ucyB9IGZyb20gJy4vaTE4bi9pbmRleCc7XG5cbmV4cG9ydCB7XG4gIGRlZmF1bHRMYW5ndWFnZSxcbiAgbGFuZ3VhZ2VDb25maWcsXG4gIGdldFRyYW5zbGF0aW9uLFxuICBnZXRUcmFuc2xhdGlvblN5bmMsXG4gIHByZWxvYWRBbGxMYW5ndWFnZXMsXG4gIGRldGVjdEJyb3dzZXJMYW5ndWFnZSxcbiAgZm9ybWF0TnVtYmVyLFxuICBmb3JtYXREYXRlXG59IGZyb20gJy4vaTE4bi9pbmRleCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VDb25maWciLCJnZXRUcmFuc2xhdGlvbiIsImdldFRyYW5zbGF0aW9uU3luYyIsInByZWxvYWRBbGxMYW5ndWFnZXMiLCJkZXRlY3RCcm93c2VyTGFuZ3VhZ2UiLCJmb3JtYXROdW1iZXIiLCJmb3JtYXREYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/i18n/index.ts":
/*!***************************!*\
  !*** ./lib/i18n/index.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: function() { return /* binding */ defaultLanguage; },\n/* harmony export */   detectBrowserLanguage: function() { return /* binding */ detectBrowserLanguage; },\n/* harmony export */   formatDate: function() { return /* binding */ formatDate; },\n/* harmony export */   formatNumber: function() { return /* binding */ formatNumber; },\n/* harmony export */   getTranslation: function() { return /* binding */ getTranslation; },\n/* harmony export */   getTranslationSync: function() { return /* binding */ getTranslationSync; },\n/* harmony export */   languageConfig: function() { return /* binding */ languageConfig; },\n/* harmony export */   preloadAllLanguages: function() { return /* binding */ preloadAllLanguages; }\n/* harmony export */ });\n/**\n * 国际化配置主文件\n * 支持动态加载语言文件\n */ const defaultLanguage = \"zh-CN\";\n// 语言配置\nconst languageConfig = {\n    \"zh-CN\": {\n        name: \"中文简体\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        direction: \"ltr\"\n    },\n    \"zh-TW\": {\n        name: \"中文繁體\",\n        flag: \"\\uD83C\\uDDF9\\uD83C\\uDDFC\",\n        direction: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        direction: \"ltr\"\n    },\n    \"ja\": {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        direction: \"ltr\"\n    }\n};\n// 动态导入语言文件\nconst loadTranslations = async (language)=>{\n    try {\n        const module = await __webpack_require__(\"(app-pages-browser)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(\"./\".concat(language, \".ts\"));\n        return module.default;\n    } catch (error) {\n        console.warn(\"Failed to load translations for \".concat(language, \", falling back to default\"));\n        const defaultModule = await __webpack_require__(\"(app-pages-browser)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(\"./\".concat(defaultLanguage, \".ts\"));\n        return defaultModule.default;\n    }\n};\n// 缓存已加载的翻译\nconst translationCache = new Map();\nconst getTranslation = async (language)=>{\n    if (translationCache.has(language)) {\n        return translationCache.get(language);\n    }\n    const translations = await loadTranslations(language);\n    translationCache.set(language, translations);\n    return translations;\n};\n// 同步获取翻译（用于已缓存的情况）\nconst getTranslationSync = (language)=>{\n    return translationCache.get(language) || null;\n};\n// 预加载所有语言\nconst preloadAllLanguages = async ()=>{\n    const languages = [\n        \"zh-CN\",\n        \"zh-TW\",\n        \"en\",\n        \"ja\"\n    ];\n    await Promise.all(languages.map(async (lang)=>{\n        try {\n            await getTranslation(lang);\n        } catch (error) {\n            console.warn(\"Failed to preload language \".concat(lang, \":\"), error);\n        }\n    }));\n};\n// 检测浏览器语言\nconst detectBrowserLanguage = ()=>{\n    var _navigator_languages;\n    if (false) {}\n    const browserLanguage = navigator.language || ((_navigator_languages = navigator.languages) === null || _navigator_languages === void 0 ? void 0 : _navigator_languages[0]);\n    if ((browserLanguage === null || browserLanguage === void 0 ? void 0 : browserLanguage.startsWith(\"zh-CN\")) || browserLanguage === \"zh\") {\n        return \"zh-CN\";\n    } else if ((browserLanguage === null || browserLanguage === void 0 ? void 0 : browserLanguage.startsWith(\"zh-TW\")) || browserLanguage === \"zh-Hant\") {\n        return \"zh-TW\";\n    } else if (browserLanguage === null || browserLanguage === void 0 ? void 0 : browserLanguage.startsWith(\"en\")) {\n        return \"en\";\n    } else if (browserLanguage === null || browserLanguage === void 0 ? void 0 : browserLanguage.startsWith(\"ja\")) {\n        return \"ja\";\n    }\n    return defaultLanguage;\n};\n// 格式化数字（根据语言环境）\nconst formatNumber = (number, language)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    return new Intl.NumberFormat(localeMap[language]).format(number);\n};\n// 格式化日期（根据语言环境）\nconst formatDate = (date, language, options)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(localeMap[language], options || defaultOptions).format(date);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9pMThuL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBZ0tNLE1BQU1BLGtCQUE0QixRQUFRO0FBRWpELE9BQU87QUFDQSxNQUFNQyxpQkFBaUI7SUFDNUIsU0FBUztRQUNQQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsV0FBVztJQUNiO0lBQ0EsU0FBUztRQUNQRixNQUFNO1FBQ05DLE1BQU07UUFDTkMsV0FBVztJQUNiO0lBQ0EsTUFBTTtRQUNKRixNQUFNO1FBQ05DLE1BQU07UUFDTkMsV0FBVztJQUNiO0lBQ0EsTUFBTTtRQUNKRixNQUFNO1FBQ05DLE1BQU07UUFDTkMsV0FBVztJQUNiO0FBQ0YsRUFBRTtBQUVGLFdBQVc7QUFDWCxNQUFNQyxtQkFBbUIsT0FBT0M7SUFDOUIsSUFBSTtRQUNGLE1BQU1DLFNBQVMsTUFBTSw2RkFBTyxLQUFzQixPQUFURCxVQUFTO1FBQ2xELE9BQU9DLE9BQU9DLE9BQU87SUFDdkIsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFDLElBQUksQ0FBQyxtQ0FBNEMsT0FBVEwsVUFBUztRQUN6RCxNQUFNTSxnQkFBZ0IsTUFBTSw2RkFBTyxLQUE2QixPQUFoQlosaUJBQWdCO1FBQ2hFLE9BQU9ZLGNBQWNKLE9BQU87SUFDOUI7QUFDRjtBQUVBLFdBQVc7QUFDWCxNQUFNSyxtQkFBbUIsSUFBSUM7QUFFdEIsTUFBTUMsaUJBQWlCLE9BQU9UO0lBQ25DLElBQUlPLGlCQUFpQkcsR0FBRyxDQUFDVixXQUFXO1FBQ2xDLE9BQU9PLGlCQUFpQkksR0FBRyxDQUFDWDtJQUM5QjtJQUVBLE1BQU1ZLGVBQWUsTUFBTWIsaUJBQWlCQztJQUM1Q08saUJBQWlCTSxHQUFHLENBQUNiLFVBQVVZO0lBQy9CLE9BQU9BO0FBQ1QsRUFBRTtBQUVGLG1CQUFtQjtBQUNaLE1BQU1FLHFCQUFxQixDQUFDZDtJQUNqQyxPQUFPTyxpQkFBaUJJLEdBQUcsQ0FBQ1gsYUFBYTtBQUMzQyxFQUFFO0FBRUYsVUFBVTtBQUNILE1BQU1lLHNCQUFzQjtJQUNqQyxNQUFNQyxZQUF3QjtRQUFDO1FBQVM7UUFBUztRQUFNO0tBQUs7SUFFNUQsTUFBTUMsUUFBUUMsR0FBRyxDQUNmRixVQUFVRyxHQUFHLENBQUMsT0FBT0M7UUFDbkIsSUFBSTtZQUNGLE1BQU1YLGVBQWVXO1FBQ3ZCLEVBQUUsT0FBT2pCLE9BQU87WUFDZEMsUUFBUUMsSUFBSSxDQUFDLDhCQUFtQyxPQUFMZSxNQUFLLE1BQUlqQjtRQUN0RDtJQUNGO0FBRUosRUFBRTtBQUVGLFVBQVU7QUFDSCxNQUFNa0Isd0JBQXdCO1FBS1dDO0lBSjlDLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUVELE1BQU1DLGtCQUFrQkQsVUFBVXRCLFFBQVEsTUFBSXNCLHVCQUFBQSxVQUFVTixTQUFTLGNBQW5CTSwyQ0FBQUEsb0JBQXFCLENBQUMsRUFBRTtJQUV0RSxJQUFJQyxDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQkMsVUFBVSxDQUFDLGFBQVlELG9CQUFvQixNQUFNO1FBQ3BFLE9BQU87SUFDVCxPQUFPLElBQUlBLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCQyxVQUFVLENBQUMsYUFBWUQsb0JBQW9CLFdBQVc7UUFDaEYsT0FBTztJQUNULE9BQU8sSUFBSUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJDLFVBQVUsQ0FBQyxPQUFPO1FBQzVDLE9BQU87SUFDVCxPQUFPLElBQUlELDRCQUFBQSxzQ0FBQUEsZ0JBQWlCQyxVQUFVLENBQUMsT0FBTztRQUM1QyxPQUFPO0lBQ1Q7SUFFQSxPQUFPOUI7QUFDVCxFQUFFO0FBRUYsZ0JBQWdCO0FBQ1QsTUFBTStCLGVBQWUsQ0FBQ0MsUUFBZ0IxQjtJQUMzQyxNQUFNMkIsWUFBWTtRQUNoQixTQUFTO1FBQ1QsU0FBUztRQUNULE1BQU07UUFDTixNQUFNO0lBQ1I7SUFFQSxPQUFPLElBQUlDLEtBQUtDLFlBQVksQ0FBQ0YsU0FBUyxDQUFDM0IsU0FBUyxFQUFFOEIsTUFBTSxDQUFDSjtBQUMzRCxFQUFFO0FBRUYsZ0JBQWdCO0FBQ1QsTUFBTUssYUFBYSxDQUFDQyxNQUFZaEMsVUFBb0JpQztJQUN6RCxNQUFNTixZQUFZO1FBQ2hCLFNBQVM7UUFDVCxTQUFTO1FBQ1QsTUFBTTtRQUNOLE1BQU07SUFDUjtJQUVBLE1BQU1PLGlCQUE2QztRQUNqREMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7SUFDUDtJQUVBLE9BQU8sSUFBSVQsS0FBS1UsY0FBYyxDQUFDWCxTQUFTLENBQUMzQixTQUFTLEVBQUVpQyxXQUFXQyxnQkFBZ0JKLE1BQU0sQ0FBQ0U7QUFDeEYsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9saWIvaTE4bi9pbmRleC50cz8wMDRiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5Zu96ZmF5YyW6YWN572u5Li75paH5Lu2XG4gKiDmlK/mjIHliqjmgIHliqDovb3or63oqIDmlofku7ZcbiAqL1xuXG5leHBvcnQgdHlwZSBMYW5ndWFnZSA9ICd6aC1DTicgfCAnemgtVFcnIHwgJ2VuJyB8ICdqYSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVHJhbnNsYXRpb25zIHtcbiAgLy8g6YCa55SoXG4gIGNvbW1vbjoge1xuICAgIGxvYWRpbmc6IHN0cmluZztcbiAgICBlcnJvcjogc3RyaW5nO1xuICAgIHN1Y2Nlc3M6IHN0cmluZztcbiAgICBjYW5jZWw6IHN0cmluZztcbiAgICBjb25maXJtOiBzdHJpbmc7XG4gICAgYmFjazogc3RyaW5nO1xuICAgIG5leHQ6IHN0cmluZztcbiAgICBzdWJtaXQ6IHN0cmluZztcbiAgICByZXRyeTogc3RyaW5nO1xuICAgIHNhdmU6IHN0cmluZztcbiAgICBkZWxldGU6IHN0cmluZztcbiAgICBlZGl0OiBzdHJpbmc7XG4gICAgYWRkOiBzdHJpbmc7XG4gICAgc2VhcmNoOiBzdHJpbmc7XG4gICAgZmlsdGVyOiBzdHJpbmc7XG4gICAgZXhwb3J0OiBzdHJpbmc7XG4gICAgaW1wb3J0OiBzdHJpbmc7XG4gICAgcmVmcmVzaDogc3RyaW5nO1xuICB9O1xuICBcbiAgLy8g55m75b2V6aG16Z2iXG4gIGxvZ2luOiB7XG4gICAgdGl0bGU6IHN0cmluZztcbiAgICBzdWJ0aXRsZTogc3RyaW5nO1xuICAgIHdlbGNvbWVCYWNrOiBzdHJpbmc7XG4gICAgdXNlcm5hbWU6IHN0cmluZztcbiAgICBwYXNzd29yZDogc3RyaW5nO1xuICAgIHJlbWVtYmVyTWU6IHN0cmluZztcbiAgICBmb3Jnb3RQYXNzd29yZDogc3RyaW5nO1xuICAgIGxvZ2luQnV0dG9uOiBzdHJpbmc7XG4gICAgbm9BY2NvdW50OiBzdHJpbmc7XG4gICAgc2lnblVwOiBzdHJpbmc7XG4gICAgbG9naW5TdWNjZXNzOiBzdHJpbmc7XG4gICAgbG9naW5FcnJvcjogc3RyaW5nO1xuICAgIGludmFsaWRDcmVkZW50aWFsczogc3RyaW5nO1xuICAgIG5ldHdvcmtFcnJvcjogc3RyaW5nO1xuICAgIHVzZXJuYW1lUmVxdWlyZWQ6IHN0cmluZztcbiAgICBwYXNzd29yZFJlcXVpcmVkOiBzdHJpbmc7XG4gICAgdXNlcm5hbWVNaW5MZW5ndGg6IHN0cmluZztcbiAgICBwYXNzd29yZE1pbkxlbmd0aDogc3RyaW5nO1xuICB9O1xuICBcbiAgLy8gQUkg55u45YWzXG4gIGFpOiB7XG4gICAgcG93ZXJlZEJ5OiBzdHJpbmc7XG4gICAgaW50ZWxsaWdlbnRTeXN0ZW06IHN0cmluZztcbiAgICBzZWN1cmVMb2dpbjogc3RyaW5nO1xuICAgIGFpQXNzaXN0YW50OiBzdHJpbmc7XG4gICAgc21hcnRBbmFseXNpczogc3RyaW5nO1xuICAgIGRhdGFQcm90ZWN0aW9uOiBzdHJpbmc7XG4gICAga25vd2xlZGdlQmFzZTogc3RyaW5nO1xuICAgIGludGVsbGlnZW50UmV0cmlldmFsOiBzdHJpbmc7XG4gICAgZG9jdW1lbnRQcm9jZXNzaW5nOiBzdHJpbmc7XG4gIH07XG4gIFxuICAvLyDor63oqIDliIfmjaJcbiAgbGFuZ3VhZ2U6IHtcbiAgICBjdXJyZW50OiBzdHJpbmc7XG4gICAgc3dpdGNoOiBzdHJpbmc7XG4gICAgY2hpbmVzZTogc3RyaW5nO1xuICAgIHRyYWRpdGlvbmFsQ2hpbmVzZTogc3RyaW5nO1xuICAgIGVuZ2xpc2g6IHN0cmluZztcbiAgICBqYXBhbmVzZTogc3RyaW5nO1xuICB9O1xuICBcbiAgLy8g5a+86IiqXG4gIG5hdmlnYXRpb246IHtcbiAgICBob21lOiBzdHJpbmc7XG4gICAgZGFzaGJvYXJkOiBzdHJpbmc7XG4gICAgZG9jdW1lbnRzOiBzdHJpbmc7XG4gICAgc2VhcmNoOiBzdHJpbmc7XG4gICAgc2V0dGluZ3M6IHN0cmluZztcbiAgICBwcm9maWxlOiBzdHJpbmc7XG4gICAgbG9nb3V0OiBzdHJpbmc7XG4gICAgYWRtaW46IHN0cmluZztcbiAgfTtcbiAgXG4gIC8vIOS7quihqOadv1xuICBkYXNoYm9hcmQ6IHtcbiAgICB3ZWxjb21lOiBzdHJpbmc7XG4gICAgb3ZlcnZpZXc6IHN0cmluZztcbiAgICBzdGF0aXN0aWNzOiBzdHJpbmc7XG4gICAgcmVjZW50QWN0aXZpdHk6IHN0cmluZztcbiAgICBxdWlja0FjdGlvbnM6IHN0cmluZztcbiAgICBzeXN0ZW1TdGF0dXM6IHN0cmluZztcbiAgICB1c2VySW5mbzogc3RyaW5nO1xuICAgIHRvdGFsRG9jdW1lbnRzOiBzdHJpbmc7XG4gICAgdG90YWxRdWVyaWVzOiBzdHJpbmc7XG4gICAgdG90YWxVc2Vyczogc3RyaW5nO1xuICAgIHN5c3RlbVNldHRpbmdzOiBzdHJpbmc7XG4gIH07XG4gIFxuICAvLyDmlofmoaPnrqHnkIZcbiAgZG9jdW1lbnRzOiB7XG4gICAgdGl0bGU6IHN0cmluZztcbiAgICB1cGxvYWQ6IHN0cmluZztcbiAgICBkb3dubG9hZDogc3RyaW5nO1xuICAgIGRlbGV0ZTogc3RyaW5nO1xuICAgIHByZXZpZXc6IHN0cmluZztcbiAgICBkZXRhaWxzOiBzdHJpbmc7XG4gICAgZmlsZU5hbWU6IHN0cmluZztcbiAgICBmaWxlU2l6ZTogc3RyaW5nO1xuICAgIHVwbG9hZFRpbWU6IHN0cmluZztcbiAgICBmaWxlVHlwZTogc3RyaW5nO1xuICAgIHN0YXR1czogc3RyaW5nO1xuICAgIHByb2Nlc3Npbmc6IHN0cmluZztcbiAgICBjb21wbGV0ZWQ6IHN0cmluZztcbiAgICBmYWlsZWQ6IHN0cmluZztcbiAgICB1cGxvYWRTdWNjZXNzOiBzdHJpbmc7XG4gICAgdXBsb2FkRXJyb3I6IHN0cmluZztcbiAgICBkZWxldGVDb25maXJtOiBzdHJpbmc7XG4gIH07XG4gIFxuICAvLyDmkJzntKJcbiAgc2VhcmNoOiB7XG4gICAgdGl0bGU6IHN0cmluZztcbiAgICBwbGFjZWhvbGRlcjogc3RyaW5nO1xuICAgIHJlc3VsdHM6IHN0cmluZztcbiAgICBub1Jlc3VsdHM6IHN0cmluZztcbiAgICBzZWFyY2hpbmc6IHN0cmluZztcbiAgICBhZHZhbmNlZDogc3RyaW5nO1xuICAgIGZpbHRlcnM6IHN0cmluZztcbiAgICBzb3J0Qnk6IHN0cmluZztcbiAgICByZWxldmFuY2U6IHN0cmluZztcbiAgICBkYXRlOiBzdHJpbmc7XG4gICAgc2l6ZTogc3RyaW5nO1xuICB9O1xuICBcbiAgLy8g6K6+572uXG4gIHNldHRpbmdzOiB7XG4gICAgdGl0bGU6IHN0cmluZztcbiAgICBnZW5lcmFsOiBzdHJpbmc7XG4gICAgYWNjb3VudDogc3RyaW5nO1xuICAgIHNlY3VyaXR5OiBzdHJpbmc7XG4gICAgbm90aWZpY2F0aW9uczogc3RyaW5nO1xuICAgIGxhbmd1YWdlOiBzdHJpbmc7XG4gICAgdGhlbWU6IHN0cmluZztcbiAgICBwcml2YWN5OiBzdHJpbmc7XG4gICAgYWJvdXQ6IHN0cmluZztcbiAgfTtcbiAgXG4gIC8vIOmUmeivr+S/oeaBr1xuICBlcnJvcnM6IHtcbiAgICBwYWdlTm90Rm91bmQ6IHN0cmluZztcbiAgICBzZXJ2ZXJFcnJvcjogc3RyaW5nO1xuICAgIG5ldHdvcmtFcnJvcjogc3RyaW5nO1xuICAgIHVuYXV0aG9yaXplZDogc3RyaW5nO1xuICAgIGZvcmJpZGRlbjogc3RyaW5nO1xuICAgIHZhbGlkYXRpb25FcnJvcjogc3RyaW5nO1xuICAgIHVua25vd25FcnJvcjogc3RyaW5nO1xuICB9O1xufVxuXG5leHBvcnQgY29uc3QgZGVmYXVsdExhbmd1YWdlOiBMYW5ndWFnZSA9ICd6aC1DTic7XG5cbi8vIOivreiogOmFjee9rlxuZXhwb3J0IGNvbnN0IGxhbmd1YWdlQ29uZmlnID0ge1xuICAnemgtQ04nOiB7XG4gICAgbmFtZTogJ+S4reaWh+eugOS9kycsXG4gICAgZmxhZzogJ/Cfh6jwn4ezJyxcbiAgICBkaXJlY3Rpb246ICdsdHInIGFzIGNvbnN0LFxuICB9LFxuICAnemgtVFcnOiB7XG4gICAgbmFtZTogJ+S4reaWh+e5gemrlCcsXG4gICAgZmxhZzogJ/Cfh7nwn4e8JyxcbiAgICBkaXJlY3Rpb246ICdsdHInIGFzIGNvbnN0LFxuICB9LFxuICAnZW4nOiB7XG4gICAgbmFtZTogJ0VuZ2xpc2gnLFxuICAgIGZsYWc6ICfwn4e68J+HuCcsXG4gICAgZGlyZWN0aW9uOiAnbHRyJyBhcyBjb25zdCxcbiAgfSxcbiAgJ2phJzoge1xuICAgIG5hbWU6ICfml6XmnKzoqp4nLFxuICAgIGZsYWc6ICfwn4ev8J+HtScsXG4gICAgZGlyZWN0aW9uOiAnbHRyJyBhcyBjb25zdCxcbiAgfSxcbn07XG5cbi8vIOWKqOaAgeWvvOWFpeivreiogOaWh+S7tlxuY29uc3QgbG9hZFRyYW5zbGF0aW9ucyA9IGFzeW5jIChsYW5ndWFnZTogTGFuZ3VhZ2UpOiBQcm9taXNlPFRyYW5zbGF0aW9ucz4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IG1vZHVsZSA9IGF3YWl0IGltcG9ydChgLi9sb2NhbGVzLyR7bGFuZ3VhZ2V9LnRzYCk7XG4gICAgcmV0dXJuIG1vZHVsZS5kZWZhdWx0O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIGxvYWQgdHJhbnNsYXRpb25zIGZvciAke2xhbmd1YWdlfSwgZmFsbGluZyBiYWNrIHRvIGRlZmF1bHRgKTtcbiAgICBjb25zdCBkZWZhdWx0TW9kdWxlID0gYXdhaXQgaW1wb3J0KGAuL2xvY2FsZXMvJHtkZWZhdWx0TGFuZ3VhZ2V9LnRzYCk7XG4gICAgcmV0dXJuIGRlZmF1bHRNb2R1bGUuZGVmYXVsdDtcbiAgfVxufTtcblxuLy8g57yT5a2Y5bey5Yqg6L2955qE57+76K+RXG5jb25zdCB0cmFuc2xhdGlvbkNhY2hlID0gbmV3IE1hcDxMYW5ndWFnZSwgVHJhbnNsYXRpb25zPigpO1xuXG5leHBvcnQgY29uc3QgZ2V0VHJhbnNsYXRpb24gPSBhc3luYyAobGFuZ3VhZ2U6IExhbmd1YWdlKTogUHJvbWlzZTxUcmFuc2xhdGlvbnM+ID0+IHtcbiAgaWYgKHRyYW5zbGF0aW9uQ2FjaGUuaGFzKGxhbmd1YWdlKSkge1xuICAgIHJldHVybiB0cmFuc2xhdGlvbkNhY2hlLmdldChsYW5ndWFnZSkhO1xuICB9XG4gIFxuICBjb25zdCB0cmFuc2xhdGlvbnMgPSBhd2FpdCBsb2FkVHJhbnNsYXRpb25zKGxhbmd1YWdlKTtcbiAgdHJhbnNsYXRpb25DYWNoZS5zZXQobGFuZ3VhZ2UsIHRyYW5zbGF0aW9ucyk7XG4gIHJldHVybiB0cmFuc2xhdGlvbnM7XG59O1xuXG4vLyDlkIzmraXojrflj5bnv7vor5HvvIjnlKjkuo7lt7LnvJPlrZjnmoTmg4XlhrXvvIlcbmV4cG9ydCBjb25zdCBnZXRUcmFuc2xhdGlvblN5bmMgPSAobGFuZ3VhZ2U6IExhbmd1YWdlKTogVHJhbnNsYXRpb25zIHwgbnVsbCA9PiB7XG4gIHJldHVybiB0cmFuc2xhdGlvbkNhY2hlLmdldChsYW5ndWFnZSkgfHwgbnVsbDtcbn07XG5cbi8vIOmihOWKoOi9veaJgOacieivreiogFxuZXhwb3J0IGNvbnN0IHByZWxvYWRBbGxMYW5ndWFnZXMgPSBhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gIGNvbnN0IGxhbmd1YWdlczogTGFuZ3VhZ2VbXSA9IFsnemgtQ04nLCAnemgtVFcnLCAnZW4nLCAnamEnXTtcbiAgXG4gIGF3YWl0IFByb21pc2UuYWxsKFxuICAgIGxhbmd1YWdlcy5tYXAoYXN5bmMgKGxhbmcpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IGdldFRyYW5zbGF0aW9uKGxhbmcpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gcHJlbG9hZCBsYW5ndWFnZSAke2xhbmd9OmAsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9KVxuICApO1xufTtcblxuLy8g5qOA5rWL5rWP6KeI5Zmo6K+t6KiAXG5leHBvcnQgY29uc3QgZGV0ZWN0QnJvd3Nlckxhbmd1YWdlID0gKCk6IExhbmd1YWdlID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuIGRlZmF1bHRMYW5ndWFnZTtcbiAgfVxuICBcbiAgY29uc3QgYnJvd3Nlckxhbmd1YWdlID0gbmF2aWdhdG9yLmxhbmd1YWdlIHx8IG5hdmlnYXRvci5sYW5ndWFnZXM/LlswXTtcbiAgXG4gIGlmIChicm93c2VyTGFuZ3VhZ2U/LnN0YXJ0c1dpdGgoJ3poLUNOJykgfHwgYnJvd3Nlckxhbmd1YWdlID09PSAnemgnKSB7XG4gICAgcmV0dXJuICd6aC1DTic7XG4gIH0gZWxzZSBpZiAoYnJvd3Nlckxhbmd1YWdlPy5zdGFydHNXaXRoKCd6aC1UVycpIHx8IGJyb3dzZXJMYW5ndWFnZSA9PT0gJ3poLUhhbnQnKSB7XG4gICAgcmV0dXJuICd6aC1UVyc7XG4gIH0gZWxzZSBpZiAoYnJvd3Nlckxhbmd1YWdlPy5zdGFydHNXaXRoKCdlbicpKSB7XG4gICAgcmV0dXJuICdlbic7XG4gIH0gZWxzZSBpZiAoYnJvd3Nlckxhbmd1YWdlPy5zdGFydHNXaXRoKCdqYScpKSB7XG4gICAgcmV0dXJuICdqYSc7XG4gIH1cbiAgXG4gIHJldHVybiBkZWZhdWx0TGFuZ3VhZ2U7XG59O1xuXG4vLyDmoLzlvI/ljJbmlbDlrZfvvIjmoLnmja7or63oqIDnjq/looPvvIlcbmV4cG9ydCBjb25zdCBmb3JtYXROdW1iZXIgPSAobnVtYmVyOiBudW1iZXIsIGxhbmd1YWdlOiBMYW5ndWFnZSk6IHN0cmluZyA9PiB7XG4gIGNvbnN0IGxvY2FsZU1hcCA9IHtcbiAgICAnemgtQ04nOiAnemgtQ04nLFxuICAgICd6aC1UVyc6ICd6aC1UVycsXG4gICAgJ2VuJzogJ2VuLVVTJyxcbiAgICAnamEnOiAnamEtSlAnLFxuICB9O1xuICBcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdChsb2NhbGVNYXBbbGFuZ3VhZ2VdKS5mb3JtYXQobnVtYmVyKTtcbn07XG5cbi8vIOagvOW8j+WMluaXpeacn++8iOagueaNruivreiogOeOr+Wig++8iVxuZXhwb3J0IGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZTogRGF0ZSwgbGFuZ3VhZ2U6IExhbmd1YWdlLCBvcHRpb25zPzogSW50bC5EYXRlVGltZUZvcm1hdE9wdGlvbnMpOiBzdHJpbmcgPT4ge1xuICBjb25zdCBsb2NhbGVNYXAgPSB7XG4gICAgJ3poLUNOJzogJ3poLUNOJyxcbiAgICAnemgtVFcnOiAnemgtVFcnLFxuICAgICdlbic6ICdlbi1VUycsXG4gICAgJ2phJzogJ2phLUpQJyxcbiAgfTtcbiAgXG4gIGNvbnN0IGRlZmF1bHRPcHRpb25zOiBJbnRsLkRhdGVUaW1lRm9ybWF0T3B0aW9ucyA9IHtcbiAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgbW9udGg6ICdsb25nJyxcbiAgICBkYXk6ICdudW1lcmljJyxcbiAgfTtcbiAgXG4gIHJldHVybiBuZXcgSW50bC5EYXRlVGltZUZvcm1hdChsb2NhbGVNYXBbbGFuZ3VhZ2VdLCBvcHRpb25zIHx8IGRlZmF1bHRPcHRpb25zKS5mb3JtYXQoZGF0ZSk7XG59O1xuIl0sIm5hbWVzIjpbImRlZmF1bHRMYW5ndWFnZSIsImxhbmd1YWdlQ29uZmlnIiwibmFtZSIsImZsYWciLCJkaXJlY3Rpb24iLCJsb2FkVHJhbnNsYXRpb25zIiwibGFuZ3VhZ2UiLCJtb2R1bGUiLCJkZWZhdWx0IiwiZXJyb3IiLCJjb25zb2xlIiwid2FybiIsImRlZmF1bHRNb2R1bGUiLCJ0cmFuc2xhdGlvbkNhY2hlIiwiTWFwIiwiZ2V0VHJhbnNsYXRpb24iLCJoYXMiLCJnZXQiLCJ0cmFuc2xhdGlvbnMiLCJzZXQiLCJnZXRUcmFuc2xhdGlvblN5bmMiLCJwcmVsb2FkQWxsTGFuZ3VhZ2VzIiwibGFuZ3VhZ2VzIiwiUHJvbWlzZSIsImFsbCIsIm1hcCIsImxhbmciLCJkZXRlY3RCcm93c2VyTGFuZ3VhZ2UiLCJuYXZpZ2F0b3IiLCJicm93c2VyTGFuZ3VhZ2UiLCJzdGFydHNXaXRoIiwiZm9ybWF0TnVtYmVyIiwibnVtYmVyIiwibG9jYWxlTWFwIiwiSW50bCIsIk51bWJlckZvcm1hdCIsImZvcm1hdCIsImZvcm1hdERhdGUiLCJkYXRlIiwib3B0aW9ucyIsImRlZmF1bHRPcHRpb25zIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiRGF0ZVRpbWVGb3JtYXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"variable":"--font-inter","subsets":["latin"]}],"variableName":"inter"} ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\",\"variable\":\"__variable_e8ce0c\"};\n    if(true) {\n      // 1750043146096\n      var cssReload = __webpack_require__(/*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzP3tcInBhdGhcIjpcImFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1widmFyaWFibGVcIjpcIi0tZm9udC1pbnRlclwiLFwic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsZ0ZBQWdGO0FBQzNHLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLGdXQUEwTCxjQUFjLHNEQUFzRDtBQUM1UixNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzP2JiYzUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ19fSW50ZXJfZThjZTBjJywgJ19fSW50ZXJfRmFsbGJhY2tfZThjZTBjJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lX2U4Y2UwY1wiLFwidmFyaWFibGVcIjpcIl9fdmFyaWFibGVfZThjZTBjXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTAwNDMxNDYwOTZcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRjovd29ya3NwYWNlL3hoYy1yYWcvd2ViL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"}":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"JetBrains_Mono","arguments":[{"variable":"--font-jetbrains-mono","subsets":["latin"]}],"variableName":"jetbrainsMono"} ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__JetBrains_Mono_3c557b', '__JetBrains_Mono_Fallback_3c557b'\",\"fontStyle\":\"normal\"},\"className\":\"__className_3c557b\",\"variable\":\"__variable_3c557b\"};\n    if(true) {\n      // 1750043146097\n      var cssReload = __webpack_require__(/*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzP3tcInBhdGhcIjpcImFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkpldEJyYWluc19Nb25vXCIsXCJhcmd1bWVudHNcIjpbe1widmFyaWFibGVcIjpcIi0tZm9udC1qZXRicmFpbnMtbW9ub1wiLFwic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImpldGJyYWluc01vbm9cIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyxrR0FBa0c7QUFDN0gsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsZ1dBQTBMLGNBQWMsc0RBQXNEO0FBQzVSLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/ZTNlYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInX19KZXRCcmFpbnNfTW9ub18zYzU1N2InLCAnX19KZXRCcmFpbnNfTW9ub19GYWxsYmFja18zYzU1N2InXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfM2M1NTdiXCIsXCJ2YXJpYWJsZVwiOlwiX192YXJpYWJsZV8zYzU1N2JcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1MDA0MzE0NjA5N1xuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJGOi93b3Jrc3BhY2UveGhjLXJhZy93ZWIvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \**********************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsMFFBQXNFO0FBQ3hFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcz9lMDlhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5taW4uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_tagged_template_literal.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_tagged_template_literal.js ***!
  \*********************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: function() { return /* binding */ _tagged_template_literal; },\n/* harmony export */   _tagged_template_literal: function() { return /* binding */ _tagged_template_literal; }\n/* harmony export */ });\nfunction _tagged_template_literal(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    return Object.freeze(Object.defineProperties(strings, { raw: { value: Object.freeze(raw) } }));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3djK2hlbHBlcnNAMC41LjUvbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9lc20vX3RhZ2dlZF90ZW1wbGF0ZV9saXRlcmFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDs7QUFFQSw0REFBNEQsT0FBTyw2QkFBNkI7QUFDaEc7QUFDeUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0Bzd2MraGVscGVyc0AwLjUuNS9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2VzbS9fdGFnZ2VkX3RlbXBsYXRlX2xpdGVyYWwuanM/NzU0NCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gX3RhZ2dlZF90ZW1wbGF0ZV9saXRlcmFsKHN0cmluZ3MsIHJhdykge1xuICAgIGlmICghcmF3KSByYXcgPSBzdHJpbmdzLnNsaWNlKDApO1xuXG4gICAgcmV0dXJuIE9iamVjdC5mcmVlemUoT2JqZWN0LmRlZmluZVByb3BlcnRpZXMoc3RyaW5ncywgeyByYXc6IHsgdmFsdWU6IE9iamVjdC5mcmVlemUocmF3KSB9IH0pKTtcbn1cbmV4cG9ydCB7IF90YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbCBhcyBfIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_tagged_template_literal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/goober@2.1.16_csstype@3.1.3/node_modules/goober/dist/goober.modern.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/goober@2.1.16_csstype@3.1.3/node_modules/goober/dist/goober.modern.js ***!
  \**************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: function() { return /* binding */ u; },\n/* harmony export */   extractCss: function() { return /* binding */ r; },\n/* harmony export */   glob: function() { return /* binding */ b; },\n/* harmony export */   keyframes: function() { return /* binding */ h; },\n/* harmony export */   setup: function() { return /* binding */ m; },\n/* harmony export */   styled: function() { return /* binding */ j; }\n/* harmony export */ });\nlet e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/goober@2.1.16_csstype@3.1.3/node_modules/goober/dist/goober.modern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs ***!
  \*************************************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckmarkIcon: function() { return /* binding */ _; },\n/* harmony export */   ErrorIcon: function() { return /* binding */ k; },\n/* harmony export */   LoaderIcon: function() { return /* binding */ V; },\n/* harmony export */   ToastBar: function() { return /* binding */ C; },\n/* harmony export */   ToastIcon: function() { return /* binding */ M; },\n/* harmony export */   Toaster: function() { return /* binding */ Oe; },\n/* harmony export */   \"default\": function() { return /* binding */ Vt; },\n/* harmony export */   resolveValue: function() { return /* binding */ f; },\n/* harmony export */   toast: function() { return /* binding */ c; },\n/* harmony export */   useToaster: function() { return /* binding */ O; },\n/* harmony export */   useToasterStore: function() { return /* binding */ D; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var goober__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! goober */ \"(app-pages-browser)/./node_modules/.pnpm/goober@2.1.16_csstype@3.1.3/node_modules/goober/dist/goober.modern.js\");\n/* __next_internal_client_entry_do_not_use__ CheckmarkIcon,ErrorIcon,LoaderIcon,ToastBar,ToastIcon,Toaster,default,resolveValue,toast,useToaster,useToasterStore auto */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n transform: scale(1) rotate(45deg);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0);\\n  opacity: 0;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(90deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(90deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n\\n  &:after,\\n  &:before {\\n    content: '';\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 150ms;\\n    position: absolute;\\n    border-radius: 3px;\\n    opacity: 0;\\n    background: \",\n        \";\\n    bottom: 9px;\\n    left: 4px;\\n    height: 2px;\\n    width: 12px;\\n  }\\n\\n  &:before {\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 180ms;\\n    transform: rotate(90deg);\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 12px;\\n  height: 12px;\\n  box-sizing: border-box;\\n  border: 2px solid;\\n  border-radius: 100%;\\n  border-color: \",\n        \";\\n  border-right-color: \",\n        \";\\n  animation: \",\n        \" 1s linear infinite;\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(45deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n0% {\\n\theight: 0;\\n\twidth: 0;\\n\topacity: 0;\\n}\\n40% {\\n  height: 0;\\n\twidth: 6px;\\n\topacity: 1;\\n}\\n100% {\\n  opacity: 1;\\n  height: 10px;\\n}\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n  &:after {\\n    content: '';\\n    box-sizing: border-box;\\n    animation: \",\n        \" 0.2s ease-out forwards;\\n    opacity: 0;\\n    animation-delay: 200ms;\\n    position: absolute;\\n    border-right: 2px solid;\\n    border-bottom: 2px solid;\\n    border-color: \",\n        \";\\n    bottom: 6px;\\n    left: 6px;\\n    height: 10px;\\n    width: 6px;\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: absolute;\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-width: 20px;\\n  min-height: 20px;\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n  min-width: 20px;\\n  animation: \",\n        \" 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  align-items: center;\\n  background: #fff;\\n  color: #363636;\\n  line-height: 1.3;\\n  will-change: transform;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\\n  max-width: 350px;\\n  pointer-events: auto;\\n  padding: 8px 10px;\\n  border-radius: 8px;\\n\"\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  justify-content: center;\\n  margin: 4px 10px;\\n  color: inherit;\\n  flex: 1 1 auto;\\n  white-space: pre-line;\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  z-index: 9999;\\n  > * {\\n    pointer-events: auto;\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nvar _s = $RefreshSig$();\nvar W = (e)=>typeof e == \"function\", f = (e, t)=>W(e) ? e(t) : e;\nvar F = (()=>{\n    let e = 0;\n    return ()=>(++e).toString();\n})(), A = (()=>{\n    let e;\n    return ()=>{\n        if (e === void 0 && typeof window < \"u\") {\n            let t = matchMedia(\"(prefers-reduced-motion: reduce)\");\n            e = !t || t.matches;\n        }\n        return e;\n    };\n})();\n\nvar Y = 20;\nvar U = (e, t)=>{\n    switch(t.type){\n        case 0:\n            return {\n                ...e,\n                toasts: [\n                    t.toast,\n                    ...e.toasts\n                ].slice(0, Y)\n            };\n        case 1:\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === t.toast.id ? {\n                        ...o,\n                        ...t.toast\n                    } : o)\n            };\n        case 2:\n            let { toast: r } = t;\n            return U(e, {\n                type: e.toasts.find((o)=>o.id === r.id) ? 1 : 0,\n                toast: r\n            });\n        case 3:\n            let { toastId: s } = t;\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === s || s === void 0 ? {\n                        ...o,\n                        dismissed: !0,\n                        visible: !1\n                    } : o)\n            };\n        case 4:\n            return t.toastId === void 0 ? {\n                ...e,\n                toasts: []\n            } : {\n                ...e,\n                toasts: e.toasts.filter((o)=>o.id !== t.toastId)\n            };\n        case 5:\n            return {\n                ...e,\n                pausedAt: t.time\n            };\n        case 6:\n            let a = t.time - (e.pausedAt || 0);\n            return {\n                ...e,\n                pausedAt: void 0,\n                toasts: e.toasts.map((o)=>({\n                        ...o,\n                        pauseDuration: o.pauseDuration + a\n                    }))\n            };\n    }\n}, P = [], y = {\n    toasts: [],\n    pausedAt: void 0\n}, u = (e)=>{\n    y = U(y, e), P.forEach((t)=>{\n        t(y);\n    });\n}, q = {\n    blank: 4e3,\n    error: 4e3,\n    success: 2e3,\n    loading: 1 / 0,\n    custom: 4e3\n}, D = function() {\n    let e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    let [t, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(y), s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(y);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>(s.current !== y && r(y), P.push(r), ()=>{\n            let o = P.indexOf(r);\n            o > -1 && P.splice(o, 1);\n        }), []);\n    let a = t.toasts.map((o)=>{\n        var n, i, p;\n        return {\n            ...e,\n            ...e[o.type],\n            ...o,\n            removeDelay: o.removeDelay || ((n = e[o.type]) == null ? void 0 : n.removeDelay) || (e == null ? void 0 : e.removeDelay),\n            duration: o.duration || ((i = e[o.type]) == null ? void 0 : i.duration) || (e == null ? void 0 : e.duration) || q[o.type],\n            style: {\n                ...e.style,\n                ...(p = e[o.type]) == null ? void 0 : p.style,\n                ...o.style\n            }\n        };\n    });\n    return {\n        ...t,\n        toasts: a\n    };\n};\nvar J = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"blank\", r = arguments.length > 2 ? arguments[2] : void 0;\n    return {\n        createdAt: Date.now(),\n        visible: !0,\n        dismissed: !1,\n        type: t,\n        ariaProps: {\n            role: \"status\",\n            \"aria-live\": \"polite\"\n        },\n        message: e,\n        pauseDuration: 0,\n        ...r,\n        id: (r == null ? void 0 : r.id) || F()\n    };\n}, x = (e)=>(t, r)=>{\n        let s = J(t, e, r);\n        return u({\n            type: 2,\n            toast: s\n        }), s.id;\n    }, c = (e, t)=>x(\"blank\")(e, t);\nc.error = x(\"error\");\nc.success = x(\"success\");\nc.loading = x(\"loading\");\nc.custom = x(\"custom\");\nc.dismiss = (e)=>{\n    u({\n        type: 3,\n        toastId: e\n    });\n};\nc.remove = (e)=>u({\n        type: 4,\n        toastId: e\n    });\nc.promise = (e, t, r)=>{\n    let s = c.loading(t.loading, {\n        ...r,\n        ...r == null ? void 0 : r.loading\n    });\n    return typeof e == \"function\" && (e = e()), e.then((a)=>{\n        let o = t.success ? f(t.success, a) : void 0;\n        return o ? c.success(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.success\n        }) : c.dismiss(s), a;\n    }).catch((a)=>{\n        let o = t.error ? f(t.error, a) : void 0;\n        o ? c.error(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.error\n        }) : c.dismiss(s);\n    }), e;\n};\n\nvar K = (e, t)=>{\n    u({\n        type: 1,\n        toast: {\n            id: e,\n            height: t\n        }\n    });\n}, X = ()=>{\n    u({\n        type: 5,\n        time: Date.now()\n    });\n}, b = new Map, Z = 1e3, ee = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : Z;\n    if (b.has(e)) return;\n    let r = setTimeout(()=>{\n        b.delete(e), u({\n            type: 4,\n            toastId: e\n        });\n    }, t);\n    b.set(e, r);\n}, O = (e)=>{\n    let { toasts: t, pausedAt: r } = D(e);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (r) return;\n        let o = Date.now(), n = t.map((i)=>{\n            if (i.duration === 1 / 0) return;\n            let p = (i.duration || 0) + i.pauseDuration - (o - i.createdAt);\n            if (p < 0) {\n                i.visible && c.dismiss(i.id);\n                return;\n            }\n            return setTimeout(()=>c.dismiss(i.id), p);\n        });\n        return ()=>{\n            n.forEach((i)=>i && clearTimeout(i));\n        };\n    }, [\n        t,\n        r\n    ]);\n    let s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        r && u({\n            type: 6,\n            time: Date.now()\n        });\n    }, [\n        r\n    ]), a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((o, n)=>{\n        let { reverseOrder: i = !1, gutter: p = 8, defaultPosition: d } = n || {}, h = t.filter((m)=>(m.position || d) === (o.position || d) && m.height), v = h.findIndex((m)=>m.id === o.id), S = h.filter((m, E)=>E < v && m.visible).length;\n        return h.filter((m)=>m.visible).slice(...i ? [\n            S + 1\n        ] : [\n            0,\n            S\n        ]).reduce((m, E)=>m + (E.height || 0) + p, 0);\n    }, [\n        t\n    ]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        t.forEach((o)=>{\n            if (o.dismissed) ee(o.id, o.removeDelay);\n            else {\n                let n = b.get(o.id);\n                n && (clearTimeout(n), b.delete(o.id));\n            }\n        });\n    }, [\n        t\n    ]), {\n        toasts: t,\n        handlers: {\n            updateHeight: K,\n            startPause: X,\n            endPause: s,\n            calculateOffset: a\n        }\n    };\n};\n\n\n\n\n\nvar oe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject()), re = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject1()), se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject2()), k = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject3(), (e)=>e.primary || \"#ff4b4b\", oe, re, (e)=>e.secondary || \"#fff\", se);\n\nvar ne = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject4()), V = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject5(), (e)=>e.secondary || \"#e0e0e0\", (e)=>e.primary || \"#616161\", ne);\n\nvar pe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject6()), de = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject7()), _ = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject8(), (e)=>e.primary || \"#61d345\", pe, de, (e)=>e.secondary || \"#fff\");\nvar ue = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject9()), le = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject10()), fe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject11()), Te = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject12(), fe), M = (param)=>{\n    let { toast: e } = param;\n    let { icon: t, type: r, iconTheme: s } = e;\n    return t !== void 0 ? typeof t == \"string\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Te, null, t) : t : r === \"blank\" ? null : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(le, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(V, {\n        ...s\n    }), r !== \"loading\" && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ue, null, r === \"error\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(k, {\n        ...s\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_, {\n        ...s\n    })));\n};\nvar ye = (e)=>\"\\n0% {transform: translate3d(0,\".concat(e * -200, \"%,0) scale(.6); opacity:.5;}\\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\\n\"), ge = (e)=>\"\\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\\n100% {transform: translate3d(0,\".concat(e * -150, \"%,-1px) scale(.6); opacity:0;}\\n\"), he = \"0%{opacity:0;} 100%{opacity:1;}\", xe = \"0%{opacity:1;} 100%{opacity:0;}\", be = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject13()), Se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject14()), Ae = (e, t)=>{\n    let s = e.includes(\"top\") ? 1 : -1, [a, o] = A() ? [\n        he,\n        xe\n    ] : [\n        ye(s),\n        ge(s)\n    ];\n    return {\n        animation: t ? \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(a), \" 0.35s cubic-bezier(.21,1.02,.73,1) forwards\") : \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(o), \" 0.4s forwards cubic-bezier(.06,.71,.55,1)\")\n    };\n}, C = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.memo((param)=>{\n    let { toast: e, position: t, style: r, children: s } = param;\n    let a = e.height ? Ae(e.position || t || \"top-center\", e.visible) : {\n        opacity: 0\n    }, o = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(M, {\n        toast: e\n    }), n = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Se, {\n        ...e.ariaProps\n    }, f(e.message, e));\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(be, {\n        className: e.className,\n        style: {\n            ...a,\n            ...r,\n            ...e.style\n        }\n    }, typeof s == \"function\" ? s({\n        icon: o,\n        message: n\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, o, n));\n});\n\n\n(0,goober__WEBPACK_IMPORTED_MODULE_2__.setup)(react__WEBPACK_IMPORTED_MODULE_1__.createElement);\nvar ve = (param)=>{\n    let { id: e, className: t, style: r, onHeightUpdate: s, children: a } = param;\n    _s();\n    let o = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((n)=>{\n        if (n) {\n            let i = ()=>{\n                let p = n.getBoundingClientRect().height;\n                s(e, p);\n            };\n            i(), new MutationObserver(i).observe(n, {\n                subtree: !0,\n                childList: !0,\n                characterData: !0\n            });\n        }\n    }, [\n        e,\n        s\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: o,\n        className: t,\n        style: r\n    }, a);\n}, Ee = (e, t)=>{\n    let r = e.includes(\"top\"), s = r ? {\n        top: 0\n    } : {\n        bottom: 0\n    }, a = e.includes(\"center\") ? {\n        justifyContent: \"center\"\n    } : e.includes(\"right\") ? {\n        justifyContent: \"flex-end\"\n    } : {};\n    return {\n        left: 0,\n        right: 0,\n        display: \"flex\",\n        position: \"absolute\",\n        transition: A() ? void 0 : \"all 230ms cubic-bezier(.21,1.02,.73,1)\",\n        transform: \"translateY(\".concat(t * (r ? 1 : -1), \"px)\"),\n        ...s,\n        ...a\n    };\n}, De = (0,goober__WEBPACK_IMPORTED_MODULE_2__.css)(_templateObject15()), R = 16, Oe = (param)=>{\n    let { reverseOrder: e, position: t = \"top-center\", toastOptions: r, gutter: s, children: a, containerStyle: o, containerClassName: n } = param;\n    let { toasts: i, handlers: p } = O(r);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        id: \"_rht_toaster\",\n        style: {\n            position: \"fixed\",\n            zIndex: 9999,\n            top: R,\n            left: R,\n            right: R,\n            bottom: R,\n            pointerEvents: \"none\",\n            ...o\n        },\n        className: n,\n        onMouseEnter: p.startPause,\n        onMouseLeave: p.endPause\n    }, i.map((d)=>{\n        let h = d.position || t, v = p.calculateOffset(d, {\n            reverseOrder: e,\n            gutter: s,\n            defaultPosition: t\n        }), S = Ee(h, v);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ve, {\n            id: d.id,\n            key: d.id,\n            onHeightUpdate: p.updateHeight,\n            className: d.visible ? De : \"\",\n            style: S\n        }, d.type === \"custom\" ? f(d.message, d) : a ? a(d) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(C, {\n            toast: d,\n            position: h\n        }));\n    }));\n};\n_s(ve, \"LQ34HCRCKbaP7NB9wB8OQNidTak=\");\nvar Vt = c;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);