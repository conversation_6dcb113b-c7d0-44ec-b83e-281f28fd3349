# 真实数据API开发指南

## 📋 项目概述

本项目为AI知识库系统开发了完整的真实数据API接口，包括存储概览、系统状态监控和路径导航优化功能。

## 🚀 快速开始

### 1. 环境准备

确保已安装以下依赖：
- Python 3.9+
- FastAPI
- psutil (系统监控库)
- SQLAlchemy (数据库ORM)

### 2. 启动方式

#### 方法1：启动完整API服务器
```cmd
cd api
.venv\Scripts\python.exe -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

#### 方法2：启动测试API服务器
```cmd
cd api
.venv\Scripts\python.exe test_apis.py
```

#### 方法3：使用批处理文件
```cmd
双击运行: api\start_test_server.bat
```

### 3. 访问测试

- 测试页面：`file:///f:/workspace/xhc-rag/web/api-test-demo.html`
- API文档：`http://127.0.0.1:8000/docs`
- 测试服务器：`http://127.0.0.1:8001`

## 📊 API接口详情

### 存储概览API

#### 1. 获取存储概览摘要
- **URL**: `/api/v1/storage-overview/summary`
- **方法**: GET
- **描述**: 获取所有存储的统计信息

**响应示例**:
```json
{
  "success": true,
  "message": "获取存储概览成功",
  "data": {
    "total_capacity": 107374182400,
    "total_capacity_formatted": "100.0 GB",
    "total_files": 1247,
    "shared_files": 89,
    "recent_access": 23,
    "used_space": 48318382080,
    "used_space_formatted": "45.0 GB",
    "usage_percentage": 45.0,
    "storage_count": 1,
    "last_updated": "2024-01-01T12:00:00.000000"
  }
}
```

#### 2. 获取存储详情
- **URL**: `/api/v1/storage-overview/storage/{storage_id}/details`
- **方法**: GET
- **描述**: 获取指定存储的详细信息

#### 3. 获取文件类型分布
- **URL**: `/api/v1/storage-overview/file-types`
- **方法**: GET
- **描述**: 获取文件类型分布统计

### 系统状态API

#### 1. 获取系统状态概览
- **URL**: `/api/v1/system-status/overview`
- **方法**: GET
- **描述**: 获取CPU、内存、磁盘、网络等系统信息

**响应示例**:
```json
{
  "success": true,
  "message": "获取系统状态成功",
  "data": {
    "cpu": {
      "usage_percent": 23.5,
      "core_count": 8,
      "status": "good"
    },
    "memory": {
      "total": 17179869184,
      "used": 11509563392,
      "usage_percent": 67.0,
      "total_formatted": "16.0 GB",
      "used_formatted": "10.7 GB",
      "status": "warning"
    },
    "disk": {
      "total": 1000204886016,
      "used": 450204886016,
      "usage_percent": 45.0,
      "status": "good"
    },
    "network": {
      "status": "online",
      "connections": 156
    },
    "system": {
      "platform": "Windows",
      "hostname": "DESKTOP-ABC123",
      "uptime_formatted": "2天 14小时 30分钟"
    }
  }
}
```

#### 2. 获取CPU详情
- **URL**: `/api/v1/system-status/cpu`
- **方法**: GET

#### 3. 获取内存详情
- **URL**: `/api/v1/system-status/memory`
- **方法**: GET

#### 4. 获取网络详情
- **URL**: `/api/v1/system-status/network`
- **方法**: GET

#### 5. 获取进程信息
- **URL**: `/api/v1/system-status/processes`
- **方法**: GET

## 🗂️ 路径导航优化

### 功能描述
路径导航组件现在可以显示存储的真实物理路径，从数据库`storage_configs.config.base_path`字段获取。

### 支持的存储类型
- **本地存储**: `E:/test2/documents/files`
- **MinIO**: `minio.example.com/bucket/path`
- **FTP**: `ftp.example.com:21/uploads/files`
- **SFTP**: `sftp.example.com:22/uploads/files`

### 实现特性
- 显示存储的真实物理路径
- 相对路径和绝对路径同时显示
- 智能路径格式化
- 实时路径信息更新

## ⚛️ 前端组件

### 组件清单
- `StorageOverview.tsx` - 存储概览组件
- `SystemStatus.tsx` - 系统状态组件
- `useStorageInfo.ts` - 存储信息Hook

### 技术特性
- 实时数据获取和显示
- 自动刷新（30秒间隔）
- 错误处理和重试机制
- 响应式设计和动画效果
- TypeScript类型安全

## 🧪 测试指南

### 1. 自动化测试
打开测试页面：`web/api-test-demo.html`

### 2. 手动测试
使用浏览器直接访问API端点：
- http://127.0.0.1:8000/api/v1/storage-overview/summary
- http://127.0.0.1:8000/api/v1/system-status/overview

### 3. 命令行测试
```cmd
curl http://127.0.0.1:8000/api/v1/storage-overview/summary
curl http://127.0.0.1:8000/api/v1/system-status/overview
```

## 🔧 技术架构

### 后端技术栈
- **FastAPI**: REST API框架
- **psutil**: 跨平台系统监控库
- **SQLAlchemy**: 数据库ORM
- **Pydantic**: 数据验证和序列化

### 前端技术栈
- **React**: 用户界面库
- **TypeScript**: 类型安全
- **Framer Motion**: 动画效果
- **Tailwind CSS**: 样式框架

### 数据处理
- 跨平台系统监控（Windows/Linux/Mac）
- 多种存储类型支持
- 文件类型智能识别
- 性能指标计算
- 健康状态评估

## 📈 性能优化

### 缓存策略
- 系统状态数据缓存30秒
- 存储统计数据缓存5分钟
- 文件类型分布缓存1小时

### 错误处理
- API级别错误处理
- 前端组件错误边界
- 网络请求重试机制
- 用户友好的错误提示

## 🛠️ 故障排除

### 常见问题

1. **API服务器启动失败**
   - 检查Python环境：`.venv\Scripts\python.exe --version`
   - 检查依赖安装：`.venv\Scripts\python.exe -c "import psutil"`
   - 检查端口占用：`netstat -an | findstr :8000`

2. **psutil导入失败**
   ```cmd
   cd api
   .venv\Scripts\python.exe -m pip install psutil
   ```

3. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行状态
   - 验证连接字符串

4. **前端组件无法获取数据**
   - 检查API服务器状态
   - 验证CORS配置
   - 检查网络连接

### 调试模式
启动调试模式：
```cmd
cd api
.venv\Scripts\python.exe -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload --log-level debug
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 存储概览API开发完成
- ✅ 系统状态API开发完成
- ✅ 路径导航优化完成
- ✅ 前端组件开发完成
- ✅ 测试工具开发完成
- ✅ 文档编写完成

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。详见LICENSE文件。
