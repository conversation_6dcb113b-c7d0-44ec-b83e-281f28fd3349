#!/usr/bin/env python3
"""
修复存储类型枚举的迁移脚本
"""
import asyncio
import asyncpg
import os
from loguru import logger


async def fix_storage_enum():
    """修复存储类型枚举"""
    
    # 从环境变量获取数据库连接信息
    db_host = os.getenv('DB_HOST', '**************')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'xhc_rag')
    db_user = os.getenv('DB_USERNAME', 'postgres')
    db_password = os.getenv('DB_PASSWORD', 'XHC12345')
    
    # 构建连接字符串
    dsn = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(dsn)
        logger.info("数据库连接成功")
        
        # 检查枚举类型是否存在
        enum_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM pg_type 
                WHERE typname = 'storagetype'
            );
        """)
        
        if not enum_exists:
            # 创建枚举类型
            await conn.execute("""
                CREATE TYPE storagetype AS ENUM ('LOCAL', 'FTP', 'MINIO');
            """)
            logger.info("✅ 创建存储类型枚举成功")
        else:
            logger.info("✅ 存储类型枚举已存在")
        
        # 检查表是否存在
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'storage_configs'
            );
        """)
        
        if not table_exists:
            # 创建存储配置表
            await conn.execute("""
                CREATE TABLE storage_configs (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) UNIQUE NOT NULL,
                    storage_type storagetype NOT NULL,
                    is_default BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    config JSON,
                    total_files INTEGER DEFAULT 0,
                    total_size BIGINT DEFAULT 0,
                    last_sync_at TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            """)
            logger.info("✅ 创建存储配置表成功")
            
            # 创建索引
            await conn.execute("""
                CREATE INDEX idx_storage_configs_name ON storage_configs(name);
                CREATE INDEX idx_storage_configs_type ON storage_configs(storage_type);
                CREATE INDEX idx_storage_configs_default ON storage_configs(is_default);
            """)
            logger.info("✅ 创建索引成功")
        else:
            logger.info("✅ 存储配置表已存在")
            
            # 检查字段类型是否正确
            try:
                # 尝试修改字段类型
                await conn.execute("""
                    ALTER TABLE storage_configs 
                    ALTER COLUMN storage_type TYPE storagetype 
                    USING storage_type::text::storagetype;
                """)
                logger.info("✅ 修复存储类型字段成功")
            except Exception as e:
                logger.info(f"存储类型字段可能已经正确: {e}")
        
        # 关闭连接
        await conn.close()
        logger.info("✅ 存储枚举类型修复完成！")
        
    except Exception as e:
        logger.error(f"数据库修复失败: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(fix_storage_enum())
