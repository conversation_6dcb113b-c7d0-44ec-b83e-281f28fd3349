@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo 修复文档分段枚举值问题
echo ========================================
echo.

REM 检查PostgreSQL连接
echo 1. 检查数据库连接...
.venv\Scripts\python.exe -c "
import os
import psycopg2
try:
    conn = psycopg2.connect(
        host='**************',
        port=5432,
        database='xhc_rag',
        user=os.getenv('DB_USERNAME', 'postgres'),
        password=os.getenv('DB_PASSWORD', 'XHC12345')
    )
    conn.close()
    print('✅ 数据库连接成功')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"

if %errorlevel% neq 0 (
    echo 数据库连接失败，请检查数据库配置
    pause
    exit /b 1
)

echo.
echo 2. 准备执行SQL修复脚本...
echo 注意：此操作将重新创建分段相关表，现有数据将被备份
echo.
set /p confirm="确认执行修复？(y/n): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 3. 执行SQL修复脚本...
echo 请手动执行以下命令：
echo.
echo psql -h ************** -p 5432 -U postgres -d xhc_rag -f sql_scripts/fix_enum_values.sql
echo.
echo 或者使用pgAdmin等工具执行 sql_scripts/fix_enum_values.sql 文件
echo.
echo 执行完成后按任意键继续...
pause >nul

echo.
echo 4. 测试修复结果...
.venv\Scripts\python.exe test_enum_fix.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 修复验证失败，请检查SQL脚本是否正确执行
    pause
    exit /b 1
)

echo.
echo 5. 重启Celery服务（如果正在运行）...
taskkill /f /im python.exe /fi "WINDOWTITLE eq Celery Worker" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq Celery Beat" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq Celery Flower" 2>nul

timeout /t 2 /nobreak >nul

echo 启动Celery服务...
start "Celery Worker" cmd /k "cd /d %cd% && .venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=4 --queues=default,upload_queue,file_queue,segment_queue --hostname=worker@%%h"

timeout /t 3 /nobreak >nul

start "Celery Beat" cmd /k "cd /d %cd% && .venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app beat --loglevel=info"

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo ✅ 数据库枚举值已修复
echo ✅ 分段相关表已重新创建
echo ✅ Celery服务已重启
echo.
echo 现在可以正常使用文档分段功能了！
echo.
echo 如果仍有问题，请检查：
echo 1. 数据库连接是否正常
echo 2. SQL脚本是否完全执行
echo 3. Celery服务是否正常启动
echo.
pause
