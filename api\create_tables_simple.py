#!/usr/bin/env python3
"""
简单的数据库表创建脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import async_engine, init_database
from app.core.config import get_settings
from app.models import Base
from loguru import logger


async def create_tables():
    """创建所有数据库表"""
    try:
        logger.info("开始创建数据库表...")

        # 初始化数据库连接
        settings = get_settings()
        await init_database(settings)

        # 检查引擎是否初始化
        if async_engine is None:
            raise Exception("数据库引擎初始化失败")

        # 创建所有表
        async with async_engine.begin() as conn:
            logger.info("创建数据库表...")
            await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表创建成功")

        logger.info("✅ 数据库表创建完成")

    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        await create_tables()
        logger.info("🎉 操作完成！")
        
    except Exception as e:
        logger.error(f"❌ 操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
