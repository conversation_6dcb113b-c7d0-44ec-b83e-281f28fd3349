"""
系统相关模型
"""

from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, JSON
from sqlalchemy.sql import func
from app.models.base import BaseModel


class SystemConfig(BaseModel):
    """系统配置表"""
    __tablename__ = "system_configs"
    
    # 基本信息
    config_key = Column(String(100), unique=True, nullable=False, comment="配置键")
    config_value = Column(Text, comment="配置值")
    config_type = Column(String(20), default="string", comment="配置类型: string, json, boolean, number")
    description = Column(String(255), comment="配置描述")
    
    # 状态
    is_system = Column(Boolean, default=False, comment="是否为系统配置")
    is_editable = Column(Boolean, default=True, comment="是否可编辑")
    
    def __repr__(self):
        return f"<SystemConfig(key='{self.config_key}', value='{self.config_value}')>"


class SystemInitStatus(BaseModel):
    """系统初始化状态表"""
    __tablename__ = "system_init_status"
    
    # 初始化步骤
    step_name = Column(String(50), unique=True, nullable=False, comment="初始化步骤名称")
    step_description = Column(String(255), comment="步骤描述")
    is_completed = Column(Boolean, default=False, comment="是否已完成")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
    
    # 额外信息
    result_data = Column(JSON, comment="步骤结果数据")
    error_message = Column(Text, comment="错误信息")
    
    def __repr__(self):
        return f"<SystemInitStatus(step='{self.step_name}', completed={self.is_completed})>"


class Dictionary(BaseModel):
    """字典表"""
    __tablename__ = "dictionaries"
    
    # 基本信息
    dict_type = Column(String(50), nullable=False, comment="字典类型")
    dict_code = Column(String(50), nullable=False, comment="字典编码")
    dict_label = Column(String(100), nullable=False, comment="字典标签")
    dict_value = Column(String(100), nullable=False, comment="字典值")
    
    # 层级关系
    parent_code = Column(String(50), comment="父级编码")
    sort_order = Column(Integer, default=0, comment="排序")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_system = Column(Boolean, default=False, comment="是否为系统字典")
    
    # 额外信息
    description = Column(String(255), comment="描述")
    extra_data = Column(JSON, comment="扩展数据")
    
    def __repr__(self):
        return f"<Dictionary(type='{self.dict_type}', code='{self.dict_code}', label='{self.dict_label}')>"


class OperationLog(BaseModel):
    """操作日志表"""
    __tablename__ = "operation_logs"
    
    # 操作信息
    operation_type = Column(String(50), nullable=False, comment="操作类型")
    operation_name = Column(String(100), nullable=False, comment="操作名称")
    operation_desc = Column(String(255), comment="操作描述")
    
    # 用户信息
    user_id = Column(Integer, comment="操作用户ID")
    username = Column(String(50), comment="用户名")
    
    # 请求信息
    request_method = Column(String(10), comment="请求方法")
    request_url = Column(String(500), comment="请求URL")
    request_ip = Column(String(50), comment="请求IP")
    user_agent = Column(String(500), comment="用户代理")
    
    # 响应信息
    response_status = Column(Integer, comment="响应状态码")
    response_time = Column(Integer, comment="响应时间(ms)")
    
    # 详细数据
    request_data = Column(JSON, comment="请求数据")
    response_data = Column(JSON, comment="响应数据")
    error_message = Column(Text, comment="错误信息")
    
    def __repr__(self):
        return f"<OperationLog(type='{self.operation_type}', user='{self.username}')>"
