@echo off
echo 修复导入路径问题...
echo.

cd /d "%~dp0"

echo 问题: 无法解析 @/lib/api-client 模块
echo 解决方案: 修改为正确的 @/lib/api 导入
echo.

echo 1. 清理缓存...
if exist .next (
    echo 删除 .next 目录...
    rmdir /s /q .next
)

echo.
echo 2. 导入修复已完成:
echo ✅ hooks/useStorageInfo.ts - 修复 API 导入
echo ✅ components/FileManager/StorageOverview.tsx - 修复 API 导入
echo ✅ components/System/SystemStatus.tsx - 修复 API 导入
echo.

echo 3. 验证修复:
echo 检查 lib/api.ts 文件存在...
if exist lib\api.ts (
    echo ✅ lib/api.ts 存在
) else (
    echo ❌ lib/api.ts 缺失
)

echo.
echo 4. 重新启动开发服务器...
echo 运行: pnpm dev
echo.

pnpm dev

pause
