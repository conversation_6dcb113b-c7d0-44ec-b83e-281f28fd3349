"""
存储统计服务
"""
import os
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.models.storage_stats import StorageStats, StorageStatsHistory
from app.models.file_management import StorageConfig
from app.services.storage_service import StorageService
from app.core.database import get_db


class StorageStatsService:
    """存储统计服务"""
    
    def __init__(self):
        self.storage_service = StorageService()
        
    def get_file_type_category(self, filename: str) -> str:
        """根据文件名获取文件类型分类"""
        ext = os.path.splitext(filename)[1].lower()
        
        document_exts = {'.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.pages', '.md', '.tex'}
        image_exts = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.tiff', '.ico'}
        video_exts = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'}
        audio_exts = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'}
        archive_exts = {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'}
        
        if ext in document_exts:
            return 'document'
        elif ext in image_exts:
            return 'image'
        elif ext in video_exts:
            return 'video'
        elif ext in audio_exts:
            return 'audio'
        elif ext in archive_exts:
            return 'archive'
        else:
            return 'other'
    
    async def calculate_storage_stats(self, storage_id: int, db: Session) -> Optional[StorageStats]:
        """计算存储统计信息"""
        try:
            # 获取存储配置
            storage = db.query(StorageConfig).filter(StorageConfig.id == storage_id).first()
            if not storage:
                return None
            
            # 初始化统计数据
            stats = {
                'total_files': 0,
                'total_folders': 0,
                'total_size': 0,
                'document_count': 0,
                'image_count': 0,
                'video_count': 0,
                'audio_count': 0,
                'archive_count': 0,
                'other_count': 0,
                'document_size': 0,
                'image_size': 0,
                'video_size': 0,
                'audio_size': 0,
                'archive_size': 0,
                'other_size': 0,
                'largest_file_size': 0,
                'smallest_file_size': float('inf'),
                'file_sizes': []
            }
            
            # 根据存储类型计算统计信息
            if storage.storage_type == 'local':
                await self._calculate_local_stats(storage, stats)
            elif storage.storage_type == 'minio':
                await self._calculate_minio_stats(storage, stats)
            elif storage.storage_type in ['ftp', 'sftp']:
                await self._calculate_ftp_stats(storage, stats)
            
            # 计算平均文件大小
            if stats['total_files'] > 0:
                stats['avg_file_size'] = stats['total_size'] / stats['total_files']
                if stats['smallest_file_size'] == float('inf'):
                    stats['smallest_file_size'] = 0
            else:
                stats['avg_file_size'] = 0
                stats['smallest_file_size'] = 0
            
            # 获取存储空间信息
            space_info = await self._get_storage_space_info(storage)
            stats.update(space_info)
            
            # 计算健康评分
            health_score = self._calculate_health_score(stats, storage)
            
            # 创建或更新统计记录
            existing_stats = db.query(StorageStats).filter(
                and_(
                    StorageStats.storage_id == storage_id,
                    StorageStats.stats_date >= datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
                )
            ).first()
            
            if existing_stats:
                # 更新现有记录
                for key, value in stats.items():
                    if hasattr(existing_stats, key):
                        setattr(existing_stats, key, value)
                existing_stats.health_score = health_score
                existing_stats.is_healthy = health_score >= 80
                existing_stats.updated_at = datetime.utcnow()
                storage_stats = existing_stats
            else:
                # 创建新记录
                storage_stats = StorageStats(
                    storage_id=storage_id,
                    stats_date=datetime.utcnow(),
                    health_score=health_score,
                    is_healthy=health_score >= 80,
                    **{k: v for k, v in stats.items() if k != 'file_sizes'}
                )
                db.add(storage_stats)
            
            db.commit()
            db.refresh(storage_stats)
            
            return storage_stats
            
        except Exception as e:
            print(f"计算存储统计失败: {e}")
            db.rollback()
            return None
    
    async def _calculate_local_stats(self, storage: StorageConfig, stats: Dict):
        """计算本地存储统计"""
        base_path = storage.config.get('base_path', '/')
        
        if not os.path.exists(base_path):
            return
        
        for root, dirs, files in os.walk(base_path):
            stats['total_folders'] += len(dirs)
            
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    stats['total_files'] += 1
                    stats['total_size'] += file_size
                    stats['file_sizes'].append(file_size)
                    
                    # 更新最大最小文件大小
                    stats['largest_file_size'] = max(stats['largest_file_size'], file_size)
                    stats['smallest_file_size'] = min(stats['smallest_file_size'], file_size)
                    
                    # 按类型统计
                    file_type = self.get_file_type_category(file)
                    stats[f'{file_type}_count'] += 1
                    stats[f'{file_type}_size'] += file_size
                    
                except (OSError, IOError):
                    continue
    
    async def _calculate_minio_stats(self, storage: StorageConfig, stats: Dict):
        """计算MinIO存储统计"""
        # TODO: 实现MinIO统计逻辑
        pass

    async def _calculate_ftp_stats(self, storage: StorageConfig, stats: Dict):
        """计算FTP存储统计"""
        # TODO: 实现FTP统计逻辑
        pass

    async def _get_storage_space_info(self, storage: StorageConfig) -> Dict:
        """获取存储空间信息"""
        space_info = {
            'used_size': 0,
            'available_size': 0
        }
        
        try:
            if storage.storage_type == 'local':
                base_path = storage.config.get('base_path', '/')
                if os.path.exists(base_path):
                    statvfs = os.statvfs(base_path)
                    space_info['available_size'] = statvfs.f_bavail * statvfs.f_frsize
                    space_info['used_size'] = (statvfs.f_blocks - statvfs.f_bavail) * statvfs.f_frsize
        except Exception as e:
            print(f"获取存储空间信息失败: {e}")
        
        return space_info
    
    def _calculate_health_score(self, stats: Dict, storage: StorageConfig) -> float:
        """计算健康评分"""
        score = 100.0
        
        # 根据错误率扣分
        if stats.get('error_count', 0) > 0:
            error_rate = stats['error_count'] / max(stats.get('total_files', 1), 1)
            score -= min(error_rate * 50, 30)
        
        # 根据存储使用率扣分
        if stats.get('available_size', 0) > 0:
            usage_rate = stats.get('used_size', 0) / (stats.get('used_size', 0) + stats.get('available_size', 1))
            if usage_rate > 0.9:
                score -= (usage_rate - 0.9) * 100
        
        return max(score, 0.0)
    
    def get_storage_stats(self, storage_id: int, db: Session) -> Optional[StorageStats]:
        """获取存储统计信息"""
        return db.query(StorageStats).filter(
            StorageStats.storage_id == storage_id
        ).order_by(desc(StorageStats.stats_date)).first()
    
    def get_storage_stats_history(self, storage_id: int, days: int, db: Session) -> List[StorageStats]:
        """获取存储统计历史"""
        start_date = datetime.utcnow() - timedelta(days=days)
        return db.query(StorageStats).filter(
            and_(
                StorageStats.storage_id == storage_id,
                StorageStats.stats_date >= start_date
            )
        ).order_by(desc(StorageStats.stats_date)).all()
    
    async def update_all_storage_stats(self, db: Session):
        """更新所有存储的统计信息"""
        storages = db.query(StorageConfig).filter(StorageConfig.is_active == True).all()
        
        for storage in storages:
            try:
                await self.calculate_storage_stats(storage.id, db)
            except Exception as e:
                print(f"更新存储 {storage.id} 统计失败: {e}")
                continue
