#!/usr/bin/env python3
"""
创建默认存储配置（简化版）
"""
import asyncio
import json
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from loguru import logger

from app.core.database import async_session_factory
from app.models.file_management import StorageConfig, StorageType


async def create_default_storage():
    """创建默认的本地存储配置"""
    
    try:
        async with async_session_factory() as session:
            # 检查是否已有存储配置
            result = await session.execute(select(StorageConfig))
            existing_configs = result.scalars().all()
            
            if existing_configs:
                logger.info("已存在 {} 个存储配置，跳过创建".format(len(existing_configs)))
                for config in existing_configs:
                    logger.info("  - ID: {}, 名称: {}, 类型: {}".format(
                        config.id, config.name, config.storage_type
                    ))
                return
            
            # 创建默认本地存储配置
            config_data = {
                "base_path": "./storage",
                "max_file_size": 100 * 1024 * 1024  # 100MB
            }
            
            # 使用原生SQL插入，避免枚举问题
            await session.execute(text("""
                INSERT INTO storage_configs (name, storage_type, is_default, is_active, config, created_at, updated_at)
                VALUES (:name, :storage_type, :is_default, :is_active, :config, NOW(), NOW())
            """), {
                "name": "默认本地存储",
                "storage_type": "LOCAL",
                "is_default": True,
                "is_active": True,
                "config": json.dumps(config_data)
            })
            
            await session.commit()
            logger.info("✅ 创建默认本地存储配置成功")
            
            # 验证创建结果
            result = await session.execute(text("SELECT * FROM storage_configs WHERE name = :name"), {
                "name": "默认本地存储"
            })
            row = result.fetchone()
            if row:
                logger.info("创建的存储配置: ID={}, 类型={}".format(row.id, row.storage_type))
            
            # 确保存储目录存在
            import os
            storage_dir = config_data["base_path"]
            if not os.path.exists(storage_dir):
                os.makedirs(storage_dir, exist_ok=True)
                logger.info("创建存储目录: {}".format(storage_dir))
            
    except Exception as e:
        logger.error("创建默认存储配置失败: {}".format(str(e)))
        raise


if __name__ == "__main__":
    asyncio.run(create_default_storage())
