"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/file-manager/segment/batch/page",{

/***/ "(app-pages-browser)/./app/file-manager/segment/batch/page.tsx":
/*!*************************************************!*\
  !*** ./app/file-manager/segment/batch/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nconst BatchSegmentPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // 解析URL参数中的文件ID列表\n    const fileIds = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        const filesParam = searchParams.get(\"files\");\n        if (!filesParam) return [];\n        // 分割文件ID并过滤空值\n        const ids = filesParam.split(\",\").filter(Boolean);\n        console.log(\"批量分段页面 - 接收到的URL参数:\", filesParam);\n        console.log(\"批量分段页面 - 解析的文件IDs:\", ids);\n        return ids;\n    }, [\n        searchParams\n    ]);\n    // 基础状态\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 任务状态\n    const [taskStatus, setTaskStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [taskId, setTaskId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [fileProgress, setFileProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 任务配置\n    const [taskName, setTaskName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        method: \"paragraph\",\n        max_length: 500,\n        overlap: 50,\n        preserve_formatting: true,\n        normalize_text: true,\n        extract_keywords: true,\n        remove_stopwords: false,\n        language: \"zh\"\n    });\n    // 文件选择相关状态\n    const [showFileSelector, setShowFileSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableFiles, setAvailableFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingAvailableFiles, setLoadingAvailableFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileSearchQuery, setFileSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStorage, setSelectedStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storageList, setStorageList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fileIds.length > 0) {\n            loadFiles();\n            // 生成默认任务名称\n            const now = new Date();\n            const timestamp = now.toLocaleString(\"zh-CN\");\n            setTaskName(\"批量分段任务 - \".concat(timestamp));\n        }\n        loadStorageList();\n    }, [\n        fileIds\n    ]);\n    // 加载存储列表\n    const loadStorageList = async ()=>{\n        try {\n            var _response_data;\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/storage-management\");\n            const data = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || [];\n            setStorageList(data);\n            if (data.length > 0) {\n                setSelectedStorage(data[0].id);\n            }\n        } catch (err) {\n            console.error(\"Failed to load storage list:\", err);\n        }\n    };\n    // 加载文件信息\n    const loadFiles = async ()=>{\n        if (fileIds.length === 0) {\n            console.log(\"批量分段页面 - 没有文件ID，跳过加载\");\n            return;\n        }\n        try {\n            var _response_data;\n            setLoading(true);\n            setError(null);\n            console.log(\"批量分段页面 - 开始加载文件信息，文件IDs:\", fileIds);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/file-management/batch-info\", {\n                file_ids: fileIds\n            });\n            console.log(\"批量分段页面 - API响应:\", response.data);\n            const data = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || [];\n            console.log(\"批量分段页面 - 解析的文件数据:\", data);\n            setFiles(data);\n            if (data.length === 0) {\n                setError(\"未找到指定的文件，请检查文件是否存在\");\n            }\n        } catch (err) {\n            var _err_response, _err_response_data, _err_response1;\n            console.error(\"批量分段页面 - 加载文件失败:\", err);\n            console.error(\"批量分段页面 - 错误详情:\", (_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data);\n            setError(((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : (_err_response_data = _err_response1.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || \"加载文件信息失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载可选文件列表\n    const loadAvailableFiles = async function(storageId) {\n        let search = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"\";\n        try {\n            var _response_data;\n            setLoadingAvailableFiles(true);\n            const params = new URLSearchParams({\n                storage_id: storageId,\n                page: \"1\",\n                page_size: \"50\"\n            });\n            if (search) {\n                params.append(\"search\", search);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/file-management?\".concat(params.toString()));\n            const data = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data || {};\n            setAvailableFiles(data.files || []);\n        } catch (err) {\n            console.error(\"Failed to load available files:\", err);\n        } finally{\n            setLoadingAvailableFiles(false);\n        }\n    };\n    // 添加文件到选择列表\n    const addFilesToSelection = (selectedFiles)=>{\n        const newFiles = selectedFiles.filter((newFile)=>!files.some((existingFile)=>existingFile.file_id === newFile.file_id));\n        if (newFiles.length > 0) {\n            setFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n            // 更新URL参数\n            const allFileIds = [\n                ...files,\n                ...newFiles\n            ].map((f)=>f.file_id);\n            const newUrl = \"/file-manager/segment/batch?files=\".concat(allFileIds.join(\",\"));\n            router.replace(newUrl);\n        }\n        setShowFileSelector(false);\n    };\n    // 移除文件\n    const removeFile = (fileId)=>{\n        const newFiles = files.filter((f)=>f.file_id !== fileId);\n        setFiles(newFiles);\n        // 更新URL参数\n        if (newFiles.length > 0) {\n            const newUrl = \"/file-manager/segment/batch?files=\".concat(newFiles.map((f)=>f.file_id).join(\",\"));\n            router.replace(newUrl);\n        } else {\n            router.push(\"/file-manager\");\n        }\n    };\n    // 开始分段任务\n    const startSegmentTask = async ()=>{\n        if (!taskName.trim()) {\n            setError(\"请输入任务名称\");\n            return;\n        }\n        if (files.length === 0) {\n            setError(\"请选择要分段的文件\");\n            return;\n        }\n        try {\n            var _response_data;\n            setTaskStatus(\"running\");\n            setError(null);\n            const taskData = {\n                task_name: taskName,\n                description: \"\",\n                file_ids: files.map((f)=>f.file_id),\n                config: config\n            };\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/document-segment/tasks\", taskData);\n            const data = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data;\n            setTaskId(data.task_id);\n            // 初始化文件进度\n            const initialProgress = {};\n            files.forEach((file)=>{\n                initialProgress[file.file_id] = {\n                    status: \"pending\",\n                    progress: 0\n                };\n            });\n            setFileProgress(initialProgress);\n            // 开始轮询任务状态\n            startProgressPolling(data.task_id);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Failed to start segment task:\", err);\n            setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.detail) || \"启动分段任务失败\");\n            setTaskStatus(\"idle\");\n        }\n    };\n    // 轮询任务进度\n    const startProgressPolling = (taskId)=>{\n        const pollInterval = setInterval(async ()=>{\n            try {\n                var _response_data;\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/document-segment/tasks/\".concat(taskId, \"/progress\"));\n                const data = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || response.data;\n                console.log(\"轮询任务进度:\", data); // 调试日志\n                // 更新文件进度\n                if (data.file_progress) {\n                    setFileProgress(data.file_progress);\n                }\n                // 检查任务状态\n                if (data.task) {\n                    const taskStatus = data.task.status;\n                    const taskProgress = data.task.progress || 0;\n                    console.log(\"任务状态:\", taskStatus, \"进度:\", taskProgress); // 调试日志\n                    if (taskStatus === \"completed\") {\n                        clearInterval(pollInterval);\n                        setTaskStatus(\"completed\");\n                        toast.success(\"AI分段处理完成！共生成 \".concat(data.task.total_segments || 0, \" 个分段\"));\n                        // 刷新文件列表\n                        await loadFiles();\n                    } else if (taskStatus === \"failed\") {\n                        clearInterval(pollInterval);\n                        setTaskStatus(\"idle\");\n                        const errorMsg = data.task.error_message || \"分段任务失败\";\n                        setError(errorMsg);\n                        toast.error(\"分段处理失败: \".concat(errorMsg));\n                    } else if (taskStatus === \"processing\") {\n                        // 任务正在处理中，保持运行状态\n                        setTaskStatus(\"running\");\n                    }\n                }\n            } catch (err) {\n                console.error(\"Failed to get task progress:\", err);\n                toast.error(\"获取任务进度失败，请检查网络连接\");\n            }\n        }, 2000);\n        // 10分钟后停止轮询\n        setTimeout(()=>{\n            clearInterval(pollInterval);\n            if (taskStatus === \"running\") {\n                setTaskStatus(\"idle\");\n                toast.warning(\"任务监控超时，请手动刷新页面查看结果\");\n            }\n        }, 600000);\n    };\n    // 返回文件管理\n    const handleBack = ()=>{\n        router.push(\"/file-manager\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"加载文件信息中...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n            lineNumber: 333,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 计算统计指标\n    const getSegmentStats = ()=>{\n        const totalFiles = files.length;\n        const pendingFiles = Object.values(fileProgress).filter((p)=>p.status === \"pending\").length;\n        const processingFiles = Object.values(fileProgress).filter((p)=>p.status === \"processing\").length;\n        const completedFiles = Object.values(fileProgress).filter((p)=>p.status === \"completed\").length;\n        const failedFiles = Object.values(fileProgress).filter((p)=>p.status === \"error\").length;\n        const totalSegments = Object.values(fileProgress).reduce((sum, p)=>sum + (p.segments || 0), 0);\n        return {\n            totalFiles,\n            pendingFiles,\n            processingFiles,\n            completedFiles,\n            failedFiles,\n            totalSegments\n        };\n    };\n    const stats = getSegmentStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 flex-shrink-0 bg-white/60 backdrop-blur-lg border-r border-white/30 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleBack,\n                                            className: \"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                                            children: \"AI智能批量分段\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-purple-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        files.length,\n                                                                        \" 个文件待处理\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                taskStatus === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: \"处理中...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                taskStatus === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 font-medium\",\n                                                                            children: \"已完成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-900\",\n                                                children: \"任务配置\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"任务名称 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: taskName,\n                                                    onChange: (e)=>setTaskName(e.target.value),\n                                                    disabled: taskStatus !== \"idle\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                    placeholder: \"请输入任务名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-900\",\n                                                children: \"分段配置\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"分段方式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: config.method,\n                                                        onChange: (e)=>setConfig({\n                                                                ...config,\n                                                                method: e.target.value\n                                                            }),\n                                                        disabled: taskStatus !== \"idle\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"paragraph\",\n                                                                children: \"按段落分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"sentence\",\n                                                                children: \"按句子分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"fixed_length\",\n                                                                children: \"固定长度分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"semantic\",\n                                                                children: \"语义分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"最大长度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: config.max_length,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        max_length: parseInt(e.target.value) || 500\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                min: \"100\",\n                                                                max: \"2000\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                                placeholder: \"500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"重叠长度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: config.overlap,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        overlap: parseInt(e.target.value) || 50\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                min: \"0\",\n                                                                max: \"500\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                                placeholder: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"语言\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: config.language,\n                                                        onChange: (e)=>setConfig({\n                                                                ...config,\n                                                                language: e.target.value\n                                                            }),\n                                                        disabled: taskStatus !== \"idle\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"zh\",\n                                                                children: \"中文\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"en\",\n                                                                children: \"英文\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"auto\",\n                                                                children: \"自动检测\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.preserve_formatting,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        preserve_formatting: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"保留格式\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.normalize_text,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        normalize_text: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"文本标准化\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.extract_keywords,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        extract_keywords: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"提取关键词\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.remove_stopwords,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        remove_stopwords: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"移除停用词\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col bg-white/40 backdrop-blur-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: \"文件分段统计\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"实时监控分段进度和状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowFileSelector(true);\n                                                            if (selectedStorage) {\n                                                                loadAvailableFiles(selectedStorage);\n                                                            }\n                                                        },\n                                                        disabled: taskStatus === \"running\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加文件\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    taskStatus === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-blue-100/50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-blue-700\",\n                                                                children: \"AI处理中\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    taskStatus === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-green-100/50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-green-700\",\n                                                                children: \"处理完成\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    taskStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startSegmentTask,\n                                                        disabled: files.length === 0 || !taskName.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"开始AI分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-blue-600 uppercase tracking-wide\",\n                                                                    children: \"文件总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-blue-900\",\n                                                                    children: stats.totalFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-3 border border-purple-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-purple-600 uppercase tracking-wide\",\n                                                                    children: \"分段总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-purple-900\",\n                                                                    children: stats.totalSegments\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-3 border border-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-gray-600 uppercase tracking-wide\",\n                                                                    children: \"待分段\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-gray-900\",\n                                                                    children: stats.pendingFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-gray-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-3 border border-yellow-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-yellow-600 uppercase tracking-wide\",\n                                                                    children: \"分段中\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-yellow-900\",\n                                                                    children: stats.processingFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-green-600 uppercase tracking-wide\",\n                                                                    children: \"已完成\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-green-900\",\n                                                                    children: stats.completedFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-red-50 to-rose-50 rounded-lg p-3 border border-red-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-red-600 uppercase tracking-wide\",\n                                                                    children: \"分段失败\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-red-900\",\n                                                                    children: stats.failedFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-lg font-medium mb-2\",\n                                                children: \"暂无文件\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: '请点击右上角的\"添加文件\"按钮来选择要分段的文件'\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: files.map((file)=>{\n                                        const progress = fileProgress[file.file_id];\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.95\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            className: \"bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-5 h-5 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                            title: file.file_name,\n                                                                            children: file.file_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                file.file_size_formatted,\n                                                                                \" • \",\n                                                                                file.file_extension.toUpperCase()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        taskStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFile(file.file_id),\n                                                            className: \"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors\",\n                                                            title: \"移除文件\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"存储类型: \",\n                                                                    file.storage_type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: new Date(file.created_at).toLocaleDateString(\"zh-CN\")\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-200/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-700\",\n                                                                    children: [\n                                                                        progress.status === \"pending\" && \"等待处理\",\n                                                                        progress.status === \"processing\" && \"处理中...\",\n                                                                        progress.status === \"completed\" && \"已完成 (\".concat(progress.segments || 0, \" 段)\"),\n                                                                        progress.status === \"error\" && \"处理失败\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        progress.progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2 rounded-full transition-all duration-300 \".concat(progress.status === \"error\" ? \"bg-red-500\" : progress.status === \"completed\" ? \"bg-green-500\" : \"bg-blue-500\"),\n                                                                style: {\n                                                                    width: \"\".concat(progress.progress, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        progress.error_message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-600 mt-1\",\n                                                            children: progress.error_message\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, file.file_id, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, undefined),\n            showFileSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    className: \"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"选择文件\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowFileSelector(false),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 804,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fileSearchQuery,\n                                                        onChange: (e)=>setFileSearchQuery(e.target.value),\n                                                        onKeyPress: (e)=>{\n                                                            if (e.key === \"Enter\") {\n                                                                loadAvailableFiles(selectedStorage, fileSearchQuery);\n                                                            }\n                                                        },\n                                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        placeholder: \"搜索文件名...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedStorage,\n                                            onChange: (e)=>{\n                                                setSelectedStorage(e.target.value);\n                                                loadAvailableFiles(e.target.value, fileSearchQuery);\n                                            },\n                                            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: storageList.map((storage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: storage.id,\n                                                    children: storage.name\n                                                }, storage.id, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>loadAvailableFiles(selectedStorage, fileSearchQuery),\n                                            disabled: loadingAvailableFiles,\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                            children: loadingAvailableFiles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 21\n                                            }, undefined) : \"搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 803,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 max-h-96 overflow-y-auto\",\n                            children: loadingAvailableFiles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 animate-spin text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"加载文件中...\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 17\n                            }, undefined) : availableFiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-300 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"没有找到文件\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileSelector, {\n                                files: availableFiles,\n                                selectedFiles: [],\n                                onSelectionChange: (selected)=>addFilesToSelection(selected),\n                                excludeFileIds: files.map((f)=>f.file_id)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-5 h-5 text-red-500 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 897,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-700 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 898,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                    lineNumber: 896,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 895,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n        lineNumber: 364,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BatchSegmentPage, \"J3JDpGTXWGj/CLAQe6lD+1cPxqo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = BatchSegmentPage;\nconst FileSelector = (param)=>{\n    let { files, selectedFiles, onSelectionChange, excludeFileIds } = param;\n    _s1();\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedFiles);\n    const toggleFile = (file)=>{\n        const isSelected = selected.some((f)=>f.file_id === file.file_id);\n        if (isSelected) {\n            setSelected(selected.filter((f)=>f.file_id !== file.file_id));\n        } else {\n            setSelected([\n                ...selected,\n                file\n            ]);\n        }\n    };\n    const handleConfirm = ()=>{\n        onSelectionChange(selected);\n    };\n    const availableFiles = files.filter((file)=>!excludeFileIds.includes(file.file_id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4\",\n                children: availableFiles.map((file)=>{\n                    const isSelected = selected.some((f)=>f.file_id === file.file_id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>toggleFile(file),\n                        className: \"p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(isSelected ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300 hover:bg-gray-50\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 rounded \".concat(isSelected ? \"bg-blue-600 border-blue-600\" : \"border-gray-300\"),\n                                    children: isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: file.file_name\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                file.file_size_formatted,\n                                                \" • \",\n                                                file.file_extension.toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 953,\n                            columnNumber: 15\n                        }, undefined)\n                    }, file.file_id, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 944,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 939,\n                columnNumber: 7\n            }, undefined),\n            selected.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"已选择 \",\n                            selected.length,\n                            \" 个文件\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 976,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleConfirm,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"确认添加\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 977,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 975,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n        lineNumber: 938,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(FileSelector, \"JClcwEvyXPIbcYGxChsKw9j5ZM8=\");\n_c1 = FileSelector;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BatchSegmentPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"BatchSegmentPage\");\n$RefreshReg$(_c1, \"FileSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/file-manager/segment/batch/page.tsx\n"));

/***/ })

});