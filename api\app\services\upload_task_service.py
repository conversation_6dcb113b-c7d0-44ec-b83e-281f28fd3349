"""
上传任务管理服务
"""
import os
import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from loguru import logger

from app.models.upload_task import UploadTask
from app.core.database import get_async_session


class UploadTaskService:
    """上传任务管理服务"""

    async def create_task(
        self,
        file_name: str,
        file_size: int,
        file_path: str,
        storage_id: int,
        upload_path: str,
        session: AsyncSession
    ) -> UploadTask:
        """创建上传任务"""
        task_id = str(uuid.uuid4())
        
        task = UploadTask(
            task_id=task_id,
            file_name=file_name,
            file_size=file_size,
            file_path=file_path,
            storage_id=storage_id,
            upload_path=upload_path,
            status="pending"
        )
        
        session.add(task)
        await session.commit()
        await session.refresh(task)
        
        logger.info(f"创建上传任务: {task_id} - {file_name}")
        return task

    async def get_tasks(
        self,
        page: int = 1,
        page_size: int = 20,
        status_filter: Optional[str] = None,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """获取上传任务列表"""
        if session is None:
            async for session in get_async_session():
                return await self._get_tasks_impl(page, page_size, status_filter, session)
        else:
            return await self._get_tasks_impl(page, page_size, status_filter, session)

    async def _get_tasks_impl(
        self,
        page: int,
        page_size: int,
        status_filter: Optional[str],
        session: AsyncSession
    ) -> Dict[str, Any]:
        """获取任务列表实现"""
        # 构建查询
        query = select(UploadTask)
        
        # 过滤已完成的任务
        query = query.where(UploadTask.status != "success")
        
        # 状态过滤
        if status_filter:
            query = query.where(UploadTask.status == status_filter)
        
        # 排序
        query = query.order_by(UploadTask.created_at.desc())
        
        # 计算总数
        count_query = select(func.count(UploadTask.id)).where(UploadTask.status != "success")
        if status_filter:
            count_query = count_query.where(UploadTask.status == status_filter)
        
        total_result = await session.execute(count_query)
        total = total_result.scalar()
        
        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        result = await session.execute(query)
        tasks = result.scalars().all()
        
        return {
            "tasks": [task.to_dict() for task in tasks],
            "total": total,
            "pages": (total + page_size - 1) // page_size,
            "current_page": page,
            "page_size": page_size
        }

    async def update_task_status(
        self,
        task_id: str,
        status: str,
        progress: Optional[int] = None,
        uploaded_bytes: Optional[int] = None,
        error_message: Optional[str] = None,
        session: AsyncSession = None
    ) -> bool:
        """更新任务状态"""
        if session is None:
            async for session in get_async_session():
                return await self._update_task_status_impl(
                    task_id, status, progress, uploaded_bytes, error_message, session
                )
        else:
            return await self._update_task_status_impl(
                task_id, status, progress, uploaded_bytes, error_message, session
            )

    async def _update_task_status_impl(
        self,
        task_id: str,
        status: str,
        progress: Optional[int],
        uploaded_bytes: Optional[int],
        error_message: Optional[str],
        session: AsyncSession
    ) -> bool:
        """更新任务状态实现"""
        try:
            update_data = {"status": status}
            
            if progress is not None:
                update_data["progress"] = progress
            
            if uploaded_bytes is not None:
                update_data["uploaded_bytes"] = uploaded_bytes
            
            if error_message is not None:
                update_data["error_message"] = error_message
            
            # 设置时间戳
            if status == "uploading" and progress == 0:
                update_data["started_at"] = func.now()
            elif status == "success":
                update_data["completed_at"] = func.now()
                update_data["progress"] = 100
            
            query = update(UploadTask).where(UploadTask.task_id == task_id).values(**update_data)
            result = await session.execute(query)
            await session.commit()
            
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"更新任务状态失败: {task_id} - {str(e)}")
            await session.rollback()
            return False

    async def delete_task(self, task_id: str, session: AsyncSession = None) -> bool:
        """删除任务"""
        if session is None:
            async for session in get_async_session():
                return await self._delete_task_impl(task_id, session)
        else:
            return await self._delete_task_impl(task_id, session)

    async def _delete_task_impl(self, task_id: str, session: AsyncSession) -> bool:
        """删除任务实现"""
        try:
            query = delete(UploadTask).where(UploadTask.task_id == task_id)
            result = await session.execute(query)
            await session.commit()
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"删除任务失败: {task_id} - {str(e)}")
            await session.rollback()
            return False

    async def delete_tasks(self, task_ids: List[str], session: AsyncSession = None) -> int:
        """批量删除任务"""
        if session is None:
            async for session in get_async_session():
                return await self._delete_tasks_impl(task_ids, session)
        else:
            return await self._delete_tasks_impl(task_ids, session)

    async def _delete_tasks_impl(self, task_ids: List[str], session: AsyncSession) -> int:
        """批量删除任务实现"""
        try:
            query = delete(UploadTask).where(UploadTask.task_id.in_(task_ids))
            result = await session.execute(query)
            await session.commit()
            return result.rowcount
        except Exception as e:
            logger.error(f"批量删除任务失败: {str(e)}")
            await session.rollback()
            return 0

    async def update_tasks_status(
        self,
        task_ids: List[str],
        status: str,
        session: AsyncSession = None
    ) -> int:
        """批量更新任务状态"""
        if session is None:
            async for session in get_async_session():
                return await self._update_tasks_status_impl(task_ids, status, session)
        else:
            return await self._update_tasks_status_impl(task_ids, status, session)

    async def _update_tasks_status_impl(
        self,
        task_ids: List[str],
        status: str,
        session: AsyncSession
    ) -> int:
        """批量更新任务状态实现"""
        try:
            query = update(UploadTask).where(UploadTask.task_id.in_(task_ids)).values(status=status)
            result = await session.execute(query)
            await session.commit()
            return result.rowcount
        except Exception as e:
            logger.error(f"批量更新任务状态失败: {str(e)}")
            await session.rollback()
            return 0

    async def get_task_by_id(self, task_id: str, session: AsyncSession = None) -> Optional[UploadTask]:
        """根据ID获取任务"""
        if session is None:
            async for session in get_async_session():
                return await self._get_task_by_id_impl(task_id, session)
        else:
            return await self._get_task_by_id_impl(task_id, session)

    async def _get_task_by_id_impl(self, task_id: str, session: AsyncSession) -> Optional[UploadTask]:
        """根据ID获取任务实现"""
        query = select(UploadTask).where(UploadTask.task_id == task_id)
        result = await session.execute(query)
        return result.scalar_one_or_none()

    async def clear_completed_tasks(self, session: AsyncSession = None) -> int:
        """清理已完成的任务"""
        if session is None:
            async for session in get_async_session():
                return await self._clear_completed_tasks_impl(session)
        else:
            return await self._clear_completed_tasks_impl(session)

    async def _clear_completed_tasks_impl(self, session: AsyncSession) -> int:
        """清理已完成的任务实现"""
        try:
            query = delete(UploadTask).where(UploadTask.status == "success")
            result = await session.execute(query)
            await session.commit()
            return result.rowcount
        except Exception as e:
            logger.error(f"清理已完成任务失败: {str(e)}")
            await session.rollback()
            return 0

    async def retry_task(self, task_id: str, session: AsyncSession = None) -> bool:
        """重试任务"""
        if session is None:
            async for session in get_async_session():
                return await self._retry_task_impl(task_id, session)
        else:
            return await self._retry_task_impl(task_id, session)

    async def _retry_task_impl(self, task_id: str, session: AsyncSession) -> bool:
        """重试任务实现"""
        try:
            # 获取任务
            task = await self._get_task_by_id_impl(task_id, session)
            if not task:
                return False
            
            # 检查文件是否存在
            if not os.path.exists(task.file_path):
                logger.warning(f"重试任务失败，文件不存在: {task.file_path}")
                await self._update_task_status_impl(
                    task_id, "error", None, None, "源文件不存在", session
                )
                return False
            
            # 重置任务状态
            update_data = {
                "status": "pending",
                "progress": 0,
                "uploaded_bytes": 0,
                "error_message": None,
                "retry_count": task.retry_count + 1
            }
            
            query = update(UploadTask).where(UploadTask.task_id == task_id).values(**update_data)
            result = await session.execute(query)
            await session.commit()
            
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"重试任务失败: {task_id} - {str(e)}")
            await session.rollback()
            return False

    async def submit_task_to_celery(self, task_id: str) -> Dict[str, Any]:
        """提交任务到Celery队列"""
        try:
            # 延迟导入避免循环依赖
            from app.tasks.upload_tasks import process_upload_task

            # 提交到Celery
            celery_result = process_upload_task.delay(task_id)

            logger.info(f"任务已提交到Celery: {task_id} -> {celery_result.id}")

            return {
                "success": True,
                "task_id": task_id,
                "celery_task_id": celery_result.id,
                "message": "任务已提交到队列"
            }
        except Exception as e:
            logger.error(f"提交任务到Celery失败: {task_id} - {str(e)}")
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e),
                "message": "提交任务失败"
            }

    async def submit_batch_tasks_to_celery(self, task_ids: List[str]) -> Dict[str, Any]:
        """批量提交任务到Celery队列"""
        try:
            # 延迟导入避免循环依赖
            from app.tasks.upload_tasks import batch_process_upload_tasks

            # 批量提交到Celery
            celery_result = batch_process_upload_tasks.delay(task_ids)

            logger.info(f"批量任务已提交到Celery: {len(task_ids)} 个任务 -> {celery_result.id}")

            return {
                "success": True,
                "task_count": len(task_ids),
                "celery_task_id": celery_result.id,
                "message": f"已提交 {len(task_ids)} 个任务到队列"
            }
        except Exception as e:
            logger.error(f"批量提交任务到Celery失败: {str(e)}")
            return {
                "success": False,
                "task_count": len(task_ids),
                "error": str(e),
                "message": "批量提交任务失败"
            }

    async def retry_task_with_celery(self, task_id: str, session: AsyncSession = None) -> Dict[str, Any]:
        """使用Celery重试任务"""
        # 先重置任务状态
        success = await self.retry_task(task_id, session)
        if not success:
            return {
                "success": False,
                "task_id": task_id,
                "message": "重置任务状态失败"
            }

        # 提交到Celery
        return await self.submit_task_to_celery(task_id)


# 全局服务实例
upload_task_service = UploadTaskService()
