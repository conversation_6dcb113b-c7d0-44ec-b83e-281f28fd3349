'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, Search, Filter, Grid, List, MoreVertical, 
  Eye, Edit, Trash2, Share2, Download, Upload,
  Brain, FileText, Zap, Clock, Users, Star,
  BookOpen, Database, Layers, TrendingUp
} from 'lucide-react';
import apiClient from '@/lib/api';

// 知识库接口
interface KnowledgeBase {
  id: number;
  kb_id: string;
  name: string;
  description: string;
  avatar_url?: string;
  status: 'active' | 'inactive' | 'deleted';
  visibility: 'private' | 'public' | 'shared';
  owner_id: string;
  document_count: number;
  segment_count: number;
  vector_count: number;
  total_tokens: number;
  embedding_model?: string;
  vector_dimension?: number;
  created_at: string;
  updated_at: string;
  last_indexed_at?: string;
}

const KnowledgeBasePage: React.FC = () => {
  const router = useRouter();
  
  // 状态管理
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [selectedKbs, setSelectedKbs] = useState<string[]>([]);

  // 加载知识库列表
  const loadKnowledgeBases = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get('/api/v1/knowledge-bases', {
        params: {
          search: searchQuery || undefined,
          status: filterStatus === 'all' ? undefined : filterStatus,
          page: 1,
          page_size: 50
        }
      });
      
      setKnowledgeBases(response.data?.data || []);
    } catch (err) {
      console.error('Failed to load knowledge bases:', err);
      setError('加载知识库失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    loadKnowledgeBases();
  }, [searchQuery, filterStatus]);

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // 处理新建知识库
  const handleCreateKnowledgeBase = () => {
    router.push('/knowledge-base/create');
  };

  // 处理知识库操作
  const handleKnowledgeBaseAction = (action: string, kbId: string) => {
    switch (action) {
      case 'view':
        router.push(`/knowledge-base/${kbId}`);
        break;
      case 'edit':
        router.push(`/knowledge-base/${kbId}/edit`);
        break;
      case 'delete':
        // TODO: 实现删除功能
        break;
      default:
        break;
    }
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN');
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-4 mx-auto">
            <Brain className="w-8 h-8 text-white animate-pulse" />
          </div>
          <p className="text-gray-600">加载知识库中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 头部 */}
      <div className="bg-white/60 backdrop-blur-lg shadow-sm border-b border-white/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  AI知识库管理
                </h1>
                <p className="text-sm text-gray-500">智能知识管理与检索平台</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={handleCreateKnowledgeBase}
                className="inline-flex items-center px-6 py-2 rounded-xl transition-all duration-200 shadow-lg bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 hover:shadow-xl"
              >
                <Plus className="w-4 h-4 mr-2" />
                新建知识库
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索和过滤栏 */}
        <div className="bg-white/60 backdrop-blur-lg rounded-2xl shadow-xl border border-white/30 p-6 mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder="搜索知识库..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            {/* 过滤器 */}
            <div className="flex items-center space-x-3">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              >
                <option value="all">全部状态</option>
                <option value="active">活跃</option>
                <option value="inactive">非活跃</option>
              </select>

              {/* 视图切换 */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-all duration-200 ${
                    viewMode === 'grid' 
                      ? 'bg-white shadow-sm text-blue-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-all duration-200 ${
                    viewMode === 'list' 
                      ? 'bg-white shadow-sm text-blue-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="text-red-500 mr-2">⚠️</div>
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* 知识库列表 */}
        {knowledgeBases.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-600 rounded-3xl flex items-center justify-center mb-6 mx-auto">
              <BookOpen className="w-12 h-12 text-white" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无知识库</h3>
            <p className="text-gray-500 mb-6">创建您的第一个AI知识库，开始智能知识管理之旅</p>
            <button
              onClick={handleCreateKnowledgeBase}
              className="inline-flex items-center px-6 py-3 rounded-xl transition-all duration-200 shadow-lg bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 hover:shadow-xl"
            >
              <Plus className="w-5 h-5 mr-2" />
              创建知识库
            </button>
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {knowledgeBases.map((kb) => (
              <motion.div
                key={kb.kb_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`bg-white/60 backdrop-blur-lg rounded-2xl shadow-xl border border-white/30 p-6 hover:shadow-2xl transition-all duration-200 ${
                  viewMode === 'list' ? 'flex items-center space-x-6' : ''
                }`}
              >
                {/* 知识库头像 */}
                <div className={`${viewMode === 'list' ? 'flex-shrink-0' : 'mb-4'}`}>
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <Database className="w-6 h-6 text-white" />
                  </div>
                </div>

                {/* 知识库信息 */}
                <div className={`${viewMode === 'list' ? 'flex-1 min-w-0' : ''}`}>
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">{kb.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(kb.status)}`}>
                      {kb.status === 'active' ? '活跃' : '非活跃'}
                    </span>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">{kb.description || '暂无描述'}</p>

                  {/* 统计信息 */}
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-blue-600">{kb.document_count}</div>
                      <div className="text-xs text-gray-500">文档</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-600">{kb.segment_count}</div>
                      <div className="text-xs text-gray-500">分段</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-purple-600">{kb.vector_count}</div>
                      <div className="text-xs text-gray-500">向量</div>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      {formatTime(kb.updated_at)}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleKnowledgeBaseAction('view', kb.kb_id)}
                        className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                        title="查看"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleKnowledgeBaseAction('edit', kb.kb_id)}
                        className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200"
                        title="编辑"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleKnowledgeBaseAction('delete', kb.kb_id)}
                        className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                        title="删除"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default KnowledgeBasePage;
