/**
 * 存储概览组件
 * 显示真实的存储统计数据
 */
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  HardDrive, 
  Files, 
  Users, 
  Clock, 
  BarChart3,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  TrendingUp
} from 'lucide-react';
import apiClient from '@/lib/api';

interface StorageOverviewData {
  total_capacity: number;
  total_capacity_formatted: string;
  total_files: number;
  shared_files: number;
  recent_access: number;
  used_space: number;
  used_space_formatted: string;
  usage_percentage: number;
  storage_count: number;
  last_updated: string;
}

interface StorageOverviewProps {
  className?: string;
}

const StorageOverview: React.FC<StorageOverviewProps> = ({ className = '' }) => {
  const [data, setData] = useState<StorageOverviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get('/api/v1/storage-overview/summary');
      
      if (response.data.success) {
        setData(response.data.data);
      } else {
        setError(response.data.message || '获取存储概览失败');
      }
    } catch (err) {
      console.error('Failed to fetch storage overview:', err);
      setError(err instanceof Error ? err.message : '获取存储概览失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewData();
  }, []);

  const handleRefresh = () => {
    fetchOverviewData();
  };

  if (loading) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl p-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="flex items-center space-x-2 text-gray-500">
            <RefreshCw className="w-5 h-5 animate-spin" />
            <span>加载存储概览...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-red-200/50 rounded-xl p-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="flex items-center space-x-2 text-red-500">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
            <button
              onClick={handleRefresh}
              className="ml-2 px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200 transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  const stats = [
    {
      title: '总容量',
      value: data.total_capacity_formatted,
      icon: HardDrive,
      color: 'blue',
      description: `${data.storage_count} 个存储`
    },
    {
      title: '文件总数',
      value: data.total_files.toLocaleString(),
      icon: Files,
      color: 'green',
      description: '所有文件'
    },
    {
      title: '共享文件',
      value: data.shared_files.toLocaleString(),
      icon: Users,
      color: 'purple',
      description: '可访问文件'
    },
    {
      title: '最近访问',
      value: data.recent_access.toLocaleString(),
      icon: Clock,
      color: 'orange',
      description: '7天内'
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-700 border-blue-200',
      green: 'bg-green-100 text-green-700 border-green-200',
      purple: 'bg-purple-100 text-purple-700 border-purple-200',
      orange: 'bg-orange-100 text-orange-700 border-orange-200'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className={`bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl p-6 ${className}`}>
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <BarChart3 className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">存储概览</h3>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">
            更新于: {new Date(data.last_updated).toLocaleTimeString()}
          </span>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="刷新数据"
          >
            <RefreshCw className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`p-4 rounded-lg border ${getColorClasses(stat.color)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <stat.icon className="w-5 h-5" />
              <TrendingUp className="w-4 h-4 opacity-60" />
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold">{stat.value}</p>
              <p className="text-sm font-medium">{stat.title}</p>
              <p className="text-xs opacity-75">{stat.description}</p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* 使用率进度条 */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">存储使用率</span>
          <span className="text-sm text-gray-500">
            {data.used_space_formatted} / {data.total_capacity_formatted}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${Math.min(data.usage_percentage, 100)}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
            className={`h-2 rounded-full ${
              data.usage_percentage > 90 
                ? 'bg-red-500' 
                : data.usage_percentage > 70 
                ? 'bg-yellow-500' 
                : 'bg-green-500'
            }`}
          />
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>0%</span>
          <span className="font-medium">
            {data.usage_percentage.toFixed(1)}% 已使用
          </span>
          <span>100%</span>
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="mt-4 pt-4 border-t border-gray-200/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span className="text-sm text-gray-600">系统运行正常</span>
          </div>
          <div className="text-xs text-gray-500">
            {data.storage_count} 个存储在线
          </div>
        </div>
      </div>
    </div>
  );
};

export default StorageOverview;
