#!/usr/bin/env python3
"""
测试上传处理器
"""
import asyncio
import tempfile
import os
from app.services.upload_processor import upload_processor
from app.services.upload_task_service import upload_task_service
from app.core.database import get_async_session


async def test_upload_processor():
    """测试上传处理器"""
    print("开始测试上传处理器...")
    
    try:
        # 创建测试文件
        test_content = b"This is a test file content for upload processor testing."
        
        with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.txt') as f:
            f.write(test_content)
            temp_file_path = f.name
        
        print(f"创建测试文件: {temp_file_path}")
        
        # 创建上传任务
        async for session in get_async_session():
            task = await upload_task_service.create_task(
                file_name="test_upload.txt",
                file_size=len(test_content),
                file_path=temp_file_path,
                storage_id=1,  # 假设存在ID为1的存储配置
                upload_path="/test_upload.txt",
                session=session
            )
            
            print(f"创建上传任务: {task.task_id}")
            break
        
        # 启动上传处理器
        await upload_processor.start()
        print("上传处理器已启动")
        
        # 等待一段时间让处理器处理任务
        print("等待处理器处理任务...")
        await asyncio.sleep(10)
        
        # 检查任务状态
        async for session in get_async_session():
            updated_task = await upload_task_service.get_task_by_id(task.task_id, session)
            if updated_task:
                print(f"任务状态: {updated_task.status}")
                print(f"任务进度: {updated_task.progress}%")
                if updated_task.error_message:
                    print(f"错误信息: {updated_task.error_message}")
            else:
                print("任务不存在")
            break
        
        # 停止上传处理器
        await upload_processor.stop()
        print("上传处理器已停止")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_upload_processor())
