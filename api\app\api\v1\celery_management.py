"""
Celery服务管理API
"""
import time
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from app.core.celery_manager import celery_manager
from app.core.dependencies import get_current_user
from app.models.user import User
from app.services.celery_config_service import celery_config_service
from app.services.celery_metrics_collector import metrics_collector
from loguru import logger

router = APIRouter(tags=["Celery管理"])


class ServiceActionRequest(BaseModel):
    """服务操作请求"""
    action: str  # start, stop, restart
    service: str = "all"  # worker, beat, flower, all


class CeleryStatusResponse(BaseModel):
    """Celery状态响应"""
    redis_connected: bool
    services: Dict[str, Dict[str, Any]]
    overall_status: str


class CeleryConfigRequest(BaseModel):
    """Celery配置请求"""
    # Redis配置
    redis_host: str = "**************"
    redis_port: int = 6379
    redis_db: int = 10
    redis_password: str = None

    # Worker配置
    worker_concurrency: int = 4
    worker_prefetch_multiplier: int = 1
    worker_max_tasks_per_child: int = 1000
    worker_max_memory_per_child: int = 200000

    # 任务配置
    task_soft_time_limit: int = 300
    task_time_limit: int = 600
    task_max_retries: int = 3
    task_default_retry_delay: int = 60

    # 结果配置
    result_expires: int = 3600

    # 队列配置
    task_default_queue: str = "default"

    # 监控配置
    worker_send_task_events: bool = True
    task_send_sent_event: bool = True

    # Flower配置
    flower_port: int = 5555
    flower_basic_auth: str = "admin:password"


class CeleryMetricsResponse(BaseModel):
    """Celery指标响应"""
    current_metrics: Dict[str, Any]
    historical_metrics: List[Dict[str, Any]]


@router.get("/status", response_model=CeleryStatusResponse)
async def get_celery_status(
    current_user: User = Depends(get_current_user)
):
    """获取Celery服务状态"""
    try:
        status = celery_manager.get_status()
        
        # 计算整体状态
        running_services = sum(1 for s in status["services"].values() if s["running"])
        total_services = len(status["services"])
        
        if running_services == 0:
            overall_status = "stopped"
        elif running_services == total_services:
            overall_status = "running"
        else:
            overall_status = "partial"
        
        return CeleryStatusResponse(
            redis_connected=status["redis_connected"],
            services=status["services"],
            overall_status=overall_status
        )
        
    except Exception as e:
        logger.error(f"获取Celery状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.post("/control")
async def control_celery_service(
    request: ServiceActionRequest,
    current_user: User = Depends(get_current_user)
):
    """控制Celery服务"""
    try:
        action = request.action.lower()
        service = request.service.lower()
        
        logger.info(f"用户 {current_user.username} 请求 {action} {service} 服务")
        
        if action not in ["start", "stop", "restart"]:
            raise HTTPException(status_code=400, detail="无效的操作类型")
        
        if service not in ["worker", "beat", "flower", "all"]:
            raise HTTPException(status_code=400, detail="无效的服务类型")
        
        results = {}
        
        if service == "all":
            if action == "start":
                results = celery_manager.start_all()
            elif action == "stop":
                results = celery_manager.stop_all()
            elif action == "restart":
                results = celery_manager.restart_all()
        else:
            if action == "start":
                results[service] = celery_manager.start_service(service)
            elif action == "stop":
                results[service] = celery_manager.stop_service(service)
            elif action == "restart":
                results[service] = celery_manager.restart_service(service)
        
        # 检查操作结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        if success_count == total_count:
            message = f"所有服务{action}成功"
            status_code = 200
        elif success_count > 0:
            message = f"部分服务{action}成功 ({success_count}/{total_count})"
            status_code = 200
        else:
            message = f"所有服务{action}失败"
            status_code = 500
        
        # 获取最新状态
        current_status = celery_manager.get_status()
        
        return {
            "success": success_count > 0,
            "message": message,
            "results": results,
            "status": current_status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"控制Celery服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")


@router.get("/logs/{service}")
async def get_service_logs(
    service: str,
    lines: int = 50,
    current_user: User = Depends(get_current_user)
):
    """获取服务日志"""
    try:
        if service not in ["worker", "beat", "flower"]:
            raise HTTPException(status_code=400, detail="无效的服务类型")
        
        log_file = f"logs/celery_{service}.log"
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
            return {
                "success": True,
                "service": service,
                "lines": len(recent_lines),
                "logs": ''.join(recent_lines)
            }
            
        except FileNotFoundError:
            return {
                "success": True,
                "service": service,
                "lines": 0,
                "logs": f"{service} 服务日志文件不存在"
            }
            
    except Exception as e:
        logger.error(f"获取 {service} 日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")


@router.post("/auto-start")
async def set_auto_start(
    enabled: bool,
    current_user: User = Depends(get_current_user)
):
    """设置Celery自动启动"""
    try:
        # 这里可以保存到配置文件或数据库
        import json
        config_file = "celery_config.json"
        
        config = {"auto_start": enabled}
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"用户 {current_user.username} 设置Celery自动启动: {enabled}")
        
        return {
            "success": True,
            "message": f"自动启动已{'启用' if enabled else '禁用'}",
            "auto_start": enabled
        }
        
    except Exception as e:
        logger.error(f"设置自动启动失败: {e}")
        raise HTTPException(status_code=500, detail=f"设置失败: {str(e)}")


@router.get("/auto-start")
async def get_auto_start(
    current_user: User = Depends(get_current_user)
):
    """获取自动启动设置"""
    try:
        import json
        config_file = "celery_config.json"
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                auto_start = config.get("auto_start", True)  # 默认启用
        except FileNotFoundError:
            auto_start = True  # 默认启用
        
        return {
            "success": True,
            "auto_start": auto_start
        }
        
    except Exception as e:
        logger.error(f"获取自动启动设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取设置失败: {str(e)}")


@router.get("/config")
async def get_celery_config(
    current_user: User = Depends(get_current_user)
):
    """获取Celery配置"""
    try:
        config = await celery_config_service.get_active_config()
        if not config:
            raise HTTPException(status_code=404, detail="未找到Celery配置")

        return {
            "success": True,
            "data": config.to_dict()
        }

    except Exception as e:
        logger.error(f"获取Celery配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.put("/config")
async def update_celery_config(
    config_request: CeleryConfigRequest,
    current_user: User = Depends(get_current_user)
):
    """更新Celery配置"""
    try:
        # 更新配置
        config_data = config_request.dict(exclude_unset=True)
        updated_config = await celery_config_service.update_config(config_data)

        # 生成新的Celery配置
        from app.core.celery_config import update_celery_config
        new_celery_config = celery_config_service.generate_celery_config(updated_config)

        # 应用新配置（需要重启服务才能完全生效）
        config_updated = update_celery_config(new_celery_config)

        logger.info(f"用户 {current_user.username} 更新了Celery配置")

        return {
            "success": True,
            "message": "配置更新成功，建议重启Celery服务以应用所有更改",
            "config_applied": config_updated,
            "data": updated_config.to_dict()
        }

    except Exception as e:
        logger.error(f"更新Celery配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.get("/metrics", response_model=CeleryMetricsResponse)
async def get_celery_metrics(
    hours: int = 24,
    current_user: User = Depends(get_current_user)
):
    """获取Celery指标"""
    try:
        # 获取当前指标
        current_metrics = await metrics_collector.get_current_metrics()

        # 获取历史指标
        historical_metrics = await metrics_collector.get_historical_metrics(hours=hours)

        return CeleryMetricsResponse(
            current_metrics=current_metrics,
            historical_metrics=historical_metrics
        )

    except Exception as e:
        logger.error(f"获取Celery指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.post("/metrics/start-collection")
async def start_metrics_collection(
    current_user: User = Depends(get_current_user)
):
    """启动指标收集"""
    try:
        await metrics_collector.start_collection()

        return {
            "success": True,
            "message": "指标收集已启动"
        }

    except Exception as e:
        logger.error(f"启动指标收集失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")


@router.post("/metrics/stop-collection")
async def stop_metrics_collection(
    current_user: User = Depends(get_current_user)
):
    """停止指标收集"""
    try:
        await metrics_collector.stop_collection()

        return {
            "success": True,
            "message": "指标收集已停止"
        }

    except Exception as e:
        logger.error(f"停止指标收集失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")


@router.get("/health")
async def celery_health_check():
    """Celery健康检查（无需认证）"""
    try:
        status = celery_manager.get_status()

        # 简单的健康检查
        redis_ok = status["redis_connected"]
        worker_ok = status["services"]["worker"]["running"]

        healthy = redis_ok and worker_ok

        return {
            "healthy": healthy,
            "redis_connected": redis_ok,
            "worker_running": worker_ok,
            "timestamp": int(time.time())
        }

    except Exception as e:
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": int(time.time())
        }
