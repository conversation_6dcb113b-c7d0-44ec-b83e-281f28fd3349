<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库表修复完成</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        .status-badge {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 5px;
        }
        .fix-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }
        .fix-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .fix-icon {
            margin-right: 10px;
            font-size: 1.5rem;
        }
        .sql-block {
            background: #0f172a;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            border-left: 4px solid #3b82f6;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before-card, .after-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
        .before-card {
            border-left: 4px solid #ef4444;
        }
        .after-card {
            border-left: 4px solid #10b981;
        }
        .card-header {
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .card-icon {
            margin-right: 8px;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .success-box {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #047857;
        }
        .fix-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .fix-item {
            display: flex;
            align-items: flex-start;
            margin: 10px 0;
            padding: 8px 0;
        }
        .fix-item-icon {
            margin-right: 10px;
            margin-top: 2px;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 数据库表修复完成</h1>
            <p class="subtitle">外键约束已移除，模板插入冲突已修复</p>
            <div>
                <span class="status-badge">✅ 外键约束移除</span>
                <span class="status-badge">✅ 唯一约束添加</span>
                <span class="status-badge">✅ 冲突处理修复</span>
                <span class="status-badge">✅ SQL脚本更新</span>
            </div>
        </div>

        <!-- 修复内容1：外键约束移除 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🔗</span>
                修复1：移除所有外键约束
            </div>
            
            <div class="before-after">
                <div class="before-card">
                    <div class="card-header">
                        <span class="card-icon">❌</span>
                        修复前
                    </div>
                    <div class="sql-block">
CREATE TABLE document_segments (
    -- ... 其他字段
    task_id VARCHAR(36) NOT NULL,
    -- ... 其他字段
    FOREIGN KEY (task_id) 
    REFERENCES document_segment_tasks(task_id) 
    ON DELETE CASCADE
);
                    </div>
                    <p><strong>问题：</strong>外键约束可能导致数据操作复杂化，影响性能</p>
                </div>

                <div class="after-card">
                    <div class="card-header">
                        <span class="card-icon">✅</span>
                        修复后
                    </div>
                    <div class="sql-block">
CREATE TABLE document_segments (
    -- ... 其他字段
    task_id VARCHAR(36) NOT NULL,
    -- ... 其他字段
    -- 外键约束已移除
);
                    </div>
                    <p><strong>优势：</strong>简化表结构，提升操作灵活性和性能</p>
                </div>
            </div>
        </div>

        <!-- 修复内容2：模板插入冲突 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🔄</span>
                修复2：模板插入冲突处理
            </div>
            
            <div class="before-after">
                <div class="before-card">
                    <div class="card-header">
                        <span class="card-icon">❌</span>
                        修复前
                    </div>
                    <div class="sql-block">
CREATE TABLE segment_templates (
    template_name VARCHAR(200) NOT NULL,
    -- ... 其他字段
);

-- 插入时报错
ON CONFLICT (template_name) DO NOTHING;
-- ERROR: there is no unique or exclusion 
-- constraint matching the ON CONFLICT specification
                    </div>
                    <p><strong>问题：</strong>template_name 没有唯一约束，无法使用 ON CONFLICT</p>
                </div>

                <div class="after-card">
                    <div class="card-header">
                        <span class="card-icon">✅</span>
                        修复后
                    </div>
                    <div class="sql-block">
CREATE TABLE segment_templates (
    template_name VARCHAR(200) UNIQUE NOT NULL,
    -- ... 其他字段
);

-- 插入成功
ON CONFLICT (template_name) DO NOTHING;
-- 正常工作，避免重复插入
                    </div>
                    <p><strong>优势：</strong>添加唯一约束，支持冲突处理，避免重复数据</p>
                </div>
            </div>
        </div>

        <!-- 修复后的完整SQL -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">📝</span>
                修复后的完整SQL脚本
            </div>
            
            <p>新的SQL脚本文件：<code>api/create_segment_tables_fixed.sql</code></p>
            
            <div class="sql-block">
-- 创建分段模板表（添加唯一约束）
CREATE TABLE IF NOT EXISTS segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(200) UNIQUE NOT NULL,  -- 添加 UNIQUE 约束
    description TEXT,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档分段表（移除外键约束）
CREATE TABLE IF NOT EXISTS document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id VARCHAR(36) NOT NULL,  -- 保留关联字段，但无外键约束
    file_id VARCHAR(500) NOT NULL,
    file_name VARCHAR(500),
    segment_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    char_count INTEGER DEFAULT 0,
    vectorize_status VARCHAR(20) DEFAULT 'pending',
    embedding_vector JSONB,
    keywords JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    -- 外键约束已移除
);

-- 插入默认模板（现在可以正常工作）
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
('通用文档分段', '适用于大多数文档的通用分段配置', 'paragraph', 500, 50,
 true, true, 'text-embedding-ada-002', 1536, 1000, 'zh', false, true, true, true, true),
('长文档分段', '适用于长文档的分段配置，较大的分段长度', 'paragraph', 1000, 100,
 true, true, 'text-embedding-ada-002', 1536, 1500, 'zh', false, true, true, false, true),
('精细分段', '适用于需要精细分段的文档，较小的分段长度', 'sentence', 200, 20,
 true, true, 'text-embedding-ada-002', 1536, 500, 'zh', true, true, true, false, true)
ON CONFLICT (template_name) DO NOTHING;  -- 现在可以正常工作
            </div>
        </div>

        <!-- 修复优势 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🎯</span>
                修复优势
            </div>
            
            <ul class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">⚡</span>
                    <span><strong>性能提升</strong> - 移除外键约束减少了数据库操作的复杂度</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">🔧</span>
                    <span><strong>操作灵活</strong> - 简化了数据插入、更新、删除操作</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">🛡️</span>
                    <span><strong>数据完整性</strong> - 通过应用层逻辑保证数据关联性</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">🔄</span>
                    <span><strong>冲突处理</strong> - 支持重复执行SQL脚本而不报错</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">📊</span>
                    <span><strong>扩展性好</strong> - 便于后续功能扩展和数据迁移</span>
                </li>
            </ul>
        </div>

        <!-- 执行步骤 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🚀</span>
                执行步骤
            </div>
            
            <ol class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">1️⃣</span>
                    <div>
                        <strong>连接数据库</strong><br>
                        使用 pgAdmin、DBeaver 等工具连接到 PostgreSQL 数据库
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">2️⃣</span>
                    <div>
                        <strong>执行修复脚本</strong><br>
                        运行 <code>api/create_segment_tables_fixed.sql</code> 脚本
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">3️⃣</span>
                    <div>
                        <strong>验证结果</strong><br>
                        检查表创建和模板数据插入是否成功
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">4️⃣</span>
                    <div>
                        <strong>测试功能</strong><br>
                        重启后端服务，测试批量分段功能
                    </div>
                </li>
            </ol>
        </div>

        <!-- 验证查询 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">✅</span>
                验证查询
            </div>
            
            <div class="sql-block">
-- 检查表是否创建成功
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('document_segment_tasks', 'document_segments', 'segment_templates', 'vector_indexes')
ORDER BY table_name;

-- 检查模板数据
SELECT template_name, description, is_default 
FROM segment_templates 
ORDER BY is_default DESC, template_name;

-- 检查唯一约束
SELECT constraint_name, table_name, column_name
FROM information_schema.key_column_usage
WHERE table_name = 'segment_templates' 
AND constraint_name LIKE '%unique%';
            </div>
            
            <div class="success-box">
                <strong>预期结果：</strong><br>
                • 4个表创建成功<br>
                • 3个默认模板插入成功<br>
                • template_name 字段有唯一约束<br>
                • 无外键约束存在
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="action-button" onclick="copyFixedSQL()">
                📋 复制修复SQL
            </button>
            <button class="action-button" onclick="showChanges()">
                🔍 查看变更详情
            </button>
            <button class="action-button" onclick="showVerification()">
                ✅ 验证方法
            </button>
            <button class="action-button" onclick="showNextSteps()">
                🚀 下一步操作
            </button>
        </div>
    </div>

    <script>
        function copyFixedSQL() {
            const sqlText = `-- 创建文档分段相关数据库表（修复版本）

-- 创建文档分段任务表
CREATE TABLE IF NOT EXISTS document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    file_ids JSONB NOT NULL,
    total_files INTEGER DEFAULT 0,
    processed_files INTEGER DEFAULT 0,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'pending',
    progress FLOAT DEFAULT 0.0,
    error_message TEXT,
    total_segments INTEGER DEFAULT 0,
    total_vectors INTEGER DEFAULT 0,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档分段表（无外键约束）
CREATE TABLE IF NOT EXISTS document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id VARCHAR(36) NOT NULL,
    file_id VARCHAR(500) NOT NULL,
    file_name VARCHAR(500),
    segment_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    char_count INTEGER DEFAULT 0,
    vectorize_status VARCHAR(20) DEFAULT 'pending',
    embedding_vector JSONB,
    keywords JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建分段模板表（添加唯一约束）
CREATE TABLE IF NOT EXISTS segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(200) UNIQUE NOT NULL,
    description TEXT,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量索引表
CREATE TABLE IF NOT EXISTS vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(200) NOT NULL,
    description TEXT,
    embedding_model VARCHAR(100) NOT NULL,
    vector_dimension INTEGER NOT NULL,
    total_vectors INTEGER DEFAULT 0,
    index_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);

-- 插入默认分段模板
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
('通用文档分段', '适用于大多数文档的通用分段配置', 'paragraph', 500, 50,
 true, true, 'text-embedding-ada-002', 1536, 1000, 'zh', false, true, true, true, true),
('长文档分段', '适用于长文档的分段配置，较大的分段长度', 'paragraph', 1000, 100,
 true, true, 'text-embedding-ada-002', 1536, 1500, 'zh', false, true, true, false, true),
('精细分段', '适用于需要精细分段的文档，较小的分段长度', 'sentence', 200, 20,
 true, true, 'text-embedding-ada-002', 1536, 500, 'zh', true, true, true, false, true)
ON CONFLICT (template_name) DO NOTHING;`;

            navigator.clipboard.writeText(sqlText).then(() => {
                alert('✅ 修复后的SQL脚本已复制到剪贴板！\n\n请在数据库管理工具中执行此脚本。');
            }).catch(() => {
                alert('❌ 复制失败，请手动复制SQL脚本。');
            });
        }

        function showChanges() {
            alert(`🔍 变更详情\n\n主要修复:\n\n1. 外键约束移除\n   • document_segments 表移除了对 document_segment_tasks 的外键约束\n   • 简化表结构，提升操作性能\n   • 通过应用层逻辑保证数据关联性\n\n2. 唯一约束添加\n   • segment_templates.template_name 添加 UNIQUE 约束\n   • 支持 ON CONFLICT 语法\n   • 避免重复插入模板数据\n\n3. 索引优化\n   • 添加 idx_segment_templates_template_name 索引\n   • 提升模板查询性能\n\n4. 冲突处理\n   • INSERT ... ON CONFLICT (template_name) DO NOTHING\n   • 支持重复执行脚本\n   • 避免重复数据插入错误\n\n文件变更:\n• api/create_segment_tables.sql - 原始文件已更新\n• api/create_segment_tables_fixed.sql - 新的修复版本\n\n现在可以正常执行SQL脚本，不会再出现冲突错误！`);
        }

        function showVerification() {
            alert(`✅ 验证方法\n\n执行以下查询验证修复效果:\n\n1. 检查表创建\nSELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE '%segment%';\n\n预期结果: 4个表\n\n2. 检查唯一约束\nSELECT constraint_name, column_name FROM information_schema.key_column_usage WHERE table_name = 'segment_templates';\n\n预期结果: template_name 有唯一约束\n\n3. 检查外键约束\nSELECT constraint_name FROM information_schema.table_constraints WHERE table_name = 'document_segments' AND constraint_type = 'FOREIGN KEY';\n\n预期结果: 无外键约束\n\n4. 检查模板数据\nSELECT COUNT(*) FROM segment_templates;\n\n预期结果: 3个模板\n\n5. 测试重复插入\n再次执行INSERT语句，应该不报错\n\n全部验证通过说明修复成功！`);
        }

        function showNextSteps() {
            alert(`🚀 下一步操作\n\n1. 执行修复脚本\n   • 在数据库管理工具中执行修复后的SQL\n   • 或使用命令行: psql -h 192.168.50.142 -U postgres -d xhc_rag -f create_segment_tables_fixed.sql\n\n2. 验证表创建\n   • 检查4个表是否创建成功\n   • 验证3个默认模板是否插入\n   • 确认唯一约束和索引\n\n3. 重启后端服务\n   • 重启FastAPI服务\n   • 确保连接到新创建的表\n\n4. 测试批量分段\n   • 访问文件管理页面\n   • 选择多个文件\n   • 点击"分段"按钮\n   • 进入批量分段配置页面\n   • 点击"开始AI分段"\n   • 验证不再出现表不存在错误\n\n5. 功能验证\n   • 测试实时进度监控\n   • 验证横向铺满布局\n   • 检查网格显示效果\n\n完成这些步骤后，批量分段功能应该完全正常工作！`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据库表修复完成页面已加载');
        });
    </script>
</body>
</html>
