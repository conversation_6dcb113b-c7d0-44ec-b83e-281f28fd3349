@echo off
REM 测试分段任务管理功能
REM 适用于Windows系统

echo ========================================
echo 分段任务管理功能测试
echo ========================================

REM 设置环境变量
set PYTHONPATH=%cd%
cd /d "%~dp0"

echo 当前目录: %cd%
echo Python路径: %PYTHONPATH%
echo.

REM 检查Python环境
python --version
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 1. 检查数据库连接...
python -c "from app.core.database import get_sync_session; from app.models.document_segment import DocumentSegmentTask; db = next(get_sync_session()); print('数据库连接正常，任务表记录数:', db.query(DocumentSegmentTask).count()); db.close()"

echo.
echo 2. 检查Celery连接...
python -c "from app.core.celery_config import celery_app; inspect = celery_app.control.inspect(); stats = inspect.stats(); print('Celery连接正常，Worker数:', len(stats) if stats else 0)"

echo.
echo 3. 测试API端点...
echo 测试系统API...
curl -X GET "http://127.0.0.1:8000/api/v1/system/celery/status" -H "accept: application/json" 2>nul
if errorlevel 1 (
    echo 警告: 系统API测试失败，请确保API服务已启动
) else (
    echo 系统API测试通过
)

echo.
echo 测试分段任务API...
curl -X GET "http://127.0.0.1:8000/api/v1/document-segment/tasks" -H "accept: application/json" 2>nul
if errorlevel 1 (
    echo 警告: 分段任务API测试失败，请确保API服务已启动
) else (
    echo 分段任务API测试通过
)

echo.
echo 4. 检查前端路由...
echo 请在浏览器中访问以下URL进行测试:
echo - 文件管理页面: http://localhost:3000/file-manager
echo - 分段任务管理: http://localhost:3000/file-manager/segment/task
echo - AI批量分段: http://localhost:3000/file-manager/segment/batch

echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 如果所有测试都通过，您可以：
echo 1. 在文件管理页面点击"分段任务管理"按钮
echo 2. 查看Celery状态和任务列表
echo 3. 点击任务查看详情或创建新任务
echo.
pause
