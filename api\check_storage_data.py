#!/usr/bin/env python3
"""
检查存储配置数据
"""
import asyncio
import asyncpg
from loguru import logger
from app.core.config import get_settings

settings = get_settings()

async def check_storage_data():
    """检查存储配置表中的数据"""
    
    # 解析数据库URL
    db_url = settings.DATABASE_URL
    if db_url.startswith("postgresql+asyncpg://"):
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(db_url)
        logger.info("Connected to database")
        
        # 检查表结构
        columns = await conn.fetch("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'storage_configs'
            ORDER BY ordinal_position
        """)
        
        logger.info("Table structure:")
        for col in columns:
            logger.info(f"  {col['column_name']}: {col['data_type']} (nullable: {col['is_nullable']})")
        
        # 检查现有数据
        rows = await conn.fetch("SELECT * FROM storage_configs")
        
        logger.info(f"\nFound {len(rows)} storage configs:")
        for row in rows:
            logger.info(f"  ID: {row['id']}")
            logger.info(f"  Name: {row['name']}")
            logger.info(f"  Storage Type: {row['storage_type']} (type: {type(row['storage_type'])})")
            logger.info(f"  Is Default: {row['is_default']}")
            logger.info(f"  Is Active: {row['is_active']}")
            logger.info(f"  Config: {row['config']}")
            logger.info(f"  Created At: {row['created_at']}")
            logger.info(f"  Updated At: {row['updated_at']}")
            logger.info("  ---")
        
        await conn.close()
        
    except Exception as e:
        logger.error(f"Error checking storage data: {e}")

if __name__ == "__main__":
    asyncio.run(check_storage_data())
