#!/usr/bin/env python3
"""
AI知识库快速启动脚本
自动处理依赖和配置问题
"""

import os
import sys
import subprocess
from pathlib import Path
from dotenv import load_dotenv


def check_and_install_dependencies():
    """检查并安装必要的依赖"""
    print("🔍 Checking dependencies...")

    # 检查基础依赖
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        print("✅ Basic dependencies are available")
    except ImportError as e:
        print(f"❌ Missing basic dependency: {e}")
        print("📦 Installing basic dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "fastapi", "uvicorn", "sqlalchemy"], check=True)

    # 检查 PostgreSQL 驱动
    try:
        import asyncpg
        print("✅ PostgreSQL driver (asyncpg) is available")
    except ImportError:
        print("📦 Installing PostgreSQL driver...")
        subprocess.run([sys.executable, "-m", "pip", "install", "asyncpg"], check=True)

    # 检查 Redis 客户端
    try:
        import redis
        import aioredis
        print("✅ Redis clients are available")
    except ImportError:
        print("📦 Installing Redis clients...")
        subprocess.run([sys.executable, "-m", "pip", "install", "redis", "aioredis"], check=True)

    # 检查其他必要依赖
    try:
        import loguru
        import pydantic
        from dotenv import load_dotenv
        print("✅ Additional dependencies are available")
    except ImportError:
        print("📦 Installing additional dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "loguru", "pydantic", "python-dotenv"], check=True)


def create_default_env():
    """创建默认环境配置"""
    env_file = Path(".env.local")
    
    if env_file.exists():
        print("✅ Environment file already exists")
        return
    
    print("📝 Creating default environment configuration...")
    
    env_content = """# AI知识库 - 开发环境配置
ENVIRONMENT=development
DEBUG=true
HOST=127.0.0.1
PORT=8000
LOG_LEVEL=DEBUG

# 应用配置
APP_NAME=AI知识库 API
APP_VERSION=0.1.0
APP_DESCRIPTION=AI知识库 - 基于人工智能的智能知识管理与检索系统

# 安全配置
SECRET_KEY=dev-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# PostgreSQL 数据库配置
DB_USERNAME=postgres
DB_PASSWORD=XHC12345
DB_HOST=**************
DB_PORT=5432
DB_DATABASE=xhc_rag
DATABASE_URL=postgresql+asyncpg://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}
DATABASE_TYPE=postgresql
DATABASE_ECHO=false

# Redis 配置
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=XHC12345
REDIS_USE_SSL=false
REDIS_DB=10
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# 插件配置
ENABLED_PLUGINS=auth,file_manager,rag_engine,storage_management

# CORS 配置
CORS_ORIGINS=*

# 文件存储配置
UPLOAD_MAX_SIZE=104857600
UPLOAD_ALLOWED_EXTENSIONS=txt,pdf,doc,docx,xls,xlsx,ppt,pptx,md,json,csv,jpg,jpeg,png,gif,mp4,avi,mov
DEFAULT_STORAGE_TYPE=local
STORAGE_BASE_PATH=./storage
"""
    
    env_file.write_text(env_content)
    print(f"✅ Created {env_file}")


def load_environment_config():
    """加载环境配置"""
    env_files = ['.env.local', '.env', '.env.example']

    for env_file in env_files:
        env_path = Path(env_file)
        if env_path.exists():
            print(f"📄 Loading environment from: {env_file}")
            load_dotenv(env_path)
            return env_file

    print("⚠️  No environment file found, using default values")
    return None


def get_env_var(key: str, default: str = "") -> str:
    """获取环境变量，支持变量替换"""
    value = os.getenv(key, default)

    # 简单的变量替换，支持 ${VAR_NAME} 格式
    if "${" in value and "}" in value:
        import re
        pattern = r'\$\{([^}]+)\}'
        matches = re.findall(pattern, value)
        for match in matches:
            env_value = os.getenv(match, "")
            # URL编码特殊字符（如@符号）
            if key == "DATABASE_URL" and "@" in env_value:
                import urllib.parse
                env_value = urllib.parse.quote(env_value, safe=':/')
            value = value.replace(f"${{{match}}}", env_value)

    return value


def check_database_connection():
    """检查数据库连接"""
    print("🔍 Checking database connection...")

    try:
        import asyncpg
        import asyncio

        # 从环境变量获取数据库配置
        db_host = get_env_var("DB_HOST", "localhost")
        db_port = int(get_env_var("DB_PORT", "5432"))
        db_user = get_env_var("DB_USERNAME", "postgres")
        db_password = get_env_var("DB_PASSWORD", "")
        db_name = get_env_var("DB_DATABASE", "xhc_rag")

        print(f"   Connecting to: {db_user}@{db_host}:{db_port}/{db_name}")

        async def test_db():
            try:
                conn = await asyncpg.connect(
                    host=db_host,
                    port=db_port,
                    user=db_user,
                    password=db_password,
                    database=db_name
                )
                await conn.close()
                return True
            except Exception as e:
                print(f"❌ Database connection failed: {e}")
                return False

        result = asyncio.run(test_db())
        if result:
            print("✅ PostgreSQL database connection successful")
        return result

    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False


def check_redis_connection():
    """检查Redis连接"""
    print("🔍 Checking Redis connection...")

    try:
        import redis

        # 从环境变量获取Redis配置
        redis_host = get_env_var("REDIS_HOST", "localhost")
        redis_port = int(get_env_var("REDIS_PORT", "6379"))
        redis_password = get_env_var("REDIS_PASSWORD", "")
        redis_db = int(get_env_var("REDIS_DB", "0"))

        print(f"   Connecting to: {redis_host}:{redis_port}/{redis_db}")

        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            password=redis_password if redis_password else None,
            db=redis_db,
            socket_timeout=5
        )

        r.ping()
        print("✅ Redis connection successful")
        return True

    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("⚠️  Redis is optional, server can still start without it")
        return False


def cleanup_logs():
    """清理可能冲突的日志文件"""
    try:
        logs_dir = Path("logs")
        if logs_dir.exists():
            # 删除可能被锁定的日志文件
            for log_file in logs_dir.glob("*.log"):
                try:
                    if log_file.exists():
                        log_file.unlink()
                        print(f"🗑️  Cleaned up log file: {log_file}")
                except PermissionError:
                    print(f"⚠️  Could not remove {log_file} (file in use)")
                except Exception:
                    pass  # 忽略其他错误
    except Exception:
        pass  # 忽略清理错误


def start_server():
    """启动服务器"""
    print("🚀 Starting AI Knowledge Base server...")

    try:
        # 清理日志文件
        cleanup_logs()

        # 设置环境变量
        os.environ["ENVIRONMENT"] = "development"
        os.environ["LOG_LEVEL"] = "INFO"  # 降低日志级别减少文件写入

        # 启动服务器
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "127.0.0.1",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]

        print(f"🔧 Running: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)

    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start server: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

    return True


def check_server_requirements():
    """检查服务器启动要求"""
    print("🔍 Checking server requirements...")

    # 检查端口是否被占用
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', 8000))
        sock.close()

        if result == 0:
            print("⚠️  Port 8000 is already in use!")
            print("💡 Please stop the existing service or use a different port")
            return False
        else:
            print("✅ Port 8000 is available")
            return True
    except Exception:
        print("✅ Port check completed")
        return True


def main():
    """主函数"""
    print("🚀 AI知识库快速启动")
    print("=" * 50)

    try:
        # 检查并安装依赖
        check_and_install_dependencies()

        # 创建默认环境配置
        create_default_env()

        # 加载环境配置
        env_file = load_environment_config()

        # 检查服务器要求
        if not check_server_requirements():
            print("❌ Server requirements check failed!")
            sys.exit(1)

        print("\n🔗 Checking external services...")
        print("-" * 30)

        # 检查数据库连接
        db_ok = check_database_connection()
        if not db_ok:
            print("❌ Database connection failed!")
            print("💡 Please ensure PostgreSQL is running and accessible:")
            print(f"   - Host: {get_env_var('DB_HOST')}:{get_env_var('DB_PORT')}")
            print(f"   - Database: {get_env_var('DB_DATABASE')}")
            print(f"   - User: {get_env_var('DB_USERNAME')}")
            print(f"   - Config file: {env_file}")
            sys.exit(1)

        # 检查Redis连接（可选）
        redis_ok = check_redis_connection()

        # 数据库表检查（仅检查，不自动初始化）
        print("🔍 Checking database tables...")
        try:
            import asyncio
            import asyncpg

            async def check_tables():
                try:
                    # 使用环境变量中的数据库配置
                    db_host = get_env_var("DB_HOST", "localhost")
                    db_port = int(get_env_var("DB_PORT", "5432"))
                    db_user = get_env_var("DB_USERNAME", "postgres")
                    db_password = get_env_var("DB_PASSWORD", "")
                    db_name = get_env_var("DB_DATABASE", "xhc_rag")

                    conn = await asyncpg.connect(
                        host=db_host,
                        port=db_port,
                        user=db_user,
                        password=db_password,
                        database=db_name
                    )

                    # 检查是否存在存储配置表
                    result = await conn.fetchval(
                        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'storage_configs')"
                    )
                    await conn.close()
                    return result
                except Exception:
                    return False

            tables_exist = asyncio.run(check_tables())

            if not tables_exist:
                print("⚠️  Database tables not found!")
                print("💡 Tables will be created automatically on first access")
                print("💡 Or run manually: python init_database.py")
            else:
                print("✅ Database tables exist")

        except Exception as e:
            print(f"⚠️  Could not check database tables: {e}")
            print("💡 Tables will be created automatically on first access")

        print("\n✅ All checks passed!")
        print("-" * 30)

        # 启动服务器
        print("\n🌐 Server will be available at:")
        print("   - API: http://127.0.0.1:8000")
        print("   - Docs: http://127.0.0.1:8000/docs")
        print("   - Health: http://127.0.0.1:8000/health")
        print("   - File Manager: http://localhost:3000/file-manager")
        print("\n📊 Database & Cache:")
        print(f"   - PostgreSQL: {'✅ Connected' if db_ok else '❌ Failed'}")
        print(f"   - Redis: {'✅ Connected' if redis_ok else '⚠️ Optional'}")
        print("\n⏹️  Press Ctrl+C to stop the server")
        print("=" * 50)

        start_server()

    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure Python 3.9+ is installed")
        print("2. Install dependencies: pip install fastapi uvicorn sqlalchemy asyncpg redis aioredis")
        print("3. Check if PostgreSQL is running on **************:5432")
        print("4. Check if Redis is running on **************:6379")
        print("5. Verify database credentials and permissions")
        print("6. Check if port 8000 is available")
        print("7. If you see log file permission errors, restart your terminal/IDE")
        print("8. Try manual start: python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
        sys.exit(1)


if __name__ == "__main__":
    main()
