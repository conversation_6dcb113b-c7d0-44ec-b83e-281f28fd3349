@echo off
echo ========================================
echo 批量分段功能数据库表结构更新脚本
echo ========================================
echo.

REM 设置数据库连接参数
set DB_HOST=**************
set DB_PORT=5432
set DB_NAME=xhc_rag
set DB_USER=postgres
set DB_PASSWORD=XHC12345

echo 正在连接数据库: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo 用户: %DB_USER%
echo.

REM 检查psql是否可用
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 psql 命令，请确保 PostgreSQL 客户端已安装并添加到 PATH
    pause
    exit /b 1
)

echo 步骤 1: 创建新的表结构...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f batch_segment_tables.sql
if %errorlevel% neq 0 (
    echo 错误: 创建表结构失败
    pause
    exit /b 1
)
echo 表结构创建完成!
echo.

echo 步骤 2: 更新现有表结构...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f update_batch_segment_tables.sql
if %errorlevel% neq 0 (
    echo 错误: 更新表结构失败
    pause
    exit /b 1
)
echo 表结构更新完成!
echo.

echo 步骤 3: 验证表结构...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT table_name, column_name FROM information_schema.columns WHERE table_name LIKE 'document_segment%' ORDER BY table_name, ordinal_position;"
echo.

echo ========================================
echo 数据库表结构更新完成!
echo ========================================
echo.
echo 新增的表:
echo - document_segment_tasks (文档分段任务表)
echo - document_segment_task_files (任务文件关联表)
echo - document_segments (分段结果表)
echo - document_segment_statistics (分段统计表)
echo.
echo 新增的字段:
echo - document_segment_tasks: processing_files, pending_files, total_segments
echo - document_segment_task_files: file_size, file_extension, storage_type, started_at, completed_at
echo.
echo 新增的功能:
echo - 自动统计触发器
echo - 实时统计更新
echo - 完整的索引支持
echo.
pause
