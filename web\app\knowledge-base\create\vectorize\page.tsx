'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, ArrowRight, Check, Settings, Zap, 
  Brain, Database, Layers, ChevronRight, Loader2,
  FileText, AlertCircle, CheckCircle
} from 'lucide-react';
import apiClient from '@/lib/api';

// 向量化配置接口
interface VectorConfig {
  embedding_model: string;
  vector_dimension: number;
  chunk_size: number;
  chunk_overlap: number;
  batch_size: number;
  max_retries: number;
}

// 可用模型接口
interface EmbeddingModel {
  name: string;
  display_name: string;
  dimension: number;
  description: string;
  provider: string;
}

const VectorizeConfigPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // 从URL参数获取数据
  const fileIds = searchParams.get('files')?.split(',') || [];
  const kbName = searchParams.get('name') || '';
  const kbDescription = searchParams.get('description') || '';

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableModels, setAvailableModels] = useState<EmbeddingModel[]>([]);
  const [processing, setProcessing] = useState(false);
  const [taskId, setTaskId] = useState<string | null>(null);

  // 向量化配置
  const [config, setConfig] = useState<VectorConfig>({
    embedding_model: 'text-embedding-ada-002',
    vector_dimension: 1536,
    chunk_size: 1000,
    chunk_overlap: 200,
    batch_size: 10,
    max_retries: 3
  });

  // 加载可用模型
  const loadAvailableModels = async () => {
    try {
      const response = await apiClient.get('/api/v1/knowledge-bases/embedding-models');
      const models = response.data?.data || [
        {
          name: 'text-embedding-ada-002',
          display_name: 'OpenAI Ada-002',
          dimension: 1536,
          description: 'OpenAI的高性能嵌入模型',
          provider: 'OpenAI'
        },
        {
          name: 'text-embedding-3-small',
          display_name: 'OpenAI Embedding-3-Small',
          dimension: 1536,
          description: 'OpenAI最新的小型嵌入模型',
          provider: 'OpenAI'
        },
        {
          name: 'text-embedding-3-large',
          display_name: 'OpenAI Embedding-3-Large',
          dimension: 3072,
          description: 'OpenAI最新的大型嵌入模型',
          provider: 'OpenAI'
        },
        {
          name: 'bge-large-zh-v1.5',
          display_name: 'BGE Large Chinese',
          dimension: 1024,
          description: '专为中文优化的嵌入模型',
          provider: 'BAAI'
        }
      ];
      setAvailableModels(models);
    } catch (err) {
      console.error('Failed to load embedding models:', err);
      // 使用默认模型
      setAvailableModels([
        {
          name: 'text-embedding-ada-002',
          display_name: 'OpenAI Ada-002',
          dimension: 1536,
          description: 'OpenAI的高性能嵌入模型',
          provider: 'OpenAI'
        }
      ]);
    }
  };

  // 初始化
  useEffect(() => {
    if (fileIds.length === 0 || !kbName) {
      router.push('/knowledge-base/create');
      return;
    }
    loadAvailableModels();
  }, []);

  // 处理模型选择
  const handleModelChange = (modelName: string) => {
    const model = availableModels.find(m => m.name === modelName);
    if (model) {
      setConfig(prev => ({
        ...prev,
        embedding_model: modelName,
        vector_dimension: model.dimension
      }));
    }
  };

  // 开始向量化处理
  const handleStartVectorization = async () => {
    try {
      setProcessing(true);
      setError(null);

      const kbData = {
        name: kbName,
        description: kbDescription,
        file_ids: fileIds,
        vector_config: config
      };

      const response = await apiClient.post('/api/v1/knowledge-bases', kbData);
      const newTaskId = response.data?.data?.task_id || response.data?.task_id;

      if (!newTaskId) {
        throw new Error('未能获取任务ID');
      }

      setTaskId(newTaskId);
      
      // 跳转到处理进度页面
      router.push(`/knowledge-base/create/progress?task_id=${newTaskId}`);

    } catch (err) {
      console.error('Failed to start vectorization:', err);
      setError('启动向量化失败');
    } finally {
      setProcessing(false);
    }
  };

  // 返回上一步
  const handleBack = () => {
    const params = new URLSearchParams({
      files: fileIds.join(','),
      name: kbName,
      description: kbDescription
    });
    router.push(`/knowledge-base/create?${params.toString()}`);
  };

  // 步骤指示器
  const steps = [
    { id: 1, name: '选择文件', icon: FileText },
    { id: 2, name: '向量化配置', icon: Settings },
    { id: 3, name: '开始处理', icon: Zap }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 头部导航 */}
      <div className="bg-white/60 backdrop-blur-lg shadow-sm border-b border-white/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>

              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    创建AI知识库
                  </h1>
                  <p className="text-sm text-gray-500">步骤 2 / {steps.length}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 步骤指示器 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center mb-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
                2 >= step.id 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-400'
              }`}>
                {2 > step.id ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <step.icon className="w-5 h-5" />
                )}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                2 >= step.id ? 'text-blue-600' : 'text-gray-400'
              }`}>
                {step.name}
              </span>
              {index < steps.length - 1 && (
                <ChevronRight className="w-5 h-5 text-gray-300 mx-4" />
              )}
            </div>
          ))}
        </div>

        {/* 主要内容 */}
        <div className="bg-white/60 backdrop-blur-lg rounded-2xl shadow-xl border border-white/30 p-8">
          <h2 className="text-xl font-bold text-gray-900 mb-6">向量化配置</h2>

          <div className="space-y-8">
            {/* 知识库信息预览 */}
            <div className="bg-blue-50/50 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">知识库信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-500">名称：</span>
                  <span className="text-sm font-medium text-gray-900">{kbName}</span>
                </div>
                <div>
                  <span className="text-sm text-gray-500">文件数量：</span>
                  <span className="text-sm font-medium text-gray-900">{fileIds.length} 个</span>
                </div>
                {kbDescription && (
                  <div className="md:col-span-2">
                    <span className="text-sm text-gray-500">描述：</span>
                    <span className="text-sm font-medium text-gray-900">{kbDescription}</span>
                  </div>
                )}
              </div>
            </div>

            {/* 嵌入模型选择 */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">嵌入模型</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableModels.map((model) => (
                  <div
                    key={model.name}
                    className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      config.embedding_model === model.name
                        ? 'border-blue-500 bg-blue-50/50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleModelChange(model.name)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{model.display_name}</h4>
                      <div className={`w-4 h-4 border-2 rounded-full transition-all duration-200 ${
                        config.embedding_model === model.name
                          ? 'bg-blue-600 border-blue-600'
                          : 'border-gray-300'
                      }`}>
                        {config.embedding_model === model.name && (
                          <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{model.description}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>维度: {model.dimension}</span>
                      <span>{model.provider}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 向量化参数 */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">向量化参数</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    分块大小
                  </label>
                  <input
                    type="number"
                    value={config.chunk_size}
                    onChange={(e) => setConfig({...config, chunk_size: parseInt(e.target.value) || 1000})}
                    min="100"
                    max="4000"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500 mt-1">建议值：500-2000</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    分块重叠
                  </label>
                  <input
                    type="number"
                    value={config.chunk_overlap}
                    onChange={(e) => setConfig({...config, chunk_overlap: parseInt(e.target.value) || 200})}
                    min="0"
                    max="1000"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500 mt-1">建议值：100-500</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    批处理大小
                  </label>
                  <input
                    type="number"
                    value={config.batch_size}
                    onChange={(e) => setConfig({...config, batch_size: parseInt(e.target.value) || 10})}
                    min="1"
                    max="100"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500 mt-1">建议值：5-20</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大重试次数
                  </label>
                  <input
                    type="number"
                    value={config.max_retries}
                    onChange={(e) => setConfig({...config, max_retries: parseInt(e.target.value) || 3})}
                    min="0"
                    max="10"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500 mt-1">建议值：1-5</p>
                </div>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={handleBack}
              disabled={processing}
              className="inline-flex items-center px-6 py-3 rounded-xl border border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              上一步
            </button>

            <button
              onClick={handleStartVectorization}
              disabled={processing}
              className={`inline-flex items-center px-6 py-3 rounded-xl transition-all duration-200 ${
                processing
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl'
              }`}
            >
              {processing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  创建中...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  开始创建知识库
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VectorizeConfigPage;
