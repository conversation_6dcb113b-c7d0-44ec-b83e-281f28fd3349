"""
MinIO存储实现
"""

import io
import mimetypes
from datetime import datetime
from typing import List, Optional, Dict, Any, AsyncGenerator
from urllib.parse import urlparse

from .base import StorageInterface, FileInfo, StorageException

try:
    from minio import Minio
    from minio.error import S3Error
    MINIO_AVAILABLE = True
except ImportError:
    MINIO_AVAILABLE = False


class MinIOStorage(StorageInterface):
    """MinIO对象存储实现"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化MinIO存储
        
        Args:
            config: 配置参数
                - endpoint: MinIO服务端点
                - access_key: 访问密钥
                - secret_key: 秘密密钥
                - bucket_name: 存储桶名称
                - secure: 是否使用HTTPS
                - region: 区域（可选）
        """
        if not MINIO_AVAILABLE:
            raise StorageException("MinIO client not available. Install with: pip install minio")
        
        super().__init__(config)
        self.endpoint = config.get('endpoint')
        self.access_key = config.get('access_key')
        self.secret_key = config.get('secret_key')
        self.bucket_name = config.get('bucket_name')
        self.secure = config.get('secure', True)
        self.region = config.get('region', 'us-east-1')
        
        if not all([self.endpoint, self.access_key, self.secret_key, self.bucket_name]):
            raise StorageException("Missing required MinIO configuration")
        
        self.client = None
        self._connected = False
    
    async def connect(self) -> bool:
        """连接到MinIO服务"""
        try:
            self.client = Minio(
                endpoint=self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure,
                region=self.region
            )
            
            # 检查存储桶是否存在，不存在则创建
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name, location=self.region)
            
            self._connected = True
            return True
        except Exception as e:
            raise StorageException(f"Failed to connect to MinIO: {e}")
    
    async def disconnect(self) -> None:
        """断开连接"""
        self.client = None
        self._connected = False
    
    def _normalize_object_name(self, path: str) -> str:
        """标准化对象名称"""
        path = self.normalize_path(path)
        # MinIO对象名不应以斜杠开头
        if path.startswith('/'):
            path = path[1:]
        return path
    
    async def list_files(self, path: str = "/", recursive: bool = False) -> List[FileInfo]:
        """列出对象"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        prefix = self._normalize_object_name(path)
        if prefix and not prefix.endswith('/'):
            prefix += '/'
        
        files = []
        
        try:
            objects = self.client.list_objects(
                bucket_name=self.bucket_name,
                prefix=prefix,
                recursive=recursive
            )
            
            for obj in objects:
                # 跳过目录本身
                if obj.object_name == prefix:
                    continue
                
                file_info = FileInfo(
                    path="/" + obj.object_name,
                    name=obj.object_name.split('/')[-1],
                    size=obj.size,
                    is_directory=obj.object_name.endswith('/'),
                    created_at=obj.last_modified,
                    modified_at=obj.last_modified,
                    mime_type=mimetypes.guess_type(obj.object_name)[0],
                    extension=obj.object_name.split('.')[-1].lower() if '.' in obj.object_name else "",
                    hash_value=obj.etag.strip('"') if obj.etag else None
                )
                files.append(file_info)
            
            # 如果不是递归模式，需要添加目录信息
            if not recursive:
                # 获取直接子目录
                seen_dirs = set()
                for obj in self.client.list_objects(
                    bucket_name=self.bucket_name,
                    prefix=prefix,
                    recursive=True
                ):
                    rel_path = obj.object_name[len(prefix):]
                    if '/' in rel_path:
                        dir_name = rel_path.split('/')[0]
                        if dir_name not in seen_dirs:
                            seen_dirs.add(dir_name)
                            dir_info = FileInfo(
                                path="/" + prefix + dir_name,
                                name=dir_name,
                                size=0,
                                is_directory=True,
                                created_at=datetime.now(),
                                modified_at=datetime.now()
                            )
                            files.append(dir_info)
        
        except S3Error as e:
            raise StorageException(f"Failed to list objects: {e}")
        
        return files
    
    async def get_file_info(self, path: str) -> Optional[FileInfo]:
        """获取对象信息"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(path)
        
        try:
            stat = self.client.stat_object(self.bucket_name, object_name)
            
            return FileInfo(
                path=path,
                name=object_name.split('/')[-1],
                size=stat.size,
                is_directory=False,
                created_at=stat.last_modified,
                modified_at=stat.last_modified,
                mime_type=stat.content_type,
                extension=object_name.split('.')[-1].lower() if '.' in object_name else "",
                hash_value=stat.etag.strip('"') if stat.etag else None,
                file_metadata=stat.metadata
            )
        except S3Error:
            return None
    
    async def file_exists(self, path: str) -> bool:
        """检查对象是否存在"""
        return await self.get_file_info(path) is not None
    
    async def create_directory(self, path: str) -> bool:
        """创建目录（在MinIO中创建空对象）"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(path)
        if not object_name.endswith('/'):
            object_name += '/'
        
        try:
            # 创建一个空对象作为目录标记
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=io.BytesIO(b''),
                length=0
            )
            return True
        except S3Error as e:
            raise StorageException(f"Failed to create directory: {e}")
    
    async def delete_file(self, path: str) -> bool:
        """删除对象"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(path)
        
        try:
            # 如果是目录，删除所有子对象
            if object_name.endswith('/') or await self._is_directory(object_name):
                if not object_name.endswith('/'):
                    object_name += '/'
                
                # 列出所有子对象
                objects = self.client.list_objects(
                    bucket_name=self.bucket_name,
                    prefix=object_name,
                    recursive=True
                )
                
                # 删除所有子对象
                for obj in objects:
                    self.client.remove_object(self.bucket_name, obj.object_name)
            else:
                self.client.remove_object(self.bucket_name, object_name)
            
            return True
        except S3Error as e:
            raise StorageException(f"Failed to delete object: {e}")
    
    async def _is_directory(self, object_name: str) -> bool:
        """检查是否为目录"""
        try:
            # 检查是否有以此路径为前缀的对象
            objects = list(self.client.list_objects(
                bucket_name=self.bucket_name,
                prefix=object_name + '/',
                recursive=False
            ))
            return len(objects) > 0
        except S3Error:
            return False
    
    async def move_file(self, source_path: str, target_path: str) -> bool:
        """移动对象（复制后删除）"""
        if await self.copy_file(source_path, target_path):
            return await self.delete_file(source_path)
        return False
    
    async def copy_file(self, source_path: str, target_path: str) -> bool:
        """复制对象"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        source_object = self._normalize_object_name(source_path)
        target_object = self._normalize_object_name(target_path)
        
        try:
            from minio.commonconfig import CopySource
            
            copy_source = CopySource(self.bucket_name, source_object)
            self.client.copy_object(
                bucket_name=self.bucket_name,
                object_name=target_object,
                source=copy_source
            )
            return True
        except S3Error as e:
            raise StorageException(f"Failed to copy object: {e}")
    
    async def upload_file(self, local_path: str, remote_path: str) -> bool:
        """上传文件"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(remote_path)
        
        try:
            self.client.fput_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                file_path=local_path
            )
            return True
        except S3Error as e:
            raise StorageException(f"Failed to upload file: {e}")
    
    async def download_file(self, remote_path: str, local_path: str) -> bool:
        """下载文件"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(remote_path)
        
        try:
            self.client.fget_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                file_path=local_path
            )
            return True
        except S3Error as e:
            raise StorageException(f"Failed to download file: {e}")
    
    async def read_file(self, path: str) -> bytes:
        """读取文件内容"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(path)
        
        try:
            response = self.client.get_object(self.bucket_name, object_name)
            data = response.read()
            response.close()
            response.release_conn()
            return data
        except S3Error as e:
            raise StorageException(f"Failed to read file: {e}")
    
    async def write_file(self, path: str, content: bytes) -> bool:
        """写入文件内容"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(path)
        
        try:
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=io.BytesIO(content),
                length=len(content)
            )
            return True
        except S3Error as e:
            raise StorageException(f"Failed to write file: {e}")
    
    async def get_file_stream(self, path: str) -> AsyncGenerator[bytes, None]:
        """获取文件流"""
        if not self.client:
            raise StorageException("Not connected to MinIO")
        
        object_name = self._normalize_object_name(path)
        
        try:
            response = self.client.get_object(self.bucket_name, object_name)
            try:
                while True:
                    chunk = response.read(8192)
                    if not chunk:
                        break
                    yield chunk
            finally:
                response.close()
                response.release_conn()
        except S3Error as e:
            raise StorageException(f"Failed to stream file: {e}")
    
    async def is_connected(self) -> bool:
        """检查连接状态"""
        if not self._connected or not self.client:
            return False
        
        try:
            self.client.bucket_exists(self.bucket_name)
            return True
        except Exception:
            return False
