# 文件管理系统使用指南

## 🎯 系统概述

AI知识库文件管理系统提供了类似Windows资源管理器的用户体验，支持多种存储后端，具有完整的文件增删改查功能。

### 主要特性

- 🗂️ **多存储支持**: 本地目录、MinIO、FTP、SFTP
- 📊 **数据库缓存**: 文件清单缓存在数据库中，提高查询性能
- 🔄 **自动同步**: 确保数据库与实际存储的一致性
- 🎨 **现代化UI**: 类似Windows资源管理器的用户体验
- 📱 **响应式设计**: 支持桌面和移动设备
- 🔍 **强大搜索**: 支持文件名和路径搜索
- 📋 **多种视图**: 列表视图和网格视图
- 🏷️ **智能分类**: 自动识别文件类型并分类显示

## 🚀 快速开始

### 1. 启动系统

```bash
# 启动后端API
cd api
uv run ai-knowledge-api run

# 启动前端
cd web
npm run dev
```

### 2. 访问文件管理器

打开浏览器访问：`http://localhost:3000/file-manager`

### 3. 配置存储

首次使用需要配置存储：

1. 点击页面顶部的存储选择器
2. 选择"添加存储"
3. 配置存储参数
4. 测试连接并保存

## 📁 存储配置

### 本地存储配置

```json
{
  "base_path": "./storage",
  "max_file_size": 104857600
}
```

### MinIO配置

```json
{
  "endpoint": "localhost:9000",
  "access_key": "your_access_key",
  "secret_key": "your_secret_key",
  "bucket_name": "ai-knowledge-base",
  "secure": false
}
```

### FTP配置

```json
{
  "host": "ftp.example.com",
  "port": 21,
  "username": "your_username",
  "password": "your_password",
  "base_path": "/",
  "passive": true,
  "encoding": "utf-8"
}
```

### SFTP配置

```json
{
  "host": "sftp.example.com",
  "port": 22,
  "username": "your_username",
  "password": "your_password",
  "base_path": "/"
}
```

## 🎮 用户界面

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ 标题栏 [存储选择器] [刷新] [设置]                        │
├─────────────┬───────────────────────────────────────────┤
│ 侧边栏      │ 工具栏 [导航] [搜索] [视图] [排序]        │
│ - 快速访问  ├───────────────────────────────────────────┤
│ - 根目录    │                                           │
│ - 文档      │                                           │
│ - 图片      │          文件浏览区域                     │
│ - 视频      │                                           │
│             │                                           │
│ 存储信息    │                                           │
│ - 总空间    │                                           │
│ - 已使用    │                                           │
│ - 可用      ├───────────────────────────────────────────┤
│             │ 状态栏 [选择信息] [路径] [存储状态]       │
└─────────────┴───────────────────────────────────────────┘
```

### 工具栏功能

- **导航控件**: 后退、前进、上级目录
- **路径导航**: 面包屑导航，可点击跳转
- **搜索框**: 实时搜索文件和文件夹
- **视图切换**: 列表视图 ⇄ 网格视图
- **排序选项**: 按名称、大小、修改时间排序

### 文件操作

#### 基本操作
- **单击**: 选择文件
- **双击**: 打开文件夹或预览文件
- **Ctrl+单击**: 多选
- **Shift+单击**: 范围选择
- **右键**: 显示上下文菜单

#### 上下文菜单
- 📄 **预览**: 查看文件内容（图片、文档等）
- 📥 **下载**: 下载文件到本地
- ✏️ **重命名**: 重命名文件或文件夹
- 📋 **复制**: 复制文件或文件夹
- 📁 **移动**: 移动文件或文件夹
- 🗑️ **删除**: 删除文件或文件夹

## 🔧 高级功能

### 批量操作

选择多个文件后，工具栏会显示批量操作选项：

- **批量下载**: 打包下载选中的文件
- **批量复制**: 复制多个文件到指定位置
- **批量移动**: 移动多个文件到指定位置
- **批量删除**: 删除多个文件

### 文件搜索

支持多种搜索方式：

- **文件名搜索**: 输入文件名关键词
- **扩展名过滤**: 按文件类型过滤
- **大小过滤**: 按文件大小范围过滤
- **时间过滤**: 按修改时间过滤

### 排序和过滤

- **排序字段**: 名称、大小、修改时间、创建时间
- **排序方向**: 升序、降序
- **文件类型过滤**: 仅显示文件或文件夹
- **实时过滤**: 输入关键词实时过滤结果

## 🔄 同步机制

### 自动同步

系统会自动同步数据库与存储的文件清单：

- **增量同步**: 定期检查文件变化
- **完全同步**: 重新扫描所有文件
- **实时同步**: 文件操作后立即同步

### 手动同步

可以手动触发同步：

1. 点击工具栏的"刷新"按钮
2. 或在存储管理中点击"同步"

### 同步状态

状态栏显示同步状态：

- 🟢 **已同步**: 数据库与存储一致
- 🟡 **同步中**: 正在进行同步操作
- 🔴 **同步失败**: 同步过程中出现错误

## 📊 性能优化

### 分页加载

- 大目录自动分页显示
- 可配置每页显示数量
- 支持无限滚动加载

### 缓存机制

- 文件列表缓存
- 缩略图缓存
- 搜索结果缓存

### 预加载

- 预加载下一页数据
- 预生成缩略图
- 预加载常用目录

## 🛠️ 故障排除

### 常见问题

1. **无法连接存储**
   - 检查网络连接
   - 验证存储配置参数
   - 确认存储服务正常运行

2. **文件上传失败**
   - 检查文件大小限制
   - 确认存储空间充足
   - 验证文件权限

3. **同步失败**
   - 检查存储连接状态
   - 查看错误日志
   - 尝试手动同步

4. **搜索结果不准确**
   - 等待同步完成
   - 清除搜索缓存
   - 重新索引文件

### 错误代码

- **404**: 文件或目录不存在
- **403**: 权限不足
- **413**: 文件过大
- **507**: 存储空间不足
- **500**: 服务器内部错误

## 🔒 安全考虑

### 访问控制

- 用户身份验证
- 基于角色的权限控制
- 文件访问日志记录

### 数据安全

- 文件传输加密
- 存储数据加密
- 定期备份

### 审计日志

- 文件操作记录
- 用户访问日志
- 系统事件日志

## 📈 监控和维护

### 系统监控

- 存储使用率监控
- 同步状态监控
- 性能指标监控

### 定期维护

- 清理临时文件
- 优化数据库索引
- 更新缓存数据

### 备份策略

- 定期备份文件数据
- 备份数据库
- 测试恢复流程

## 🆘 技术支持

如果遇到问题，请：

1. 查看系统日志
2. 检查网络连接
3. 验证配置参数
4. 联系技术支持

---

**注意**: 本系统仍在持续开发中，功能和界面可能会有所变化。请关注更新日志获取最新信息。
