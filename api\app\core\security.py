"""
安全相关功能
包括密码哈希、JWT令牌生成和验证
"""

from datetime import datetime, timedelta
from typing import Any, Union, Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
import warnings
import logging

# 忽略bcrypt版本警告
warnings.filterwarnings("ignore", message=".*bcrypt.*")
warnings.filterwarnings("ignore", category=UserWarning, module="passlib")

# 抑制passlib的调试日志
logging.getLogger("passlib").setLevel(logging.ERROR)
from loguru import logger

from app.core.config import get_settings

settings = get_settings()

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置
ALGORITHM = "HS256"


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建访问令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        JWT访问令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "type": "access"})
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error("Error creating access token: {}".format(str(e)))
        raise


def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建刷新令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        JWT刷新令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error("Error creating refresh token: {}".format(str(e)))
        raise


def decode_access_token(token: str) -> dict:
    """
    解码访问令牌
    
    Args:
        token: JWT访问令牌
        
    Returns:
        解码后的数据
        
    Raises:
        JWTError: 令牌无效或过期
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        
        # 验证令牌类型
        if payload.get("type") != "access":
            raise JWTError("Invalid token type")
        
        return payload
    except JWTError as e:
        logger.warning("Invalid access token: {}".format(str(e)))
        raise
    except Exception as e:
        logger.error("Error decoding access token: {}".format(str(e)))
        raise JWTError("Token decode error")


def decode_refresh_token(token: str) -> dict:
    """
    解码刷新令牌
    
    Args:
        token: JWT刷新令牌
        
    Returns:
        解码后的数据
        
    Raises:
        JWTError: 令牌无效或过期
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        
        # 验证令牌类型
        if payload.get("type") != "refresh":
            raise JWTError("Invalid token type")
        
        return payload
    except JWTError as e:
        logger.warning("Invalid refresh token: {}".format(str(e)))
        raise
    except Exception as e:
        logger.error("Error decoding refresh token: {}".format(str(e)))
        raise JWTError("Token decode error")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    
    Args:
        plain_password: 明文密码
        hashed_password: 哈希密码
        
    Returns:
        密码是否匹配
    """
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error("Error verifying password: {}".format(str(e)))
        return False


def get_password_hash(password: str) -> str:
    """
    生成密码哈希

    Args:
        password: 明文密码

    Returns:
        哈希密码
    """
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error("Error hashing password: {}".format(str(e)))
        raise


def generate_password_reset_token(email: str) -> str:
    """
    生成密码重置令牌
    
    Args:
        email: 用户邮箱
        
    Returns:
        密码重置令牌
    """
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "password_reset"},
        settings.SECRET_KEY,
        algorithm=ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    验证密码重置令牌
    
    Args:
        token: 密码重置令牌
        
    Returns:
        用户邮箱，如果令牌无效则返回None
    """
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        
        # 验证令牌类型
        if decoded_token.get("type") != "password_reset":
            return None
        
        return decoded_token["sub"]
    except JWTError:
        return None


def generate_email_verification_token(email: str) -> str:
    """
    生成邮箱验证令牌
    
    Args:
        email: 用户邮箱
        
    Returns:
        邮箱验证令牌
    """
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "email_verification"},
        settings.SECRET_KEY,
        algorithm=ALGORITHM,
    )
    return encoded_jwt


def verify_email_verification_token(token: str) -> Optional[str]:
    """
    验证邮箱验证令牌
    
    Args:
        token: 邮箱验证令牌
        
    Returns:
        用户邮箱，如果令牌无效则返回None
    """
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        
        # 验证令牌类型
        if decoded_token.get("type") != "email_verification":
            return None
        
        return decoded_token["sub"]
    except JWTError:
        return None


def create_api_key(user_id: int, name: str, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建API密钥
    
    Args:
        user_id: 用户ID
        name: API密钥名称
        expires_delta: 过期时间增量
        
    Returns:
        API密钥
    """
    to_encode = {
        "sub": str(user_id),
        "name": name,
        "type": "api_key"
    }
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        # API密钥默认1年过期
        expire = datetime.utcnow() + timedelta(days=365)
    
    to_encode.update({"exp": expire})
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error("Error creating API key: {}".format(str(e)))
        raise


def verify_api_key(api_key: str) -> Optional[dict]:
    """
    验证API密钥
    
    Args:
        api_key: API密钥
        
    Returns:
        解码后的数据，如果无效则返回None
    """
    try:
        payload = jwt.decode(api_key, settings.SECRET_KEY, algorithms=[ALGORITHM])
        
        # 验证令牌类型
        if payload.get("type") != "api_key":
            return None
        
        return payload
    except JWTError:
        return None


def check_password_strength(password: str) -> dict:
    """
    检查密码强度
    
    Args:
        password: 密码
        
    Returns:
        密码强度检查结果
    """
    result = {
        "is_strong": True,
        "score": 0,
        "issues": []
    }
    
    # 长度检查
    if len(password) < 8:
        result["issues"].append("密码长度至少8位")
        result["is_strong"] = False
    else:
        result["score"] += 1
    
    # 包含大写字母
    if not any(c.isupper() for c in password):
        result["issues"].append("密码应包含大写字母")
        result["is_strong"] = False
    else:
        result["score"] += 1
    
    # 包含小写字母
    if not any(c.islower() for c in password):
        result["issues"].append("密码应包含小写字母")
        result["is_strong"] = False
    else:
        result["score"] += 1
    
    # 包含数字
    if not any(c.isdigit() for c in password):
        result["issues"].append("密码应包含数字")
        result["is_strong"] = False
    else:
        result["score"] += 1
    
    # 包含特殊字符
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        result["issues"].append("密码应包含特殊字符")
        result["is_strong"] = False
    else:
        result["score"] += 1
    
    return result
