"""
文件解析服务 - 支持多种文件格式解析为HTML/Markdown
"""
import os
import io
import tempfile
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import mimetypes

from loguru import logger

# 文档解析库 - 使用更宽松的导入策略
DOCX_AVAILABLE = False
OPENPYXL_AVAILABLE = False
XLRD_AVAILABLE = False
PPTX_AVAILABLE = False
PDF_AVAILABLE = False
MARKDOWN_AVAILABLE = False

try:
    import docx
    from docx import Document
    DOCX_AVAILABLE = True
    logger.info("python-docx available")
except ImportError as e:
    logger.warning(f"python-docx not available: {e}")

try:
    import openpyxl
    from openpyxl import load_workbook
    OPENPYXL_AVAILABLE = True
    logger.info("openpyxl available")
except ImportError as e:
    logger.warning(f"openpyxl not available: {e}")

try:
    import xlrd
    XLRD_AVAILABLE = True
    logger.info("xlrd available")
except ImportError as e:
    logger.warning(f"xlrd not available: {e}")

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
    logger.info("python-pptx available")
except ImportError as e:
    logger.warning(f"python-pptx not available: {e}")

try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
    logger.info("PyPDF2/pdfplumber available")
except ImportError as e:
    logger.warning(f"PyPDF2/pdfplumber not available: {e}")

try:
    import markdown
    MARKDOWN_AVAILABLE = True
    logger.info("markdown available")
except ImportError as e:
    logger.warning(f"markdown not available: {e}")


class FileParserService:
    """文件解析服务"""
    
    def __init__(self):
        # 基础支持的格式（不需要额外依赖）
        self.supported_formats = {
            '.txt': self._parse_text,
            '.md': self._parse_markdown,
            '.markdown': self._parse_markdown,
            '.html': self._parse_html,
            '.htm': self._parse_html,
            '.json': self._parse_json,
            '.xml': self._parse_xml,
            '.csv': self._parse_csv
        }

        # 根据可用的库动态添加支持
        if DOCX_AVAILABLE:
            self.supported_formats['.docx'] = self._parse_docx
        if OPENPYXL_AVAILABLE:
            self.supported_formats['.xlsx'] = self._parse_xlsx
        if XLRD_AVAILABLE:
            self.supported_formats['.xls'] = self._parse_xls
        if PPTX_AVAILABLE:
            self.supported_formats['.pptx'] = self._parse_pptx
        if PDF_AVAILABLE:
            self.supported_formats['.pdf'] = self._parse_pdf

        # 对于不支持的格式，提供占位符
        self.supported_formats['.doc'] = self._parse_doc
        self.supported_formats['.ppt'] = self._parse_ppt
    
    def is_supported(self, file_extension: str) -> bool:
        """检查文件格式是否支持解析"""
        return file_extension.lower() in self.supported_formats
    
    def parse_file(self, file_path: str, file_content: bytes = None) -> Dict[str, Any]:
        """
        解析文件内容
        
        Args:
            file_path: 文件路径
            file_content: 文件二进制内容（可选）
            
        Returns:
            解析结果字典，包含content、format、metadata等
        """
        try:
            file_extension = Path(file_path).suffix.lower()
            
            if not self.is_supported(file_extension):
                return {
                    'success': False,
                    'error': f'不支持的文件格式: {file_extension}',
                    'content': '',
                    'format': 'text',
                    'metadata': {}
                }
            
            # 如果提供了文件内容，使用内容解析
            if file_content:
                return self._parse_from_content(file_extension, file_content, file_path)
            
            # 否则从文件路径读取
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': '文件不存在',
                    'content': '',
                    'format': 'text',
                    'metadata': {}
                }
            
            return self._parse_from_file(file_extension, file_path)
            
        except Exception as e:
            logger.error(f"文件解析失败 {file_path}: {e}")
            return {
                'success': False,
                'error': f'解析失败: {str(e)}',
                'content': '',
                'format': 'text',
                'metadata': {}
            }
    
    def _parse_from_content(self, file_extension: str, content: bytes, file_path: str) -> Dict[str, Any]:
        """从文件内容解析"""
        parser_func = self.supported_formats.get(file_extension)
        if not parser_func:
            raise ValueError(f"不支持的文件格式: {file_extension}")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=file_extension, delete=False) as temp_file:
            temp_file.write(content)
            temp_file.flush()
            
            try:
                result = parser_func(temp_file.name)
                result['metadata']['original_path'] = file_path
                return result
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file.name)
                except:
                    pass
    
    def _parse_from_file(self, file_extension: str, file_path: str) -> Dict[str, Any]:
        """从文件路径解析"""
        parser_func = self.supported_formats.get(file_extension)
        if not parser_func:
            raise ValueError(f"不支持的文件格式: {file_extension}")
        
        result = parser_func(file_path)
        result['metadata']['file_path'] = file_path
        return result
    
    def _parse_text(self, file_path: str) -> Dict[str, Any]:
        """解析文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ['gbk', 'gb2312', 'latin1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValueError("无法解码文件内容")
        
        # 转换为HTML格式
        html_content = f"<pre>{content}</pre>"
        
        return {
            'success': True,
            'content': html_content,
            'raw_content': content,
            'format': 'html',
            'metadata': {
                'type': 'text',
                'encoding': 'utf-8',
                'lines': len(content.splitlines())
            }
        }
    
    def _parse_markdown(self, file_path: str) -> Dict[str, Any]:
        """解析Markdown文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if MARKDOWN_AVAILABLE:
            # 转换为HTML
            md = markdown.Markdown(extensions=['tables', 'fenced_code', 'toc'])
            html_content = md.convert(content)
        else:
            # 简单的HTML包装
            html_content = f"<pre>{content}</pre>"
        
        return {
            'success': True,
            'content': html_content,
            'raw_content': content,
            'format': 'html',
            'metadata': {
                'type': 'markdown',
                'lines': len(content.splitlines())
            }
        }
    
    def _parse_docx(self, file_path: str) -> Dict[str, Any]:
        """解析DOCX文件"""
        try:
            # 首先尝试读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()

            # 检查是否为有效的DOCX文件（ZIP格式）
            if not content.startswith(b'PK'):
                # 如果不是ZIP格式，可能是纯文本内容
                try:
                    text_content = content.decode('utf-8')
                    html_content = f'<div class="docx-content"><p>{text_content}</p></div>'
                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'docx',
                            'note': '文件内容为纯文本格式'
                        }
                    }
                except UnicodeDecodeError:
                    return {
                        'success': False,
                        'error': '文件格式不正确，无法解析',
                        'content': '',
                        'format': 'text',
                        'metadata': {'type': 'docx'}
                    }

            # 如果有python-docx库，尝试解析
            if DOCX_AVAILABLE:
                try:
                    doc = Document(file_path)

                    # 提取文本内容并转换为HTML
                    html_parts = []
                    html_parts.append('<div class="docx-content">')

                    for paragraph in doc.paragraphs:
                        if paragraph.text.strip():
                            # 检查段落样式
                            style_name = paragraph.style.name if paragraph.style else 'Normal'

                            if 'Heading' in style_name:
                                level = style_name.replace('Heading ', '') if 'Heading ' in style_name else '1'
                                try:
                                    level = int(level)
                                    level = min(max(level, 1), 6)  # 限制在1-6之间
                                except:
                                    level = 1
                                html_parts.append(f'<h{level}>{paragraph.text}</h{level}>')
                            else:
                                html_parts.append(f'<p>{paragraph.text}</p>')

                    # 处理表格
                    for table in doc.tables:
                        html_parts.append('<table class="table table-bordered">')
                        for row in table.rows:
                            html_parts.append('<tr>')
                            for cell in row.cells:
                                html_parts.append(f'<td>{cell.text}</td>')
                            html_parts.append('</tr>')
                        html_parts.append('</table>')

                    html_parts.append('</div>')
                    html_content = '\n'.join(html_parts)

                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'docx',
                            'paragraphs': len(doc.paragraphs),
                            'tables': len(doc.tables)
                        }
                    }
                except Exception as docx_error:
                    logger.warning(f"python-docx解析失败: {docx_error}")
                    # 降级到文本解析
                    pass

            # 降级处理：尝试解析为文本
            try:
                text_content = content.decode('utf-8')
                html_content = f'<div class="docx-content"><pre>{text_content}</pre></div>'
                return {
                    'success': True,
                    'content': html_content,
                    'format': 'html',
                    'metadata': {
                        'type': 'docx',
                        'note': '使用文本模式解析'
                    }
                }
            except UnicodeDecodeError:
                return {
                    'success': False,
                    'error': '无法解析DOCX文件内容',
                    'content': '',
                    'format': 'text',
                    'metadata': {'type': 'docx'}
                }

        except Exception as e:
            logger.error(f"DOCX解析失败: {e}")
            return {
                'success': False,
                'error': f'DOCX解析失败: {str(e)}',
                'content': '',
                'format': 'text',
                'metadata': {'type': 'docx'}
            }
    
    def _parse_doc(self, file_path: str) -> Dict[str, Any]:
        """解析DOC文件（旧格式）"""
        # DOC格式较复杂，这里提供基础实现
        # 实际项目中可能需要使用 python-docx2txt 或其他库
        return {
            'success': False,
            'error': 'DOC格式暂不支持，请转换为DOCX格式',
            'content': '',
            'format': 'text',
            'metadata': {'type': 'doc'}
        }
    
    def _parse_xlsx(self, file_path: str) -> Dict[str, Any]:
        """解析XLSX文件"""
        try:
            # 首先检查文件内容
            with open(file_path, 'rb') as f:
                content = f.read()

            # 检查是否为有效的XLSX文件（ZIP格式）
            if not content.startswith(b'PK'):
                # 如果不是ZIP格式，可能是JSON格式的表格数据
                try:
                    import json
                    text_content = content.decode('utf-8')
                    sheets_data = json.loads(text_content)

                    html_parts = []
                    html_parts.append('<div class="xlsx-content">')

                    for sheet in sheets_data:
                        sheet_name = sheet.get('name', 'Sheet1')
                        sheet_data = sheet.get('data', [])

                        html_parts.append(f'<h3>工作表: {sheet_name}</h3>')
                        html_parts.append('<table class="table table-bordered">')

                        for row in sheet_data:
                            html_parts.append('<tr>')
                            for cell in row:
                                cell_value = cell.get('value', '') if isinstance(cell, dict) else str(cell)
                                html_parts.append(f'<td>{cell_value}</td>')
                            html_parts.append('</tr>')

                        html_parts.append('</table>')

                    html_parts.append('</div>')
                    html_content = '\n'.join(html_parts)

                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'xlsx',
                            'sheets': len(sheets_data),
                            'note': '从JSON数据解析'
                        }
                    }
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # 尝试作为文本处理
                    try:
                        text_content = content.decode('utf-8')
                        html_content = f'<div class="xlsx-content"><pre>{text_content}</pre></div>'
                        return {
                            'success': True,
                            'content': html_content,
                            'format': 'html',
                            'metadata': {
                                'type': 'xlsx',
                                'note': '文件内容为纯文本格式'
                            }
                        }
                    except UnicodeDecodeError:
                        return {
                            'success': False,
                            'error': '文件格式不正确，无法解析',
                            'content': '',
                            'format': 'text',
                            'metadata': {'type': 'xlsx'}
                        }

            # 如果有openpyxl库，尝试解析
            if OPENPYXL_AVAILABLE:
                try:
                    workbook = load_workbook(file_path, read_only=True)

                    html_parts = []
                    html_parts.append('<div class="xlsx-content">')

                    for sheet_name in workbook.sheetnames:
                        sheet = workbook[sheet_name]
                        html_parts.append(f'<h3>工作表: {sheet_name}</h3>')
                        html_parts.append('<table class="table table-bordered">')

                        for row in sheet.iter_rows(values_only=True):
                            if any(cell is not None for cell in row):  # 跳过空行
                                html_parts.append('<tr>')
                                for cell in row:
                                    cell_value = str(cell) if cell is not None else ''
                                    html_parts.append(f'<td>{cell_value}</td>')
                                html_parts.append('</tr>')

                        html_parts.append('</table>')

                    html_parts.append('</div>')
                    html_content = '\n'.join(html_parts)

                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'xlsx',
                            'sheets': len(workbook.sheetnames),
                            'sheet_names': workbook.sheetnames
                        }
                    }
                except Exception as xlsx_error:
                    logger.warning(f"openpyxl解析失败: {xlsx_error}")
                    pass

            # 降级处理
            return {
                'success': False,
                'error': 'XLSX文件解析失败，请检查文件格式',
                'content': '',
                'format': 'text',
                'metadata': {'type': 'xlsx'}
            }

        except Exception as e:
            logger.error(f"XLSX解析失败: {e}")
            return {
                'success': False,
                'error': f'XLSX解析失败: {str(e)}',
                'content': '',
                'format': 'text',
                'metadata': {'type': 'xlsx'}
            }
    
    def _parse_xls(self, file_path: str) -> Dict[str, Any]:
        """解析XLS文件"""
        if not XLRD_AVAILABLE:
            raise ImportError("xlrd库未安装")
        
        # XLS格式解析实现
        return {
            'success': False,
            'error': 'XLS格式暂不支持，请转换为XLSX格式',
            'content': '',
            'format': 'text',
            'metadata': {'type': 'xls'}
        }
    
    def _parse_pptx(self, file_path: str) -> Dict[str, Any]:
        """解析PPTX文件"""
        try:
            # 首先检查文件内容
            with open(file_path, 'rb') as f:
                content = f.read()

            # 检查是否为有效的PPTX文件（ZIP格式）
            if not content.startswith(b'PK'):
                # 如果不是ZIP格式，可能是JSON格式的幻灯片数据
                try:
                    import json
                    text_content = content.decode('utf-8')
                    slides_data = json.loads(text_content)

                    html_parts = []
                    html_parts.append('<div class="pptx-content">')

                    for i, slide in enumerate(slides_data, 1):
                        html_parts.append(f'<div class="slide" data-slide="{i}">')
                        html_parts.append(f'<h2>{slide.get("title", f"幻灯片 {i}")}</h2>')
                        html_parts.append(f'<div class="slide-content">{slide.get("content", "")}</div>')
                        html_parts.append('</div>')

                    html_parts.append('</div>')
                    html_content = '\n'.join(html_parts)

                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'pptx',
                            'slides': len(slides_data),
                            'note': '从JSON数据解析'
                        }
                    }
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # 尝试作为文本处理
                    try:
                        text_content = content.decode('utf-8')
                        html_content = f'<div class="pptx-content"><pre>{text_content}</pre></div>'
                        return {
                            'success': True,
                            'content': html_content,
                            'format': 'html',
                            'metadata': {
                                'type': 'pptx',
                                'note': '文件内容为纯文本格式'
                            }
                        }
                    except UnicodeDecodeError:
                        return {
                            'success': False,
                            'error': '文件格式不正确，无法解析',
                            'content': '',
                            'format': 'text',
                            'metadata': {'type': 'pptx'}
                        }

            # 如果有python-pptx库，尝试解析
            if PPTX_AVAILABLE:
                try:
                    prs = Presentation(file_path)

                    html_parts = []
                    html_parts.append('<div class="pptx-content">')

                    for i, slide in enumerate(prs.slides, 1):
                        html_parts.append(f'<div class="slide" data-slide="{i}">')
                        html_parts.append(f'<h2>幻灯片 {i}</h2>')

                        for shape in slide.shapes:
                            if hasattr(shape, "text") and shape.text.strip():
                                html_parts.append(f'<p>{shape.text}</p>')

                        html_parts.append('</div>')

                    html_parts.append('</div>')
                    html_content = '\n'.join(html_parts)

                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'pptx',
                            'slides': len(prs.slides)
                        }
                    }
                except Exception as pptx_error:
                    logger.warning(f"python-pptx解析失败: {pptx_error}")
                    pass

            # 降级处理
            return {
                'success': False,
                'error': 'PPTX文件解析失败，请检查文件格式',
                'content': '',
                'format': 'text',
                'metadata': {'type': 'pptx'}
            }

        except Exception as e:
            logger.error(f"PPTX解析失败: {e}")
            return {
                'success': False,
                'error': f'PPTX解析失败: {str(e)}',
                'content': '',
                'format': 'text',
                'metadata': {'type': 'pptx'}
            }
    
    def _parse_ppt(self, file_path: str) -> Dict[str, Any]:
        """解析PPT文件"""
        return {
            'success': False,
            'error': 'PPT格式暂不支持，请转换为PPTX格式',
            'content': '',
            'format': 'text',
            'metadata': {'type': 'ppt'}
        }
    
    def _parse_pdf(self, file_path: str) -> Dict[str, Any]:
        """解析PDF文件"""
        try:
            # 首先检查文件内容
            with open(file_path, 'rb') as f:
                content = f.read()

            # 检查是否为有效的PDF文件
            if not content.startswith(b'%PDF'):
                # 如果不是PDF格式，可能是JSON格式的PDF数据
                try:
                    import json
                    text_content = content.decode('utf-8')
                    pdf_data = json.loads(text_content)

                    html_parts = []
                    html_parts.append('<div class="pdf-content">')

                    if 'pages' in pdf_data and isinstance(pdf_data['pages'], list):
                        for i, page in enumerate(pdf_data['pages'], 1):
                            html_parts.append(f'<div class="pdf-page" data-page="{i}">')
                            html_parts.append(f'<h3>第 {i} 页</h3>')

                            page_content = page.get('content', '') if isinstance(page, dict) else str(page)
                            html_parts.append(page_content)

                            html_parts.append('</div>')
                    else:
                        # 单页PDF数据
                        html_parts.append('<div class="pdf-page" data-page="1">')
                        html_parts.append('<h3>第 1 页</h3>')
                        html_parts.append(pdf_data.get('content', str(pdf_data)))
                        html_parts.append('</div>')

                    html_parts.append('</div>')
                    html_content = '\n'.join(html_parts)

                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'pdf',
                            'pages': len(pdf_data.get('pages', [1])),
                            'note': '从JSON数据解析'
                        }
                    }
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # 尝试作为文本处理
                    try:
                        text_content = content.decode('utf-8')
                        html_content = f'<div class="pdf-content"><div class="pdf-page"><h3>第 1 页</h3><pre>{text_content}</pre></div></div>'
                        return {
                            'success': True,
                            'content': html_content,
                            'format': 'html',
                            'metadata': {
                                'type': 'pdf',
                                'pages': 1,
                                'note': '文件内容为纯文本格式'
                            }
                        }
                    except UnicodeDecodeError:
                        return {
                            'success': False,
                            'error': '文件格式不正确，无法解析',
                            'content': '',
                            'format': 'text',
                            'metadata': {'type': 'pdf'}
                        }

            # 如果有PDF解析库，尝试解析真实PDF
            if PDF_AVAILABLE:
                try:
                    import pdfplumber

                    html_parts = []
                    html_parts.append('<div class="pdf-content">')

                    with pdfplumber.open(file_path) as pdf:
                        for i, page in enumerate(pdf.pages, 1):
                            text = page.extract_text()
                            if text:
                                html_parts.append(f'<div class="pdf-page" data-page="{i}">')
                                html_parts.append(f'<h3>第 {i} 页</h3>')
                                # 保持段落格式
                                paragraphs = text.split('\n\n')
                                for para in paragraphs:
                                    if para.strip():
                                        html_parts.append(f'<p>{para.strip()}</p>')
                                html_parts.append('</div>')

                    html_parts.append('</div>')
                    html_content = '\n'.join(html_parts)

                    return {
                        'success': True,
                        'content': html_content,
                        'format': 'html',
                        'metadata': {
                            'type': 'pdf',
                            'pages': len(pdf.pages)
                        }
                    }
                except Exception as pdf_error:
                    logger.warning(f"pdfplumber解析失败: {pdf_error}")
                    pass

            # 降级处理
            return {
                'success': False,
                'error': 'PDF文件解析失败，请检查文件格式',
                'content': '',
                'format': 'text',
                'metadata': {'type': 'pdf'}
            }

        except Exception as e:
            logger.error(f"PDF解析失败: {e}")
            return {
                'success': False,
                'error': f'PDF解析失败: {str(e)}',
                'content': '',
                'format': 'text',
                'metadata': {'type': 'pdf'}
            }
    
    def _parse_html(self, file_path: str) -> Dict[str, Any]:
        """解析HTML文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            'success': True,
            'content': content,
            'format': 'html',
            'metadata': {
                'type': 'html',
                'size': len(content)
            }
        }
    
    def _parse_json(self, file_path: str) -> Dict[str, Any]:
        """解析JSON文件"""
        import json
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            # 验证JSON格式
            json.loads(content)
            # 格式化显示
            html_content = f'<pre class="json-content"><code>{content}</code></pre>'
        except json.JSONDecodeError as e:
            html_content = f'<div class="error">JSON格式错误: {str(e)}</div><pre>{content}</pre>'
        
        return {
            'success': True,
            'content': html_content,
            'raw_content': content,
            'format': 'html',
            'metadata': {
                'type': 'json',
                'size': len(content)
            }
        }
    
    def _parse_xml(self, file_path: str) -> Dict[str, Any]:
        """解析XML文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的XML显示
        html_content = f'<pre class="xml-content"><code>{content}</code></pre>'
        
        return {
            'success': True,
            'content': html_content,
            'raw_content': content,
            'format': 'html',
            'metadata': {
                'type': 'xml',
                'size': len(content)
            }
        }
    
    def _parse_csv(self, file_path: str) -> Dict[str, Any]:
        """解析CSV文件"""
        import csv
        
        html_parts = []
        html_parts.append('<div class="csv-content">')
        html_parts.append('<table class="table table-bordered">')
        
        with open(file_path, 'r', encoding='utf-8') as f:
            csv_reader = csv.reader(f)
            for i, row in enumerate(csv_reader):
                if i == 0:
                    # 表头
                    html_parts.append('<thead><tr>')
                    for cell in row:
                        html_parts.append(f'<th>{cell}</th>')
                    html_parts.append('</tr></thead><tbody>')
                else:
                    html_parts.append('<tr>')
                    for cell in row:
                        html_parts.append(f'<td>{cell}</td>')
                    html_parts.append('</tr>')
        
        html_parts.append('</tbody></table></div>')
        html_content = '\n'.join(html_parts)
        
        return {
            'success': True,
            'content': html_content,
            'format': 'html',
            'metadata': {
                'type': 'csv'
            }
        }


# 全局实例
file_parser_service = FileParserService()
