'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import '../../styles/document-editor.css';
import { 
  FileText, 
  Presentation, 
  FileSpreadsheet, 
  File,
  Loader2,
  AlertCircle,
  Eye,
  Edit3
} from 'lucide-react';
import DocumentEditor from './DocumentEditor';
import PowerPointViewer from './PowerPointViewer';
import ExcelEditor from './ExcelEditor';
import PDFViewer from './PDFViewer';
import apiClient from '@/lib/api';

interface DocumentEditorWrapperProps {
  fileId: string;
  fileName: string;
  fileType: string;
  mode?: 'view' | 'edit';
  onClose?: () => void;
}

interface FileData {
  content?: string;
  slides?: any[];
  worksheets?: any[];
  pdfUrl?: string;
  metadata?: any;
}

const DocumentEditorWrapper: React.FC<DocumentEditorWrapperProps> = ({
  fileId,
  fileName,
  fileType,
  mode = 'view',
  onClose
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fileData, setFileData] = useState<FileData | null>(null);
  const [currentMode, setCurrentMode] = useState(mode);

  useEffect(() => {
    loadFileData();
  }, [fileId]);

  const loadFileData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取文件解析内容
      const encodedFileId = encodeURIComponent(fileId);
      const response = await apiClient.get(`/api/v1/file-management/files/${encodedFileId}/content`);
      const parsedData = response.data;

      if (!parsedData.success) {
        throw new Error(parsedData.error || '文件解析失败');
      }

      // 根据文件类型处理数据
      let processedData: FileData = {};

      switch (fileType.toLowerCase()) {
        case 'docx':
        case 'doc':
        case 'txt':
        case 'md':
        case 'markdown':
          processedData.content = parsedData.content;
          break;

        case 'pptx':
        case 'ppt':
          // 解析PowerPoint数据
          processedData.slides = parsePowerPointContent(parsedData.content);
          break;

        case 'xlsx':
        case 'xls':
          // 解析Excel数据
          processedData.worksheets = parseExcelContent(parsedData.content);
          break;

        case 'pdf':
          // PDF文件直接使用下载URL
          processedData.pdfUrl = `/api/v1/file-management/files/${fileId}/download`;
          processedData.content = parsedData.content;
          break;

        default:
          processedData.content = parsedData.content;
      }

      processedData.metadata = parsedData.metadata;
      setFileData(processedData);

    } catch (err) {
      console.error('Failed to load file data:', err);
      setError(err instanceof Error ? err.message : '加载文件失败');
    } finally {
      setLoading(false);
    }
  };

  // 解析PowerPoint内容
  const parsePowerPointContent = (htmlContent: string) => {
    // 从HTML内容中提取幻灯片
    const slides = [];
    const slideElements = htmlContent.match(/<div class="slide"[^>]*>(.*?)<\/div>/gs) || [];
    
    slideElements.forEach((slideHtml, index) => {
      const titleMatch = slideHtml.match(/<h2[^>]*>(.*?)<\/h2>/);
      const contentMatch = slideHtml.match(/<p[^>]*>(.*?)<\/p>/gs);
      
      slides.push({
        id: `slide-${index + 1}`,
        title: titleMatch ? titleMatch[1] : `幻灯片 ${index + 1}`,
        content: contentMatch ? contentMatch.join('') : '',
        layout: 'content' as const,
        background: '#ffffff',
        animations: []
      });
    });

    return slides.length > 0 ? slides : [{
      id: 'slide-1',
      title: '幻灯片 1',
      content: htmlContent,
      layout: 'content' as const,
      background: '#ffffff',
      animations: []
    }];
  };

  // 解析Excel内容
  const parseExcelContent = (htmlContent: string) => {
    const worksheets = [];
    
    // 从HTML内容中提取工作表
    const sheetMatches = htmlContent.match(/<h3>工作表: ([^<]+)<\/h3>(.*?)(?=<h3>|$)/gs) || [];
    
    if (sheetMatches.length > 0) {
      sheetMatches.forEach((sheetHtml, index) => {
        const nameMatch = sheetHtml.match(/<h3>工作表: ([^<]+)<\/h3>/);
        const tableMatch = sheetHtml.match(/<table[^>]*>(.*?)<\/table>/s);
        
        const data = [];
        if (tableMatch) {
          const rows = tableMatch[1].match(/<tr[^>]*>(.*?)<\/tr>/gs) || [];
          rows.forEach(rowHtml => {
            const cells = rowHtml.match(/<td[^>]*>(.*?)<\/td>/gs) || [];
            const rowData = cells.map(cellHtml => ({
              value: cellHtml.replace(/<[^>]*>/g, '').trim()
            }));
            data.push(rowData);
          });
        }

        worksheets.push({
          id: `sheet-${index + 1}`,
          name: nameMatch ? nameMatch[1] : `Sheet${index + 1}`,
          data: data.length > 0 ? data : Array(20).fill(null).map(() => 
            Array(10).fill(null).map(() => ({ value: '' }))
          )
        });
      });
    } else {
      // 如果没有找到工作表，创建默认工作表
      worksheets.push({
        id: 'sheet-1',
        name: 'Sheet1',
        data: Array(20).fill(null).map(() => 
          Array(10).fill(null).map(() => ({ value: '' }))
        )
      });
    }

    return worksheets;
  };

  // 保存文件
  const handleSave = async (data: any) => {
    try {
      let content = '';
      
      switch (fileType.toLowerCase()) {
        case 'docx':
        case 'doc':
        case 'txt':
        case 'md':
        case 'markdown':
          content = data;
          break;
        case 'pptx':
        case 'ppt':
          content = JSON.stringify(data);
          break;
        case 'xlsx':
        case 'xls':
          content = JSON.stringify(data);
          break;
        default:
          content = data;
      }

      await apiClient.put(`/api/v1/file-management/files/${fileId}/content`, {
        content
      });

      // 重新加载数据
      await loadFileData();
    } catch (error) {
      console.error('保存失败:', error);
      throw error;
    }
  };

  // 获取文件图标
  const getFileIcon = () => {
    switch (fileType.toLowerCase()) {
      case 'docx':
      case 'doc':
      case 'txt':
      case 'md':
      case 'markdown':
        return <FileText className="w-6 h-6 text-blue-500" />;
      case 'pptx':
      case 'ppt':
        return <Presentation className="w-6 h-6 text-orange-500" />;
      case 'xlsx':
      case 'xls':
        return <FileSpreadsheet className="w-6 h-6 text-green-500" />;
      case 'pdf':
        return <File className="w-6 h-6 text-red-500" />;
      default:
        return <FileText className="w-6 h-6 text-gray-500" />;
    }
  };

  // 渲染编辑器
  const renderEditor = () => {
    if (!fileData) return null;

    const isReadOnly = currentMode === 'view';

    switch (fileType.toLowerCase()) {
      case 'docx':
      case 'doc':
      case 'txt':
      case 'md':
      case 'markdown':
        return (
          <DocumentEditor
            fileId={fileId}
            fileName={fileName}
            fileType={fileType as any}
            content={fileData.content || ''}
            isReadOnly={isReadOnly}
            onSave={currentMode === 'edit' ? handleSave : undefined}
            onClose={onClose}
          />
        );

      case 'pptx':
      case 'ppt':
        return (
          <PowerPointViewer
            fileId={fileId}
            fileName={fileName}
            slides={fileData.slides || []}
            isReadOnly={isReadOnly}
            onEdit={() => setCurrentMode('edit')}
          />
        );

      case 'xlsx':
      case 'xls':
        return (
          <ExcelEditor
            fileId={fileId}
            fileName={fileName}
            worksheets={fileData.worksheets || []}
            isReadOnly={isReadOnly}
            onSave={currentMode === 'edit' ? handleSave : undefined}
            onClose={onClose}
          />
        );

      case 'pdf':
        return (
          <PDFViewer
            fileId={fileId}
            fileName={fileName}
            pdfUrl={fileData.pdfUrl || ''}
            isReadOnly={true} // PDF总是只读
            onClose={onClose}
          />
        );

      default:
        return (
          <DocumentEditor
            fileId={fileId}
            fileName={fileName}
            fileType="txt"
            content={fileData.content || ''}
            isReadOnly={isReadOnly}
            onSave={currentMode === 'edit' ? handleSave : undefined}
            onClose={onClose}
          />
        );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">加载文档中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-center max-w-md">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <div className="space-x-4">
            <button
              onClick={loadFileData}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              重试
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                关闭
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* 编辑器内容 - 直接渲染，模式切换按钮已整合到各编辑器的工具栏中 */}
      <div className="flex-1">
        <AnimatePresence mode="wait">
          <motion.div
            key={`${fileId}-${currentMode}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            {renderEditor()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default DocumentEditorWrapper;
