#!/bin/bash

# 停止Celery相关服务的脚本

echo "停止AI知识库Celery服务..."

# 停止Flower
if [ -f /tmp/celery_flower.pid ]; then
    echo "停止Flower监控..."
    kill -TERM $(cat /tmp/celery_flower.pid)
    rm -f /tmp/celery_flower.pid
    echo "Flower已停止"
else
    echo "Flower PID文件不存在，可能未运行"
fi

# 停止Beat
if [ -f /tmp/celery_beat.pid ]; then
    echo "停止Celery Beat..."
    kill -TERM $(cat /tmp/celery_beat.pid)
    rm -f /tmp/celery_beat.pid
    echo "Celery Beat已停止"
else
    echo "Beat PID文件不存在，可能未运行"
fi

# 停止Worker
if [ -f /tmp/celery_worker.pid ]; then
    echo "停止Celery Worker..."
    kill -TERM $(cat /tmp/celery_worker.pid)
    rm -f /tmp/celery_worker.pid
    echo "Celery Worker已停止"
else
    echo "Worker PID文件不存在，可能未运行"
fi

# 强制杀死所有celery进程 (如果PID文件方式失败)
echo "检查并清理残留的Celery进程..."
pkill -f "celery.*worker"
pkill -f "celery.*beat"
pkill -f "celery.*flower"

echo "Celery服务已全部停止!"
