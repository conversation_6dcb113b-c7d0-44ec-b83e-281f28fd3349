/**
 * 认证相关API
 */

import request from '@/utils/request'

export const authApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录信息
   * @param {string} data.username - 用户名或邮箱
   * @param {string} data.password - 密码
   */
  login(data) {
    return request({
      url: '/api/v1/auth/login',
      method: 'post',
      data: {
        username: data.username,
        password: data.password
      }
    })
  },

  /**
   * 用户注册
   * @param {Object} data - 注册信息
   * @param {string} data.username - 用户名
   * @param {string} data.email - 邮箱
   * @param {string} data.password - 密码
   * @param {string} data.full_name - 姓名
   */
  register(data) {
    return request({
      url: '/api/v1/auth/register',
      method: 'post',
      data
    })
  },

  /**
   * 刷新访问令牌
   * @param {string} refreshToken - 刷新令牌
   */
  refreshToken(refreshToken) {
    return request({
      url: '/api/v1/auth/refresh',
      method: 'post',
      data: {
        refresh_token: refreshToken
      }
    })
  },

  /**
   * 用户登出
   */
  logout() {
    return request({
      url: '/api/v1/auth/logout',
      method: 'post'
    })
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return request({
      url: '/api/v1/auth/me',
      method: 'get'
    })
  },

  /**
   * 更新用户信息
   * @param {Object} data - 用户信息
   */
  updateProfile(data) {
    return request({
      url: '/api/v1/auth/profile',
      method: 'put',
      data
    })
  },

  /**
   * 修改密码
   * @param {Object} data - 密码信息
   * @param {string} data.old_password - 旧密码
   * @param {string} data.new_password - 新密码
   */
  changePassword(data) {
    return request({
      url: '/api/v1/auth/change-password',
      method: 'post',
      data
    })
  },

  /**
   * 验证令牌有效性
   * @param {string} token - 访问令牌
   */
  verifyToken(token) {
    return request({
      url: '/api/v1/auth/verify-token',
      method: 'post',
      data: {
        token
      }
    })
  }
}
