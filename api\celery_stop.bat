@echo off
chcp 65001 >nul
title AI知识库 - Celery服务停止器

echo ========================================
echo    AI知识库 Celery服务停止器
echo ========================================
echo.

echo [停止] 正在停止Celery服务...
echo.

REM 通过窗口标题停止服务
echo [1/3] 停止Flower监控...
taskkill /f /fi "WINDOWTITLE eq Celery Flower" >nul 2>&1
taskkill /f /fi "WINDOWTITLE eq Celery-Flower" >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✓ Flower监控已停止
) else (
    echo   - Flower监控未运行或已停止
)

echo [2/3] 停止Celery Beat...
taskkill /f /fi "WINDOWTITLE eq Celery Beat" >nul 2>&1
taskkill /f /fi "WINDOWTITLE eq Celery-Beat" >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✓ Celery Beat已停止
) else (
    echo   - Celery Beat未运行或已停止
)

echo [3/3] 停止Celery Worker...
taskkill /f /fi "WINDOWTITLE eq Celery Worker" >nul 2>&1
taskkill /f /fi "WINDOWTITLE eq Celery-Worker" >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✓ Celery Worker已停止
) else (
    echo   - Celery Worker未运行或已停止
)

echo.
echo [清理] 检查残留进程...

REM 强制停止所有celery相关进程
taskkill /f /im "celery.exe" >nul 2>&1
taskkill /f /im "python.exe" /fi "COMMANDLINE eq *celery*" >nul 2>&1

REM 检查是否还有相关进程
set "found_processes="
for /f "tokens=2" %%i in ('tasklist /fi "IMAGENAME eq python.exe" /fo csv ^| findstr /i celery') do (
    set "found_processes=1"
)

if defined found_processes (
    echo   ⚠ 发现残留进程，正在强制清理...
    taskkill /f /im "python.exe" /fi "COMMANDLINE eq *celery*" >nul 2>&1
    echo   ✓ 残留进程已清理
) else (
    echo   ✓ 没有发现残留进程
)

echo.
echo ========================================
echo           停止完成！
echo ========================================
echo.
echo 所有Celery服务已停止
echo.
echo 如需重新启动服务，请运行: celery_start.bat
echo.
pause
