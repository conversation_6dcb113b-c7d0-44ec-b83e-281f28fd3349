'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronLeft, 
  ChevronRight, 
  Play, 
  Pause, 
  Square, 
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Maximize,
  Grid3X3,
  Eye,
  Edit3,
  Download,
  Printer
} from 'lucide-react';

interface Slide {
  id: string;
  title: string;
  content: string;
  layout?: string;
  background?: string;
  animations?: any[];
}

interface PowerPointViewerProps {
  fileId: string;
  fileName: string;
  slides: Slide[];
  isReadOnly?: boolean;
  onEdit?: () => void;
}

const PowerPointViewer: React.FC<PowerPointViewerProps> = ({
  fileId,
  fileName,
  slides,
  isReadOnly = true,
  onEdit
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [zoom, setZoom] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showThumbnails, setShowThumbnails] = useState(true);

  // 自动播放逻辑
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying && slides.length > 1) {
      interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slides.length);
      }, 5000); // 5秒切换一次
    }
    return () => clearInterval(interval);
  }, [isPlaying, slides.length]);

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          setCurrentSlide((prev) => Math.max(0, prev - 1));
          break;
        case 'ArrowRight':
          setCurrentSlide((prev) => Math.min(slides.length - 1, prev + 1));
          break;
        case ' ':
          e.preventDefault();
          setIsPlaying(!isPlaying);
          break;
        case 'Escape':
          setIsFullscreen(false);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isPlaying, slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => Math.min(slides.length - 1, prev + 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => Math.max(0, prev - 1));
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const stopPresentation = () => {
    setIsPlaying(false);
    setCurrentSlide(0);
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  const currentSlideData = slides[currentSlide] || { title: '', content: '', id: '' };

  return (
    <div className={`flex flex-col h-screen bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* PowerPoint风格头部工具栏 */}
      <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white p-3 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <h1 className="text-lg font-semibold">{fileName}</h1>
            <span className="text-orange-100 text-sm">
              {currentSlide + 1} / {slides.length}
            </span>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* 模式切换 */}
            <div className="flex bg-white bg-opacity-20 rounded-lg p-1 mr-4">
              <button
                className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
                  isReadOnly 
                    ? 'bg-white text-orange-600 shadow-sm' 
                    : 'text-white hover:bg-white hover:bg-opacity-20'
                }`}
              >
                <Eye className="w-4 h-4" />
                <span>查看</span>
              </button>
              <button
                onClick={onEdit}
                className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
                  !isReadOnly 
                    ? 'bg-white text-orange-600 shadow-sm' 
                    : 'text-white hover:bg-white hover:bg-opacity-20'
                }`}
              >
                <Edit3 className="w-4 h-4" />
                <span>编辑</span>
              </button>
            </div>

            {/* 操作按钮 */}
            <button className="flex items-center space-x-2 px-4 py-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors">
              <Download className="w-4 h-4" />
              <span>下载</span>
            </button>
            
            <button 
              onClick={() => window.print()}
              className="flex items-center space-x-2 px-4 py-2 bg-white bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
            >
              <Printer className="w-4 h-4" />
              <span>打印</span>
            </button>
          </div>
        </div>
      </div>

      {/* 播放控制栏 */}
      <div className="bg-white border-b border-gray-200 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={prevSlide}
              disabled={currentSlide === 0}
              className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            
            <button
              onClick={togglePlay}
              className="p-2 text-white bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors"
            >
              {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
            </button>
            
            <button
              onClick={stopPresentation}
              className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
            >
              <Square className="w-5 h-5" />
            </button>
            
            <button
              onClick={nextSlide}
              disabled={currentSlide === slides.length - 1}
              className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowThumbnails(!showThumbnails)}
              className={`p-2 rounded-lg transition-colors ${
                showThumbnails 
                  ? 'text-orange-600 bg-orange-50' 
                  : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
              }`}
            >
              <Grid3X3 className="w-5 h-5" />
            </button>
            
            <button
              onClick={() => setZoom(Math.max(50, zoom - 10))}
              className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
            >
              <ZoomOut className="w-5 h-5" />
            </button>
            
            <span className="text-sm text-gray-600 min-w-12 text-center">{zoom}%</span>
            
            <button
              onClick={() => setZoom(Math.min(200, zoom + 10))}
              className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
            >
              <ZoomIn className="w-5 h-5" />
            </button>
            
            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
            >
              <Maximize className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 缩略图面板 */}
        {showThumbnails && (
          <div className="w-64 bg-white border-r border-gray-200 p-4 overflow-y-auto">
            <h3 className="text-sm font-medium text-gray-900 mb-3">幻灯片</h3>
            <div className="space-y-2">
              {slides.map((slide, index) => (
                <motion.div
                  key={slide.id}
                  whileHover={{ scale: 1.02 }}
                  onClick={() => setCurrentSlide(index)}
                  className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                    index === currentSlide
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 hover:border-orange-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="text-xs font-medium text-gray-900 mb-1">
                    {index + 1}. {slide.title || `幻灯片 ${index + 1}`}
                  </div>
                  <div 
                    className="text-xs text-gray-600 line-clamp-2"
                    dangerouslySetInnerHTML={{ __html: slide.content.substring(0, 50) + '...' }}
                  />
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* 幻灯片显示区域 */}
        <div className="flex-1 flex items-center justify-center p-8 bg-gray-100">
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
            className="bg-white shadow-2xl rounded-lg overflow-hidden"
            style={{
              transform: `scale(${zoom / 100})`,
              width: '800px',
              height: '600px',
              transformOrigin: 'center'
            }}
          >
            <div 
              className="w-full h-full p-8 flex flex-col"
              style={{ backgroundColor: currentSlideData.background || '#ffffff' }}
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-6 text-center">
                {currentSlideData.title || `幻灯片 ${currentSlide + 1}`}
              </h1>
              <div 
                className="flex-1 text-gray-700 text-lg leading-relaxed"
                dangerouslySetInnerHTML={{ __html: currentSlideData.content }}
              />
            </div>
          </motion.div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <div className="bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>幻灯片 {currentSlide + 1} / {slides.length}</span>
            <span>•</span>
            <span>{isPlaying ? '播放中' : '已暂停'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>缩放: {zoom}%</span>
            {isFullscreen && (
              <>
                <span>•</span>
                <span>全屏模式 (按 ESC 退出)</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PowerPointViewer;
