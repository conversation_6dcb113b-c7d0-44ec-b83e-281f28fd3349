"""
上传任务管理API
"""
import os
import shutil
import tempfile
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.database import get_async_session
from app.services.upload_task_service import upload_task_service
from app.core.dependencies import get_current_user
from app.models.user import User

router = APIRouter()


class CreateTaskRequest(BaseModel):
    """创建任务请求"""
    file_name: str
    file_size: int
    storage_id: int
    upload_path: str


class UpdateTaskStatusRequest(BaseModel):
    """更新任务状态请求"""
    status: str
    progress: Optional[int] = None
    uploaded_bytes: Optional[int] = None
    error_message: Optional[str] = None


class BatchOperationRequest(BaseModel):
    """批量操作请求"""
    task_ids: List[str]
    action: str  # pause, resume, delete


@router.post("/create-with-file")
async def create_task_with_file(
    file: UploadFile = File(...),
    storage_id: int = Form(...),
    upload_path: str = Form(...),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """创建上传任务并保存文件到临时目录"""
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="upload_")
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        # 保存文件到临时目录
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 创建上传任务
        task = await upload_task_service.create_task(
            file_name=file.filename,
            file_size=file.size,
            file_path=temp_file_path,
            storage_id=storage_id,
            upload_path=upload_path,
            session=session
        )

        # 自动提交到Celery队列
        celery_result = await upload_task_service.submit_task_to_celery(task.task_id)

        return {
            "success": True,
            "message": "任务创建并提交成功",
            "data": {
                **task.to_dict(),
                "celery_task_id": celery_result.get("celery_task_id"),
                "celery_status": "submitted" if celery_result.get("success") else "failed"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")


@router.get("/list")
async def get_tasks(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """获取上传任务列表"""
    try:
        result = await upload_task_service.get_tasks(
            page=page,
            page_size=page_size,
            status_filter=status,
            session=session
        )
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.put("/{task_id}/status")
async def update_task_status(
    task_id: str,
    request: UpdateTaskStatusRequest,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """更新任务状态"""
    try:
        success = await upload_task_service.update_task_status(
            task_id=task_id,
            status=request.status,
            progress=request.progress,
            uploaded_bytes=request.uploaded_bytes,
            error_message=request.error_message,
            session=session
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "success": True,
            "message": "状态更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新状态失败: {str(e)}")


@router.delete("/{task_id}")
async def delete_task(
    task_id: str,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """删除任务"""
    try:
        # 获取任务信息
        task = await upload_task_service.get_task_by_id(task_id, session)
        if task:
            # 删除临时文件
            try:
                if os.path.exists(task.file_path):
                    os.remove(task.file_path)
                    # 如果是临时目录且为空，也删除目录
                    temp_dir = os.path.dirname(task.file_path)
                    if temp_dir.startswith(tempfile.gettempdir()) and not os.listdir(temp_dir):
                        os.rmdir(temp_dir)
            except Exception as e:
                print(f"删除临时文件失败: {e}")
        
        success = await upload_task_service.delete_task(task_id, session)
        
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "success": True,
            "message": "任务删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")


@router.post("/batch-operation")
async def batch_operation(
    request: BatchOperationRequest,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """批量操作任务"""
    try:
        if request.action == "delete":
            # 批量删除前先删除临时文件
            for task_id in request.task_ids:
                task = await upload_task_service.get_task_by_id(task_id, session)
                if task and os.path.exists(task.file_path):
                    try:
                        os.remove(task.file_path)
                        temp_dir = os.path.dirname(task.file_path)
                        if temp_dir.startswith(tempfile.gettempdir()) and not os.listdir(temp_dir):
                            os.rmdir(temp_dir)
                    except Exception as e:
                        print(f"删除临时文件失败: {e}")
            
            count = await upload_task_service.delete_tasks(request.task_ids, session)
            return {
                "success": True,
                "message": f"成功删除 {count} 个任务"
            }
        
        elif request.action in ["pause", "resume"]:
            status = "paused" if request.action == "pause" else "pending"
            count = await upload_task_service.update_tasks_status(
                request.task_ids, status, session
            )
            return {
                "success": True,
                "message": f"成功{request.action} {count} 个任务"
            }
        
        else:
            raise HTTPException(status_code=400, detail="不支持的操作")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")


@router.post("/{task_id}/retry")
async def retry_task(
    task_id: str,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """重试任务"""
    try:
        result = await upload_task_service.retry_task_with_celery(task_id, session)

        if not result.get("success"):
            raise HTTPException(status_code=404, detail=result.get("message", "任务重试失败"))

        return {
            "success": True,
            "message": "任务重试并提交成功",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重试任务失败: {str(e)}")


@router.post("/{task_id}/submit")
async def submit_task_to_queue(
    task_id: str,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """手动提交任务到Celery队列"""
    try:
        result = await upload_task_service.submit_task_to_celery(task_id)

        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result.get("message", "提交任务失败"))

        return {
            "success": True,
            "message": "任务提交成功",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")


@router.post("/batch-submit")
async def batch_submit_tasks(
    request: BatchOperationRequest,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """批量提交任务到Celery队列"""
    try:
        if request.action != "submit":
            raise HTTPException(status_code=400, detail="操作类型必须是submit")

        result = await upload_task_service.submit_batch_tasks_to_celery(request.task_ids)

        return {
            "success": True,
            "message": f"批量提交成功",
            "data": result
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量提交失败: {str(e)}")


@router.delete("/clear-completed")
async def clear_completed_tasks(
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """清理已完成的任务"""
    try:
        count = await upload_task_service.clear_completed_tasks(session)
        
        return {
            "success": True,
            "message": f"成功清理 {count} 个已完成任务"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理任务失败: {str(e)}")


@router.get("/{task_id}")
async def get_task(
    task_id: str,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """获取单个任务详情"""
    try:
        task = await upload_task_service.get_task_by_id(task_id, session)
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {
            "success": True,
            "data": task.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务失败: {str(e)}")
