<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>🤖 AI知识库</h1>
        <p>创建您的账户</p>
      </div>

      <form @submit.prevent="handleRegister" class="register-form">
        <div class="form-group">
          <label>用户名</label>
          <input 
            v-model="registerForm.username" 
            type="text" 
            placeholder="请输入用户名（3-50个字符）"
            required
            minlength="3"
            maxlength="50"
            :disabled="loading"
          />
        </div>
        
        <div class="form-group">
          <label>邮箱</label>
          <input 
            v-model="registerForm.email" 
            type="email" 
            placeholder="请输入邮箱地址"
            required
            :disabled="loading"
          />
        </div>
        
        <div class="form-group">
          <label>姓名</label>
          <input 
            v-model="registerForm.fullName" 
            type="text" 
            placeholder="请输入真实姓名"
            :disabled="loading"
          />
        </div>
        
        <div class="form-group">
          <label>密码</label>
          <input 
            v-model="registerForm.password" 
            type="password" 
            placeholder="请输入密码（至少6位）"
            required
            minlength="6"
            :disabled="loading"
          />
          <div class="password-strength">
            <div class="strength-bar" :class="passwordStrength.class">
              <div class="strength-fill" :style="{ width: passwordStrength.width }"></div>
            </div>
            <span class="strength-text">{{ passwordStrength.text }}</span>
          </div>
        </div>
        
        <div class="form-group">
          <label>确认密码</label>
          <input 
            v-model="registerForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入密码"
            required
            :disabled="loading"
          />
        </div>
        
        <div class="form-options">
          <label class="checkbox-label">
            <input v-model="registerForm.agreeTerms" type="checkbox" required />
            <span>我已阅读并同意 <a href="#" class="terms-link">用户协议</a> 和 <a href="#" class="terms-link">隐私政策</a></span>
          </label>
        </div>
        
        <button type="submit" class="btn-register" :disabled="loading || !canSubmit">
          <i v-if="loading" class="fas fa-spinner fa-spin"></i>
          {{ loading ? '注册中...' : '注册账户' }}
        </button>
      </form>

      <div class="register-footer">
        <p>已有账户？ <router-link to="/login">立即登录</router-link></p>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <span>{{ error }}</span>
      </div>

      <!-- 成功信息 -->
      <div v-if="success" class="success-message">
        <i class="fas fa-check-circle"></i>
        <span>{{ success }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { authApi } from '@/api/auth'

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    
    const registerForm = reactive({
      username: '',
      email: '',
      fullName: '',
      password: '',
      confirmPassword: '',
      agreeTerms: false
    })
    
    // 密码强度检测
    const passwordStrength = computed(() => {
      const password = registerForm.password
      if (!password) {
        return { class: '', width: '0%', text: '' }
      }
      
      let score = 0
      let text = ''
      let className = ''
      
      // 长度检查
      if (password.length >= 6) score += 1
      if (password.length >= 8) score += 1
      
      // 复杂度检查
      if (/[a-z]/.test(password)) score += 1
      if (/[A-Z]/.test(password)) score += 1
      if (/[0-9]/.test(password)) score += 1
      if (/[^A-Za-z0-9]/.test(password)) score += 1
      
      if (score <= 2) {
        text = '弱'
        className = 'weak'
      } else if (score <= 4) {
        text = '中等'
        className = 'medium'
      } else {
        text = '强'
        className = 'strong'
      }
      
      return {
        class: className,
        width: `${(score / 6) * 100}%`,
        text
      }
    })
    
    // 是否可以提交
    const canSubmit = computed(() => {
      return registerForm.username &&
             registerForm.email &&
             registerForm.password &&
             registerForm.confirmPassword &&
             registerForm.password === registerForm.confirmPassword &&
             registerForm.agreeTerms
    })
    
    // 处理注册
    const handleRegister = async () => {
      // 验证密码一致性
      if (registerForm.password !== registerForm.confirmPassword) {
        error.value = '两次输入的密码不一致'
        return
      }
      
      loading.value = true
      error.value = ''
      success.value = ''
      
      try {
        // 调用注册API
        const response = await authApi.register({
          username: registerForm.username,
          email: registerForm.email,
          password: registerForm.password,
          full_name: registerForm.fullName || registerForm.username
        })
        
        success.value = '注册成功！正在跳转到登录页面...'
        
        // 延迟跳转到登录页
        setTimeout(() => {
          router.push('/login')
        }, 2000)
        
      } catch (err) {
        console.error('注册失败:', err)
        error.value = err.response?.data?.detail || '注册失败，请稍后重试'
      } finally {
        loading.value = false
      }
    }
    
    return {
      loading,
      error,
      success,
      registerForm,
      passwordStrength,
      canSubmit,
      handleRegister
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 450px;
  width: 100%;
}

.register-header {
  text-align: center;
  margin-bottom: 40px;
}

.register-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.register-header p {
  color: #666;
  font-size: 16px;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s;
}

.strength-bar.weak .strength-fill {
  background: #f44336;
}

.strength-bar.medium .strength-fill {
  background: #ff9800;
}

.strength-bar.strong .strength-fill {
  background: #4caf50;
}

.strength-text {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

.form-options {
  margin: 10px 0;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  margin-top: 2px;
}

.terms-link {
  color: #2196f3;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.btn-register {
  background: #2196f3;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-register:hover:not(:disabled) {
  background: #1976d2;
}

.btn-register:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.register-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.register-footer p {
  color: #666;
  font-size: 14px;
}

.register-footer a {
  color: #2196f3;
  text-decoration: none;
}

.register-footer a:hover {
  text-decoration: underline;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.success-message {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}
</style>
