<template>
  <div class="system-init-container">
    <div class="init-card">
      <div class="init-header">
        <h1>🚀 AI知识库系统初始化</h1>
        <p>欢迎使用AI知识库！请完成系统初始化配置。</p>
      </div>

      <!-- 初始化步骤指示器 -->
      <div class="steps-indicator">
        <div 
          v-for="(step, index) in steps" 
          :key="step.key"
          class="step-item"
          :class="{ 
            'completed': step.completed, 
            'active': currentStep === step.key,
            'disabled': !step.enabled
          }"
        >
          <div class="step-number">
            <i v-if="step.completed" class="fas fa-check"></i>
            <span v-else>{{ index + 1 }}</span>
          </div>
          <div class="step-label">{{ step.label }}</div>
        </div>
      </div>

      <!-- 管理员账户初始化 -->
      <div v-if="currentStep === 'admin'" class="init-step">
        <h2>👤 创建管理员账户</h2>
        <p>请创建系统管理员账户，用于管理整个知识库系统。</p>
        
        <form @submit.prevent="initializeAdmin" class="admin-form">
          <div class="form-group">
            <label>用户名</label>
            <input 
              v-model="adminForm.username" 
              type="text" 
              placeholder="请输入管理员用户名"
              required
              :disabled="loading"
            />
          </div>
          
          <div class="form-group">
            <label>邮箱</label>
            <input 
              v-model="adminForm.email" 
              type="email" 
              placeholder="请输入管理员邮箱"
              required
              :disabled="loading"
            />
          </div>
          
          <div class="form-group">
            <label>密码</label>
            <input 
              v-model="adminForm.password" 
              type="password" 
              placeholder="请输入管理员密码（至少6位）"
              required
              minlength="6"
              :disabled="loading"
            />
          </div>
          
          <div class="form-group">
            <label>确认密码</label>
            <input 
              v-model="adminForm.confirmPassword" 
              type="password" 
              placeholder="请再次输入密码"
              required
              :disabled="loading"
            />
          </div>
          
          <div class="form-group">
            <label>姓名</label>
            <input 
              v-model="adminForm.fullName" 
              type="text" 
              placeholder="请输入管理员姓名"
              :disabled="loading"
            />
          </div>
          
          <button type="submit" class="btn-primary" :disabled="loading">
            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
            {{ loading ? '创建中...' : '创建管理员账户' }}
          </button>
        </form>
      </div>

      <!-- 自动初始化步骤 -->
      <div v-if="currentStep === 'auto'" class="init-step">
        <h2>⚙️ 系统配置初始化</h2>
        <p>正在自动初始化系统配置、字典数据和存储配置...</p>
        
        <div class="auto-init-progress">
          <div v-for="task in autoInitTasks" :key="task.key" class="task-item">
            <div class="task-icon">
              <i v-if="task.status === 'completed'" class="fas fa-check-circle text-success"></i>
              <i v-else-if="task.status === 'running'" class="fas fa-spinner fa-spin text-primary"></i>
              <i v-else class="fas fa-circle text-muted"></i>
            </div>
            <div class="task-content">
              <div class="task-name">{{ task.name }}</div>
              <div class="task-description">{{ task.description }}</div>
            </div>
          </div>
        </div>
        
        <button 
          v-if="!autoInitStarted" 
          @click="startAutoInit" 
          class="btn-primary"
          :disabled="loading"
        >
          开始自动初始化
        </button>
      </div>

      <!-- 初始化完成 -->
      <div v-if="currentStep === 'completed'" class="init-step">
        <div class="completion-message">
          <i class="fas fa-check-circle text-success"></i>
          <h2>🎉 系统初始化完成！</h2>
          <p>恭喜！AI知识库系统已成功初始化，您现在可以开始使用了。</p>
          
          <div class="completion-actions">
            <button @click="goToLogin" class="btn-primary">
              前往登录
            </button>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <span>{{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { systemInitApi } from '@/api/systemInit'

export default {
  name: 'SystemInit',
  setup() {
    const router = useRouter()
    
    const loading = ref(false)
    const error = ref('')
    const currentStep = ref('admin')
    const autoInitStarted = ref(false)
    
    const adminForm = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '系统管理员'
    })
    
    const steps = ref([
      { key: 'admin', label: '创建管理员', completed: false, enabled: true },
      { key: 'auto', label: '系统配置', completed: false, enabled: false },
      { key: 'completed', label: '初始化完成', completed: false, enabled: false }
    ])
    
    const autoInitTasks = ref([
      { key: 'dictionaries', name: '字典数据', description: '初始化系统字典数据', status: 'pending' },
      { key: 'storage', name: '存储配置', description: '创建默认存储配置', status: 'pending' },
      { key: 'system-config', name: '系统配置', description: '初始化系统配置项', status: 'pending' }
    ])
    
    // 检查初始化状态
    const checkInitStatus = async () => {
      try {
        const response = await systemInitApi.getStatus()
        if (response.data.is_initialized) {
          // 系统已初始化，跳转到登录页
          router.push('/login')
        }
      } catch (err) {
        console.error('检查初始化状态失败:', err)
      }
    }
    
    // 初始化管理员账户
    const initializeAdmin = async () => {
      if (adminForm.password !== adminForm.confirmPassword) {
        error.value = '两次输入的密码不一致'
        return
      }
      
      loading.value = true
      error.value = ''
      
      try {
        await systemInitApi.initializeAdmin({
          username: adminForm.username,
          email: adminForm.email,
          password: adminForm.password,
          full_name: adminForm.fullName
        })
        
        // 标记管理员步骤完成
        steps.value[0].completed = true
        steps.value[1].enabled = true
        currentStep.value = 'auto'
        
      } catch (err) {
        error.value = err.response?.data?.detail || '创建管理员账户失败'
      } finally {
        loading.value = false
      }
    }
    
    // 开始自动初始化
    const startAutoInit = async () => {
      autoInitStarted.value = true
      loading.value = true
      
      try {
        // 依次执行初始化任务
        for (const task of autoInitTasks.value) {
          task.status = 'running'
          
          let apiCall
          switch (task.key) {
            case 'dictionaries':
              apiCall = systemInitApi.initializeDictionaries
              break
            case 'storage':
              apiCall = systemInitApi.initializeStorage
              break
            case 'system-config':
              apiCall = systemInitApi.initializeSystemConfig
              break
          }
          
          await apiCall()
          task.status = 'completed'
        }
        
        // 标记自动初始化步骤完成
        steps.value[1].completed = true
        steps.value[2].enabled = true
        steps.value[2].completed = true
        currentStep.value = 'completed'
        
      } catch (err) {
        error.value = err.response?.data?.detail || '自动初始化失败'
        // 标记当前任务失败
        const runningTask = autoInitTasks.value.find(t => t.status === 'running')
        if (runningTask) {
          runningTask.status = 'failed'
        }
      } finally {
        loading.value = false
      }
    }
    
    // 前往登录页
    const goToLogin = () => {
      router.push('/login')
    }
    
    onMounted(() => {
      checkInitStatus()
    })
    
    return {
      loading,
      error,
      currentStep,
      autoInitStarted,
      adminForm,
      steps,
      autoInitTasks,
      initializeAdmin,
      startAutoInit,
      goToLogin
    }
  }
}
</script>

<style scoped>
.system-init-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.init-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 600px;
  width: 100%;
}

.init-header {
  text-align: center;
  margin-bottom: 40px;
}

.init-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.init-header p {
  color: #666;
  font-size: 16px;
}

.steps-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
  position: relative;
}

.steps-indicator::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-weight: bold;
  color: #666;
}

.step-item.completed .step-number {
  background: #4caf50;
  color: white;
}

.step-item.active .step-number {
  background: #2196f3;
  color: white;
}

.step-label {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.init-step {
  margin-bottom: 30px;
}

.init-step h2 {
  color: #333;
  margin-bottom: 10px;
}

.init-step p {
  color: #666;
  margin-bottom: 30px;
}

.admin-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.form-group input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.btn-primary {
  background: #2196f3;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-primary:hover:not(:disabled) {
  background: #1976d2;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.auto-init-progress {
  margin: 30px 0;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.task-item:last-child {
  border-bottom: none;
}

.task-icon {
  margin-right: 15px;
  font-size: 20px;
}

.task-content {
  flex: 1;
}

.task-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.task-description {
  font-size: 14px;
  color: #666;
}

.completion-message {
  text-align: center;
  padding: 40px 0;
}

.completion-message i {
  font-size: 60px;
  margin-bottom: 20px;
}

.completion-message h2 {
  color: #333;
  margin-bottom: 15px;
}

.completion-message p {
  color: #666;
  margin-bottom: 30px;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.text-success { color: #4caf50; }
.text-primary { color: #2196f3; }
.text-muted { color: #999; }
</style>
