"""
文件上传Celery任务
"""
import os
import asyncio
from typing import Dict, Any
from celery import Task
from celery.exceptions import Retry
from loguru import logger

from app.core.celery_config import celery_app
from app.core.database import get_async_session
from app.services.upload_task_service import upload_task_service
from app.services.simple_file_manager import SimpleFileManagerService


class CallbackTask(Task):
    """支持回调的任务基类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        logger.info(f"任务 {task_id} 执行成功: {retval}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        logger.error(f"任务 {task_id} 执行失败: {exc}")
        # 更新数据库中的任务状态
        asyncio.create_task(self._update_task_status_on_failure(task_id, str(exc)))
    
    async def _update_task_status_on_failure(self, task_id: str, error_message: str):
        """更新失败任务的状态"""
        try:
            async for session in get_async_session():
                await upload_task_service.update_task_status(
                    task_id, "error", error_message=error_message, session=session
                )
                break
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")


@celery_app.task(
    bind=True,
    base=CallbackTask,
    queue='upload_queue',
    max_retries=3,
    default_retry_delay=60
)
def process_upload_task(self, task_id: str) -> Dict[str, Any]:
    """
    处理文件上传任务
    
    Args:
        task_id: 上传任务ID
        
    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        logger.info(f"开始处理上传任务: {task_id}")
        
        # 使用asyncio运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(_process_upload_task_async(task_id, self))
            return result
        finally:
            loop.close()
            
    except Exception as exc:
        logger.error(f"上传任务处理失败: {task_id} - {exc}")
        
        # 重试逻辑
        if self.request.retries < self.max_retries:
            logger.info(f"重试任务: {task_id} (第 {self.request.retries + 1} 次)")
            raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))
        
        # 最终失败
        raise exc


async def _process_upload_task_async(task_id: str, celery_task) -> Dict[str, Any]:
    """异步处理上传任务"""
    
    async for session in get_async_session():
        try:
            # 获取任务信息
            task = await upload_task_service.get_task_by_id(task_id, session)
            if not task:
                raise Exception(f"任务不存在: {task_id}")
            
            # 检查任务状态
            if task.status not in ["pending", "error"]:
                logger.info(f"任务状态不允许处理: {task.status}")
                return {"status": "skipped", "message": f"任务状态: {task.status}"}
            
            # 更新任务状态为处理中
            await upload_task_service.update_task_status(
                task_id, "uploading", progress=0, session=session
            )
            
            # 检查文件是否存在
            if not os.path.exists(task.file_path):
                raise Exception("源文件不存在")
            
            # 执行文件上传
            file_service = SimpleFileManagerService()
            
            # 分阶段更新进度
            await _update_progress(task_id, 10, session)  # 开始处理
            
            # 读取文件
            with open(task.file_path, 'rb') as f:
                file_content = f.read()
            
            await _update_progress(task_id, 30, session)  # 文件读取完成
            
            # 模拟上传进度
            for i in range(4, 9):
                await asyncio.sleep(0.5)  # 模拟上传时间
                progress = 30 + (i - 3) * 15
                await _update_progress(task_id, progress, session)
            
            # 执行实际上传
            result = await file_service.upload_file(
                storage_id=task.storage_id,
                file_content=file_content,
                file_path=task.upload_path,
                session=session
            )
            
            if not result.get("success"):
                raise Exception(result.get("message", "上传失败"))
            
            # 上传成功
            await upload_task_service.update_task_status(
                task_id, "success", progress=100, session=session
            )
            
            # 清理临时文件
            await _cleanup_temp_file(task.file_path)
            
            logger.info(f"任务完成: {task_id} - {task.file_name}")
            
            return {
                "status": "success",
                "task_id": task_id,
                "file_name": task.file_name,
                "upload_path": task.upload_path
            }
            
        except Exception as e:
            # 更新任务状态为失败
            await upload_task_service.update_task_status(
                task_id, "error", error_message=str(e), session=session
            )
            raise
        
        finally:
            break  # 退出async for循环


async def _update_progress(task_id: str, progress: int, session):
    """更新任务进度"""
    try:
        uploaded_bytes = 0  # 这里可以根据实际情况计算
        await upload_task_service.update_task_status(
            task_id, "uploading", progress=progress, 
            uploaded_bytes=uploaded_bytes, session=session
        )
    except Exception as e:
        logger.warning(f"更新进度失败: {e}")


async def _cleanup_temp_file(file_path: str):
    """清理临时文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            
            # 如果目录为空，也删除目录
            temp_dir = os.path.dirname(file_path)
            if os.path.exists(temp_dir) and not os.listdir(temp_dir):
                os.rmdir(temp_dir)
                
            logger.debug(f"临时文件清理完成: {file_path}")
    except Exception as e:
        logger.warning(f"清理临时文件失败: {file_path} - {str(e)}")


@celery_app.task(queue='upload_queue')
def batch_process_upload_tasks(task_ids: list) -> Dict[str, Any]:
    """
    批量处理上传任务
    
    Args:
        task_ids: 任务ID列表
        
    Returns:
        Dict[str, Any]: 批量处理结果
    """
    results = []
    
    for task_id in task_ids:
        try:
            # 异步提交单个任务
            result = process_upload_task.delay(task_id)
            results.append({
                "task_id": task_id,
                "celery_task_id": result.id,
                "status": "submitted"
            })
        except Exception as e:
            results.append({
                "task_id": task_id,
                "status": "failed",
                "error": str(e)
            })
    
    return {
        "total": len(task_ids),
        "submitted": len([r for r in results if r["status"] == "submitted"]),
        "failed": len([r for r in results if r["status"] == "failed"]),
        "results": results
    }


@celery_app.task(queue='upload_queue')
def cleanup_completed_tasks() -> Dict[str, Any]:
    """清理已完成的任务"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(_cleanup_completed_tasks_async())
            return result
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"清理任务失败: {e}")
        return {"status": "error", "message": str(e)}


async def _cleanup_completed_tasks_async() -> Dict[str, Any]:
    """异步清理已完成的任务"""
    async for session in get_async_session():
        try:
            # 这里可以添加清理逻辑
            # 例如删除超过一定时间的已完成任务
            logger.info("清理已完成的任务")
            return {"status": "success", "message": "清理完成"}
        except Exception as e:
            logger.error(f"清理任务失败: {e}")
            raise
        finally:
            break
