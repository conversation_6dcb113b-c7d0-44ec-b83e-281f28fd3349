@tailwind base;
@tailwind components;
@tailwind utilities;

/* 引入AI主题样式 */
@import '../styles/ai-theme.css';

/* Word编辑器样式 */
.word-editor-content {
  /* 确保编辑器内容样式正确 */
  min-height: 100%;
  outline: none;
  caret-color: #2563eb;
}

.word-editor-content h1,
.word-editor-content h2,
.word-editor-content h3,
.word-editor-content h4,
.word-editor-content h5,
.word-editor-content h6 {
  margin: 1em 0 0.5em 0;
  font-weight: bold;
  line-height: 1.2;
}

.word-editor-content h1 { font-size: 2em; }
.word-editor-content h2 { font-size: 1.5em; }
.word-editor-content h3 { font-size: 1.17em; }
.word-editor-content h4 { font-size: 1em; }
.word-editor-content h5 { font-size: 0.83em; }
.word-editor-content h6 { font-size: 0.67em; }

.word-editor-content p {
  margin: 0.5em 0;
  line-height: 1.6;
}

.word-editor-content ul,
.word-editor-content ol {
  margin: 0.5em 0;
  padding-left: 2em;
}

.word-editor-content li {
  margin: 0.25em 0;
}

.word-editor-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.word-editor-content table td,
.word-editor-content table th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.word-editor-content table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

/* 防止编辑器内容跳转 */
.word-editor-content:focus {
  outline: none;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-jetbrains-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
