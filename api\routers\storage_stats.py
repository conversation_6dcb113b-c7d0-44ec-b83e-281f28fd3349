"""
存储统计API路由
"""
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.models.storage_stats import StorageStats
from app.services.storage_stats_service import StorageStatsService
from app.schemas.storage_stats import StorageStatsResponse, StorageStatsHistoryResponse

router = APIRouter(prefix="/api/v1/storage-stats", tags=["storage-stats"])
storage_stats_service = StorageStatsService()


@router.get("/storage/{storage_id}", response_model=StorageStatsResponse)
async def get_storage_stats(
    storage_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取存储统计信息"""
    try:
        stats = storage_stats_service.get_storage_stats(storage_id, db)
        
        if not stats:
            # 如果没有统计数据，尝试计算
            stats = await storage_stats_service.calculate_storage_stats(storage_id, db)
            
        if not stats:
            raise HTTPException(status_code=404, detail="存储统计信息不存在")
        
        return StorageStatsResponse(
            success=True,
            message="获取存储统计信息成功",
            data=stats.to_dict()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储统计信息失败: {str(e)}")


@router.get("/storage/{storage_id}/history", response_model=StorageStatsHistoryResponse)
async def get_storage_stats_history(
    storage_id: int,
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取存储统计历史"""
    try:
        if days > 365:
            days = 365  # 限制最多查询一年的数据
            
        history = storage_stats_service.get_storage_stats_history(storage_id, days, db)
        
        return StorageStatsHistoryResponse(
            success=True,
            message="获取存储统计历史成功",
            data=[stats.to_dict() for stats in history]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储统计历史失败: {str(e)}")


@router.post("/storage/{storage_id}/calculate")
async def calculate_storage_stats(
    storage_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """手动计算存储统计信息"""
    try:
        # 在后台任务中计算统计信息
        background_tasks.add_task(
            storage_stats_service.calculate_storage_stats,
            storage_id,
            db
        )
        
        return {
            "success": True,
            "message": "存储统计计算任务已启动"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动存储统计计算失败: {str(e)}")


@router.post("/calculate-all")
async def calculate_all_storage_stats(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """计算所有存储的统计信息"""
    try:
        # 在后台任务中计算所有存储的统计信息
        background_tasks.add_task(
            storage_stats_service.update_all_storage_stats,
            db
        )
        
        return {
            "success": True,
            "message": "所有存储统计计算任务已启动"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动所有存储统计计算失败: {str(e)}")


@router.get("/overview")
async def get_storage_overview(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取存储概览信息"""
    try:
        # 获取所有存储的最新统计信息
        from sqlalchemy import func
        from app.models.file_management import StorageConfig
        
        # 查询所有活跃存储及其最新统计
        query = db.query(
            StorageConfig.id,
            StorageConfig.name,
            StorageConfig.storage_type,
            func.max(StorageStats.stats_date).label('latest_stats_date')
        ).outerjoin(StorageStats).filter(
            StorageConfig.is_active == True
        ).group_by(StorageConfig.id, StorageConfig.name, StorageConfig.storage_type)
        
        storage_overview = query.all()
        
        # 获取详细统计信息
        overview_data = []
        total_files = 0
        total_size = 0
        total_storages = len(storage_overview)
        healthy_storages = 0
        
        for storage_info in storage_overview:
            stats = storage_stats_service.get_storage_stats(storage_info.id, db)
            
            if stats:
                total_files += stats.total_files
                total_size += stats.total_size
                if stats.is_healthy:
                    healthy_storages += 1
                
                overview_data.append({
                    'storage_id': storage_info.id,
                    'storage_name': storage_info.name,
                    'storage_type': storage_info.storage_type,
                    'total_files': stats.total_files,
                    'total_size': stats.total_size,
                    'health_score': stats.health_score,
                    'is_healthy': stats.is_healthy,
                    'last_updated': stats.updated_at.isoformat() if stats.updated_at else None
                })
            else:
                overview_data.append({
                    'storage_id': storage_info.id,
                    'storage_name': storage_info.name,
                    'storage_type': storage_info.storage_type,
                    'total_files': 0,
                    'total_size': 0,
                    'health_score': 0,
                    'is_healthy': False,
                    'last_updated': None
                })
        
        return {
            "success": True,
            "message": "获取存储概览成功",
            "data": {
                "summary": {
                    "total_storages": total_storages,
                    "healthy_storages": healthy_storages,
                    "total_files": total_files,
                    "total_size": total_size,
                    "health_rate": (healthy_storages / total_storages * 100) if total_storages > 0 else 0
                },
                "storages": overview_data
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储概览失败: {str(e)}")


@router.get("/health-check")
async def storage_health_check(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """存储健康检查"""
    try:
        from app.models.file_management import StorageConfig

        # 获取所有活跃存储
        storages = db.query(StorageConfig).filter(StorageConfig.is_active == True).all()
        
        health_report = {
            "total_storages": len(storages),
            "healthy_storages": 0,
            "warning_storages": 0,
            "critical_storages": 0,
            "details": []
        }
        
        for storage in storages:
            stats = storage_stats_service.get_storage_stats(storage.id, db)
            
            if stats:
                health_score = stats.health_score
                if health_score >= 80:
                    status = "healthy"
                    health_report["healthy_storages"] += 1
                elif health_score >= 60:
                    status = "warning"
                    health_report["warning_storages"] += 1
                else:
                    status = "critical"
                    health_report["critical_storages"] += 1
            else:
                status = "unknown"
                health_score = 0
            
            health_report["details"].append({
                "storage_id": storage.id,
                "storage_name": storage.name,
                "storage_type": storage.storage_type,
                "status": status,
                "health_score": health_score,
                "last_error": stats.last_error if stats else None
            })
        
        return {
            "success": True,
            "message": "存储健康检查完成",
            "data": health_report
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"存储健康检查失败: {str(e)}")
