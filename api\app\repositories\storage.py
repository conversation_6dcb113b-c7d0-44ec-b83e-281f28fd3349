"""
存储配置Repository
处理存储配置相关的数据访问操作
"""

from typing import Optional, List, Dict, Any
from sqlalchemy import select, func, and_, or_, update
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.models.file_management import StorageConfig, FileRecord, SyncLog
from app.repositories.base import BaseRepository
from app.schemas.storage import StorageConfigCreate, StorageConfigUpdate


class StorageConfigRepository(BaseRepository[StorageConfig, StorageConfigCreate, StorageConfigUpdate]):
    """存储配置Repository"""
    
    def __init__(self, session: AsyncSession):
        super().__init__(StorageConfig, session)
    
    async def get_by_name(self, name: str) -> Optional[StorageConfig]:
        """根据名称获取存储配置"""
        try:
            result = await self.session.execute(
                select(StorageConfig).where(StorageConfig.name == name)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting storage config by name {name}: {e}")
            return None
    
    async def get_default_storage(self) -> Optional[StorageConfig]:
        """获取默认存储配置"""
        try:
            result = await self.session.execute(
                select(StorageConfig).where(
                    and_(StorageConfig.is_default == True, StorageConfig.is_active == True)
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting default storage config: {e}")
            return None
    
    async def get_active_storages(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[StorageConfig]:
        """获取活跃的存储配置列表"""
        return await self.get_multi(
            skip=skip,
            limit=limit,
            filters={"is_active": True},
            order_by="created_at",
            desc=True
        )
    
    async def get_by_type(
        self, 
        storage_type: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[StorageConfig]:
        """根据存储类型获取配置列表"""
        return await self.get_multi(
            skip=skip,
            limit=limit,
            filters={"storage_type": storage_type, "is_active": True},
            order_by="created_at",
            desc=True
        )
    
    async def set_as_default(self, storage_id: int) -> bool:
        """设置为默认存储"""
        try:
            # 首先取消所有默认设置
            await self.session.execute(
                update(StorageConfig).values(is_default=False)
            )
            
            # 设置指定存储为默认
            storage = await self.get(storage_id)
            if storage:
                storage.is_default = True
                storage.is_active = True  # 默认存储必须是活跃的
                await self.session.commit()
                logger.info(f"Set storage {storage_id} as default")
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error setting storage {storage_id} as default: {e}")
            return False
    
    async def activate_storage(self, storage_id: int) -> bool:
        """激活存储配置"""
        try:
            storage = await self.get(storage_id)
            if storage:
                storage.is_active = True
                await self.session.commit()
                logger.info(f"Activated storage {storage_id}")
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error activating storage {storage_id}: {e}")
            return False
    
    async def deactivate_storage(self, storage_id: int) -> bool:
        """停用存储配置"""
        try:
            storage = await self.get(storage_id)
            if storage:
                # 如果是默认存储，不能停用
                if storage.is_default:
                    logger.warning(f"Cannot deactivate default storage {storage_id}")
                    return False
                
                storage.is_active = False
                await self.session.commit()
                logger.info(f"Deactivated storage {storage_id}")
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error deactivating storage {storage_id}: {e}")
            return False
    
    async def update_storage_stats(
        self, 
        storage_id: int, 
        total_files: int = None, 
        total_size: int = None
    ) -> bool:
        """更新存储统计信息"""
        try:
            storage = await self.get(storage_id)
            if storage:
                if total_files is not None:
                    storage.total_files = total_files
                if total_size is not None:
                    storage.total_size = total_size
                
                from datetime import datetime
                storage.last_sync_at = datetime.utcnow()
                
                await self.session.commit()
                logger.info(f"Updated stats for storage {storage_id}")
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error updating storage stats {storage_id}: {e}")
            return False
    
    async def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            # 总存储配置数
            total_storages = await self.count()
            
            # 活跃存储数
            active_storages = await self.count({"is_active": True})
            
            # 按类型统计
            type_stats = {}
            for storage_type in ["local", "minio", "ftp", "sftp"]:
                count = await self.count({"storage_type": storage_type, "is_active": True})
                type_stats[storage_type] = count
            
            # 总文件数和大小
            result = await self.session.execute(
                select(
                    func.sum(StorageConfig.total_files),
                    func.sum(StorageConfig.total_size)
                ).where(StorageConfig.is_active == True)
            )
            total_files, total_size = result.first() or (0, 0)
            
            return {
                "total_storages": total_storages,
                "active_storages": active_storages,
                "inactive_storages": total_storages - active_storages,
                "type_stats": type_stats,
                "total_files": total_files or 0,
                "total_size": total_size or 0
            }
        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            return {
                "total_storages": 0,
                "active_storages": 0,
                "inactive_storages": 0,
                "type_stats": {},
                "total_files": 0,
                "total_size": 0
            }


class FileRecordRepository(BaseRepository[FileRecord, dict, dict]):
    """文件记录Repository"""
    
    def __init__(self, session: AsyncSession):
        super().__init__(FileRecord, session)
    
    async def get_by_file_id(self, file_id: str) -> Optional[FileRecord]:
        """根据文件ID获取文件记录"""
        try:
            result = await self.session.execute(
                select(FileRecord).where(FileRecord.file_id == file_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting file record by file_id {file_id}: {e}")
            return None
    
    async def get_by_storage(
        self, 
        storage_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[FileRecord]:
        """根据存储ID获取文件记录"""
        return await self.get_multi(
            skip=skip,
            limit=limit,
            filters={"storage_id": storage_id},
            order_by="created_at",
            desc=True
        )
    
    async def get_by_path(self, storage_id: int, file_path: str) -> Optional[FileRecord]:
        """根据存储ID和路径获取文件记录"""
        try:
            result = await self.session.execute(
                select(FileRecord).where(
                    and_(
                        FileRecord.storage_id == storage_id,
                        FileRecord.file_path == file_path
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting file record by path {file_path}: {e}")
            return None
    
    async def search_files(
        self, 
        query: str, 
        storage_id: int = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[FileRecord]:
        """搜索文件"""
        try:
            search_query = select(FileRecord).where(
                or_(
                    FileRecord.file_name.ilike(f"%{query}%"),
                    FileRecord.file_path.ilike(f"%{query}%")
                )
            )
            
            if storage_id:
                search_query = search_query.where(FileRecord.storage_id == storage_id)
            
            search_query = search_query.offset(skip).limit(limit).order_by(
                FileRecord.created_at.desc()
            )
            
            result = await self.session.execute(search_query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error searching files with query {query}: {e}")
            return []


class SyncLogRepository(BaseRepository[SyncLog, dict, dict]):
    """同步日志Repository"""
    
    def __init__(self, session: AsyncSession):
        super().__init__(SyncLog, session)
    
    async def get_by_storage(
        self, 
        storage_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[SyncLog]:
        """根据存储ID获取同步日志"""
        return await self.get_multi(
            skip=skip,
            limit=limit,
            filters={"storage_id": storage_id},
            order_by="created_at",
            desc=True
        )
    
    async def get_latest_sync(self, storage_id: int) -> Optional[SyncLog]:
        """获取最新的同步记录"""
        try:
            result = await self.session.execute(
                select(SyncLog)
                .where(SyncLog.storage_id == storage_id)
                .order_by(SyncLog.created_at.desc())
                .limit(1)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting latest sync for storage {storage_id}: {e}")
            return None
