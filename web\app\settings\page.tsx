'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  User,
  Shield,
  Database,
  Bell,
  Activity,
  Server,
  Palette,
  Globe,
  HardDrive,
  Wifi,
  Lock,
  FileText,
  Zap,
  ChevronRight
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/Layout/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';

const SettingsPage: React.FC = () => {
  const router = useRouter();
  const { t, isLoading: langLoading } = useLanguage();

  if (langLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl mb-6">
            <Settings className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t?.navigation?.settings || '系统设置'}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            管理和配置您的AI知识库系统，优化性能和用户体验
          </p>
        </motion.div>



        {/* 设置分类 */}
        <div className="space-y-8">
          {/* 系统管理 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Server className="w-6 h-6 mr-3 text-blue-600" />
              系统管理
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Celery任务队列 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => router.push('/settings/celery')}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-purple-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Activity className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-purple-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-purple-900">
                  Celery任务队列
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  管理异步任务处理服务，监控Worker状态和性能指标
                </p>
                <div className="mt-4 flex items-center text-xs text-purple-600">
                  <Zap className="w-3 h-3 mr-1" />
                  <span>实时监控</span>
                </div>
              </motion.div>

              {/* 数据库管理 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-blue-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Database className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-900">
                  数据库管理
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  配置数据库连接，管理数据备份和恢复操作
                </p>
                <div className="mt-4 flex items-center text-xs text-blue-600">
                  <HardDrive className="w-3 h-3 mr-1" />
                  <span>数据安全</span>
                </div>
              </motion.div>

              {/* 存储配置 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-green-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <HardDrive className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-green-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-green-900">
                  存储配置
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  管理文件存储配置，支持本地、MinIO、FTP等存储方式
                </p>
                <div className="mt-4 flex items-center text-xs text-green-600">
                  <FileText className="w-3 h-3 mr-1" />
                  <span>多存储支持</span>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* 用户与安全 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Shield className="w-6 h-6 mr-3 text-green-600" />
              用户与安全
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 用户管理 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-indigo-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <User className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-indigo-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-900">
                  用户管理
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  管理用户账户、角色权限和访问控制策略
                </p>
                <div className="mt-4 flex items-center text-xs text-indigo-600">
                  <User className="w-3 h-3 mr-1" />
                  <span>权限控制</span>
                </div>
              </motion.div>

              {/* 安全设置 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-red-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Lock className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-red-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-red-900">
                  安全设置
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  配置系统安全策略、密码规则和访问限制
                </p>
                <div className="mt-4 flex items-center text-xs text-red-600">
                  <Lock className="w-3 h-3 mr-1" />
                  <span>安全防护</span>
                </div>
              </motion.div>

              {/* 网络配置 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-teal-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Wifi className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-teal-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-teal-900">
                  网络配置
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  配置网络代理、API接口和外部服务连接
                </p>
                <div className="mt-4 flex items-center text-xs text-teal-600">
                  <Wifi className="w-3 h-3 mr-1" />
                  <span>连接管理</span>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* 界面与体验 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Palette className="w-6 h-6 mr-3 text-pink-600" />
              界面与体验
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 主题设置 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-pink-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-pink-500 to-rose-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Palette className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-pink-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-pink-900">
                  主题设置
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  自定义界面主题、颜色方案和视觉效果
                </p>
                <div className="mt-4 flex items-center text-xs text-pink-600">
                  <Palette className="w-3 h-3 mr-1" />
                  <span>个性化</span>
                </div>
              </motion.div>

              {/* 语言设置 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-amber-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-amber-500 to-orange-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Globe className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-amber-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-amber-900">
                  语言设置
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  选择界面语言和区域设置，支持多语言切换
                </p>
                <div className="mt-4 flex items-center text-xs text-amber-600">
                  <Globe className="w-3 h-3 mr-1" />
                  <span>多语言</span>
                </div>
              </motion.div>

              {/* 通知设置 */}
              <motion.div
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="group bg-white p-6 rounded-2xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-orange-300 cursor-pointer transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl group-hover:scale-110 transition-transform duration-300">
                    <Bell className="w-6 h-6 text-white" />
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-orange-600 transition-colors" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-orange-900">
                  通知设置
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  配置系统通知、提醒方式和消息推送设置
                </p>
                <div className="mt-4 flex items-center text-xs text-orange-600">
                  <Bell className="w-3 h-3 mr-1" />
                  <span>消息推送</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default SettingsPage;
