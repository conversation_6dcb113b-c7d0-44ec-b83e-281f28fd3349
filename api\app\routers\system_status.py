"""
系统状态API路由
提供CPU使用率、内存使用、网络状态等系统监控数据
"""
import psutil
import platform
import socket
from datetime import datetime
from typing import Dict, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.database import get_sync_session
# from app.core.dependencies import get_current_user
# from app.models.user import User

router = APIRouter(prefix="/api/v1/system-status", tags=["system-status"])


@router.get("/overview")
async def get_system_overview():
    """获取系统状态概览"""
    try:
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # 内存信息
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # 磁盘信息
        disk_usage = psutil.disk_usage('/')
        
        # 网络信息
        network_stats = psutil.net_io_counters()
        network_connections = len(psutil.net_connections())
        
        # 系统信息
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        
        return {
            "success": True,
            "message": "获取系统状态成功",
            "data": {
                "cpu": {
                    "usage_percent": round(cpu_percent, 1),
                    "core_count": cpu_count,
                    "frequency": {
                        "current": round(cpu_freq.current, 2) if cpu_freq else 0,
                        "min": round(cpu_freq.min, 2) if cpu_freq else 0,
                        "max": round(cpu_freq.max, 2) if cpu_freq else 0
                    },
                    "status": _get_status_level(cpu_percent)
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "usage_percent": round(memory.percent, 1),
                    "total_formatted": _format_bytes(memory.total),
                    "available_formatted": _format_bytes(memory.available),
                    "used_formatted": _format_bytes(memory.used),
                    "swap_total": swap.total,
                    "swap_used": swap.used,
                    "swap_percent": round(swap.percent, 1),
                    "status": _get_status_level(memory.percent)
                },
                "disk": {
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "usage_percent": round((disk_usage.used / disk_usage.total) * 100, 1),
                    "total_formatted": _format_bytes(disk_usage.total),
                    "used_formatted": _format_bytes(disk_usage.used),
                    "free_formatted": _format_bytes(disk_usage.free),
                    "status": _get_status_level((disk_usage.used / disk_usage.total) * 100)
                },
                "network": {
                    "bytes_sent": network_stats.bytes_sent,
                    "bytes_recv": network_stats.bytes_recv,
                    "packets_sent": network_stats.packets_sent,
                    "packets_recv": network_stats.packets_recv,
                    "bytes_sent_formatted": _format_bytes(network_stats.bytes_sent),
                    "bytes_recv_formatted": _format_bytes(network_stats.bytes_recv),
                    "connections": network_connections,
                    "status": "online" if _check_internet_connection() else "offline"
                },
                "system": {
                    "platform": platform.system(),
                    "platform_version": platform.version(),
                    "architecture": platform.architecture()[0],
                    "hostname": socket.gethostname(),
                    "boot_time": boot_time.isoformat(),
                    "uptime_seconds": int(uptime.total_seconds()),
                    "uptime_formatted": _format_uptime(uptime)
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.get("/cpu")
async def get_cpu_details():
    """获取CPU详细信息"""
    try:
        # CPU使用率（每个核心）
        cpu_percent_per_core = psutil.cpu_percent(interval=1, percpu=True)
        
        # CPU时间
        cpu_times = psutil.cpu_times()
        
        # 负载平均值（仅Linux/Unix）
        load_avg = None
        try:
            load_avg = psutil.getloadavg()
        except AttributeError:
            # Windows系统不支持getloadavg
            pass
        
        return {
            "success": True,
            "message": "获取CPU详情成功",
            "data": {
                "overall_usage": round(psutil.cpu_percent(interval=1), 1),
                "per_core_usage": [round(usage, 1) for usage in cpu_percent_per_core],
                "core_count": psutil.cpu_count(),
                "logical_count": psutil.cpu_count(logical=True),
                "cpu_times": {
                    "user": cpu_times.user,
                    "system": cpu_times.system,
                    "idle": cpu_times.idle
                },
                "load_average": list(load_avg) if load_avg else None,
                "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取CPU详情失败: {str(e)}")


@router.get("/memory")
async def get_memory_details():
    """获取内存详细信息"""
    try:
        virtual_memory = psutil.virtual_memory()
        swap_memory = psutil.swap_memory()
        
        return {
            "success": True,
            "message": "获取内存详情成功",
            "data": {
                "virtual_memory": {
                    "total": virtual_memory.total,
                    "available": virtual_memory.available,
                    "used": virtual_memory.used,
                    "free": virtual_memory.free,
                    "percent": round(virtual_memory.percent, 1),
                    "total_formatted": _format_bytes(virtual_memory.total),
                    "available_formatted": _format_bytes(virtual_memory.available),
                    "used_formatted": _format_bytes(virtual_memory.used),
                    "free_formatted": _format_bytes(virtual_memory.free)
                },
                "swap_memory": {
                    "total": swap_memory.total,
                    "used": swap_memory.used,
                    "free": swap_memory.free,
                    "percent": round(swap_memory.percent, 1),
                    "total_formatted": _format_bytes(swap_memory.total),
                    "used_formatted": _format_bytes(swap_memory.used),
                    "free_formatted": _format_bytes(swap_memory.free)
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取内存详情失败: {str(e)}")


@router.get("/network")
async def get_network_details():
    """获取网络详细信息"""
    try:
        # 网络IO统计
        net_io = psutil.net_io_counters()
        
        # 网络接口信息
        net_interfaces = {}
        for interface, addrs in psutil.net_if_addrs().items():
            net_interfaces[interface] = []
            for addr in addrs:
                net_interfaces[interface].append({
                    "family": str(addr.family),
                    "address": addr.address,
                    "netmask": addr.netmask,
                    "broadcast": addr.broadcast
                })
        
        # 网络连接
        connections = psutil.net_connections()
        connection_stats = {
            "total": len(connections),
            "established": len([c for c in connections if c.status == 'ESTABLISHED']),
            "listening": len([c for c in connections if c.status == 'LISTEN'])
        }
        
        return {
            "success": True,
            "message": "获取网络详情成功",
            "data": {
                "io_counters": {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_recv": net_io.packets_recv,
                    "errin": net_io.errin,
                    "errout": net_io.errout,
                    "dropin": net_io.dropin,
                    "dropout": net_io.dropout,
                    "bytes_sent_formatted": _format_bytes(net_io.bytes_sent),
                    "bytes_recv_formatted": _format_bytes(net_io.bytes_recv)
                },
                "interfaces": net_interfaces,
                "connections": connection_stats,
                "internet_status": "online" if _check_internet_connection() else "offline",
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取网络详情失败: {str(e)}")


@router.get("/processes")
async def get_top_processes(
    limit: int = 10
):
    """获取占用资源最多的进程"""
    try:
        processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'memory_info']):
            try:
                proc_info = proc.info
                proc_info['memory_mb'] = proc_info['memory_info'].rss / 1024 / 1024
                processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 按CPU使用率排序
        processes_by_cpu = sorted(processes, key=lambda x: x['cpu_percent'] or 0, reverse=True)[:limit]
        
        # 按内存使用率排序
        processes_by_memory = sorted(processes, key=lambda x: x['memory_percent'] or 0, reverse=True)[:limit]
        
        return {
            "success": True,
            "message": "获取进程信息成功",
            "data": {
                "top_cpu_processes": [
                    {
                        "pid": p['pid'],
                        "name": p['name'],
                        "cpu_percent": round(p['cpu_percent'] or 0, 1),
                        "memory_percent": round(p['memory_percent'] or 0, 1),
                        "memory_mb": round(p['memory_mb'], 1)
                    }
                    for p in processes_by_cpu
                ],
                "top_memory_processes": [
                    {
                        "pid": p['pid'],
                        "name": p['name'],
                        "cpu_percent": round(p['cpu_percent'] or 0, 1),
                        "memory_percent": round(p['memory_percent'] or 0, 1),
                        "memory_mb": round(p['memory_mb'], 1)
                    }
                    for p in processes_by_memory
                ],
                "total_processes": len(processes),
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取进程信息失败: {str(e)}")


def _get_status_level(percentage: float) -> str:
    """根据使用率百分比获取状态级别"""
    if percentage < 50:
        return "good"
    elif percentage < 80:
        return "warning"
    else:
        return "critical"


def _check_internet_connection() -> bool:
    """检查网络连接状态"""
    try:
        socket.create_connection(("8.8.8.8", 53), timeout=3)
        return True
    except OSError:
        return False


def _format_bytes(bytes_value: int) -> str:
    """格式化字节数"""
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = float(bytes_value)
    unit_index = 0
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size)} {units[unit_index]}"
    else:
        return f"{size:.1f} {units[unit_index]}"


def _format_uptime(uptime) -> str:
    """格式化运行时间"""
    days = uptime.days
    hours, remainder = divmod(uptime.seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    
    if days > 0:
        return f"{days}天 {hours}小时 {minutes}分钟"
    elif hours > 0:
        return f"{hours}小时 {minutes}分钟"
    else:
        return f"{minutes}分钟"
