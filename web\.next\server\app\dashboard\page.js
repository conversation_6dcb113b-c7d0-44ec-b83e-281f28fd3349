/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$":
/*!**************************************************************!*\
  !*** ./lib/i18n/locales/ lazy ^\.\/.*\.ts$ namespace object ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.ts": [
		"(ssr)/./lib/i18n/locales/en.ts",
		"_ssr_lib_i18n_locales_en_ts"
	],
	"./ja.ts": [
		"(ssr)/./lib/i18n/locales/ja.ts",
		"_ssr_lib_i18n_locales_ja_ts"
	],
	"./zh-CN.ts": [
		"(ssr)/./lib/i18n/locales/zh-CN.ts",
		"_ssr_lib_i18n_locales_zh-CN_ts"
	],
	"./zh-TW.ts": [
		"(ssr)/./lib/i18n/locales/zh-TW.ts",
		"_ssr_lib_i18n_locales_zh-TW_ts"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1aa5\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBMEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvPzQ0YTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFx3b3Jrc3BhY2VcXFxceGhjLXJhZ1xcXFx3ZWJcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/LanguageContext.tsx */ \"(ssr)/./contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Database,FileText,FolderOpen,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Database,FileText,FolderOpen,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Database,FileText,FolderOpen,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Database,FileText,FolderOpen,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Database,FileText,FolderOpen,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Database,FileText,FolderOpen,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Database,FileText,FolderOpen,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Layout/MainLayout */ \"(ssr)/./components/Layout/MainLayout.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Dashboard = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { t, isLoading: langLoading } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUser = async ()=>{\n            try {\n                const userData = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n                setUser(userData);\n            } catch (error) {\n                console.error(\"Failed to fetch user:\", error);\n                router.push(\"/login\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUser();\n    }, [\n        router\n    ]);\n    if (loading || langLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                animate: {\n                    rotate: 360\n                },\n                transition: {\n                    duration: 1,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                },\n                className: \"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full\"\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_MainLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[calc(100vh-4rem)] overflow-y-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: [\n                                    t?.dashboard?.welcome || \"欢迎回来\",\n                                    \", \",\n                                    user?.username,\n                                    \"!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: t?.dashboard?.subtitle || \"管理您的AI驱动的文档处理工作流\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.2\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            {\n                                title: t?.dashboard?.stats?.documents || \"文档\",\n                                value: \"1,234\",\n                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                color: \"from-blue-500 to-blue-600\"\n                            },\n                            {\n                                title: t?.dashboard?.stats?.queries || \"查询\",\n                                value: \"5,678\",\n                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                color: \"from-green-500 to-green-600\"\n                            },\n                            {\n                                title: t?.dashboard?.stats?.users || \"用户\",\n                                value: \"89\",\n                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                color: \"from-purple-500 to-purple-600\"\n                            },\n                            {\n                                title: t?.dashboard?.stats?.settings || \"设置\",\n                                value: \"12\",\n                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                color: \"from-orange-500 to-orange-600\"\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: \"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                whileHover: {\n                                    scale: 1.02,\n                                    y: -2\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm font-medium\",\n                                                    children: stat.title\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `p-3 rounded-lg bg-gradient-to-r ${stat.color}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                onClick: ()=>router.push(\"/file-manager\"),\n                                className: \"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer group\",\n                                whileHover: {\n                                    scale: 1.02,\n                                    y: -2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-blue-500/10 rounded-xl group-hover:bg-blue-500/20 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: t?.navigation?.documents || \"文件管理\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: t?.dashboard?.fileManager?.description || \"上传、组织和管理您的文件库\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t?.dashboard?.fileManager?.stats || \"125 个文件\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                onClick: ()=>router.push(\"/knowledge\"),\n                                className: \"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer group\",\n                                whileHover: {\n                                    scale: 1.02,\n                                    y: -2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-green-500/10 rounded-xl group-hover:bg-green-500/20 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: t?.navigation?.knowledge || \"知识库\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: t?.dashboard?.knowledge?.description || \"构建和查询您的知识库\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t?.dashboard?.knowledge?.stats || \"89 个条目\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                onClick: ()=>router.push(\"/search\"),\n                                className: \"bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer group\",\n                                whileHover: {\n                                    scale: 1.02,\n                                    y: -2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-purple-500/10 rounded-xl group-hover:bg-purple-500/20 transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: t?.navigation?.search || \"智能搜索\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: t?.dashboard?.search?.description || \"使用AI进行智能内容搜索\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-purple-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t?.dashboard?.search?.stats || \"1,234 次查询\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 border border-gray-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t?.dashboard?.recentActivity || \"最近活动\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            {\n                                                action: t?.dashboard?.activities?.documentUploaded || \"文档已上传\",\n                                                time: \"2 分钟前\",\n                                                type: \"upload\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                            },\n                                            {\n                                                action: t?.dashboard?.activities?.queryProcessed || \"查询已处理\",\n                                                time: \"5 分钟前\",\n                                                type: \"query\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                                            },\n                                            {\n                                                action: t?.dashboard?.activities?.userLoggedIn || \"用户已登录\",\n                                                time: \"10 分钟前\",\n                                                type: \"login\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                                            },\n                                            {\n                                                action: t?.dashboard?.activities?.settingsUpdated || \"设置已更新\",\n                                                time: \"1 小时前\",\n                                                type: \"settings\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                            }\n                                        ].map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(activity.icon, {\n                                                            className: \"w-4 h-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 text-sm font-medium\",\n                                                                children: activity.action\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-xs\",\n                                                                children: activity.time\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl p-6 border border-gray-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: t?.dashboard?.quickActions || \"快速操作\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            {\n                                                title: t?.dashboard?.actions?.uploadDocument || \"上传文档\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                color: \"from-blue-500 to-blue-600\",\n                                                onClick: ()=>router.push(\"/file-manager\")\n                                            },\n                                            {\n                                                title: t?.dashboard?.actions?.runQuery || \"运行查询\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                color: \"from-green-500 to-green-600\",\n                                                onClick: ()=>router.push(\"/search\")\n                                            },\n                                            {\n                                                title: t?.dashboard?.actions?.manageUsers || \"管理用户\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                color: \"from-purple-500 to-purple-600\",\n                                                onClick: ()=>router.push(\"/settings\")\n                                            },\n                                            {\n                                                title: t?.dashboard?.actions?.systemSettings || \"系统设置\",\n                                                icon: _barrel_optimize_names_BarChart3_Database_FileText_FolderOpen_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                color: \"from-orange-500 to-orange-600\",\n                                                onClick: ()=>router.push(\"/settings\")\n                                            }\n                                        ].map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                                onClick: action.onClick,\n                                                className: `p-4 rounded-lg bg-gradient-to-r ${action.color} text-white font-medium text-sm flex flex-col items-center space-y-2 hover:shadow-lg transition-all duration-200`,\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: action.title\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        className: \"mt-8 bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: \"User Information\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Username\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-medium\",\n                                                children: user?.username\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-medium\",\n                                                children: user?.email\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-medium\",\n                                                children: user?.is_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-400\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400\",\n                                                    children: \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Member Since\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-medium\",\n                                                children: user?.created_at ? new Date(user.created_at).toLocaleDateString() : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/LanguageSwitcher.tsx":
/*!*****************************************!*\
  !*** ./components/LanguageSwitcher.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst LanguageSwitcher = ()=>{\n    const { language, setLanguage, t, isLoading } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 如果翻译还未加载，显示加载状态\n    if (isLoading || !t) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: \"sm\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined);\n    }\n    const languages = [\n        {\n            code: \"zh-CN\",\n            name: t.language.chinese,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"zh-CN\"].flag\n        },\n        {\n            code: \"zh-TW\",\n            name: t.language.traditionalChinese,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"zh-TW\"].flag\n        },\n        {\n            code: \"en\",\n            name: t.language.english,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"en\"].flag\n        },\n        {\n            code: \"ja\",\n            name: t.language.japanese,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"ja\"].flag\n        }\n    ];\n    const currentLanguage = languages.find((lang)=>lang.code === language);\n    const handleLanguageChange = (newLanguage)=>{\n        setLanguage(newLanguage);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-200\",\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: [\n                            currentLanguage?.flag,\n                            \" \",\n                            currentLanguage?.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        animate: {\n                            rotate: isOpen ? 180 : 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 14\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            className: \"fixed inset-0 z-40\",\n                            onClick: ()=>setIsOpen(false)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10,\n                                scale: 0.95\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -10,\n                                scale: 0.95\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            className: \"absolute top-full mt-2 right-0 z-50 min-w-[200px] bg-white/95 backdrop-blur-md rounded-xl border border-white/20 shadow-2xl overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-2\",\n                                children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: ()=>handleLanguageChange(lang.code),\n                                        className: `w-full px-4 py-3 text-left flex items-center space-x-3 hover:bg-blue-50 transition-colors duration-150 ${language === lang.code ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-700\"}`,\n                                        whileHover: {\n                                            x: 4\n                                        },\n                                        transition: {\n                                            duration: 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: lang.flag\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: lang.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            language === lang.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                className: \"ml-auto w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, lang.code, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Layout/MainLayout.tsx":
/*!******************************************!*\
  !*** ./components/Layout/MainLayout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,Database,FolderOpen,Home,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LanguageSwitcher */ \"(ssr)/./components/LanguageSwitcher.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst MainLayout = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t, isLoading: langLoading } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    // 导航菜单项\n    const navigationItems = [\n        {\n            name: t?.navigation?.dashboard || \"仪表板\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            current: pathname === \"/dashboard\"\n        },\n        {\n            name: t?.navigation?.documents || \"文件管理\",\n            href: \"/file-manager\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            current: pathname === \"/file-manager\"\n        },\n        {\n            name: t?.navigation?.knowledge || \"知识库\",\n            href: \"/knowledge-base\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            current: pathname.startsWith(\"/knowledge-base\")\n        },\n        {\n            name: t?.navigation?.search || \"智能搜索\",\n            href: \"/search\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            current: pathname === \"/search\"\n        },\n        {\n            name: t?.navigation?.analytics || \"数据分析\",\n            href: \"/analytics\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            current: pathname === \"/analytics\"\n        },\n        {\n            name: t?.navigation?.settings || \"系统设置\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            current: pathname === \"/settings\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUser = async ()=>{\n            try {\n                const userData = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.getCurrentUser)();\n                setUser(userData);\n            } catch (error) {\n                console.error(\"Failed to fetch user:\", error);\n                router.push(\"/login\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUser();\n    }, [\n        router\n    ]);\n    const handleLogout = async ()=>{\n        try {\n            await (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.logout)();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"退出成功\");\n            router.push(\"/login\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"退出失败\");\n        }\n    };\n    const handleNavigation = (href)=>{\n        router.push(href);\n        setSidebarOpen(false);\n    };\n    if (loading || langLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                animate: {\n                    rotate: 360\n                },\n                transition: {\n                    duration: 1,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                },\n                className: \"w-8 h-8 border-2 border-white border-t-transparent rounded-full\"\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-gray-200 fixed w-full top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-full mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                        className: \"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                        children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 32\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 60\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-8 h-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"AI知识库\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-1\",\n                                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNavigation(item.href),\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${item.current ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden sm:flex items-center space-x-2 text-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: user?.username\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleLogout,\n                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg bg-red-50 text-red-600 hover:bg-red-100 transition-colors duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: \"退出\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, undefined),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-40\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            x: -300\n                        },\n                        animate: {\n                            x: 0\n                        },\n                        exit: {\n                            x: -300\n                        },\n                        className: \"fixed left-0 top-0 bottom-0 w-64 bg-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_Database_FolderOpen_Home_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-8 h-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"AI知识库\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"p-4 space-y-2\",\n                                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleNavigation(item.href),\n                                        className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${item.current ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-16\",\n                children: children\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\Layout\\\\MainLayout.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Layout/MainLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/LoadingSpinner.tsx":
/*!***************************************!*\
  !*** ./components/LoadingSpinner.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst LoadingSpinner = ({ size = \"md\", className = \"\", text })=>{\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-8 h-8\",\n        lg: \"w-12 h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col items-center justify-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                animate: {\n                    rotate: 360\n                },\n                transition: {\n                    duration: 1,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                },\n                className: `${sizeClasses[size]} border-2 border-white border-t-transparent rounded-full`\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoadingSpinner.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 0.5\n                },\n                className: \"mt-2 text-sm text-white/80\",\n                children: text\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoadingSpinner.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n    const [t, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化语言设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeLanguage = async ()=>{\n            setIsLoading(true);\n            try {\n                // 预加载所有语言\n                await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.preloadAllLanguages)();\n                // 从localStorage加载语言设置\n                const savedLanguage = localStorage.getItem(\"language\");\n                let targetLanguage;\n                if (savedLanguage && [\n                    \"zh-CN\",\n                    \"zh-TW\",\n                    \"en\",\n                    \"ja\"\n                ].includes(savedLanguage)) {\n                    targetLanguage = savedLanguage;\n                } else {\n                    // 检测浏览器语言\n                    targetLanguage = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.detectBrowserLanguage)();\n                }\n                setLanguageState(targetLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(targetLanguage);\n                setTranslations(translations);\n            } catch (error) {\n                console.error(\"Failed to initialize language:\", error);\n                // 回退到默认语言\n                setLanguageState(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                setTranslations(translations);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeLanguage();\n    }, []);\n    const setLanguage = async (newLanguage)=>{\n        setIsLoading(true);\n        try {\n            setLanguageState(newLanguage);\n            const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(newLanguage);\n            setTranslations(translations);\n            localStorage.setItem(\"language\", newLanguage);\n        } catch (error) {\n            console.error(\"Failed to set language:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage,\n            t,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkHealth: () => (/* binding */ checkHealth),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storageApi: () => (/* binding */ storageApi),\n/* harmony export */   testCors: () => (/* binding */ testCors)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API客户端配置\n * 与后端FastAPI接口对接\n */ \n\n// API基础配置\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    },\n    withCredentials: false\n});\n// 添加调试日志\nconsole.log(\"API Client initialized with base URL:\", API_BASE_URL);\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 添加认证token\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    // 添加请求ID用于追踪\n    config.headers[\"X-Request-ID\"] = generateRequestId();\n    // 调试日志\n    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    console.log(`   Base URL: ${config.baseURL}`);\n    console.log(`   Full URL: ${config.baseURL}${config.url}`);\n    console.log(`   Timeout: ${config.timeout}ms`);\n    return config;\n}, (error)=>{\n    console.error(\"❌ Request interceptor error:\", error);\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);\n    return response;\n}, async (error)=>{\n    console.error(`❌ API Error: ${error.code} ${error.config?.method?.toUpperCase()} ${error.config?.url}`);\n    console.error(`   Message: ${error.message}`);\n    console.error(`   Status: ${error.response?.status}`);\n    const originalRequest = error.config;\n    // 处理401未授权错误\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        // 尝试刷新token\n        const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n        if (refreshToken) {\n            try {\n                const response = await refreshAccessToken(refreshToken);\n                const newToken = response.data.access_token;\n                // 更新token\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", newToken, {\n                    expires: 1\n                });\n                // 重试原请求\n                originalRequest.headers.Authorization = `Bearer ${newToken}`;\n                return apiClient(originalRequest);\n            } catch (refreshError) {\n                // 刷新失败，清除token并跳转到登录页\n                clearAuthTokens();\n                window.location.href = \"/login\";\n                return Promise.reject(refreshError);\n            }\n        } else {\n            // 没有刷新token，直接跳转到登录页\n            clearAuthTokens();\n            window.location.href = \"/login\";\n        }\n    }\n    return Promise.reject(error);\n});\n// 生成请求ID\nfunction generateRequestId() {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n// 清除认证token\nfunction clearAuthTokens() {\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n}\n// 刷新访问token\nasync function refreshAccessToken(refreshToken) {\n    return axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/api/v1/auth/refresh`, {\n        refresh_token: refreshToken\n    });\n}\n// 登录\nconst login = async (credentials)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Attempting login with:\", {\n            username: credentials.username\n        });\n        console.log(\"\\uD83C\\uDF10 API Base URL:\", API_BASE_URL);\n        console.log(\"\\uD83D\\uDCE1 Full login URL:\", `${API_BASE_URL}/api/v1/auth/login`);\n        const response = await apiClient.post(\"/api/v1/auth/login\", credentials);\n        console.log(\"✅ Login response:\", response.data);\n        // 保存token到cookie\n        const { access_token, refresh_token, expires_in } = response.data;\n        // 设置cookie过期时间（转换为天数）\n        const expiresInDays = expires_in / (60 * 60 * 24);\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access_token, {\n            expires: expiresInDays\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", refresh_token, {\n            expires: 7\n        }); // 刷新token保存7天\n        // 同时保存到localStorage作为备份\n        localStorage.setItem(\"token\", access_token);\n        localStorage.setItem(\"refresh_token\", refresh_token);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ Login error details:\", error);\n        console.error(\"   Error code:\", error.code);\n        console.error(\"   Error message:\", error.message);\n        console.error(\"   Error response:\", error.response?.data);\n        console.error(\"   Error status:\", error.response?.status);\n        console.error(\"   Request config:\", error.config);\n        // 处理不同类型的错误响应\n        if (error.code === \"ECONNABORTED\") {\n            throw new Error(`请求超时，请检查网络连接或稍后重试。服务器地址：${API_BASE_URL}`);\n        } else if (error.response?.data?.detail) {\n            throw new Error(error.response.data.detail);\n        } else if (error.response?.data?.error?.message) {\n            throw new Error(error.response.data.error.message);\n        } else if (error.response?.status === 401) {\n            throw new Error(\"用户名或密码错误\");\n        } else if (error.response?.status >= 500) {\n            throw new Error(\"服务器错误，请稍后重试\");\n        } else if (error.code === \"ECONNREFUSED\" || error.message.includes(\"Network Error\")) {\n            throw new Error(`无法连接到服务器，请检查网络连接。服务器地址：${API_BASE_URL}`);\n        } else {\n            throw new Error(error.message || \"Login failed\");\n        }\n    }\n};\n// 登出\nconst logout = async ()=>{\n    try {\n        await apiClient.post(\"/api/v1/auth/logout\");\n    } catch (error) {\n        // 即使后端登出失败，也要清除本地token\n        console.error(\"Logout error:\", error);\n    } finally{\n        clearAuthTokens();\n    }\n};\n// 获取当前用户信息\nconst getCurrentUser = async ()=>{\n    try {\n        const response = await apiClient.get(\"/api/v1/auth/me\");\n        return response.data;\n    } catch (error) {\n        const apiError = error.response?.data;\n        throw new Error(apiError?.error?.message || \"Failed to get user info\");\n    }\n};\n// 检查健康状态\nconst checkHealth = async ()=>{\n    try {\n        const response = await apiClient.get(\"/health\");\n        return response.data;\n    } catch (error) {\n        throw new Error(\"API health check failed\");\n    }\n};\n// CORS测试\nconst testCors = async ()=>{\n    try {\n        console.log(\"\\uD83E\\uDDEA Testing CORS configuration...\");\n        const response = await apiClient.get(\"/api/v1/auth/test-cors\");\n        console.log(\"✅ CORS test successful:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ CORS test failed:\", error);\n        throw error;\n    }\n};\n// 存储管理API\nconst storageApi = {\n    // 获取存储列表\n    getStorages: async ()=>{\n        const response = await apiClient.get(\"/api/v1/storage/\");\n        return response.data;\n    },\n    // 创建存储\n    createStorage: async (data)=>{\n        const response = await apiClient.post(\"/api/v1/storage/\", data);\n        return response.data;\n    },\n    // 测试存储连接\n    testStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/test`);\n        return response.data;\n    },\n    // 删除存储\n    deleteStorage: async (storageId)=>{\n        const response = await apiClient.delete(`/api/v1/storage/${storageId}`);\n        return response.data;\n    },\n    // 同步存储\n    syncStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/sync`);\n        return response.data;\n    }\n};\n// 检查token是否有效\nconst isAuthenticated = ()=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    return !!token;\n};\n// 获取token\nconst getAccessToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatDate),\n/* harmony export */   formatNumber: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   getTranslation: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslation),\n/* harmony export */   getTranslationSync: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslationSync),\n/* harmony export */   languageConfig: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.preloadAllLanguages)\n/* harmony export */ });\n/* harmony import */ var _i18n_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./i18n/index */ \"(ssr)/./lib/i18n/index.ts\");\n/**\n * 多语言支持 - 兼容性导出文件\n * 重新导出新架构中的所有功能\n */ // 重新导出所有类型和函数\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFRCxjQUFjO0FBWVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9saWIvaTE4bi50cz80OWFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5aSa6K+t6KiA5pSv5oyBIC0g5YW85a655oCn5a+85Ye65paH5Lu2XG4gKiDph43mlrDlr7zlh7rmlrDmnrbmnoTkuK3nmoTmiYDmnInlip/og71cbiAqL1xuXG4vLyDph43mlrDlr7zlh7rmiYDmnInnsbvlnovlkozlh73mlbBcbmV4cG9ydCB0eXBlIHsgTGFuZ3VhZ2UsIFRyYW5zbGF0aW9ucyB9IGZyb20gJy4vaTE4bi9pbmRleCc7XG5cbmV4cG9ydCB7XG4gIGRlZmF1bHRMYW5ndWFnZSxcbiAgbGFuZ3VhZ2VDb25maWcsXG4gIGdldFRyYW5zbGF0aW9uLFxuICBnZXRUcmFuc2xhdGlvblN5bmMsXG4gIHByZWxvYWRBbGxMYW5ndWFnZXMsXG4gIGRldGVjdEJyb3dzZXJMYW5ndWFnZSxcbiAgZm9ybWF0TnVtYmVyLFxuICBmb3JtYXREYXRlXG59IGZyb20gJy4vaTE4bi9pbmRleCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VDb25maWciLCJnZXRUcmFuc2xhdGlvbiIsImdldFRyYW5zbGF0aW9uU3luYyIsInByZWxvYWRBbGxMYW5ndWFnZXMiLCJkZXRlY3RCcm93c2VyTGFuZ3VhZ2UiLCJmb3JtYXROdW1iZXIiLCJmb3JtYXREYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/index.ts":
/*!***************************!*\
  !*** ./lib/i18n/index.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* binding */ detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   getTranslationSync: () => (/* binding */ getTranslationSync),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* binding */ preloadAllLanguages)\n/* harmony export */ });\n/**\n * 国际化配置主文件\n * 支持动态加载语言文件\n */ const defaultLanguage = \"zh-CN\";\n// 语言配置\nconst languageConfig = {\n    \"zh-CN\": {\n        name: \"中文简体\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        direction: \"ltr\"\n    },\n    \"zh-TW\": {\n        name: \"中文繁體\",\n        flag: \"\\uD83C\\uDDF9\\uD83C\\uDDFC\",\n        direction: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        direction: \"ltr\"\n    },\n    \"ja\": {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        direction: \"ltr\"\n    }\n};\n// 动态导入语言文件\nconst loadTranslations = async (language)=>{\n    try {\n        const module = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${language}.ts`);\n        return module.default;\n    } catch (error) {\n        console.warn(`Failed to load translations for ${language}, falling back to default`);\n        const defaultModule = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${defaultLanguage}.ts`);\n        return defaultModule.default;\n    }\n};\n// 缓存已加载的翻译\nconst translationCache = new Map();\nconst getTranslation = async (language)=>{\n    if (translationCache.has(language)) {\n        return translationCache.get(language);\n    }\n    const translations = await loadTranslations(language);\n    translationCache.set(language, translations);\n    return translations;\n};\n// 同步获取翻译（用于已缓存的情况）\nconst getTranslationSync = (language)=>{\n    return translationCache.get(language) || null;\n};\n// 预加载所有语言\nconst preloadAllLanguages = async ()=>{\n    const languages = [\n        \"zh-CN\",\n        \"zh-TW\",\n        \"en\",\n        \"ja\"\n    ];\n    await Promise.all(languages.map(async (lang)=>{\n        try {\n            await getTranslation(lang);\n        } catch (error) {\n            console.warn(`Failed to preload language ${lang}:`, error);\n        }\n    }));\n};\n// 检测浏览器语言\nconst detectBrowserLanguage = ()=>{\n    if (true) {\n        return defaultLanguage;\n    }\n    const browserLanguage = navigator.language || navigator.languages?.[0];\n    if (browserLanguage?.startsWith(\"zh-CN\") || browserLanguage === \"zh\") {\n        return \"zh-CN\";\n    } else if (browserLanguage?.startsWith(\"zh-TW\") || browserLanguage === \"zh-Hant\") {\n        return \"zh-TW\";\n    } else if (browserLanguage?.startsWith(\"en\")) {\n        return \"en\";\n    } else if (browserLanguage?.startsWith(\"ja\")) {\n        return \"ja\";\n    }\n    return defaultLanguage;\n};\n// 格式化数字（根据语言环境）\nconst formatNumber = (number, language)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    return new Intl.NumberFormat(localeMap[language]).format(number);\n};\n// 格式化日期（根据语言环境）\nconst formatDate = (date, language, options)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(localeMap[language], options || defaultOptions).format(date);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/index.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0cc9297c17c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZ2xvYmFscy5jc3M/YWJhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBjYzkyOTdjMTdjNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI知识库 - 智能文档管理平台\",\n    description: \"基于AI技术的智能文档管理和知识库系统\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#4ade80\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#useLanguage`);


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZmF2aWNvbi5pY28/ZjUyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736","vendor-chunks/mime-db@1.52.0","vendor-chunks/axios@1.9.0","vendor-chunks/lucide-react@0.300.0_react@18.3.1","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/debug@4.4.1","vendor-chunks/form-data@4.0.3","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/asynckit@0.4.0","vendor-chunks/combined-stream@1.0.8","vendor-chunks/mime-types@2.1.35","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-symbols@1.1.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/es-set-tostringtag@2.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/es-errors@1.3.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/has-tostringtag@1.0.2","vendor-chunks/es-object-atoms@1.1.1","vendor-chunks/js-cookie@3.0.5"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();