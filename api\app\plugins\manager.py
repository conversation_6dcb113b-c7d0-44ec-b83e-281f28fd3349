"""
插件管理器
负责插件的加载、注册和生命周期管理
"""

import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Type, Any, Optional
from fastapi import FastAPI
from loguru import logger

from app.core.config import Settings
from app.plugins.base import BasePlugin, PluginConfig, PluginMetadata


class PluginManager:
    """插件管理器"""
    
    def __init__(self, settings: Settings):
        """
        初始化插件管理器
        
        Args:
            settings: 应用配置
        """
        self.settings = settings
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_configs: Dict[str, PluginConfig] = {}
        self._load_plugin_configs()
    
    def _load_plugin_configs(self) -> None:
        """加载插件配置"""
        # TODO: 从配置文件或数据库加载插件配置
        # 这里使用默认配置
        enabled_plugins = self.settings.enabled_plugins_list
        
        for plugin_name in enabled_plugins:
            self.plugin_configs[plugin_name] = PluginConfig(
                enabled=True,
                settings={}
            )
    
    def load_plugins(self) -> None:
        """加载所有插件"""
        plugins_dir = Path(__file__).parent
        
        # 扫描插件目录
        for plugin_path in plugins_dir.iterdir():
            if plugin_path.is_dir() and not plugin_path.name.startswith('_'):
                plugin_name = plugin_path.name
                
                # 检查插件是否在启用列表中
                if plugin_name not in self.plugin_configs:
                    continue
                
                try:
                    self._load_plugin(plugin_name, plugin_path)
                except Exception as e:
                    logger.error(f"Failed to load plugin {plugin_name}: {e}")
    
    def _load_plugin(self, plugin_name: str, plugin_path: Path) -> None:
        """
        加载单个插件
        
        Args:
            plugin_name: 插件名称
            plugin_path: 插件路径
        """
        # 导入插件模块
        module_name = f"app.plugins.{plugin_name}.plugin"
        
        try:
            module = importlib.import_module(module_name)
        except ImportError as e:
            logger.error(f"Failed to import plugin module {module_name}: {e}")
            return
        
        # 查找插件类
        plugin_class = None
        for name, obj in inspect.getmembers(module):
            if (inspect.isclass(obj) and 
                issubclass(obj, BasePlugin) and 
                obj != BasePlugin):
                plugin_class = obj
                break
        
        if not plugin_class:
            logger.error(f"No plugin class found in {module_name}")
            return
        
        # 创建插件实例
        config = self.plugin_configs.get(plugin_name, PluginConfig())
        plugin_instance = plugin_class(config)
        
        # 验证插件元数据
        metadata = plugin_instance.metadata
        if not self._validate_plugin_metadata(metadata):
            logger.error(f"Invalid plugin metadata for {plugin_name}")
            return
        
        self.plugins[plugin_name] = plugin_instance
        logger.info(f"Loaded plugin: {plugin_name} v{metadata.version}")
    
    def _validate_plugin_metadata(self, metadata: PluginMetadata) -> bool:
        """
        验证插件元数据
        
        Args:
            metadata: 插件元数据
            
        Returns:
            是否有效
        """
        # TODO: 实现版本兼容性检查
        return True
    
    async def initialize_plugins(self) -> None:
        """初始化所有插件"""
        for plugin_name, plugin in self.plugins.items():
            if not plugin.is_enabled:
                continue
            
            try:
                await plugin._initialize(self.settings)
                logger.info(f"Initialized plugin: {plugin_name}")
            except Exception as e:
                logger.error(f"Failed to initialize plugin {plugin_name}: {e}")
    
    def register_plugins(self, app: FastAPI) -> None:
        """
        注册插件到FastAPI应用
        
        Args:
            app: FastAPI应用实例
        """
        for plugin_name, plugin in self.plugins.items():
            if not plugin.is_enabled or not plugin.is_initialized:
                continue
            
            try:
                # 注册路由
                plugin.register_routes(app)
                
                # 注册中间件
                plugin.register_middleware(app)
                
                # 注册依赖
                plugin.register_dependencies(app)
                
                logger.info(f"Registered plugin: {plugin_name}")
            except Exception as e:
                logger.error(f"Failed to register plugin {plugin_name}: {e}")
    
    async def startup(self) -> None:
        """启动所有插件"""
        # 首先初始化插件
        await self.initialize_plugins()
        
        # 然后启动插件
        for plugin_name, plugin in self.plugins.items():
            if not plugin.is_enabled or not plugin.is_initialized:
                continue
            
            try:
                await plugin._startup()
                logger.info(f"Started plugin: {plugin_name}")
            except Exception as e:
                logger.error(f"Failed to start plugin {plugin_name}: {e}")
    
    async def shutdown(self) -> None:
        """关闭所有插件"""
        for plugin_name, plugin in self.plugins.items():
            if not plugin.is_started:
                continue
            
            try:
                await plugin._shutdown()
                logger.info(f"Shutdown plugin: {plugin_name}")
            except Exception as e:
                logger.error(f"Failed to shutdown plugin {plugin_name}: {e}")
    
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """
        获取插件实例
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            插件实例
        """
        return self.plugins.get(plugin_name)
    
    def get_enabled_plugins(self) -> List[BasePlugin]:
        """
        获取启用的插件列表
        
        Returns:
            启用的插件列表
        """
        return [plugin for plugin in self.plugins.values() if plugin.is_enabled]
    
    def get_plugin_info(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """
        获取插件信息
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            插件信息
        """
        plugin = self.plugins.get(plugin_name)
        if not plugin:
            return None
        
        metadata = plugin.metadata
        return {
            "name": metadata.name,
            "version": metadata.version,
            "description": metadata.description,
            "author": metadata.author,
            "dependencies": metadata.dependencies,
            "enabled": plugin.is_enabled,
            "initialized": plugin.is_initialized,
            "started": plugin.is_started,
        }
    
    async def get_plugins_health(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有插件的健康状态
        
        Returns:
            插件健康状态字典
        """
        health_status = {}
        
        for plugin_name, plugin in self.plugins.items():
            try:
                health_status[plugin_name] = await plugin.health_check()
            except Exception as e:
                health_status[plugin_name] = {
                    "name": plugin_name,
                    "status": "error",
                    "error": str(e)
                }
        
        return health_status
