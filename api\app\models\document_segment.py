"""
文档分段数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, DateTime, Boolean, BigInteger, Text, Enum as SQLEnum, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel, get_text_type, get_json_type


class SegmentMethod(str, Enum):
    """分段方法枚举"""
    PARAGRAPH = "paragraph"  # 按段落分段
    SENTENCE = "sentence"    # 按句子分段
    FIXED_LENGTH = "fixed_length"  # 固定长度分段
    SEMANTIC = "semantic"    # 语义分段


class SegmentStatus(str, Enum):
    """分段状态枚举"""
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败


class VectorizeStatus(str, Enum):
    """向量化状态枚举"""
    PENDING = "pending"      # 待向量化
    PROCESSING = "processing"  # 向量化中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败


class DocumentSegmentTask(BaseModel):
    """文档分段任务表"""
    __tablename__ = "document_segment_tasks"
    
    # 基本信息
    task_id = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()), comment="任务唯一ID")
    task_name = Column(String(200), nullable=False, comment="任务名称")
    description = Column(get_text_type(500), comment="任务描述")
    
    # 文件信息
    file_ids = Column(get_json_type(), nullable=False, comment="文件ID列表")
    total_files = Column(Integer, default=0, comment="总文件数")
    processed_files = Column(Integer, default=0, comment="已处理文件数")
    
    # 分段配置
    segment_method = Column(SQLEnum(SegmentMethod), nullable=False, comment="分段方法")
    max_length = Column(Integer, default=500, comment="最大长度(字符)")
    overlap = Column(Integer, default=50, comment="重叠长度(字符)")
    preserve_formatting = Column(Boolean, default=True, comment="是否保留格式")
    
    # 向量化配置
    enable_vectorization = Column(Boolean, default=True, comment="是否启用向量化")
    embedding_model = Column(String(100), default="text-embedding-ada-002", comment="嵌入模型")
    vector_dimension = Column(Integer, default=1536, comment="向量维度")
    chunk_size = Column(Integer, default=1000, comment="向量化块大小")
    
    # 高级配置
    language = Column(String(10), default="zh", comment="文档语言")
    remove_stopwords = Column(Boolean, default=False, comment="是否移除停用词")
    normalize_text = Column(Boolean, default=True, comment="是否标准化文本")
    extract_keywords = Column(Boolean, default=True, comment="是否提取关键词")
    
    # 任务状态
    status = Column(SQLEnum(SegmentStatus), default=SegmentStatus.PENDING, comment="任务状态")
    progress = Column(Float, default=0.0, comment="进度百分比")
    error_message = Column(Text, comment="错误信息")
    
    # 统计信息
    total_segments = Column(Integer, default=0, comment="总分段数")
    total_vectors = Column(Integer, default=0, comment="总向量数")

    # 元数据
    segment_metadata = Column(get_json_type(), comment="分段元数据")

    # 时间信息
    started_at = Column(DateTime(timezone=True), comment="开始时间")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
    
    # 注释：移除了relationship关系，避免外键约束
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        
        # 添加计算字段
        if self.started_at and self.completed_at:
            duration = (self.completed_at - self.started_at).total_seconds()
            result['duration_seconds'] = duration
            result['duration_formatted'] = self.format_duration(duration)
        
        # 计算成功率
        if self.total_files > 0:
            result['success_rate'] = (self.processed_files / self.total_files) * 100
        else:
            result['success_rate'] = 0
            
        return result
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化持续时间"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"


class DocumentSegment(BaseModel):
    """文档分段表"""
    __tablename__ = "document_segments"
    
    # 基本信息
    segment_id = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()), comment="分段唯一ID")
    task_id = Column(Integer, nullable=False, comment="任务ID")  # 移除外键约束
    file_id = Column(String(36), nullable=False, comment="文件ID")
    
    # 分段内容
    content = Column(Text, nullable=False, comment="分段内容")
    content_hash = Column(String(64), comment="内容哈希值")
    
    # 位置信息
    segment_index = Column(Integer, nullable=False, comment="分段索引")
    start_position = Column(Integer, default=0, comment="开始位置")
    end_position = Column(Integer, default=0, comment="结束位置")
    
    # 统计信息
    word_count = Column(Integer, default=0, comment="字符数")
    sentence_count = Column(Integer, default=0, comment="句子数")
    
    # 元数据
    segment_metadata = Column(get_json_type(), comment="分段元数据")
    keywords = Column(get_json_type(), comment="关键词列表")
    
    # 向量化信息
    vectorize_status = Column(SQLEnum(VectorizeStatus), default=VectorizeStatus.PENDING, comment="向量化状态")
    vector_id = Column(String(100), comment="向量ID")
    embedding_vector = Column(get_json_type(), comment="嵌入向量")
    
    # 质量评分
    quality_score = Column(Float, default=0.0, comment="质量评分")
    readability_score = Column(Float, default=0.0, comment="可读性评分")
    
    # 注释：移除了relationship关系，避免外键约束
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        
        # 添加计算字段
        result['content_preview'] = self.content[:100] + "..." if len(self.content) > 100 else self.content
        result['has_vector'] = bool(self.embedding_vector)
        
        return result


class SegmentTemplate(BaseModel):
    """分段模板表"""
    __tablename__ = "segment_templates"
    
    # 基本信息
    template_name = Column(String(100), unique=True, nullable=False, comment="模板名称")
    description = Column(get_text_type(500), comment="模板描述")
    
    # 分段配置
    segment_method = Column(SQLEnum(SegmentMethod), nullable=False, comment="分段方法")
    max_length = Column(Integer, default=500, comment="最大长度(字符)")
    overlap = Column(Integer, default=50, comment="重叠长度(字符)")
    preserve_formatting = Column(Boolean, default=True, comment="是否保留格式")
    
    # 向量化配置
    enable_vectorization = Column(Boolean, default=True, comment="是否启用向量化")
    embedding_model = Column(String(100), default="text-embedding-ada-002", comment="嵌入模型")
    vector_dimension = Column(Integer, default=1536, comment="向量维度")
    chunk_size = Column(Integer, default=1000, comment="向量化块大小")
    
    # 高级配置
    language = Column(String(10), default="zh", comment="文档语言")
    remove_stopwords = Column(Boolean, default=False, comment="是否移除停用词")
    normalize_text = Column(Boolean, default=True, comment="是否标准化文本")
    extract_keywords = Column(Boolean, default=True, comment="是否提取关键词")
    
    # 模板属性
    is_default = Column(Boolean, default=False, comment="是否为默认模板")
    is_system = Column(Boolean, default=False, comment="是否为系统模板")
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        return result


class VectorIndex(BaseModel):
    """向量索引表"""
    __tablename__ = "vector_indexes"
    
    # 基本信息
    index_name = Column(String(100), unique=True, nullable=False, comment="索引名称")
    description = Column(get_text_type(500), comment="索引描述")
    
    # 索引配置
    embedding_model = Column(String(100), nullable=False, comment="嵌入模型")
    vector_dimension = Column(Integer, nullable=False, comment="向量维度")
    similarity_metric = Column(String(20), default="cosine", comment="相似度度量")
    
    # 统计信息
    total_vectors = Column(Integer, default=0, comment="总向量数")
    total_documents = Column(Integer, default=0, comment="总文档数")
    index_size_mb = Column(Float, default=0.0, comment="索引大小(MB)")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    last_updated_at = Column(DateTime(timezone=True), comment="最后更新时间")
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        
        # 添加计算字段
        result['index_size_formatted'] = f"{self.index_size_mb:.2f} MB"
        
        return result
