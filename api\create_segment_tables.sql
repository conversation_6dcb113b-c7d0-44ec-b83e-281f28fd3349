-- 创建文档分段相关数据库表

-- 创建文档分段任务表
CREATE TABLE IF NOT EXISTS document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    file_ids JSONB NOT NULL,
    total_files INTEGER DEFAULT 0,
    processed_files INTEGER DEFAULT 0,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'pending',
    progress FLOAT DEFAULT 0.0,
    error_message TEXT,
    total_segments INTEGER DEFAULT 0,
    total_vectors INTEGER DEFAULT 0,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档分段表
CREATE TABLE IF NOT EXISTS document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id VARCHAR(36) NOT NULL,
    file_id VARCHAR(500) NOT NULL,
    file_name VARCHAR(500),
    segment_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    char_count INTEGER DEFAULT 0,
    vectorize_status VARCHAR(20) DEFAULT 'pending',
    embedding_vector JSONB,
    keywords JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建分段模板表
CREATE TABLE IF NOT EXISTS segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(200) UNIQUE NOT NULL,
    description TEXT,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量索引表
CREATE TABLE IF NOT EXISTS vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(200) NOT NULL,
    description TEXT,
    embedding_model VARCHAR(100) NOT NULL,
    vector_dimension INTEGER NOT NULL,
    total_vectors INTEGER DEFAULT 0,
    index_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);

-- 插入默认分段模板
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
(
    '通用文档分段', '适用于大多数文档的通用分段配置', 'paragraph', 500, 50,
    true, true, 'text-embedding-ada-002', 1536,
    1000, 'zh', false, true, true,
    true, true
),
(
    '长文档分段', '适用于长文档的分段配置，较大的分段长度', 'paragraph', 1000, 100,
    true, true, 'text-embedding-ada-002', 1536,
    1500, 'zh', false, true, true,
    false, true
),
(
    '精细分段', '适用于需要精细分段的文档，较小的分段长度', 'sentence', 200, 20,
    true, true, 'text-embedding-ada-002', 1536,
    500, 'zh', true, true, true,
    false, true
)
ON CONFLICT (template_name) DO NOTHING;
