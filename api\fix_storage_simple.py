#!/usr/bin/env python3
"""
简化的存储配置修复脚本
"""
import asyncio
import asyncpg
import os
from loguru import logger


async def fix_storage_simple():
    """简化修复存储配置"""
    
    # 从环境变量获取数据库连接信息
    db_host = os.getenv('DB_HOST', '**************')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'xhc_rag')
    db_user = os.getenv('DB_USERNAME', 'postgres')
    db_password = os.getenv('DB_PASSWORD', 'XHC12345')
    
    # 构建连接字符串
    dsn = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(dsn)
        logger.info("数据库连接成功")
        
        # 删除现有表（如果存在）
        await conn.execute("DROP TABLE IF EXISTS storage_configs CASCADE;")
        logger.info("删除旧表成功")
        
        # 删除枚举类型（如果存在）
        await conn.execute("DROP TYPE IF EXISTS storagetype CASCADE;")
        logger.info("删除旧枚举类型成功")
        
        # 创建枚举类型
        await conn.execute("""
            CREATE TYPE storagetype AS ENUM ('LOCAL', 'FTP', 'MINIO');
        """)
        logger.info("✅ 创建存储类型枚举成功")
        
        # 创建存储配置表
        await conn.execute("""
            CREATE TABLE storage_configs (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                storage_type storagetype NOT NULL,
                is_default BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                config JSON,
                total_files INTEGER DEFAULT 0,
                total_size BIGINT DEFAULT 0,
                last_sync_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        logger.info("✅ 创建存储配置表成功")
        
        # 创建索引
        await conn.execute("""
            CREATE INDEX idx_storage_configs_name ON storage_configs(name);
            CREATE INDEX idx_storage_configs_type ON storage_configs(storage_type);
            CREATE INDEX idx_storage_configs_default ON storage_configs(is_default);
        """)
        logger.info("✅ 创建索引成功")
        
        # 插入默认的本地存储配置
        await conn.execute("""
            INSERT INTO storage_configs (name, storage_type, is_default, is_active, config)
            VALUES ('默认本地存储', 'LOCAL', TRUE, TRUE, '{"base_path": "./storage"}');
        """)
        logger.info("✅ 插入默认存储配置成功")
        
        # 关闭连接
        await conn.close()
        logger.info("✅ 存储配置修复完成！")
        
    except Exception as e:
        logger.error(f"数据库修复失败: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(fix_storage_simple())
