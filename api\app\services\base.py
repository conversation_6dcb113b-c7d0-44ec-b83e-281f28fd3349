"""
Service层基类
实现业务逻辑层的抽象
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Optional, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.repositories.base import BaseRepository

# 泛型类型变量
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")
RepositoryType = TypeVar("RepositoryType", bound=BaseRepository)


class IService(ABC, Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Service接口"""
    
    @abstractmethod
    async def get(self, id: int) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        pass
    
    @abstractmethod
    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        filters: Dict[str, Any] = None
    ) -> List[ModelType]:
        """获取多个记录"""
        pass
    
    @abstractmethod
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """创建记录"""
        pass
    
    @abstractmethod
    async def update(self, id: int, obj_in: UpdateSchemaType) -> Optional[ModelType]:
        """更新记录"""
        pass
    
    @abstractmethod
    async def delete(self, id: int) -> bool:
        """删除记录"""
        pass


class BaseService(IService[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Service基类"""
    
    def __init__(self, repository: RepositoryType):
        self.repository = repository
    
    async def get(self, id: int) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        try:
            return await self.repository.get(id)
        except Exception as e:
            logger.error(f"Service error getting record with id {id}: {e}")
            raise
    
    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        filters: Dict[str, Any] = None,
        order_by: str = None,
        desc: bool = False
    ) -> List[ModelType]:
        """获取多个记录"""
        try:
            return await self.repository.get_multi(
                skip=skip,
                limit=limit,
                filters=filters,
                order_by=order_by,
                desc=desc
            )
        except Exception as e:
            logger.error(f"Service error getting multiple records: {e}")
            raise
    
    async def count(self, filters: Dict[str, Any] = None) -> int:
        """统计记录数量"""
        try:
            return await self.repository.count(filters)
        except Exception as e:
            logger.error(f"Service error counting records: {e}")
            raise
    
    async def create(self, obj_in: Union[CreateSchemaType, Dict[str, Any]]) -> ModelType:
        """创建记录"""
        try:
            # 在创建前进行业务逻辑验证
            await self._validate_create(obj_in)
            
            # 创建记录
            result = await self.repository.create(obj_in)
            
            # 创建后的业务逻辑处理
            await self._after_create(result)
            
            return result
        except Exception as e:
            logger.error(f"Service error creating record: {e}")
            raise
    
    async def update(
        self, 
        id: int, 
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> Optional[ModelType]:
        """更新记录"""
        try:
            # 获取现有记录
            existing = await self.repository.get(id)
            if not existing:
                return None
            
            # 在更新前进行业务逻辑验证
            await self._validate_update(id, obj_in, existing)
            
            # 更新记录
            result = await self.repository.update(id, obj_in)
            
            # 更新后的业务逻辑处理
            if result:
                await self._after_update(result, existing)
            
            return result
        except Exception as e:
            logger.error(f"Service error updating record with id {id}: {e}")
            raise
    
    async def delete(self, id: int) -> bool:
        """删除记录"""
        try:
            # 获取现有记录
            existing = await self.repository.get(id)
            if not existing:
                return False
            
            # 在删除前进行业务逻辑验证
            await self._validate_delete(id, existing)
            
            # 删除记录
            result = await self.repository.delete(id)
            
            # 删除后的业务逻辑处理
            if result:
                await self._after_delete(existing)
            
            return result
        except Exception as e:
            logger.error(f"Service error deleting record with id {id}: {e}")
            raise
    
    async def exists(self, id: int) -> bool:
        """检查记录是否存在"""
        try:
            return await self.repository.exists(id)
        except Exception as e:
            logger.error(f"Service error checking existence of record with id {id}: {e}")
            raise
    
    async def bulk_create(
        self, 
        objs_in: List[Union[CreateSchemaType, Dict[str, Any]]]
    ) -> List[ModelType]:
        """批量创建记录"""
        try:
            # 批量验证
            for obj_in in objs_in:
                await self._validate_create(obj_in)
            
            # 批量创建
            results = await self.repository.bulk_create(objs_in)
            
            # 批量创建后处理
            for result in results:
                await self._after_create(result)
            
            return results
        except Exception as e:
            logger.error(f"Service error bulk creating records: {e}")
            raise
    
    # 钩子方法，子类可以重写以实现特定的业务逻辑
    
    async def _validate_create(self, obj_in: Union[CreateSchemaType, Dict[str, Any]]) -> None:
        """创建前验证钩子"""
        pass
    
    async def _validate_update(
        self, 
        id: int, 
        obj_in: Union[UpdateSchemaType, Dict[str, Any]], 
        existing: ModelType
    ) -> None:
        """更新前验证钩子"""
        pass
    
    async def _validate_delete(self, id: int, existing: ModelType) -> None:
        """删除前验证钩子"""
        pass
    
    async def _after_create(self, created: ModelType) -> None:
        """创建后处理钩子"""
        pass
    
    async def _after_update(self, updated: ModelType, original: ModelType) -> None:
        """更新后处理钩子"""
        pass
    
    async def _after_delete(self, deleted: ModelType) -> None:
        """删除后处理钩子"""
        pass


class ServiceException(Exception):
    """Service层异常"""
    
    def __init__(self, message: str, code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.code = code or "SERVICE_ERROR"
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(ServiceException):
    """验证异常"""
    
    def __init__(self, message: str, field: str = None, details: Dict[str, Any] = None):
        super().__init__(message, "VALIDATION_ERROR", details)
        self.field = field


class NotFoundException(ServiceException):
    """未找到异常"""
    
    def __init__(self, resource: str, id: Any = None):
        message = f"{resource} not found"
        if id is not None:
            message += f" with id: {id}"
        super().__init__(message, "NOT_FOUND")


class ConflictException(ServiceException):
    """冲突异常"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "CONFLICT", details)


class PermissionException(ServiceException):
    """权限异常"""

    def __init__(self, message: str = "Permission denied", details: Dict[str, Any] = None):
        super().__init__(message, "PERMISSION_DENIED", details)
