"""
系统初始化服务
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, delete
from loguru import logger

from app.models.system import SystemInitStatus, SystemConfig, Dictionary
from app.models.file_management import StorageConfig
from app.models.user import User
from app.core.security import get_password_hash
from app.repositories.base import BaseRepository


class SystemInitService:
    """系统初始化服务"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.init_repo = BaseRepository(SystemInitStatus, session)
        self.config_repo = BaseRepository(SystemConfig, session)
        self.dict_repo = BaseRepository(Dictionary, session)
        self.storage_repo = BaseRepository(StorageConfig, session)
        self.user_repo = BaseRepository(User, session)
    
    async def check_system_initialized(self) -> bool:
        """检查系统是否已初始化"""
        try:
            # 检查是否有管理员用户
            admin_user = await self.session.execute(
                select(User).where(User.is_superuser == True)
            )
            has_admin = admin_user.scalar_one_or_none() is not None
            
            # 检查初始化状态
            init_status = await self.init_repo.get_by_field("step_name", "admin_init")
            admin_initialized = init_status and init_status.is_completed
            
            return has_admin and admin_initialized
            
        except Exception as e:
            logger.error(f"Error checking system initialization: {e}")
            return False
    
    async def get_initialization_status(self) -> Dict[str, Any]:
        """获取初始化状态"""
        try:
            # 获取所有初始化步骤
            steps = await self.init_repo.get_all()
            
            status = {
                "is_initialized": await self.check_system_initialized(),
                "steps": {},
                "next_step": None
            }
            
            # 预定义的初始化步骤
            required_steps = [
                "admin_init",
                "dictionary_init", 
                "storage_init",
                "system_config_init"
            ]
            
            for step_name in required_steps:
                step = next((s for s in steps if s.step_name == step_name), None)
                status["steps"][step_name] = {
                    "completed": step.is_completed if step else False,
                    "completed_at": step.completed_at if step else None,
                    "description": step.step_description if step else self._get_step_description(step_name)
                }
            
            # 找到下一个未完成的步骤
            for step_name in required_steps:
                if not status["steps"][step_name]["completed"]:
                    status["next_step"] = step_name
                    break
            
            return status
            
        except Exception as e:
            logger.error("Error getting initialization status: {}".format(str(e)))
            return {
                "is_initialized": False,
                "steps": {},
                "next_step": "admin_init",
                "error": str(e)
            }
    
    async def initialize_admin_user(self, admin_data: Dict[str, Any]) -> Dict[str, Any]:
        """初始化管理员用户"""
        try:
            # 检查是否已有管理员
            existing_admin = await self.session.execute(
                select(User).where(User.is_superuser == True)
            )
            if existing_admin.scalar_one_or_none():
                return {
                    "success": False,
                    "message": "管理员用户已存在"
                }
            
            # 创建管理员用户
            admin_user = User(
                username=admin_data["username"],
                email=admin_data["email"],
                hashed_password=get_password_hash(admin_data["password"]),
                full_name=admin_data.get("full_name", "系统管理员"),
                is_active=True,
                is_superuser=True
            )
            
            self.session.add(admin_user)
            await self.session.flush()
            
            # 记录初始化状态
            await self._mark_step_completed("admin_init", {
                "admin_id": admin_user.id,
                "username": admin_user.username
            })
            
            await self.session.commit()
            
            logger.info(f"Admin user created: {admin_user.username}")
            
            return {
                "success": True,
                "message": "管理员用户创建成功",
                "admin_id": admin_user.id
            }
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error creating admin user: {e}")
            return {
                "success": False,
                "message": f"创建管理员用户失败: {str(e)}"
            }
    
    async def initialize_dictionaries(self) -> Dict[str, Any]:
        """初始化字典数据"""
        try:
            # 检查是否已初始化
            init_status = await self.init_repo.get_by_field("step_name", "dictionary_init")
            if init_status and init_status.is_completed:
                return {
                    "success": True,
                    "message": "字典已初始化"
                }
            
            # 初始化字典数据
            dictionaries = self._get_default_dictionaries()
            
            for dict_data in dictionaries:
                # 检查是否已存在
                existing = await self.session.execute(
                    select(Dictionary).where(
                        Dictionary.dict_type == dict_data["dict_type"],
                        Dictionary.dict_code == dict_data["dict_code"]
                    )
                )
                if not existing.scalar_one_or_none():
                    dictionary = Dictionary(**dict_data)
                    self.session.add(dictionary)
            
            # 记录初始化状态
            await self._mark_step_completed("dictionary_init", {
                "dictionaries_count": len(dictionaries)
            })
            
            await self.session.commit()
            
            logger.info(f"Initialized {len(dictionaries)} dictionary entries")
            
            return {
                "success": True,
                "message": f"成功初始化 {len(dictionaries)} 个字典项"
            }
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error initializing dictionaries: {e}")
            return {
                "success": False,
                "message": f"初始化字典失败: {str(e)}"
            }
    
    async def initialize_storage_configs(self, custom_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """初始化存储配置"""
        try:
            # 检查是否已初始化
            init_status = await self.init_repo.get_by_field("step_name", "storage_init")
            if init_status and init_status.is_completed:
                return {
                    "success": True,
                    "message": "存储配置已初始化"
                }
            
            # 使用自定义配置或默认配置
            if custom_config:
                storage_type = custom_config.get("storage_type", "local")
                base_path = custom_config.get("base_path", "./storage")
                name = "默认{}存储".format(storage_type)
            else:
                storage_type = "local"
                base_path = "./storage"
                name = "默认本地存储"

            # 创建存储配置（简化版本，避免数据库表结构问题）
            storage_data = {
                "name": name,
                "storage_type": storage_type,
                "config": {
                    "base_path": base_path,
                    "max_file_size": 104857600,  # 100MB
                    "allowed_extensions": [
                        "txt", "pdf", "doc", "docx", "xls", "xlsx",
                        "ppt", "pptx", "md", "json", "csv"
                    ]
                },
                "is_default": True,
                "is_active": True
            }

            # 使用原生SQL插入，避免ORM问题
            from sqlalchemy import text
            import json

            # 将config转换为JSON字符串
            storage_data_for_db = {
                "name": storage_data["name"],
                "storage_type": storage_data["storage_type"],
                "is_default": storage_data["is_default"],
                "is_active": storage_data["is_active"],
                "config": json.dumps(storage_data["config"])
            }

            await self.session.execute(text("""
                INSERT INTO storage_configs (name, storage_type, is_default, is_active, config)
                VALUES (:name, :storage_type, :is_default, :is_active, :config)
            """), storage_data_for_db)
            
            # 记录初始化状态
            await self._mark_step_completed("storage_init", {
                "storage_name": name
            })
            
            await self.session.commit()
            
            logger.info("Default storage config created: {}".format(name))
            
            return {
                "success": True,
                "message": "默认存储配置创建成功"
            }
            
        except Exception as e:
            await self.session.rollback()
            logger.error("Error initializing storage configs: {}".format(str(e)))
            return {
                "success": False,
                "message": "初始化存储配置失败: {}".format(str(e))
            }
    
    async def reset_system(self) -> Dict[str, Any]:
        """重置系统，清除所有数据"""
        try:
            logger.info("开始重置系统...")

            # 删除所有用户（除了系统用户）
            from app.models.user import User
            await self.session.execute(
                delete(User)
            )

            # 删除所有存储配置
            from app.models.file_management import StorageConfig
            await self.session.execute(
                delete(StorageConfig)
            )

            # 删除所有字典数据
            from app.models.system import Dictionary
            await self.session.execute(
                delete(Dictionary)
            )

            # 删除所有系统配置
            from app.models.system import SystemConfig
            await self.session.execute(
                delete(SystemConfig)
            )

            await self.session.commit()

            logger.info("系统重置完成")
            return {
                "success": True,
                "message": "系统重置成功，所有数据已清除"
            }

        except Exception as e:
            await self.session.rollback()
            logger.error("系统重置失败: {}".format(str(e)))
            return {
                "success": False,
                "message": "系统重置失败: {}".format(str(e))
            }

    async def initialize_system_configs(self) -> Dict[str, Any]:
        """初始化系统配置"""
        try:
            # 检查是否已初始化
            init_status = await self.init_repo.get_by_field("step_name", "system_config_init")
            if init_status and init_status.is_completed:
                return {
                    "success": True,
                    "message": "系统配置已初始化"
                }
            
            # 初始化系统配置
            configs = self._get_default_system_configs()
            
            for config_data in configs:
                # 检查是否已存在
                existing = await self.session.execute(
                    select(SystemConfig).where(
                        SystemConfig.config_key == config_data["config_key"]
                    )
                )
                if not existing.scalar_one_or_none():
                    config = SystemConfig(**config_data)
                    self.session.add(config)
            
            # 记录初始化状态
            await self._mark_step_completed("system_config_init", {
                "configs_count": len(configs)
            })
            
            await self.session.commit()
            
            logger.info("Initialized {} system configs".format(len(configs)))

            return {
                "success": True,
                "message": "成功初始化 {} 个系统配置".format(len(configs))
            }
            
        except Exception as e:
            await self.session.rollback()
            logger.error("Error initializing system configs: {}".format(str(e)))
            return {
                "success": False,
                "message": "初始化系统配置失败: {}".format(str(e))
            }
    
    async def _mark_step_completed(self, step_name: str, result_data: Dict[str, Any] = None):
        """标记步骤完成"""
        from datetime import datetime
        
        # 查找或创建步骤记录
        step = await self.init_repo.get_by_field("step_name", step_name)
        if not step:
            step = SystemInitStatus(
                step_name=step_name,
                step_description=self._get_step_description(step_name)
            )
            self.session.add(step)
        
        step.is_completed = True
        step.completed_at = datetime.utcnow()
        step.result_data = result_data or {}
    
    def _get_step_description(self, step_name: str) -> str:
        """获取步骤描述"""
        descriptions = {
            "admin_init": "初始化管理员账户",
            "dictionary_init": "初始化系统字典",
            "storage_init": "初始化存储配置",
            "system_config_init": "初始化系统配置"
        }
        return descriptions.get(step_name, step_name)
    
    def _get_default_dictionaries(self) -> List[Dict[str, Any]]:
        """获取默认字典数据"""
        return [
            # 文件类型字典
            {"dict_type": "file_type", "dict_code": "document", "dict_label": "文档", "dict_value": "document", "sort_order": 1, "is_system": True},
            {"dict_type": "file_type", "dict_code": "image", "dict_label": "图片", "dict_value": "image", "sort_order": 2, "is_system": True},
            {"dict_type": "file_type", "dict_code": "video", "dict_label": "视频", "dict_value": "video", "sort_order": 3, "is_system": True},
            {"dict_type": "file_type", "dict_code": "audio", "dict_label": "音频", "dict_value": "audio", "sort_order": 4, "is_system": True},
            
            # 存储类型字典
            {"dict_type": "storage_type", "dict_code": "local", "dict_label": "本地存储", "dict_value": "local", "sort_order": 1, "is_system": True},
            {"dict_type": "storage_type", "dict_code": "minio", "dict_label": "MinIO存储", "dict_value": "minio", "sort_order": 2, "is_system": True},
            {"dict_type": "storage_type", "dict_code": "ftp", "dict_label": "FTP存储", "dict_value": "ftp", "sort_order": 3, "is_system": True},
            
            # 用户状态字典
            {"dict_type": "user_status", "dict_code": "active", "dict_label": "正常", "dict_value": "active", "sort_order": 1, "is_system": True},
            {"dict_type": "user_status", "dict_code": "inactive", "dict_label": "禁用", "dict_value": "inactive", "sort_order": 2, "is_system": True},
            {"dict_type": "user_status", "dict_code": "pending", "dict_label": "待激活", "dict_value": "pending", "sort_order": 3, "is_system": True},
        ]
    
    def _get_default_system_configs(self) -> List[Dict[str, Any]]:
        """获取默认系统配置"""
        return [
            {
                "config_key": "system.name",
                "config_value": "AI知识库",
                "config_type": "string",
                "description": "系统名称",
                "is_system": True,
                "is_editable": True
            },
            {
                "config_key": "system.version",
                "config_value": "1.0.0",
                "config_type": "string", 
                "description": "系统版本",
                "is_system": True,
                "is_editable": False
            },
            {
                "config_key": "file.max_size",
                "config_value": "104857600",
                "config_type": "number",
                "description": "文件最大上传大小(字节)",
                "is_system": False,
                "is_editable": True
            },
            {
                "config_key": "user.allow_register",
                "config_value": "true",
                "config_type": "boolean",
                "description": "是否允许用户注册",
                "is_system": False,
                "is_editable": True
            }
        ]
