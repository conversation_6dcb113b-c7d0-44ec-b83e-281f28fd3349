"""
数据库模型基类
提供通用字段和方法，兼容多种数据库
"""

from datetime import datetime
from typing import Any, Dict
from sqlalchemy import Column, Integer, DateTime, String, Boolean, Text
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func
from app.core.database import Base


class TimestampMixin:
    """时间戳混入类"""
    
    @declared_attr
    def created_at(cls):
        """创建时间"""
        return Column(
            DateTime(timezone=True),
            server_default=func.now(),
            nullable=False,
            comment="创建时间"
        )
    
    @declared_attr
    def updated_at(cls):
        """更新时间"""
        return Column(
            DateTime(timezone=True),
            server_default=func.now(),
            onupdate=func.now(),
            nullable=False,
            comment="更新时间"
        )


class SoftDeleteMixin:
    """软删除混入类"""
    
    @declared_attr
    def is_deleted(cls):
        """是否已删除"""
        return Column(
            <PERSON><PERSON><PERSON>,
            default=False,
            nullable=False,
            comment="是否已删除"
        )
    
    @declared_attr
    def deleted_at(cls):
        """删除时间"""
        return Column(
            DateTime(timezone=True),
            nullable=True,
            comment="删除时间"
        )


class BaseModel(Base, TimestampMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    @declared_attr
    def id(cls):
        """主键ID"""
        return Column(
            Integer,
            primary_key=True,
            autoincrement=True,
            comment="主键ID"
        )
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        exclude_fields = exclude_fields or set()
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: set = None):
        """从字典更新属性"""
        exclude_fields = exclude_fields or {'id', 'created_at', 'updated_at'}
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self):
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={getattr(self, 'id', None)})>"


class BaseModelWithSoftDelete(BaseModel, SoftDeleteMixin):
    """带软删除的基础模型类"""
    
    __abstract__ = True
    
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None


# 数据库兼容性工具函数
def get_text_type(length: int = None):
    """
    获取文本类型，兼容不同数据库
    
    Args:
        length: 文本长度限制
        
    Returns:
        SQLAlchemy文本类型
    """
    if length and length <= 255:
        return String(length)
    else:
        return Text


def get_json_type():
    """
    获取JSON类型，兼容不同数据库
    
    Returns:
        SQLAlchemy JSON类型
    """
    try:
        from sqlalchemy import JSON
        return JSON
    except ImportError:
        # 如果数据库不支持JSON类型，使用Text
        return Text


def get_uuid_type():
    """
    获取UUID类型，兼容不同数据库
    
    Returns:
        SQLAlchemy UUID类型
    """
    try:
        from sqlalchemy.dialects.postgresql import UUID
        return UUID(as_uuid=True)
    except ImportError:
        # 如果不是PostgreSQL，使用String
        return String(36)
