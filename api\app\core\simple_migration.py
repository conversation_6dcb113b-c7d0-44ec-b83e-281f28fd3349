"""
简化的数据库迁移管理器
直接使用SQL创建和更新表结构
"""

import asyncio
import re
from typing import Dict, List, Any
from loguru import logger


def clean_sql(sql: str) -> str:
    """清理SQL字符串"""
    # 移除多余的空白字符和换行符
    cleaned = re.sub(r'\s+', ' ', sql.strip())
    # 移除注释
    cleaned = re.sub(r'--.*$', '', cleaned, flags=re.MULTILINE)
    # 移除多余的逗号和空格
    cleaned = re.sub(r',\s*,', ',', cleaned)
    cleaned = re.sub(r'\(\s*,', '(', cleaned)
    cleaned = re.sub(r',\s*\)', ')', cleaned)
    return cleaned.strip()


async def create_tables_directly() -> Dict[str, Any]:
    """直接使用SQL创建表结构"""
    result = {
        "status": "success",
        "actions": [],
        "errors": []
    }
    
    try:
        # 导入数据库连接
        from app.core.database import async_engine
        from app.core.config import get_settings
        
        if not async_engine:
            raise RuntimeError("Database engine not initialized")
        
        settings = get_settings()
        db_type = settings.database_url.split("+")[0].split("://")[0]
        
        logger.info(f"Creating tables for database type: {db_type}")
        
        # 定义表创建SQL
        table_sqls = get_table_creation_sqls(db_type)

        async with async_engine.begin() as conn:
            # 创建所有表（无外键依赖，顺序无关紧要）
            for table_name, sql in table_sqls.items():
                try:
                    # 检查表是否已存在
                    if db_type == "postgresql":
                        check_sql = f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = '{table_name}'
                        )
                        """
                    elif db_type == "mysql":
                        check_sql = f"SHOW TABLES LIKE '{table_name}'"
                    elif db_type == "sqlite":
                        check_sql = f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
                    else:
                        # 默认尝试创建，如果存在会报错但不影响
                        check_sql = None
                    
                    table_exists = False
                    if check_sql:
                        from sqlalchemy import text
                        result_proxy = await conn.execute(text(check_sql))
                        if db_type == "postgresql":
                            table_exists = result_proxy.scalar()
                        else:
                            table_exists = result_proxy.fetchone() is not None
                    
                    if not table_exists:
                        from sqlalchemy import text
                        # 使用专门的SQL清理函数
                        cleaned_sql = clean_sql(sql)
                        logger.debug(f"Executing SQL for table {table_name}: {cleaned_sql[:100]}...")
                        await conn.execute(text(cleaned_sql))
                        result["actions"].append(f"Created table: {table_name}")
                        logger.info(f"Created table: {table_name}")
                    else:
                        result["actions"].append(f"Table already exists: {table_name}")
                        
                except Exception as e:
                    error_msg = f"Failed to create table {table_name}: {e}"
                    result["errors"].append(error_msg)
                    logger.error(error_msg)
        
        result["summary"] = {
            "total_actions": len(result["actions"]),
            "errors": len(result["errors"])
        }
        
    except Exception as e:
        result["status"] = "error"
        result["errors"].append(str(e))
        logger.error(f"Table creation failed: {e}")
    
    return result


def get_table_creation_sqls(db_type: str) -> Dict[str, str]:
    """获取表创建SQL语句"""
    
    if db_type == "postgresql":
        return get_postgresql_sqls()
    elif db_type == "mysql":
        return get_mysql_sqls()
    elif db_type == "sqlite":
        return get_sqlite_sqls()
    else:
        # 默认使用PostgreSQL语法
        return get_postgresql_sqls()


def get_postgresql_sqls() -> Dict[str, str]:
    """PostgreSQL表创建SQL"""
    return {
        "users": """CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                hashed_password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                is_superuser BOOLEAN DEFAULT FALSE,
                avatar_url VARCHAR(255),
                bio TEXT,
                last_login_at TIMESTAMP WITH TIME ZONE,
                login_count INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )""",
        
        "storage_configs": """CREATE TABLE IF NOT EXISTS storage_configs (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                storage_type VARCHAR(20) NOT NULL,
                config JSONB NOT NULL,
                is_default BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                total_files INTEGER DEFAULT 0,
                total_size BIGINT DEFAULT 0,
                last_sync_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )""",
        
        "file_records": """CREATE TABLE IF NOT EXISTS file_records (
                id SERIAL PRIMARY KEY,
                file_id VARCHAR(36) UNIQUE NOT NULL,
                storage_id INTEGER,
                file_path TEXT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                parent_path TEXT,
                file_type VARCHAR(20),
                file_size BIGINT,
                mime_type VARCHAR(100),
                file_extension VARCHAR(20),
                file_hash VARCHAR(64),
                file_created_at TIMESTAMP WITH TIME ZONE,
                file_modified_at TIMESTAMP WITH TIME ZONE,
                status VARCHAR(20) DEFAULT 'active',
                file_metadata JSONB,
                thumbnail_path VARCHAR(500),
                has_thumbnail BOOLEAN DEFAULT FALSE,
                last_sync_at TIMESTAMP WITH TIME ZONE,
                sync_version INTEGER DEFAULT 1,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )""",
        
        "sync_logs": """CREATE TABLE IF NOT EXISTS sync_logs (
                id SERIAL PRIMARY KEY,
                storage_id INTEGER,
                sync_type VARCHAR(20) NOT NULL,
                files_scanned INTEGER DEFAULT 0,
                files_added INTEGER DEFAULT 0,
                files_updated INTEGER DEFAULT 0,
                files_deleted INTEGER DEFAULT 0,
                status VARCHAR(20) NOT NULL,
                error_message TEXT,
                started_at TIMESTAMP WITH TIME ZONE,
                completed_at TIMESTAMP WITH TIME ZONE,
                duration_seconds INTEGER,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )""",

        "system_configs": """CREATE TABLE IF NOT EXISTS system_configs (
                id SERIAL PRIMARY KEY,
                config_key VARCHAR(100) UNIQUE NOT NULL,
                config_value TEXT,
                config_type VARCHAR(20) DEFAULT 'string',
                description VARCHAR(255),
                is_system BOOLEAN DEFAULT FALSE,
                is_editable BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )""",

        "system_init_status": """CREATE TABLE IF NOT EXISTS system_init_status (
                id SERIAL PRIMARY KEY,
                step_name VARCHAR(50) UNIQUE NOT NULL,
                step_description VARCHAR(255),
                is_completed BOOLEAN DEFAULT FALSE,
                completed_at TIMESTAMP WITH TIME ZONE,
                result_data JSONB,
                error_message TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )""",

        "dictionaries": """CREATE TABLE IF NOT EXISTS dictionaries (
                id SERIAL PRIMARY KEY,
                dict_type VARCHAR(50) NOT NULL,
                dict_code VARCHAR(50) NOT NULL,
                dict_label VARCHAR(100) NOT NULL,
                dict_value VARCHAR(100) NOT NULL,
                parent_code VARCHAR(50),
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                is_system BOOLEAN DEFAULT FALSE,
                description VARCHAR(255),
                extra_data JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )""",

        "operation_logs": """CREATE TABLE IF NOT EXISTS operation_logs (
                id SERIAL PRIMARY KEY,
                operation_type VARCHAR(50) NOT NULL,
                operation_name VARCHAR(100) NOT NULL,
                operation_desc VARCHAR(255),
                user_id INTEGER,
                username VARCHAR(50),
                request_method VARCHAR(10),
                request_url VARCHAR(500),
                request_ip VARCHAR(50),
                user_agent VARCHAR(500),
                response_status INTEGER,
                response_time INTEGER,
                request_data JSONB,
                response_data JSONB,
                error_message TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )"""
    }


def get_mysql_sqls() -> Dict[str, str]:
    """MySQL表创建SQL"""
    return {
        "users": """
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                hashed_password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                is_superuser BOOLEAN DEFAULT FALSE,
                avatar_url VARCHAR(255),
                bio TEXT,
                last_login_at TIMESTAMP NULL,
                login_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        """,
        
        "storage_configs": """
            CREATE TABLE IF NOT EXISTS storage_configs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                storage_type VARCHAR(20) NOT NULL,
                config JSON NOT NULL,
                is_default BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                total_files INT DEFAULT 0,
                total_size BIGINT DEFAULT 0,
                last_sync_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        """,
        
        "file_records": """CREATE TABLE IF NOT EXISTS file_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                file_id VARCHAR(36) UNIQUE NOT NULL,
                storage_id INT,
                file_path TEXT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                parent_path TEXT,
                file_type VARCHAR(20),
                file_size BIGINT,
                mime_type VARCHAR(100),
                file_extension VARCHAR(20),
                file_hash VARCHAR(64),
                file_created_at TIMESTAMP NULL,
                file_modified_at TIMESTAMP NULL,
                status VARCHAR(20) DEFAULT 'active',
                file_metadata JSON,
                thumbnail_path VARCHAR(500),
                has_thumbnail BOOLEAN DEFAULT FALSE,
                last_sync_at TIMESTAMP NULL,
                sync_version INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )""",
        
        "sync_logs": """CREATE TABLE IF NOT EXISTS sync_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                storage_id INT,
                sync_type VARCHAR(20) NOT NULL,
                files_scanned INT DEFAULT 0,
                files_added INT DEFAULT 0,
                files_updated INT DEFAULT 0,
                files_deleted INT DEFAULT 0,
                status VARCHAR(20) NOT NULL,
                error_message TEXT,
                started_at TIMESTAMP NULL,
                completed_at TIMESTAMP NULL,
                duration_seconds INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )"""
    }


def get_sqlite_sqls() -> Dict[str, str]:
    """SQLite表创建SQL"""
    return {
        "users": """
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                hashed_password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                is_active BOOLEAN DEFAULT 1,
                is_superuser BOOLEAN DEFAULT 0,
                avatar_url VARCHAR(255),
                bio TEXT,
                last_login_at TIMESTAMP,
                login_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """,
        
        "storage_configs": """
            CREATE TABLE IF NOT EXISTS storage_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                storage_type VARCHAR(20) NOT NULL,
                config TEXT NOT NULL,
                is_default BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                total_files INTEGER DEFAULT 0,
                total_size INTEGER DEFAULT 0,
                last_sync_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """,
        
        "file_records": """CREATE TABLE IF NOT EXISTS file_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id VARCHAR(36) UNIQUE NOT NULL,
                storage_id INTEGER REFERENCES storage_configs(id),
                file_path TEXT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                parent_path TEXT,
                file_type VARCHAR(20),
                file_size INTEGER,
                mime_type VARCHAR(100),
                file_extension VARCHAR(20),
                file_hash VARCHAR(64),
                file_created_at TIMESTAMP,
                file_modified_at TIMESTAMP,
                status VARCHAR(20) DEFAULT 'active',
                file_metadata TEXT,
                thumbnail_path VARCHAR(500),
                has_thumbnail BOOLEAN DEFAULT 0,
                last_sync_at TIMESTAMP,
                sync_version INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )""",
        
        "sync_logs": """
            CREATE TABLE IF NOT EXISTS sync_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                storage_id INTEGER REFERENCES storage_configs(id),
                sync_type VARCHAR(20) NOT NULL,
                files_scanned INTEGER DEFAULT 0,
                files_added INTEGER DEFAULT 0,
                files_updated INTEGER DEFAULT 0,
                files_deleted INTEGER DEFAULT 0,
                status VARCHAR(20) NOT NULL,
                error_message TEXT,
                started_at TIMESTAMP,
                completed_at TIMESTAMP,
                duration_seconds INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
    }
