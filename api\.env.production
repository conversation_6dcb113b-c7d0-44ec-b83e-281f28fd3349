# 知识库智能平台 - 生产环境配置
ENVIRONMENT="production"
DEBUG=false
HOST="0.0.0.0"
PORT=8000
LOG_LEVEL="INFO"

# PostgreSQL 数据库配置
DB_USERNAME=postgres
DB_PASSWORD=XHC12345
DB_HOST=**************
DB_PORT=5432
DB_DATABASE=xhc_rag
DATABASE_URL=postgresql+asyncpg://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}
DATABASE_TYPE=postgresql
DATABASE_ECHO=false
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=40

# Redis 配置
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=XHC12345
REDIS_USE_SSL=false
REDIS_DB=10
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# 生产Celery
CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/11
CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/12

# 生产安全配置
SECRET_KEY="${SECRET_KEY}"
ACCESS_TOKEN_EXPIRE_MINUTES=15
REFRESH_TOKEN_EXPIRE_DAYS=1

# 生产CORS - 限制来源
CORS_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
CORS_ALLOW_CREDENTIALS=true

# 生产插件
ENABLED_PLUGINS="auth,file_manager,rag_engine,storage_management"

# 文件存储配置
UPLOAD_MAX_SIZE=104857600
UPLOAD_ALLOWED_EXTENSIONS="txt,pdf,doc,docx,xls,xlsx,ppt,pptx,md,json,csv,jpg,jpeg,png,gif,mp4,avi,mov"
DEFAULT_STORAGE_TYPE="local"
STORAGE_BASE_PATH="./storage"

# 生产监控
ENABLE_METRICS=true
METRICS_PORT=9090
