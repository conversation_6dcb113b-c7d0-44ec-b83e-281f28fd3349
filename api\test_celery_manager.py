#!/usr/bin/env python3
"""
测试Celery管理器的脚本
"""
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from app.core.celery_manager import celery_manager
from loguru import logger


def test_celery_manager():
    """测试Celery管理器"""
    try:
        logger.info("测试Celery管理器...")

        # 检查环境变量
        import os
        logger.info(f"Redis配置:")
        logger.info(f"  REDIS_HOST: {os.getenv('REDIS_HOST')}")
        logger.info(f"  REDIS_PORT: {os.getenv('REDIS_PORT')}")
        logger.info(f"  REDIS_DB: {os.getenv('REDIS_DB')}")
        logger.info(f"  REDIS_PASSWORD: {'***' if os.getenv('REDIS_PASSWORD') else '(无)'}")

        # 测试获取状态
        status = celery_manager.get_status()
        logger.info(f"当前状态: {status}")
        
        # 测试启动服务
        logger.info("测试启动服务...")
        results = celery_manager.start_all()
        logger.info(f"启动结果: {results}")
        
        # 等待一下
        time.sleep(5)
        
        # 再次检查状态
        status = celery_manager.get_status()
        logger.info(f"启动后状态: {status}")
        
        # 测试停止服务
        logger.info("测试停止服务...")
        results = celery_manager.stop_all()
        logger.info(f"停止结果: {results}")
        
        logger.info("测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = test_celery_manager()
    sys.exit(0 if success else 1)
