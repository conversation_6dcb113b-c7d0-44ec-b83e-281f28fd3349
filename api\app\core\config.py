"""
应用配置管理
支持多环境配置和动态配置加载
"""

import os
from functools import lru_cache
from typing import List, Optional, Any, Dict
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = Field(default="AI知识库 API", description="应用名称")
    APP_VERSION: str = Field(default="0.1.0", description="应用版本")
    APP_DESCRIPTION: str = Field(default="AI知识库 - 基于人工智能的智能知识管理与检索系统", description="应用描述")
    ENVIRONMENT: str = Field(default="development", description="运行环境")
    DEBUG: bool = Field(default=False, description="调试模式")
    HOST: str = Field(default="127.0.0.1", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    
    # 安全配置
    SECRET_KEY: str = Field(default="change-me-in-production", description="密钥")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间(分钟)")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="刷新令牌过期时间(天)")
    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = Field(default=48, description="邮箱重置令牌过期时间(小时)")
    ALGORITHM: str = Field(default="HS256", description="JWT算法")
    
    # 数据库配置 - PostgreSQL
    DB_USERNAME: str = Field(default="postgres", description="数据库用户名")
    DB_PASSWORD: str = Field(default="XHC12345", description="数据库密码")
    DB_HOST: str = Field(default="**************", description="数据库主机")
    DB_PORT: int = Field(default=5432, description="数据库端口")
    DB_DATABASE: str = Field(default="xhc_rag", description="数据库名称")
    DATABASE_URL: str = Field(default="", description="数据库连接URL")
    DATABASE_TYPE: str = Field(default="postgresql", description="数据库类型")
    DATABASE_ECHO: bool = Field(default=False, description="数据库SQL日志")
    DATABASE_POOL_SIZE: int = Field(default=10, description="数据库连接池大小")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, description="数据库连接池最大溢出")
    DATABASE_TIMEOUT: int = Field(default=30, description="数据库连接超时时间(秒)")
    DATABASE_RETRY_ATTEMPTS: int = Field(default=3, description="数据库连接重试次数")
    
    # Redis配置
    REDIS_HOST: str = Field(default="**************", description="Redis主机")
    REDIS_PORT: int = Field(default=6379, description="Redis端口")
    REDIS_USERNAME: str = Field(default="", description="Redis用户名")
    REDIS_PASSWORD: str = Field(default="XHC12345", description="Redis密码")
    REDIS_USE_SSL: bool = Field(default=False, description="Redis使用SSL")
    REDIS_DB: int = Field(default=10, description="Redis数据库")
    REDIS_URL: str = Field(default="", description="Redis连接URL")
    
    # Celery配置
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", description="Celery代理URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", description="Celery结果后端")
    CELERY_TASK_ALWAYS_EAGER: bool = Field(default=False, description="Celery同步执行")
    CELERY_TASK_EAGER_PROPAGATES: bool = Field(default=False, description="Celery异常传播")
    
    # 文件存储配置
    UPLOAD_DIR: str = Field(default="uploads", description="上传目录")
    MAX_FILE_SIZE: int = Field(default=10485760, description="最大文件大小(字节)")
    ALLOWED_FILE_TYPES: str = Field(default="jpg,jpeg,png,gif,pdf,txt", description="允许的文件类型")

    # 新增文件管理配置
    UPLOAD_MAX_SIZE: int = Field(default=104857600, description="最大上传文件大小(字节)")
    UPLOAD_ALLOWED_EXTENSIONS: str = Field(default="txt,pdf,doc,docx,xls,xlsx,ppt,pptx,md,json,csv", description="允许的文件扩展名")
    DEFAULT_STORAGE_TYPE: str = Field(default="local", description="默认存储类型")
    STORAGE_BASE_PATH: str = Field(default="./storage", description="存储基础路径")
    
    # 邮件配置
    SMTP_HOST: Optional[str] = Field(default=None, description="SMTP主机")
    SMTP_PORT: int = Field(default=587, description="SMTP端口")
    SMTP_USER: Optional[str] = Field(default=None, description="SMTP用户")
    SMTP_PASSWORD: Optional[str] = Field(default=None, description="SMTP密码")
    SMTP_TLS: bool = Field(default=True, description="SMTP TLS")
    
    # 第三方服务配置
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API密钥")
    OPENAI_BASE_URL: str = Field(default="https://api.openai.com/v1", description="OpenAI API基础URL")
    
    # 插件配置
    PLUGINS_DIR: str = Field(default="plugins", description="插件目录")
    ENABLED_PLUGINS: str = Field(default="", description="启用的插件列表")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=False, description="启用指标监控")
    METRICS_PORT: int = Field(default=9090, description="指标端口")
    
    # CORS配置
    CORS_ORIGINS: str = Field(default="*", description="CORS允许的来源")
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True, description="CORS允许凭证")
    CORS_ALLOW_METHODS: str = Field(default="GET,POST,PUT,DELETE,OPTIONS", description="CORS允许的方法")
    CORS_ALLOW_HEADERS: str = Field(default="*", description="CORS允许的头部")
    
    @field_validator("ENVIRONMENT")
    @classmethod
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed_envs = ["development", "testing", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of {allowed_envs}")
        return v

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v):
        """验证日志级别"""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of {allowed_levels}")
        return v.upper()
    
    @property
    def cors_origins_list(self) -> List[str]:
        """获取CORS来源列表"""
        if self.CORS_ORIGINS == "*":
            return ["*"]
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]
    
    @property
    def cors_methods_list(self) -> List[str]:
        """获取CORS方法列表"""
        return [method.strip() for method in self.CORS_ALLOW_METHODS.split(",")]
    
    @property
    def allowed_file_types_list(self) -> List[str]:
        """获取允许的文件类型列表"""
        return [file_type.strip() for file_type in self.ALLOWED_FILE_TYPES.split(",")]

    @property
    def upload_allowed_extensions_list(self) -> List[str]:
        """获取允许的上传文件扩展名列表"""
        return [ext.strip() for ext in self.UPLOAD_ALLOWED_EXTENSIONS.split(",")]
    
    @property
    def enabled_plugins_list(self) -> List[str]:
        """获取启用的插件列表"""
        if not self.ENABLED_PLUGINS:
            return []
        return [plugin.strip() for plugin in self.ENABLED_PLUGINS.split(",")]

    @property
    def database_url(self) -> str:
        """动态生成数据库连接URL"""
        # 如果DATABASE_URL已设置且不包含模板变量，直接使用
        if self.DATABASE_URL and not ("${" in self.DATABASE_URL):
            return self.DATABASE_URL

        # URL编码用户名和密码中的特殊字符
        import urllib.parse
        encoded_username = urllib.parse.quote(self.DB_USERNAME, safe='')
        encoded_password = urllib.parse.quote(self.DB_PASSWORD, safe='')

        return f"postgresql+asyncpg://{encoded_username}:{encoded_password}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}"

    @property
    def redis_url(self) -> str:
        """动态生成Redis连接URL"""
        if self.REDIS_URL and not ("${" in self.REDIS_URL):
            return self.REDIS_URL

        auth_part = ""
        if self.REDIS_USERNAME and self.REDIS_PASSWORD:
            auth_part = f"{self.REDIS_USERNAME}:{self.REDIS_PASSWORD}@"
        elif self.REDIS_PASSWORD:
            auth_part = f":{self.REDIS_PASSWORD}@"

        protocol = "rediss" if self.REDIS_USE_SSL else "redis"
        return f"{protocol}://{auth_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"
    
    model_config = {
        "env_file": [
            ".env.local",
            f".env.{os.getenv('ENVIRONMENT', 'development')}",
            ".env"
        ],
        "env_file_encoding": "utf-8",
        "case_sensitive": True
    }


@lru_cache()
def get_settings() -> Settings:
    """获取应用配置单例"""
    return Settings()


# 导出配置实例
settings = get_settings()
