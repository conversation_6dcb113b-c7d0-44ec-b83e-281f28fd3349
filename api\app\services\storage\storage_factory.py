"""
存储工厂
根据配置创建相应的存储实例
"""

from typing import Dict, Any
from app.models.file_management import StorageType
from .base import StorageInterface, StorageException
from .local_storage import LocalStorage
from .minio_storage import MinIOStorage
from .ftp_storage import FTPStorage


class StorageFactory:
    """存储工厂类"""
    
    _storage_classes = {
        StorageType.LOCAL: LocalStorage,
        StorageType.MINIO: MinIOStorage,
        StorageType.FTP: FTPStorage,
        StorageType.SFTP: FTPStorage,  # SFTP暂时使用FTP实现
    }
    
    @classmethod
    def create_storage(cls, storage_type: StorageType, config: Dict[str, Any]) -> StorageInterface:
        """
        创建存储实例
        
        Args:
            storage_type: 存储类型
            config: 存储配置
            
        Returns:
            StorageInterface: 存储实例
            
        Raises:
            StorageException: 不支持的存储类型
        """
        if storage_type not in cls._storage_classes:
            raise StorageException(f"Unsupported storage type: {storage_type}")
        
        storage_class = cls._storage_classes[storage_type]
        return storage_class(config)
    
    @classmethod
    def get_supported_types(cls) -> list[StorageType]:
        """
        获取支持的存储类型列表
        
        Returns:
            list[StorageType]: 支持的存储类型
        """
        return list(cls._storage_classes.keys())
    
    @classmethod
    def validate_config(cls, storage_type: StorageType, config: Dict[str, Any]) -> Dict[str, str]:
        """
        验证存储配置
        
        Args:
            storage_type: 存储类型
            config: 存储配置
            
        Returns:
            Dict[str, str]: 验证错误信息，空字典表示验证通过
        """
        errors = {}
        
        if storage_type == StorageType.LOCAL:
            if not config.get('base_path'):
                errors['base_path'] = 'Base path is required for local storage'
        
        elif storage_type == StorageType.MINIO:
            required_fields = ['endpoint', 'access_key', 'secret_key', 'bucket_name']
            for field in required_fields:
                if not config.get(field):
                    errors[field] = f'{field} is required for MinIO storage'
        
        elif storage_type in [StorageType.FTP, StorageType.SFTP]:
            required_fields = ['host', 'username', 'password']
            for field in required_fields:
                if not config.get(field):
                    errors[field] = f'{field} is required for FTP storage'
            
            # 验证端口号
            port = config.get('port')
            if port is not None:
                try:
                    port_int = int(port)
                    if not (1 <= port_int <= 65535):
                        errors['port'] = 'Port must be between 1 and 65535'
                except ValueError:
                    errors['port'] = 'Port must be a valid integer'
        
        return errors
    
    @classmethod
    def get_default_config(cls, storage_type: StorageType) -> Dict[str, Any]:
        """
        获取默认配置
        
        Args:
            storage_type: 存储类型
            
        Returns:
            Dict[str, Any]: 默认配置
        """
        defaults = {
            StorageType.LOCAL: {
                'base_path': './storage',
                'max_file_size': 100 * 1024 * 1024,  # 100MB
            },
            StorageType.MINIO: {
                'endpoint': 'localhost:9000',
                'secure': False,
                'region': 'us-east-1',
            },
            StorageType.FTP: {
                'port': 21,
                'base_path': '/',
                'passive': True,
                'encoding': 'utf-8',
            },
            StorageType.SFTP: {
                'port': 22,
                'base_path': '/',
            }
        }
        
        return defaults.get(storage_type, {})
    
    @classmethod
    async def test_connection(cls, storage_type: StorageType, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        测试存储连接
        
        Args:
            storage_type: 存储类型
            config: 存储配置
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        result = {
            'success': False,
            'message': '',
            'details': {}
        }
        
        try:
            # 验证配置
            validation_errors = cls.validate_config(storage_type, config)
            if validation_errors:
                result['message'] = 'Configuration validation failed'
                result['details'] = validation_errors
                return result
            
            # 创建存储实例并测试连接
            storage = cls.create_storage(storage_type, config)
            
            # 测试连接
            connected = await storage.connect()
            if not connected:
                result['message'] = 'Failed to connect to storage'
                return result
            
            # 测试基本操作
            try:
                # 尝试列出根目录
                files = await storage.list_files('/')
                result['details']['root_files_count'] = len(files)
                
                # 获取存储信息
                storage_info = await storage.get_storage_info()
                result['details']['storage_info'] = storage_info
                
                result['success'] = True
                result['message'] = 'Connection test successful'
            
            except Exception as e:
                result['message'] = f'Connection established but operation failed: {str(e)}'
            
            finally:
                await storage.disconnect()
        
        except Exception as e:
            result['message'] = f'Connection test failed: {str(e)}'
        
        return result
