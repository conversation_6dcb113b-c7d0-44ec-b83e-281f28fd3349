<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>浏览器兼容性测试 - AI工具集</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            background: -webkit-linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            background: -moz-linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            background: -o-linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 紧凑头部 */
        .header {
            text-align: center;
            margin-bottom: 15px;
            padding: 8px 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            -webkit-border-radius: 8px;
            -moz-border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            -moz-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .header h1 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #374151;
            margin: 0 0 2px 0;
            line-height: 1.2;
        }

        .header p {
            font-size: 0.75rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.2;
        }

        /* 卡片网格 - 兼容性优化 */
        .cards-grid {
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-flex-wrap: wrap;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
            margin: -8px;
        }

        .card {
            -webkit-box-flex: 0;
            -webkit-flex: 0 0 calc(33.333% - 16px);
            -moz-box-flex: 0;
            -ms-flex: 0 0 calc(33.333% - 16px);
            flex: 0 0 calc(33.333% - 16px);
            margin: 8px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            -webkit-border-radius: 12px;
            -moz-border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            -webkit-box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            -moz-box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
            -moz-transition: all 0.3s ease;
            -o-transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: start;
            -webkit-align-items: flex-start;
            -moz-box-align: start;
            -ms-flex-align: start;
            align-items: flex-start;
        }

        .card:hover {
            transform: translateY(-2px);
            -webkit-transform: translateY(-2px);
            -moz-transform: translateY(-2px);
            -o-transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            -webkit-box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            -moz-box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: #e0e7ff;
            background: -webkit-linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            background: -moz-linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 10px;
            -webkit-border-radius: 10px;
            -moz-border-radius: 10px;
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -moz-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            -moz-box-pack: center;
            -ms-flex-pack: center;
            justify-content: center;
            font-size: 24px;
            color: #6366f1;
            margin-right: 12px;
            -webkit-box-flex: 0;
            -webkit-flex-shrink: 0;
            -moz-box-flex: 0;
            -ms-flex-negative: 0;
            flex-shrink: 0;
        }

        .card-content {
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            -moz-box-flex: 1;
            -ms-flex: 1;
            flex: 1;
            min-width: 0;
        }

        .card-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .card-meta {
            font-size: 0.75rem;
            color: #9ca3af;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .card-company {
            font-weight: 500;
            color: #6366f1;
        }

        .card-creator {
            color: #8b5cf6;
        }

        .card-description {
            font-size: 0.85rem;
            color: #6b7280;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                margin-bottom: 10px;
                padding: 6px 15px;
            }
            
            .header h1 {
                font-size: 1.1rem;
            }
            
            .header p {
                font-size: 0.7rem;
            }
            
            .card {
                -webkit-flex: 0 0 100%;
                -ms-flex: 0 0 100%;
                flex: 0 0 100%;
                margin: 6px 0;
            }
            
            .card-icon {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }
            
            .card-title {
                font-size: 0.95rem;
            }
            
            .card-description {
                font-size: 0.8rem;
            }
        }

        @media screen and (max-width: 1024px) and (min-width: 769px) {
            .card {
                -webkit-flex: 0 0 calc(50% - 16px);
                -ms-flex: 0 0 calc(50% - 16px);
                flex: 0 0 calc(50% - 16px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI智能工具集</h1>
            <p>兼容IE9+、Chrome、Firefox、Safari、Edge等主流浏览器</p>
        </div>

        <div class="cards-grid">
            <div class="card">
                <div class="card-icon">🤖</div>
                <div class="card-content">
                    <div class="card-title">ChatGPT</div>
                    <div class="card-meta">
                        <span class="card-company">OpenAI</span>
                        <span> • </span>
                        <span class="card-creator">Sam Altman</span>
                    </div>
                    <div class="card-description">OpenAI开发的强大对话AI，支持文本生成、问答、编程等多种任务</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">🎨</div>
                <div class="card-content">
                    <div class="card-title">Midjourney</div>
                    <div class="card-meta">
                        <span class="card-company">Midjourney Inc</span>
                        <span> • </span>
                        <span class="card-creator">David Holz</span>
                    </div>
                    <div class="card-description">AI图像生成工具，通过文本描述创造惊艳的艺术作品和插图</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">📝</div>
                <div class="card-content">
                    <div class="card-title">Notion AI</div>
                    <div class="card-meta">
                        <span class="card-company">Notion Labs</span>
                        <span> • </span>
                        <span class="card-creator">Ivan Zhao</span>
                    </div>
                    <div class="card-description">集成在Notion中的AI写作助手，帮助提升文档编写效率</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">🎵</div>
                <div class="card-content">
                    <div class="card-title">AIVA</div>
                    <div class="card-meta">
                        <span class="card-company">AIVA Technologies</span>
                        <span> • </span>
                        <span class="card-creator">Pierre Barreau</span>
                    </div>
                    <div class="card-description">AI音乐创作平台，自动生成各种风格的原创音乐作品</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">🎬</div>
                <div class="card-content">
                    <div class="card-title">Runway ML</div>
                    <div class="card-meta">
                        <span class="card-company">Runway AI</span>
                        <span> • </span>
                        <span class="card-creator">Cristóbal Valenzuela</span>
                    </div>
                    <div class="card-description">AI视频编辑工具，提供视频生成、编辑和特效制作功能</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">💻</div>
                <div class="card-content">
                    <div class="card-title">GitHub Copilot</div>
                    <div class="card-meta">
                        <span class="card-company">GitHub/Microsoft</span>
                        <span> • </span>
                        <span class="card-creator">Nat Friedman</span>
                    </div>
                    <div class="card-description">AI编程助手，实时提供代码建议和自动补全功能</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 兼容性JavaScript - 支持IE9+
        (function() {
            'use strict';
            
            // 兼容性检测
            function checkBrowserSupport() {
                var support = {
                    flexbox: false,
                    grid: false,
                    addEventListener: !!document.addEventListener
                };
                
                // 检测Flexbox支持
                var testEl = document.createElement('div');
                testEl.style.display = 'flex';
                support.flexbox = testEl.style.display === 'flex';
                
                // 检测Grid支持
                testEl.style.display = 'grid';
                support.grid = testEl.style.display === 'grid';
                
                return support;
            }
            
            // 事件绑定兼容性函数
            function addEvent(element, event, handler) {
                if (element.addEventListener) {
                    element.addEventListener(event, handler, false);
                } else if (element.attachEvent) {
                    element.attachEvent('on' + event, handler);
                } else {
                    element['on' + event] = handler;
                }
            }
            
            // DOM Ready兼容性函数
            function ready(fn) {
                if (document.readyState === 'complete' || document.readyState === 'interactive') {
                    setTimeout(fn, 1);
                } else if (document.addEventListener) {
                    document.addEventListener('DOMContentLoaded', fn);
                } else if (document.attachEvent) {
                    document.attachEvent('onreadystatechange', function() {
                        if (document.readyState === 'complete') {
                            fn();
                        }
                    });
                } else {
                    window.onload = fn;
                }
            }
            
            // 初始化
            ready(function() {
                var support = checkBrowserSupport();
                console.log('浏览器支持情况:', support);
                
                // 为不支持Flexbox的浏览器添加fallback
                if (!support.flexbox) {
                    var cards = document.querySelectorAll('.card');
                    for (var i = 0; i < cards.length; i++) {
                        cards[i].style.display = 'block';
                        cards[i].style.width = '300px';
                        cards[i].style.float = 'left';
                        cards[i].style.margin = '8px';
                    }
                }
                
                // 添加点击事件
                var cards = document.querySelectorAll('.card');
                for (var i = 0; i < cards.length; i++) {
                    (function(card) {
                        addEvent(card, 'click', function() {
                            var title = card.querySelector('.card-title').textContent || card.querySelector('.card-title').innerText;
                            alert('点击了: ' + title);
                        });
                    })(cards[i]);
                }
            });
        })();
    </script>
</body>
</html>
