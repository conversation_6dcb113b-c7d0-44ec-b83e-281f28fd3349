"""
添加Celery配置和指标表的迁移脚本
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from app.core.database import get_async_session, init_database
from loguru import logger


async def create_celery_tables():
    """创建Celery相关表"""
    
    # Celery配置表
    celery_configurations_sql = """
    CREATE TABLE IF NOT EXISTS celery_configurations (
        id SERIAL PRIMARY KEY,
        redis_host VARCHAR(255) DEFAULT '**************',
        redis_port INTEGER DEFAULT 6379,
        redis_db INTEGER DEFAULT 10,
        redis_password VARCHAR(255),
        worker_concurrency INTEGER DEFAULT 4,
        worker_prefetch_multiplier INTEGER DEFAULT 1,
        worker_max_tasks_per_child INTEGER DEFAULT 1000,
        worker_max_memory_per_child INTEGER DEFAULT 200000,
        task_soft_time_limit INTEGER DEFAULT 300,
        task_time_limit INTEGER DEFAULT 600,
        task_max_retries INTEGER DEFAULT 3,
        task_default_retry_delay INTEGER DEFAULT 60,
        result_expires INTEGER DEFAULT 3600,
        result_backend_transport_options TEXT,
        task_routes TEXT,
        task_default_queue VARCHAR(100) DEFAULT 'default',
        worker_send_task_events BOOLEAN DEFAULT TRUE,
        task_send_sent_event BOOLEAN DEFAULT TRUE,
        flower_port INTEGER DEFAULT 5555,
        flower_basic_auth VARCHAR(255) DEFAULT 'admin:password',
        timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
        enable_utc BOOLEAN DEFAULT TRUE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    # Celery指标表
    celery_metrics_sql = """
    CREATE TABLE IF NOT EXISTS celery_metrics (
        id SERIAL PRIMARY KEY,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        active_workers INTEGER DEFAULT 0,
        total_workers INTEGER DEFAULT 0,
        active_tasks INTEGER DEFAULT 0,
        processed_tasks INTEGER DEFAULT 0,
        failed_tasks INTEGER DEFAULT 0,
        retried_tasks INTEGER DEFAULT 0,
        queue_lengths TEXT,
        avg_task_runtime REAL DEFAULT 0.0,
        task_throughput REAL DEFAULT 0.0,
        worker_memory_usage TEXT,
        redis_memory_usage INTEGER DEFAULT 0,
        redis_connected_clients INTEGER DEFAULT 0
    );
    """
    
    # 创建索引
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_celery_configurations_active ON celery_configurations(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_celery_metrics_timestamp ON celery_metrics(timestamp);",
    ]
    
    try:
        async for session in get_async_session():
            # 创建表
            await session.execute(text(celery_configurations_sql))
            await session.execute(text(celery_metrics_sql))
            
            # 创建索引
            for index_sql in create_indexes_sql:
                await session.execute(text(index_sql))
            
            await session.commit()
            logger.info("Celery表创建成功")
            break
            
    except Exception as e:
        logger.error(f"创建Celery表失败: {e}")
        raise


async def main():
    """主函数"""
    try:
        # 初始化数据库
        from app.core.config import get_settings
        settings = get_settings()
        await init_database(settings)
        logger.info("数据库初始化成功")

        # 创建Celery表
        await create_celery_tables()

    except Exception as e:
        logger.error(f"迁移失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
