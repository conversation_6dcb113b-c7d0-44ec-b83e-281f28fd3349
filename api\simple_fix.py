"""
简单的数据库修复脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("开始修复数据库...")
    
    # 导入模型
    from app.models import *
    from app.core.database import engine, Base
    
    print("✓ 模型导入成功")
    
    # 重新创建表
    Base.metadata.create_all(bind=engine)
    print("✓ 数据库表创建成功")
    
    print("🎉 修复完成！")
    
except Exception as e:
    print(f"❌ 修复失败: {e}")
    import traceback
    traceback.print_exc()
