#!/usr/bin/env python3
"""
Celery Flower监控启动脚本
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.celery_config import celery_app

if __name__ == '__main__':
    # 启动Flower监控
    from flower.command import FlowerCommand
    
    flower = FlowerCommand()
    flower.execute_from_commandline([
        'flower',
        '--broker=' + celery_app.conf.broker_url,
        '--port=5555',
        '--basic_auth=admin:password'
    ])
