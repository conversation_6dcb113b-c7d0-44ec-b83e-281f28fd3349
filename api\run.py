#!/usr/bin/env python3
"""
简单的启动脚本
避免CLI工具的复杂性
"""

import uvicorn
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 设置默认环境
if not os.getenv('ENVIRONMENT'):
    os.environ['ENVIRONMENT'] = 'development'

if __name__ == "__main__":
    # 导入应用
    from main import app
    
    # 运行服务器
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
