"""Add missing columns to storage_configs

Revision ID: 002
Revises: 001
Create Date: 2025-06-14 11:49:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add missing columns to storage_configs table"""
    
    # Check if columns exist before adding them
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('storage_configs')]
    
    # Add total_files column if it doesn't exist
    if 'total_files' not in columns:
        op.add_column('storage_configs', sa.Column('total_files', sa.Integer(), nullable=True, default=0, comment='总文件数'))
    
    # Add total_size column if it doesn't exist
    if 'total_size' not in columns:
        op.add_column('storage_configs', sa.Column('total_size', sa.BigInteger(), nullable=True, default=0, comment='总大小(字节)'))
    
    # Add last_sync_at column if it doesn't exist
    if 'last_sync_at' not in columns:
        op.add_column('storage_configs', sa.Column('last_sync_at', sa.DateTime(timezone=True), nullable=True, comment='最后同步时间'))
    
    # Update existing records to have default values
    op.execute("UPDATE storage_configs SET total_files = 0 WHERE total_files IS NULL")
    op.execute("UPDATE storage_configs SET total_size = 0 WHERE total_size IS NULL")


def downgrade() -> None:
    """Remove the added columns"""
    
    # Check if columns exist before dropping them
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('storage_configs')]
    
    if 'last_sync_at' in columns:
        op.drop_column('storage_configs', 'last_sync_at')
    
    if 'total_size' in columns:
        op.drop_column('storage_configs', 'total_size')
    
    if 'total_files' in columns:
        op.drop_column('storage_configs', 'total_files')
