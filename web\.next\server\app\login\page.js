/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$":
/*!**************************************************************!*\
  !*** ./lib/i18n/locales/ lazy ^\.\/.*\.ts$ namespace object ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.ts": [
		"(ssr)/./lib/i18n/locales/en.ts",
		"_ssr_lib_i18n_locales_en_ts"
	],
	"./ja.ts": [
		"(ssr)/./lib/i18n/locales/ja.ts",
		"_ssr_lib_i18n_locales_ja_ts"
	],
	"./zh-CN.ts": [
		"(ssr)/./lib/i18n/locales/zh-CN.ts",
		"_ssr_lib_i18n_locales_zh-CN_ts"
	],
	"./zh-TW.ts": [
		"(ssr)/./lib/i18n/locales/zh-TW.ts",
		"_ssr_lib_i18n_locales_zh-TW_ts"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1aa5\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(ssr)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFzRiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWtub3dsZWRnZS1iYXNlLXdlYi8/ZGFkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXHdvcmtzcGFjZVxcXFx4aGMtcmFnXFxcXHdlYlxcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/LanguageContext.tsx */ \"(ssr)/./contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q2NvbnRleHRzJTVDJTVDTGFuZ3VhZ2VDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkxhbmd1YWdlUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRiUzQSU1QyU1Q3dvcmtzcGFjZSU1QyU1Q3hoYy1yYWclNUMlNUN3ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjMwX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtaW50ZXIlNUMlMjIlMkMlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Qy5wbnBtJTVDJTVDbmV4dCU0MDE0LjIuMzBfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySmV0QnJhaW5zX01vbm8lNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1qZXRicmFpbnMtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmpldGJyYWluc01vbm8lNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRiUzQSU1QyU1Q3dvcmtzcGFjZSU1QyU1Q3hoYy1yYWclNUMlNUN3ZWIlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Qy5wbnBtJTVDJTVDcmVhY3QtaG90LXRvYXN0JTQwMi41LjJfcmVhY3RfOWJjMDU0YWEzZGU4Y2FlNTdiZDNiNzhhOGE4NzFhNGQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNyZWFjdC1ob3QtdG9hc3QlNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXFJO0FBQ3JJO0FBQ0Esc1dBQWdPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWkta25vd2xlZGdlLWJhc2Utd2ViLz9kZmNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTGFuZ3VhZ2VQcm92aWRlclwiXSAqLyBcIkY6XFxcXHdvcmtzcGFjZVxcXFx4aGMtcmFnXFxcXHdlYlxcXFxjb250ZXh0c1xcXFxMYW5ndWFnZUNvbnRleHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUb2FzdGVyXCJdICovIFwiRjpcXFxcd29ya3NwYWNlXFxcXHhoYy1yYWdcXFxcd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxyZWFjdC1ob3QtdG9hc3RAMi41LjJfcmVhY3RfOWJjMDU0YWEzZGU4Y2FlNTdiZDNiNzhhOGE4NzFhNGRcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Loader2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _components_AIBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AIBackground */ \"(ssr)/./components/AIBackground.tsx\");\n/* harmony import */ var _components_LoginForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoginForm */ \"(ssr)/./components/LoginForm.tsx\");\n/* harmony import */ var _components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LanguageSwitcher */ \"(ssr)/./components/LanguageSwitcher.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(ssr)/./components/ErrorBoundary.tsx\");\n/* harmony import */ var _lib_systemInit__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/systemInit */ \"(ssr)/./lib/systemInit.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nconst LoginPage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkSystemStatus = async ()=>{\n            try {\n                // 首先检查系统是否需要首次设置\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/api/v1/setup/status\");\n                const status = response.data.data;\n                if (status.needs_setup) {\n                    // 系统需要首次设置，跳转到设置页面\n                    router.push(\"/setup\");\n                    return;\n                }\n                // 检查是否是从设置页面跳转过来的\n                const message = searchParams.get(\"message\");\n                if (message === \"setup_complete\") {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"\\uD83C\\uDF89 系统初始化完成！请使用管理员账户登录\", {\n                        duration: 5000,\n                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-5 h-5 text-green-500\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 19\n                        }, undefined)\n                    });\n                }\n            } catch (error) {\n                console.error(\"检查系统状态失败:\", error);\n                // 如果检查失败，尝试检查旧的初始化状态\n                try {\n                    const { need_init } = await _lib_systemInit__WEBPACK_IMPORTED_MODULE_8__.systemInitApi.checkNeedInit();\n                    if (need_init) {\n                        router.push(\"/system-init\");\n                        return;\n                    }\n                } catch (legacyError) {\n                    console.error(\"检查旧初始化状态失败:\", legacyError);\n                    // 如果都失败，跳转到首次设置页面\n                    router.push(\"/setup\");\n                    return;\n                }\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        checkSystemStatus();\n    }, [\n        router,\n        searchParams\n    ]);\n    // 如果正在检查系统状态，显示加载界面\n    if (isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        animate: {\n                            rotate: 360\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"inline-block mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-12 h-12 text-white\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"正在检查系统状态...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIBackground__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 min-h-screen flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"hidden lg:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.h1, {\n                                            className: \"text-5xl font-bold mb-6 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.2\n                                            },\n                                            children: [\n                                                \"知识库\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"智能平台\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                            className: \"text-xl text-blue-200 mb-8 leading-relaxed\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            children: \"体验智能文档处理的未来， 基于AI的智能知识管理与检索系统。\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            className: \"space-y-4\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.6\n                                            },\n                                            children: [\n                                                \"\\uD83E\\uDDE0 智能AI处理\",\n                                                \"\\uD83D\\uDD0D 智能文档检索\",\n                                                \"⚡ 实时分析\",\n                                                \"\\uD83D\\uDEE1️ 企业级安全\"\n                                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    className: \"flex items-center space-x-3\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: 0.8 + index * 0.1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-100\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            className: \"mt-12 grid grid-cols-3 gap-6\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 1.2\n                                            },\n                                            children: [\n                                                {\n                                                    number: \"99.9%\",\n                                                    label: \"Uptime\"\n                                                },\n                                                {\n                                                    number: \"10M+\",\n                                                    label: \"Documents\"\n                                                },\n                                                {\n                                                    number: \"500+\",\n                                                    label: \"Enterprises\"\n                                                }\n                                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-1\",\n                                                            children: stat.number\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-blue-200\",\n                                                            children: stat.label\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoginForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 1\n                    },\n                    className: \"absolute top-6 right-6 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 1,\n                        delay: 1.5\n                    },\n                    className: \"absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-50\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 4000,\n                        style: {\n                            background: \"rgba(255, 255, 255, 0.1)\",\n                            backdropFilter: \"blur(10px)\",\n                            border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                            color: \"white\"\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/AIBackground.tsx":
/*!*************************************!*\
  !*** ./components/AIBackground.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst AIBackground = ()=>{\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        // 设置画布大小\n        const resizeCanvas = ()=>{\n            canvas.width = window.innerWidth;\n            canvas.height = window.innerHeight;\n        };\n        resizeCanvas();\n        window.addEventListener(\"resize\", resizeCanvas);\n        // 粒子系统\n        const particles = [];\n        const particleCount = 100;\n        class Particle {\n            constructor(){\n                this.x = Math.random() * canvas.width;\n                this.y = Math.random() * canvas.height;\n                this.vx = (Math.random() - 0.5) * 0.5;\n                this.vy = (Math.random() - 0.5) * 0.5;\n                this.size = Math.random() * 2 + 1;\n                this.opacity = Math.random() * 0.5 + 0.2;\n                this.color = `rgba(59, 130, 246, ${this.opacity})`; // 蓝色主题\n            }\n            update() {\n                this.x += this.vx;\n                this.y += this.vy;\n                // 边界检测\n                if (this.x < 0 || this.x > canvas.width) this.vx *= -1;\n                if (this.y < 0 || this.y > canvas.height) this.vy *= -1;\n                // 保持在画布内\n                this.x = Math.max(0, Math.min(canvas.width, this.x));\n                this.y = Math.max(0, Math.min(canvas.height, this.y));\n            }\n            draw() {\n                ctx.beginPath();\n                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n                ctx.fillStyle = this.color;\n                ctx.fill();\n            }\n        }\n        // 初始化粒子\n        for(let i = 0; i < particleCount; i++){\n            particles.push(new Particle());\n        }\n        // 绘制连接线\n        const drawConnections = ()=>{\n            for(let i = 0; i < particles.length; i++){\n                for(let j = i + 1; j < particles.length; j++){\n                    const dx = particles[i].x - particles[j].x;\n                    const dy = particles[i].y - particles[j].y;\n                    const distance = Math.sqrt(dx * dx + dy * dy);\n                    if (distance < 100) {\n                        const opacity = (100 - distance) / 100 * 0.1;\n                        ctx.beginPath();\n                        ctx.moveTo(particles[i].x, particles[i].y);\n                        ctx.lineTo(particles[j].x, particles[j].y);\n                        ctx.strokeStyle = `rgba(59, 130, 246, ${opacity})`;\n                        ctx.lineWidth = 1;\n                        ctx.stroke();\n                    }\n                }\n            }\n        };\n        // 动画循环\n        const animate = ()=>{\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            // 更新和绘制粒子\n            particles.forEach((particle)=>{\n                particle.update();\n                particle.draw();\n            });\n            // 绘制连接线\n            drawConnections();\n            requestAnimationFrame(animate);\n        };\n        animate();\n        return ()=>{\n            window.removeEventListener(\"resize\", resizeCanvas);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 -z-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900\"\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 opacity-60\"\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl\",\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            opacity: [\n                                0.3,\n                                0.5,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-3/4 right-1/4 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl\",\n                        animate: {\n                            scale: [\n                                1.2,\n                                1,\n                                1.2\n                            ],\n                            opacity: [\n                                0.2,\n                                0.4,\n                                0.2\n                            ]\n                        },\n                        transition: {\n                            duration: 5,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/2 right-1/3 w-64 h-64 bg-indigo-500/20 rounded-full blur-3xl\",\n                        animate: {\n                            scale: [\n                                1,\n                                1.3,\n                                1\n                            ],\n                            opacity: [\n                                0.4,\n                                0.2,\n                                0.4\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-50\"\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\AIBackground.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIBackground);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AIBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ErrorBoundary.tsx":
/*!**************************************!*\
  !*** ./components/ErrorBoundary.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"max-w-md w-full bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                scale: 0\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            transition: {\n                                delay: 0.2\n                            },\n                            className: \"inline-flex items-center justify-center w-16 h-16 bg-red-500/20 rounded-full mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-8 h-8 text-red-400\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-white mb-4\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-200 mb-6\",\n                            children: \"We encountered an unexpected error. Please try refreshing the page.\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"mb-6 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"text-sm text-blue-300 cursor-pointer mb-2\",\n                                    children: \"Error Details\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-xs text-red-300 bg-black/20 p-3 rounded overflow-auto max-h-32\",\n                                    children: this.state.error.message\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            onClick: this.handleRetry,\n                            className: \"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.state = {\n            hasError: false\n        };\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/LanguageSwitcher.tsx":
/*!*****************************************!*\
  !*** ./components/LanguageSwitcher.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst LanguageSwitcher = ()=>{\n    const { language, setLanguage, t, isLoading } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 如果翻译还未加载，显示加载状态\n    if (isLoading || !t) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: \"sm\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined);\n    }\n    const languages = [\n        {\n            code: \"zh-CN\",\n            name: t.language.chinese,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"zh-CN\"].flag\n        },\n        {\n            code: \"zh-TW\",\n            name: t.language.traditionalChinese,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"zh-TW\"].flag\n        },\n        {\n            code: \"en\",\n            name: t.language.english,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"en\"].flag\n        },\n        {\n            code: \"ja\",\n            name: t.language.japanese,\n            flag: _lib_i18n__WEBPACK_IMPORTED_MODULE_3__.languageConfig[\"ja\"].flag\n        }\n    ];\n    const currentLanguage = languages.find((lang)=>lang.code === language);\n    const handleLanguageChange = (newLanguage)=>{\n        setLanguage(newLanguage);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-200\",\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: [\n                            currentLanguage?.flag,\n                            \" \",\n                            currentLanguage?.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        animate: {\n                            rotate: isOpen ? 180 : 0\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 14\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            className: \"fixed inset-0 z-40\",\n                            onClick: ()=>setIsOpen(false)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10,\n                                scale: 0.95\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -10,\n                                scale: 0.95\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            className: \"absolute top-full mt-2 right-0 z-50 min-w-[200px] bg-white/95 backdrop-blur-md rounded-xl border border-white/20 shadow-2xl overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-2\",\n                                children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: ()=>handleLanguageChange(lang.code),\n                                        className: `w-full px-4 py-3 text-left flex items-center space-x-3 hover:bg-blue-50 transition-colors duration-150 ${language === lang.code ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-700\"}`,\n                                        whileHover: {\n                                            x: 4\n                                        },\n                                        transition: {\n                                            duration: 0.1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: lang.flag\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: lang.name\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            language === lang.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                className: \"ml-auto w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, lang.code, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LanguageSwitcher.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./components/LoadingSpinner.tsx":
/*!***************************************!*\
  !*** ./components/LoadingSpinner.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst LoadingSpinner = ({ size = \"md\", className = \"\", text })=>{\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-8 h-8\",\n        lg: \"w-12 h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col items-center justify-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                animate: {\n                    rotate: 360\n                },\n                transition: {\n                    duration: 1,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                },\n                className: `${sizeClasses[size]} border-2 border-white border-t-transparent rounded-full`\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoadingSpinner.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 0.5\n                },\n                className: \"mt-2 text-sm text-white/80\",\n                children: text\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoadingSpinner.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingSpinner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./components/LoginForm.tsx":
/*!**********************************!*\
  !*** ./components/LoginForm.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/.pnpm/react-hook-form@7.57.0_react@18.3.1/node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Eye,EyeOff,Loader2,Lock,Shield,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst LoginForm = ()=>{\n    const { t, isLoading: langLoading } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 始终调用所有 Hooks，避免条件调用\n    const { register, handleSubmit, formState: { errors }, setError } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)();\n    // 如果语言还在加载中，显示加载状态\n    if (langLoading || !t) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"lg\",\n                text: \"Loading language...\",\n                className: \"h-96\"\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined);\n    }\n    const onSubmit = async (data)=>{\n        setIsLoading(true);\n        try {\n            const loginData = {\n                username: data.username,\n                password: data.password\n            };\n            await (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.login)(loginData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(t.login.loginSuccess, {\n                icon: \"\\uD83C\\uDF89\",\n                style: {\n                    background: \"#10B981\",\n                    color: \"white\"\n                }\n            });\n            // 登录成功后跳转到仪表板\n            setTimeout(()=>{\n                window.location.href = \"/dashboard\";\n            }, 1000);\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            let errorMessage = t.login.loginError;\n            if (error.message.includes(\"Invalid credentials\") || error.message.includes(\"401\")) {\n                errorMessage = t.login.invalidCredentials;\n            } else if (error.message.includes(\"Network Error\") || error.message.includes(\"timeout\")) {\n                errorMessage = t.login.networkError;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(errorMessage, {\n                icon: \"❌\",\n                style: {\n                    background: \"#EF4444\",\n                    color: \"white\"\n                }\n            });\n            setError(\"root\", {\n                message: errorMessage\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const aiFeatures = [\n        {\n            icon: _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: t.ai.intelligentSystem,\n            description: t.ai.smartAnalysis\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: t.ai.secureLogin,\n            description: t.ai.dataProtection\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: t.ai.aiAssistant,\n            description: t.ai.poweredBy\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-4\",\n                        whileHover: {\n                            scale: 1.05,\n                            rotate: 5\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-8 h-8 text-white\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-white mb-2\",\n                        children: t.login.title\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-200 text-sm\",\n                        children: t.login.subtitle\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.2\n                },\n                className: \"bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-white mb-2\",\n                                children: t.login.welcomeBack\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: t.ai.poweredBy\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-white mb-2\",\n                                        children: t.login.username\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...register(\"username\", {\n                                                    required: t.login.usernameRequired,\n                                                    minLength: {\n                                                        value: 3,\n                                                        message: t.login.usernameMinLength\n                                                    }\n                                                }),\n                                                type: \"text\",\n                                                className: \"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                                                placeholder: t.login.username,\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"mt-1 text-sm text-red-300\",\n                                        children: errors.username.message\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-white mb-2\",\n                                        children: t.login.password\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...register(\"password\", {\n                                                    required: t.login.passwordRequired,\n                                                    minLength: {\n                                                        value: 6,\n                                                        message: t.login.passwordMinLength\n                                                    }\n                                                }),\n                                                type: showPassword ? \"text\" : \"password\",\n                                                className: \"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                                                placeholder: t.login.password,\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setShowPassword(!showPassword),\n                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-blue-300 hover:text-white transition-colors duration-200\",\n                                                disabled: isLoading,\n                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 33\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 66\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: -10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"mt-1 text-sm text-red-300\",\n                                        children: errors.password.message\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ...register(\"rememberMe\"),\n                                                type: \"checkbox\",\n                                                className: \"w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-blue-200\",\n                                                children: t.login.rememberMe\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"text-sm text-blue-300 hover:text-white transition-colors duration-200\",\n                                        disabled: isLoading,\n                                        children: t.login.forgotPassword\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                                type: \"submit\",\n                                disabled: isLoading,\n                                className: \"w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                whileHover: {\n                                    scale: isLoading ? 1 : 1.02\n                                },\n                                whileTap: {\n                                    scale: isLoading ? 1 : 0.98\n                                },\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Eye_EyeOff_Loader2_Lock_Shield_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        t.common.loading\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, undefined) : t.login.loginButton\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-200 text-sm\",\n                                        children: [\n                                            t.login.noAccount,\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"text-sm text-blue-300 hover:text-white font-medium transition-colors duration-200\",\n                                        disabled: isLoading,\n                                        children: t.login.signUp\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"mt-8 grid grid-cols-3 gap-4\",\n                children: aiFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"text-center\",\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center justify-center w-12 h-12 bg-white/10 rounded-lg mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                    className: \"w-6 h-6 text-blue-300\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-medium text-white mb-1\",\n                                children: feature.title\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-blue-200\",\n                                children: feature.description\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\components\\\\LoginForm.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginForm);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n    const [t, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化语言设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeLanguage = async ()=>{\n            setIsLoading(true);\n            try {\n                // 预加载所有语言\n                await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.preloadAllLanguages)();\n                // 从localStorage加载语言设置\n                const savedLanguage = localStorage.getItem(\"language\");\n                let targetLanguage;\n                if (savedLanguage && [\n                    \"zh-CN\",\n                    \"zh-TW\",\n                    \"en\",\n                    \"ja\"\n                ].includes(savedLanguage)) {\n                    targetLanguage = savedLanguage;\n                } else {\n                    // 检测浏览器语言\n                    targetLanguage = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.detectBrowserLanguage)();\n                }\n                setLanguageState(targetLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(targetLanguage);\n                setTranslations(translations);\n            } catch (error) {\n                console.error(\"Failed to initialize language:\", error);\n                // 回退到默认语言\n                setLanguageState(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                setTranslations(translations);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeLanguage();\n    }, []);\n    const setLanguage = async (newLanguage)=>{\n        setIsLoading(true);\n        try {\n            setLanguageState(newLanguage);\n            const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(newLanguage);\n            setTranslations(translations);\n            localStorage.setItem(\"language\", newLanguage);\n        } catch (error) {\n            console.error(\"Failed to set language:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage,\n            t,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkHealth: () => (/* binding */ checkHealth),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storageApi: () => (/* binding */ storageApi),\n/* harmony export */   testCors: () => (/* binding */ testCors)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API客户端配置\n * 与后端FastAPI接口对接\n */ \n\n// API基础配置\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    },\n    withCredentials: false\n});\n// 添加调试日志\nconsole.log(\"API Client initialized with base URL:\", API_BASE_URL);\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 添加认证token\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    // 添加请求ID用于追踪\n    config.headers[\"X-Request-ID\"] = generateRequestId();\n    // 调试日志\n    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    console.log(`   Base URL: ${config.baseURL}`);\n    console.log(`   Full URL: ${config.baseURL}${config.url}`);\n    console.log(`   Timeout: ${config.timeout}ms`);\n    return config;\n}, (error)=>{\n    console.error(\"❌ Request interceptor error:\", error);\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);\n    return response;\n}, async (error)=>{\n    console.error(`❌ API Error: ${error.code} ${error.config?.method?.toUpperCase()} ${error.config?.url}`);\n    console.error(`   Message: ${error.message}`);\n    console.error(`   Status: ${error.response?.status}`);\n    const originalRequest = error.config;\n    // 处理401未授权错误\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        // 尝试刷新token\n        const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n        if (refreshToken) {\n            try {\n                const response = await refreshAccessToken(refreshToken);\n                const newToken = response.data.access_token;\n                // 更新token\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", newToken, {\n                    expires: 1\n                });\n                // 重试原请求\n                originalRequest.headers.Authorization = `Bearer ${newToken}`;\n                return apiClient(originalRequest);\n            } catch (refreshError) {\n                // 刷新失败，清除token并跳转到登录页\n                clearAuthTokens();\n                window.location.href = \"/login\";\n                return Promise.reject(refreshError);\n            }\n        } else {\n            // 没有刷新token，直接跳转到登录页\n            clearAuthTokens();\n            window.location.href = \"/login\";\n        }\n    }\n    return Promise.reject(error);\n});\n// 生成请求ID\nfunction generateRequestId() {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n// 清除认证token\nfunction clearAuthTokens() {\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n}\n// 刷新访问token\nasync function refreshAccessToken(refreshToken) {\n    return axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/api/v1/auth/refresh`, {\n        refresh_token: refreshToken\n    });\n}\n// 登录\nconst login = async (credentials)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Attempting login with:\", {\n            username: credentials.username\n        });\n        console.log(\"\\uD83C\\uDF10 API Base URL:\", API_BASE_URL);\n        console.log(\"\\uD83D\\uDCE1 Full login URL:\", `${API_BASE_URL}/api/v1/auth/login`);\n        const response = await apiClient.post(\"/api/v1/auth/login\", credentials);\n        console.log(\"✅ Login response:\", response.data);\n        // 保存token到cookie\n        const { access_token, refresh_token, expires_in } = response.data;\n        // 设置cookie过期时间（转换为天数）\n        const expiresInDays = expires_in / (60 * 60 * 24);\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access_token, {\n            expires: expiresInDays\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", refresh_token, {\n            expires: 7\n        }); // 刷新token保存7天\n        // 同时保存到localStorage作为备份\n        localStorage.setItem(\"token\", access_token);\n        localStorage.setItem(\"refresh_token\", refresh_token);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ Login error details:\", error);\n        console.error(\"   Error code:\", error.code);\n        console.error(\"   Error message:\", error.message);\n        console.error(\"   Error response:\", error.response?.data);\n        console.error(\"   Error status:\", error.response?.status);\n        console.error(\"   Request config:\", error.config);\n        // 处理不同类型的错误响应\n        if (error.code === \"ECONNABORTED\") {\n            throw new Error(`请求超时，请检查网络连接或稍后重试。服务器地址：${API_BASE_URL}`);\n        } else if (error.response?.data?.detail) {\n            throw new Error(error.response.data.detail);\n        } else if (error.response?.data?.error?.message) {\n            throw new Error(error.response.data.error.message);\n        } else if (error.response?.status === 401) {\n            throw new Error(\"用户名或密码错误\");\n        } else if (error.response?.status >= 500) {\n            throw new Error(\"服务器错误，请稍后重试\");\n        } else if (error.code === \"ECONNREFUSED\" || error.message.includes(\"Network Error\")) {\n            throw new Error(`无法连接到服务器，请检查网络连接。服务器地址：${API_BASE_URL}`);\n        } else {\n            throw new Error(error.message || \"Login failed\");\n        }\n    }\n};\n// 登出\nconst logout = async ()=>{\n    try {\n        await apiClient.post(\"/api/v1/auth/logout\");\n    } catch (error) {\n        // 即使后端登出失败，也要清除本地token\n        console.error(\"Logout error:\", error);\n    } finally{\n        clearAuthTokens();\n    }\n};\n// 获取当前用户信息\nconst getCurrentUser = async ()=>{\n    try {\n        const response = await apiClient.get(\"/api/v1/auth/me\");\n        return response.data;\n    } catch (error) {\n        const apiError = error.response?.data;\n        throw new Error(apiError?.error?.message || \"Failed to get user info\");\n    }\n};\n// 检查健康状态\nconst checkHealth = async ()=>{\n    try {\n        const response = await apiClient.get(\"/health\");\n        return response.data;\n    } catch (error) {\n        throw new Error(\"API health check failed\");\n    }\n};\n// CORS测试\nconst testCors = async ()=>{\n    try {\n        console.log(\"\\uD83E\\uDDEA Testing CORS configuration...\");\n        const response = await apiClient.get(\"/api/v1/auth/test-cors\");\n        console.log(\"✅ CORS test successful:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ CORS test failed:\", error);\n        throw error;\n    }\n};\n// 存储管理API\nconst storageApi = {\n    // 获取存储列表\n    getStorages: async ()=>{\n        const response = await apiClient.get(\"/api/v1/storage/\");\n        return response.data;\n    },\n    // 创建存储\n    createStorage: async (data)=>{\n        const response = await apiClient.post(\"/api/v1/storage/\", data);\n        return response.data;\n    },\n    // 测试存储连接\n    testStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/test`);\n        return response.data;\n    },\n    // 删除存储\n    deleteStorage: async (storageId)=>{\n        const response = await apiClient.delete(`/api/v1/storage/${storageId}`);\n        return response.data;\n    },\n    // 同步存储\n    syncStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/sync`);\n        return response.data;\n    }\n};\n// 检查token是否有效\nconst isAuthenticated = ()=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    return !!token;\n};\n// 获取token\nconst getAccessToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatDate),\n/* harmony export */   formatNumber: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   getTranslation: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslation),\n/* harmony export */   getTranslationSync: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslationSync),\n/* harmony export */   languageConfig: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.preloadAllLanguages)\n/* harmony export */ });\n/* harmony import */ var _i18n_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./i18n/index */ \"(ssr)/./lib/i18n/index.ts\");\n/**\n * 多语言支持 - 兼容性导出文件\n * 重新导出新架构中的所有功能\n */ // 重新导出所有类型和函数\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFRCxjQUFjO0FBWVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9saWIvaTE4bi50cz80OWFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5aSa6K+t6KiA5pSv5oyBIC0g5YW85a655oCn5a+85Ye65paH5Lu2XG4gKiDph43mlrDlr7zlh7rmlrDmnrbmnoTkuK3nmoTmiYDmnInlip/og71cbiAqL1xuXG4vLyDph43mlrDlr7zlh7rmiYDmnInnsbvlnovlkozlh73mlbBcbmV4cG9ydCB0eXBlIHsgTGFuZ3VhZ2UsIFRyYW5zbGF0aW9ucyB9IGZyb20gJy4vaTE4bi9pbmRleCc7XG5cbmV4cG9ydCB7XG4gIGRlZmF1bHRMYW5ndWFnZSxcbiAgbGFuZ3VhZ2VDb25maWcsXG4gIGdldFRyYW5zbGF0aW9uLFxuICBnZXRUcmFuc2xhdGlvblN5bmMsXG4gIHByZWxvYWRBbGxMYW5ndWFnZXMsXG4gIGRldGVjdEJyb3dzZXJMYW5ndWFnZSxcbiAgZm9ybWF0TnVtYmVyLFxuICBmb3JtYXREYXRlXG59IGZyb20gJy4vaTE4bi9pbmRleCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VDb25maWciLCJnZXRUcmFuc2xhdGlvbiIsImdldFRyYW5zbGF0aW9uU3luYyIsInByZWxvYWRBbGxMYW5ndWFnZXMiLCJkZXRlY3RCcm93c2VyTGFuZ3VhZ2UiLCJmb3JtYXROdW1iZXIiLCJmb3JtYXREYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/index.ts":
/*!***************************!*\
  !*** ./lib/i18n/index.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* binding */ detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   getTranslationSync: () => (/* binding */ getTranslationSync),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* binding */ preloadAllLanguages)\n/* harmony export */ });\n/**\n * 国际化配置主文件\n * 支持动态加载语言文件\n */ const defaultLanguage = \"zh-CN\";\n// 语言配置\nconst languageConfig = {\n    \"zh-CN\": {\n        name: \"中文简体\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        direction: \"ltr\"\n    },\n    \"zh-TW\": {\n        name: \"中文繁體\",\n        flag: \"\\uD83C\\uDDF9\\uD83C\\uDDFC\",\n        direction: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        direction: \"ltr\"\n    },\n    \"ja\": {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        direction: \"ltr\"\n    }\n};\n// 动态导入语言文件\nconst loadTranslations = async (language)=>{\n    try {\n        const module = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${language}.ts`);\n        return module.default;\n    } catch (error) {\n        console.warn(`Failed to load translations for ${language}, falling back to default`);\n        const defaultModule = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${defaultLanguage}.ts`);\n        return defaultModule.default;\n    }\n};\n// 缓存已加载的翻译\nconst translationCache = new Map();\nconst getTranslation = async (language)=>{\n    if (translationCache.has(language)) {\n        return translationCache.get(language);\n    }\n    const translations = await loadTranslations(language);\n    translationCache.set(language, translations);\n    return translations;\n};\n// 同步获取翻译（用于已缓存的情况）\nconst getTranslationSync = (language)=>{\n    return translationCache.get(language) || null;\n};\n// 预加载所有语言\nconst preloadAllLanguages = async ()=>{\n    const languages = [\n        \"zh-CN\",\n        \"zh-TW\",\n        \"en\",\n        \"ja\"\n    ];\n    await Promise.all(languages.map(async (lang)=>{\n        try {\n            await getTranslation(lang);\n        } catch (error) {\n            console.warn(`Failed to preload language ${lang}:`, error);\n        }\n    }));\n};\n// 检测浏览器语言\nconst detectBrowserLanguage = ()=>{\n    if (true) {\n        return defaultLanguage;\n    }\n    const browserLanguage = navigator.language || navigator.languages?.[0];\n    if (browserLanguage?.startsWith(\"zh-CN\") || browserLanguage === \"zh\") {\n        return \"zh-CN\";\n    } else if (browserLanguage?.startsWith(\"zh-TW\") || browserLanguage === \"zh-Hant\") {\n        return \"zh-TW\";\n    } else if (browserLanguage?.startsWith(\"en\")) {\n        return \"en\";\n    } else if (browserLanguage?.startsWith(\"ja\")) {\n        return \"ja\";\n    }\n    return defaultLanguage;\n};\n// 格式化数字（根据语言环境）\nconst formatNumber = (number, language)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    return new Intl.NumberFormat(localeMap[language]).format(number);\n};\n// 格式化日期（根据语言环境）\nconst formatDate = (date, language, options)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(localeMap[language], options || defaultOptions).format(date);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/index.ts\n");

/***/ }),

/***/ "(ssr)/./lib/systemInit.ts":
/*!***************************!*\
  !*** ./lib/systemInit.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   systemInitApi: () => (/* binding */ systemInitApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/axios.js\");\n/**\n * 系统初始化相关API\n */ \nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 响应拦截器\napi.interceptors.response.use((response)=>response, (error)=>{\n    console.error(\"API Error:\", error);\n    return Promise.reject(error);\n});\nconst systemInitApi = {\n    /**\n   * 检查系统是否需要初始化\n   */ async checkNeedInit () {\n        try {\n            const response = await api.get(\"/api/v1/system-init/check\");\n            return response.data.data;\n        } catch (error) {\n            console.error(\"检查系统初始化状态失败:\", error);\n            // 如果API调用失败，假设需要初始化\n            return {\n                need_init: true,\n                is_initialized: false\n            };\n        }\n    },\n    /**\n   * 获取系统初始化状态\n   */ async getStatus () {\n        const response = await api.get(\"/api/v1/system-init/status\");\n        return response.data.data;\n    },\n    /**\n   * 初始化管理员账户\n   */ async initializeAdmin (data) {\n        const response = await api.post(\"/api/v1/system-init/admin\", data);\n        return response.data.data;\n    },\n    /**\n   * 初始化字典数据\n   */ async initializeDictionaries () {\n        await api.post(\"/api/v1/system-init/dictionaries\");\n    },\n    /**\n   * 初始化存储配置\n   */ async initializeStorage (config) {\n        await api.post(\"/api/v1/system-init/storage\", config);\n    },\n    /**\n   * 初始化系统配置\n   */ async initializeSystemConfig () {\n        await api.post(\"/api/v1/system-init/system-config\");\n    },\n    /**\n   * 自动完成所有初始化步骤\n   */ async autoInitialize () {\n        await api.post(\"/api/v1/system-init/auto-init\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/systemInit.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0cc9297c17c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZ2xvYmFscy5jc3M/YWJhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBjYzkyOTdjMTdjNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI知识库 - 智能文档管理平台\",\n    description: \"基于AI技术的智能文档管理和知识库系统\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#4ade80\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\app\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#useLanguage`);


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZmF2aWNvbi5pY28/ZjUyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736","vendor-chunks/mime-db@1.52.0","vendor-chunks/axios@1.9.0","vendor-chunks/lucide-react@0.300.0_react@18.3.1","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/debug@4.4.1","vendor-chunks/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d","vendor-chunks/form-data@4.0.3","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/asynckit@0.4.0","vendor-chunks/combined-stream@1.0.8","vendor-chunks/mime-types@2.1.35","vendor-chunks/js-cookie@3.0.5","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-symbols@1.1.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/function-bind@1.1.2","vendor-chunks/es-set-tostringtag@2.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/es-errors@1.3.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/has-tostringtag@1.0.2","vendor-chunks/es-object-atoms@1.1.1","vendor-chunks/react-hook-form@7.57.0_react@18.3.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();