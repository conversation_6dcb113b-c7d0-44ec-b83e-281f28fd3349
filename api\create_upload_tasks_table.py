#!/usr/bin/env python3
"""
创建upload_tasks表的迁移脚本
"""
import asyncio
from sqlalchemy import text
from loguru import logger

from app.core.database import get_async_session


async def create_upload_tasks_table():
    """创建upload_tasks表"""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS upload_tasks (
        id SERIAL PRIMARY KEY,
        task_id VARCHAR(100) UNIQUE NOT NULL,
        file_name VARCHAR(500) NOT NULL,
        file_size BIGINT NOT NULL,
        file_path TEXT NOT NULL,
        storage_id INTEGER NOT NULL,
        upload_path TEXT NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        progress INTEGER DEFAULT 0,
        uploaded_bytes BIGINT DEFAULT 0,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        started_at TIMESTAMP WITH TIME ZONE,
        completed_at TIMESTAMP WITH TIME ZONE
    );
    """
    
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_upload_tasks_task_id ON upload_tasks(task_id);",
        "CREATE INDEX IF NOT EXISTS idx_upload_tasks_status ON upload_tasks(status);",
        "CREATE INDEX IF NOT EXISTS idx_upload_tasks_created_at ON upload_tasks(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_upload_tasks_storage_id ON upload_tasks(storage_id);"
    ]
    
    try:
        async for session in get_async_session():
            logger.info("开始创建upload_tasks表...")
            
            # 创建表
            await session.execute(text(create_table_sql))
            logger.info("✅ upload_tasks表创建成功")
            
            # 创建索引
            for index_sql in create_indexes_sql:
                await session.execute(text(index_sql))
            logger.info("✅ 索引创建成功")
            
            # 提交更改
            await session.commit()
            logger.info("✅ 数据库迁移完成！")
            
            break  # 退出async for循环
            
    except Exception as e:
        logger.error(f"创建upload_tasks表失败: {str(e)}")
        raise


async def check_table_exists():
    """检查表是否存在"""
    check_sql = """
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'upload_tasks'
    );
    """
    
    try:
        async for session in get_async_session():
            result = await session.execute(text(check_sql))
            exists = result.scalar()
            
            if exists:
                logger.info("✅ upload_tasks表已存在")
            else:
                logger.info("❌ upload_tasks表不存在")
            
            return exists
            
    except Exception as e:
        logger.error(f"检查表是否存在失败: {str(e)}")
        return False


async def main():
    """主函数"""
    logger.info("开始upload_tasks表迁移...")
    
    # 检查表是否已存在
    exists = await check_table_exists()
    
    if not exists:
        # 创建表
        await create_upload_tasks_table()
    else:
        logger.info("表已存在，跳过创建")
    
    logger.info("迁移完成！")


if __name__ == "__main__":
    asyncio.run(main())
