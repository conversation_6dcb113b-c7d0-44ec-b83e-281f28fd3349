"""
简单的模型测试
"""
try:
    print("开始测试模型导入...")
    
    # 测试基础导入
    from app.models.base import BaseModel
    print("✓ BaseModel 导入成功")
    
    from app.models.user import User
    print("✓ User 模型导入成功")
    
    from app.models.file_management import StorageConfig
    print("✓ StorageConfig 模型导入成功")
    
    from app.models.storage_stats import StorageStats, StorageStatsHistory
    print("✓ StorageStats 模型导入成功")
    
    # 测试模型初始化
    from app.models import *
    print("✓ 所有模型初始化成功")
    
    print("\n🎉 模型修复成功！所有导入正常！")
    
except Exception as e:
    print(f"❌ 模型测试失败: {e}")
    import traceback
    traceback.print_exc()
