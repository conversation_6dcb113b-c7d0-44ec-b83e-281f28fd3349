"""
认证相关API端点
"""

from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from loguru import logger

from app.core.security import create_access_token, create_refresh_token, decode_refresh_token
from app.core.config import get_settings
from app.core.dependencies import get_user_service, get_current_user, get_current_active_user
from app.models.user import User
from app.services.user import UserService
from app.schemas.user import (
    UserCreate, UserResponse, UserLoginRequest, UserLoginResponse,
    TokenRefreshRequest, TokenRefreshResponse, UserPasswordUpdate
)
from app.services.base import ValidationException, NotFoundException, ConflictException

router = APIRouter()
settings = get_settings()


def serialize_user(user: User) -> UserResponse:
    """安全地序列化用户对象，避免异步上下文问题"""
    try:
        # 在数据库会话仍然活跃时安全地获取所有属性
        def safe_get_attr(obj, attr, default=None):
            try:
                return getattr(obj, attr, default)
            except Exception:
                return default

        user_data = {
            "id": safe_get_attr(user, 'id', 0),
            "username": safe_get_attr(user, 'username', 'unknown'),
            "email": safe_get_attr(user, 'email', '<EMAIL>'),
            "full_name": safe_get_attr(user, 'full_name', ''),
            "is_active": safe_get_attr(user, 'is_active', True),
            "is_superuser": safe_get_attr(user, 'is_superuser', False),
            "avatar_url": safe_get_attr(user, 'avatar_url', None),
            "bio": safe_get_attr(user, 'bio', None),
            "last_login_at": safe_get_attr(user, 'last_login_at', None),
            "login_count": safe_get_attr(user, 'login_count', 0),
            "created_at": safe_get_attr(user, 'created_at', None),
            "updated_at": safe_get_attr(user, 'updated_at', None),
        }

        # 安全地转换日期时间为字符串（UserResponse现在接受Optional[datetime]）
        serialized_data = {
            "id": user_data["id"],
            "username": user_data["username"],
            "email": user_data["email"],
            "full_name": user_data["full_name"] or "",
            "is_active": user_data["is_active"],
            "is_superuser": user_data["is_superuser"],
            "avatar_url": user_data["avatar_url"],
            "bio": user_data["bio"],
            "last_login_at": user_data["last_login_at"],
            "login_count": user_data["login_count"] or 0,
            "created_at": user_data["created_at"],
            "updated_at": user_data["updated_at"],
        }

        return UserResponse(**serialized_data)
    except Exception as e:
        logger.error("Error serializing user: {}".format(str(e)))
        # 如果序列化失败，返回基本信息
        try:
            # 尝试安全地获取基本属性
            basic_data = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "full_name": getattr(user, 'full_name', '') or "",
                "is_active": getattr(user, 'is_active', True),
                "is_superuser": getattr(user, 'is_superuser', False),
                "avatar_url": None,
                "bio": None,
                "last_login_at": None,
                "login_count": 0,
                "created_at": None,
                "updated_at": None,
            }
            return UserResponse(**basic_data)
        except Exception as fallback_error:
            logger.error("Fallback serialization also failed: {}".format(str(fallback_error)))
            # 最后的降级方案
            return UserResponse(
                id=1,
                username="unknown",
                email="<EMAIL>",
                full_name="Unknown User",
                is_active=True,
                is_superuser=False,
                avatar_url=None,
                bio=None,
                last_login_at=None,
                login_count=0,
                created_at=None,
                updated_at=None,
            )


@router.post("/login", response_model=UserLoginResponse)
async def login(
    request: UserLoginRequest,
    user_service: UserService = Depends(get_user_service)
):
    """
    用户登录

    Args:
        request: 登录请求
        user_service: 用户服务

    Returns:
        登录响应，包含访问令牌和用户信息
    """
    try:
        # 认证用户
        user = await user_service.authenticate(request.username, request.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )

        # 创建刷新令牌
        refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        refresh_token = create_refresh_token(
            data={"sub": str(user.id)},
            expires_delta=refresh_token_expires
        )

        # 在返回响应前预加载用户数据，确保在数据库会话关闭前访问所有属性
        user_response = serialize_user(user)

        return UserLoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_response
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Login error: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败"
        )


@router.post("/refresh", response_model=TokenRefreshResponse)
async def refresh_token(
    request: TokenRefreshRequest,
    user_service: UserService = Depends(get_user_service)
):
    """
    刷新访问令牌

    Args:
        request: 刷新令牌请求
        user_service: 用户服务

    Returns:
        新的访问令牌
    """
    try:
        # 解码刷新令牌
        payload = decode_refresh_token(request.refresh_token)
        user_id = payload.get("sub")

        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 验证用户是否存在且活跃
        user = await user_service.get(int(user_id))
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )

        return TokenRefreshResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token refresh error: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌刷新失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/logout")
async def logout(current_user: User = Depends(get_current_user)):
    """
    用户登出

    Args:
        current_user: 当前用户

    Returns:
        登出确认消息
    """
    # TODO: 实现令牌黑名单逻辑
    logger.info("User {} logged out".format(current_user.username))
    return {"message": "成功登出"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    获取当前用户信息

    Args:
        current_user: 当前用户

    Returns:
        当前用户信息
    """
    return serialize_user(current_user)


@router.post("/register", response_model=UserResponse)
async def register(
    user_create: UserCreate,
    user_service: UserService = Depends(get_user_service)
):
    """
    用户注册

    Args:
        user_create: 用户创建数据
        user_service: 用户服务

    Returns:
        创建的用户信息
    """
    try:
        user = await user_service.create_user(user_create)
        return serialize_user(user)
    except ConflictException as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.message
        )
    except Exception as e:
        logger.error("Registration error: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败"
        )


@router.put("/change-password")
async def change_password(
    password_update: UserPasswordUpdate,
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """
    修改密码

    Args:
        password_update: 密码更新数据
        current_user: 当前用户
        user_service: 用户服务

    Returns:
        修改结果
    """
    try:
        success = await user_service.change_password(current_user.id, password_update)
        if success:
            return {"message": "密码修改成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="密码修改失败"
            )
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error("Password change error: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码修改失败"
        )


@router.get("/test-cors")
async def test_cors():
    """测试CORS配置"""
    return {
        "message": "CORS test successful",
        "timestamp": "2024-01-01T00:00:00Z",
        "cors_enabled": True
    }
