"""
Celery控制服务
"""
import os
import sys
import signal
import subprocess
import psutil
import redis
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from pathlib import Path

from app.core.config import settings


class CeleryControlService:
    """Celery控制服务"""
    
    def __init__(self):
        self.redis_client = None
        self.base_dir = Path(__file__).parent.parent
        self.venv_python = self._get_venv_python()
        
    def _get_venv_python(self) -> str:
        """获取虚拟环境Python路径"""
        if sys.platform == "win32":
            return str(self.base_dir / ".venv" / "Scripts" / "python.exe")
        else:
            return str(self.base_dir / ".venv" / "bin" / "python")
    
    def _get_redis_client(self) -> redis.Redis:
        """获取Redis客户端"""
        if not self.redis_client:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True
            )
        return self.redis_client
    
    def check_redis_connection(self) -> bool:
        """检查Redis连接"""
        try:
            client = self._get_redis_client()
            client.ping()
            return True
        except Exception as e:
            print(f"Redis连接失败: {e}")
            return False
    
    def get_celery_processes(self) -> Dict[str, Dict]:
        """获取Celery进程信息"""
        processes = {
            'worker': {'running': False, 'pid': None},
            'beat': {'running': False, 'pid': None},
            'flower': {'running': False, 'pid': None}
        }
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    
                    if 'celery' in cmdline.lower():
                        if 'worker' in cmdline:
                            processes['worker'] = {
                                'running': True,
                                'pid': proc.info['pid']
                            }
                        elif 'beat' in cmdline:
                            processes['beat'] = {
                                'running': True,
                                'pid': proc.info['pid']
                            }
                        elif 'flower' in cmdline:
                            processes['flower'] = {
                                'running': True,
                                'pid': proc.info['pid']
                            }
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            print(f"获取Celery进程信息失败: {e}")
        
        return processes
    
    def get_celery_status(self) -> Dict:
        """获取Celery状态"""
        redis_connected = self.check_redis_connection()
        services = self.get_celery_processes()
        
        # 计算整体状态
        running_services = sum(1 for service in services.values() if service['running'])
        if running_services == 3:
            overall_status = 'running'
        elif running_services == 0:
            overall_status = 'stopped'
        else:
            overall_status = 'partial'
        
        return {
            'redis_connected': redis_connected,
            'services': services,
            'overall_status': overall_status,
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def start_celery_worker(self) -> Tuple[bool, str]:
        """启动Celery Worker"""
        try:
            # 检查是否已经运行
            processes = self.get_celery_processes()
            if processes['worker']['running']:
                return True, "Celery Worker已经在运行"
            
            # 启动命令
            cmd = [
                self.venv_python,
                "-m", "celery",
                "-A", "main.celery",
                "worker",
                "--loglevel=info",
                "--concurrency=4"
            ]
            
            # 启动进程
            if sys.platform == "win32":
                # Windows下使用CREATE_NEW_PROCESS_GROUP
                process = subprocess.Popen(
                    cmd,
                    cwd=self.base_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
            else:
                # Linux/Mac下使用nohup
                process = subprocess.Popen(
                    cmd,
                    cwd=self.base_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )
            
            # 等待一下确保启动
            time.sleep(2)
            
            # 检查是否启动成功
            if process.poll() is None:
                return True, f"Celery Worker启动成功，PID: {process.pid}"
            else:
                stdout, stderr = process.communicate()
                return False, f"Celery Worker启动失败: {stderr.decode()}"
                
        except Exception as e:
            return False, f"启动Celery Worker失败: {str(e)}"
    
    def start_celery_beat(self) -> Tuple[bool, str]:
        """启动Celery Beat"""
        try:
            # 检查是否已经运行
            processes = self.get_celery_processes()
            if processes['beat']['running']:
                return True, "Celery Beat已经在运行"
            
            # 启动命令
            cmd = [
                self.venv_python,
                "-m", "celery",
                "-A", "main.celery",
                "beat",
                "--loglevel=info"
            ]
            
            # 启动进程
            if sys.platform == "win32":
                process = subprocess.Popen(
                    cmd,
                    cwd=self.base_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
            else:
                process = subprocess.Popen(
                    cmd,
                    cwd=self.base_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )
            
            time.sleep(2)
            
            if process.poll() is None:
                return True, f"Celery Beat启动成功，PID: {process.pid}"
            else:
                stdout, stderr = process.communicate()
                return False, f"Celery Beat启动失败: {stderr.decode()}"
                
        except Exception as e:
            return False, f"启动Celery Beat失败: {str(e)}"
    
    def start_flower(self) -> Tuple[bool, str]:
        """启动Flower"""
        try:
            # 检查是否已经运行
            processes = self.get_celery_processes()
            if processes['flower']['running']:
                return True, "Flower已经在运行"
            
            # 启动命令
            cmd = [
                self.venv_python,
                "-m", "flower",
                "--broker=redis://{}:{}/{}".format(
                    settings.REDIS_HOST,
                    settings.REDIS_PORT,
                    settings.REDIS_DB
                ),
                "--port=5555"
            ]
            
            # 启动进程
            if sys.platform == "win32":
                process = subprocess.Popen(
                    cmd,
                    cwd=self.base_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
            else:
                process = subprocess.Popen(
                    cmd,
                    cwd=self.base_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )
            
            time.sleep(2)
            
            if process.poll() is None:
                return True, f"Flower启动成功，PID: {process.pid}"
            else:
                stdout, stderr = process.communicate()
                return False, f"Flower启动失败: {stderr.decode()}"
                
        except Exception as e:
            return False, f"启动Flower失败: {str(e)}"
    
    def stop_celery_service(self, service: str) -> Tuple[bool, str]:
        """停止Celery服务"""
        try:
            processes = self.get_celery_processes()
            
            if service not in processes:
                return False, f"未知的服务: {service}"
            
            if not processes[service]['running']:
                return True, f"{service}服务未运行"
            
            pid = processes[service]['pid']
            
            try:
                process = psutil.Process(pid)
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=10)
                except psutil.TimeoutExpired:
                    # 强制杀死进程
                    process.kill()
                    process.wait(timeout=5)
                
                return True, f"{service}服务停止成功"
                
            except psutil.NoSuchProcess:
                return True, f"{service}服务已停止"
            except Exception as e:
                return False, f"停止{service}服务失败: {str(e)}"
                
        except Exception as e:
            return False, f"停止{service}服务失败: {str(e)}"
    
    def start_all_services(self) -> Dict[str, Tuple[bool, str]]:
        """启动所有服务"""
        results = {}
        
        # 启动Worker
        results['worker'] = self.start_celery_worker()
        
        # 启动Beat
        results['beat'] = self.start_celery_beat()
        
        # 启动Flower
        results['flower'] = self.start_flower()
        
        return results
    
    def stop_all_services(self) -> Dict[str, Tuple[bool, str]]:
        """停止所有服务"""
        results = {}
        
        # 停止所有服务
        for service in ['worker', 'beat', 'flower']:
            results[service] = self.stop_celery_service(service)
        
        return results
    
    def restart_all_services(self) -> Dict[str, Tuple[bool, str]]:
        """重启所有服务"""
        # 先停止所有服务
        stop_results = self.stop_all_services()
        
        # 等待一下确保进程完全停止
        time.sleep(3)
        
        # 再启动所有服务
        start_results = self.start_all_services()
        
        # 合并结果
        results = {}
        for service in ['worker', 'beat', 'flower']:
            stop_success, stop_msg = stop_results.get(service, (False, "未执行"))
            start_success, start_msg = start_results.get(service, (False, "未执行"))
            
            if stop_success and start_success:
                results[service] = (True, f"重启成功: {start_msg}")
            else:
                results[service] = (False, f"重启失败 - 停止: {stop_msg}, 启动: {start_msg}")
        
        return results
