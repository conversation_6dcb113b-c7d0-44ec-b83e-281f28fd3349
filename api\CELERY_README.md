# AI知识库 Celery任务队列使用指南

## 概述

本项目使用Celery作为分布式任务队列来处理文件上传等异步任务，支持Windows和Linux系统。

## 系统要求

- Python 3.8+
- Redis服务器 (默认: **************:6379/10)
- 已安装项目依赖

## 快速开始

### Windows系统

#### 方式一：一键启动（推荐）
```bash
# 启动所有服务
celery_start.bat

# 检查服务状态
celery_status.bat

# 停止所有服务
celery_stop.bat
```

#### 方式二：批处理脚本
```bash
# 启动服务
start_celery.bat

# 停止服务
stop_celery.bat
```

#### 方式三：PowerShell脚本（高级用户）
```powershell
# 启动服务
powershell -ExecutionPolicy Bypass -File start_celery.ps1

# 停止服务
powershell -ExecutionPolicy Bypass -File stop_celery.ps1
```

### Linux/macOS系统

```bash
# 启动服务
chmod +x start_celery.sh
./start_celery.sh

# 停止服务
chmod +x stop_celery.sh
./stop_celery.sh
```

## 服务组件

### 1. Celery Worker
- **功能**: 处理异步任务（文件上传、处理等）
- **队列**: default, upload_queue, file_queue
- **并发**: 2-4个进程

### 2. Celery Beat
- **功能**: 定时任务调度器
- **用途**: 清理临时文件、统计计算等

### 3. Flower监控
- **功能**: Web界面监控任务状态
- **地址**: http://localhost:5555
- **认证**: admin/password

## 环境变量配置

在`.env`文件中配置Redis连接：

```env
# Redis配置
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_DB=10
REDIS_PASSWORD=
```

## 任务类型

### 上传任务 (upload_queue)
- `process_upload_task`: 处理单个文件上传
- `batch_process_upload_tasks`: 批量处理上传任务
- `cleanup_completed_tasks`: 清理已完成任务

### 文件任务 (file_queue)
- `calculate_storage_statistics`: 计算存储统计信息
- `cleanup_temp_files`: 清理临时文件

## API使用

### 创建并提交上传任务
```python
# 创建任务时自动提交到Celery
POST /api/v1/upload-tasks/create-with-file
```

### 手动提交任务
```python
# 提交单个任务
POST /api/v1/upload-tasks/{task_id}/submit

# 批量提交任务
POST /api/v1/upload-tasks/batch-submit
```

### 重试任务
```python
# 重试并重新提交到队列
POST /api/v1/upload-tasks/{task_id}/retry
```

## 监控和调试

### 1. Flower Web界面
访问 http://localhost:5555 查看：
- 任务执行状态
- Worker负载情况
- 队列长度
- 任务历史

### 2. 日志文件
```bash
# Windows
logs/celery_worker.log    # Worker日志
logs/celery_beat.log      # Beat日志
logs/celery_flower.log    # Flower日志

# Linux
/tmp/celery_worker.log
/tmp/celery_beat.log
/tmp/celery_flower.log
```

### 3. 命令行监控
```bash
# 查看活跃任务
celery -A app.core.celery_config:celery_app inspect active

# 查看注册的任务
celery -A app.core.celery_config:celery_app inspect registered

# 查看Worker状态
celery -A app.core.celery_config:celery_app inspect stats
```

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证连接配置（主机、端口、数据库）
   - 检查防火墙设置

2. **任务执行失败**
   - 查看Worker日志
   - 检查文件路径是否存在
   - 验证存储配置

3. **Flower无法访问**
   - 确认端口5555未被占用
   - 检查防火墙设置
   - 验证认证信息

### 重置服务
```bash
# Windows
celery_stop.bat
# 等待几秒
celery_start.bat

# Linux
./stop_celery.sh
./start_celery.sh
```

### 清理队列
```python
# 清空所有队列
celery -A app.core.celery_config:celery_app purge

# 清空指定队列
celery -A app.core.celery_config:celery_app purge -Q upload_queue
```

## 性能优化

### Worker配置
- 根据CPU核心数调整并发数
- 设置合适的内存限制
- 配置任务超时时间

### 队列配置
- 按任务类型分离队列
- 设置队列优先级
- 配置消息持久化

### 监控指标
- 任务执行时间
- 队列长度
- Worker内存使用
- 错误率统计

## 生产环境部署

### 系统服务配置
建议将Celery配置为系统服务：

#### Windows服务
使用NSSM或类似工具将脚本注册为Windows服务

#### Linux Systemd
创建systemd服务文件管理Celery进程

### 高可用配置
- 多Worker实例
- Redis集群
- 负载均衡
- 故障转移

## 开发指南

### 添加新任务
1. 在`app/tasks/`目录下创建任务文件
2. 使用`@celery_app.task`装饰器
3. 在`celery_config.py`中配置路由
4. 更新API接口调用

### 任务最佳实践
- 保持任务幂等性
- 设置合理的重试策略
- 记录详细的执行日志
- 处理异常情况

## 更新日志

- v1.0.0: 初始版本，支持文件上传任务队列
- v1.1.0: 添加Windows脚本支持
- v1.2.0: 集成Flower监控界面
