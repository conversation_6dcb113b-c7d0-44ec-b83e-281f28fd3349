"""Create file management tables

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构"""
    
    # 创建存储配置表
    op.create_table('storage_configs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
        sa.Column('name', sa.String(length=100), nullable=False, comment='存储配置名称'),
        sa.Column('storage_type', sa.Enum('local', 'minio', 'ftp', 'sftp', name='storagetype'), nullable=False, comment='存储类型'),
        sa.Column('is_default', sa.<PERSON>(), nullable=True, comment='是否为默认存储'),
        sa.Column('is_active', sa.<PERSON>(), nullable=True, comment='是否启用'),
        sa.Column('config', sa.J<PERSON>(), nullable=True, comment='存储配置参数'),
        sa.Column('total_files', sa.Integer(), nullable=True, comment='总文件数'),
        sa.Column('total_size', sa.BigInteger(), nullable=True, comment='总大小(字节)'),
        sa.Column('last_sync_at', sa.DateTime(timezone=True), nullable=True, comment='最后同步时间'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    
    # 创建文件记录表
    op.create_table('file_records',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
        sa.Column('file_id', sa.String(length=36), nullable=False, comment='文件唯一ID'),
        sa.Column('storage_id', sa.Integer(), nullable=False, comment='存储配置ID'),
        sa.Column('file_path', sa.Text(), nullable=False, comment='文件完整路径'),
        sa.Column('file_name', sa.String(length=255), nullable=False, comment='文件名'),
        sa.Column('parent_path', sa.Text(), nullable=True, comment='父目录路径'),
        sa.Column('file_type', sa.Enum('file', 'directory', name='filetype'), nullable=False, comment='文件类型'),
        sa.Column('file_size', sa.BigInteger(), nullable=True, comment='文件大小(字节)'),
        sa.Column('mime_type', sa.String(length=100), nullable=True, comment='MIME类型'),
        sa.Column('file_extension', sa.String(length=20), nullable=True, comment='文件扩展名'),
        sa.Column('file_hash', sa.String(length=64), nullable=True, comment='文件MD5哈希值'),
        sa.Column('file_created_at', sa.DateTime(timezone=True), nullable=True, comment='文件创建时间'),
        sa.Column('file_modified_at', sa.DateTime(timezone=True), nullable=True, comment='文件修改时间'),
        sa.Column('status', sa.Enum('active', 'deleted', 'syncing', 'error', name='filestatus'), nullable=True, comment='文件状态'),
        sa.Column('file_metadata', sa.JSON(), nullable=True, comment='文件元数据'),
        sa.Column('thumbnail_path', sa.String(length=500), nullable=True, comment='缩略图路径'),
        sa.Column('has_thumbnail', sa.Boolean(), nullable=True, comment='是否有缩略图'),
        sa.Column('last_sync_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='最后同步时间'),
        sa.Column('sync_version', sa.Integer(), nullable=True, comment='同步版本号'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('file_id')
    )
    
    # 创建同步日志表
    op.create_table('sync_logs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键ID'),
        sa.Column('storage_id', sa.Integer(), nullable=False, comment='存储配置ID'),
        sa.Column('sync_type', sa.String(length=20), nullable=False, comment='同步类型: full/incremental'),
        sa.Column('files_scanned', sa.Integer(), nullable=True, comment='扫描文件数'),
        sa.Column('files_added', sa.Integer(), nullable=True, comment='新增文件数'),
        sa.Column('files_updated', sa.Integer(), nullable=True, comment='更新文件数'),
        sa.Column('files_deleted', sa.Integer(), nullable=True, comment='删除文件数'),
        sa.Column('status', sa.String(length=20), nullable=True, comment='同步状态: running/completed/failed'),
        sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
        sa.Column('started_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='开始时间'),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True, comment='完成时间'),
        sa.Column('duration_seconds', sa.Integer(), nullable=True, comment='耗时(秒)'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index('idx_storage_configs_type', 'storage_configs', ['storage_type'])
    op.create_index('idx_storage_configs_default', 'storage_configs', ['is_default'])
    op.create_index('idx_storage_configs_active', 'storage_configs', ['is_active'])
    
    op.create_index('idx_file_records_storage_path', 'file_records', ['storage_id', 'file_path'])
    op.create_index('idx_file_records_parent_path', 'file_records', ['storage_id', 'parent_path'])
    op.create_index('idx_file_records_type', 'file_records', ['file_type'])
    op.create_index('idx_file_records_status', 'file_records', ['status'])
    op.create_index('idx_file_records_name', 'file_records', ['file_name'])
    op.create_index('idx_file_records_extension', 'file_records', ['file_extension'])
    op.create_index('idx_file_records_modified', 'file_records', ['file_modified_at'])
    op.create_index('idx_file_records_sync', 'file_records', ['last_sync_at'])
    
    op.create_index('idx_sync_logs_storage', 'sync_logs', ['storage_id'])
    op.create_index('idx_sync_logs_status', 'sync_logs', ['status'])
    op.create_index('idx_sync_logs_started', 'sync_logs', ['started_at'])


def downgrade() -> None:
    """降级数据库结构"""
    
    # 删除索引
    op.drop_index('idx_sync_logs_started')
    op.drop_index('idx_sync_logs_status')
    op.drop_index('idx_sync_logs_storage')
    
    op.drop_index('idx_file_records_sync')
    op.drop_index('idx_file_records_modified')
    op.drop_index('idx_file_records_extension')
    op.drop_index('idx_file_records_name')
    op.drop_index('idx_file_records_status')
    op.drop_index('idx_file_records_type')
    op.drop_index('idx_file_records_parent_path')
    op.drop_index('idx_file_records_storage_path')
    
    op.drop_index('idx_storage_configs_active')
    op.drop_index('idx_storage_configs_default')
    op.drop_index('idx_storage_configs_type')
    
    # 删除表
    op.drop_table('sync_logs')
    op.drop_table('file_records')
    op.drop_table('storage_configs')
    
    # 删除枚举类型
    op.execute('DROP TYPE IF EXISTS filestatus')
    op.execute('DROP TYPE IF EXISTS filetype')
    op.execute('DROP TYPE IF EXISTS storagetype')
