"""
API v1版本
包含第一版本的所有API端点
"""

from fastapi import APIRouter

from . import auth, storage_management, migration, system_init, file_management, first_time_setup, upload_tasks, celery_management, document_segment, knowledge_base
# from app.routers import storage_overview, system_status

# 创建v1版本的路由器
v1_router = APIRouter()

# 注册各个模块的路由
v1_router.include_router(first_time_setup.router, tags=["首次设置"])  # first_time_setup.router已经有prefix="/setup"
v1_router.include_router(system_init.router)  # system_init.router已经有prefix="/system-init"
v1_router.include_router(auth.router, prefix="/auth", tags=["认证"])
v1_router.include_router(storage_management.router, prefix="/storage", tags=["存储管理"])
v1_router.include_router(file_management.router, tags=["文件管理"])  # file_management.router已经有prefix="/file-management"
v1_router.include_router(migration.router, prefix="/migration", tags=["数据库迁移"])
v1_router.include_router(upload_tasks.router, prefix="/upload-tasks", tags=["上传任务"])
v1_router.include_router(celery_management.router, prefix="/celery", tags=["Celery管理"])
v1_router.include_router(document_segment.router, tags=["文档分段"])  # document_segment.router已经有prefix="/document-segment"
v1_router.include_router(knowledge_base.router, prefix="/knowledge-bases", tags=["知识库管理"])
# v1_router.include_router(storage_overview.router, tags=["存储概览"])
# v1_router.include_router(system_status.router, tags=["系统状态"])
