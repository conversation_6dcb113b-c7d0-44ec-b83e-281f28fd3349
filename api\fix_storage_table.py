#!/usr/bin/env python3
"""
修复storage_configs表结构
"""
import asyncio
import asyncpg
from loguru import logger
from app.core.config import get_settings

settings = get_settings()

async def fix_storage_table():
    """修复storage_configs表，添加缺少的列"""
    
    # 解析数据库URL
    db_url = settings.DATABASE_URL
    if db_url.startswith("postgresql+asyncpg://"):
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(db_url)
        logger.info("Connected to database")
        
        # 检查表是否存在
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'storage_configs'
            );
        """)
        
        if not table_exists:
            logger.error("Table storage_configs does not exist")
            return False
        
        # 获取现有列
        columns = await conn.fetch("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'storage_configs'
        """)
        
        existing_columns = [row['column_name'] for row in columns]
        logger.info(f"Existing columns: {existing_columns}")
        
        # 添加缺少的列
        columns_to_add = [
            ("total_files", "INTEGER DEFAULT 0"),
            ("total_size", "BIGINT DEFAULT 0"),
            ("last_sync_at", "TIMESTAMP WITH TIME ZONE")
        ]
        
        for column_name, column_def in columns_to_add:
            if column_name not in existing_columns:
                sql = f"ALTER TABLE storage_configs ADD COLUMN {column_name} {column_def}"
                logger.info(f"Adding column: {sql}")
                await conn.execute(sql)
                logger.info(f"✅ Added column {column_name}")
            else:
                logger.info(f"✅ Column {column_name} already exists")
        
        # 更新现有记录的默认值
        await conn.execute("UPDATE storage_configs SET total_files = 0 WHERE total_files IS NULL")
        await conn.execute("UPDATE storage_configs SET total_size = 0 WHERE total_size IS NULL")
        
        logger.info("✅ Storage table structure fixed successfully")
        
        # 验证修复结果
        final_columns = await conn.fetch("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'storage_configs'
            ORDER BY ordinal_position
        """)
        
        logger.info("Final columns: {}".format([row['column_name'] for row in final_columns]))
        
        await conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Error fixing storage table: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(fix_storage_table())
