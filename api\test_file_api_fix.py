#!/usr/bin/env python3
"""
测试文件API修复
"""
import urllib.parse
import requests
import time

def test_file_api():
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(3)
    
    # 测试文件ID
    original_file_id = "local_5_/新建文档222.md"
    print(f"原始文件ID: {original_file_id}")
    
    # 前端编码（模拟前端的encodeURIComponent）
    encoded_file_id = urllib.parse.quote(original_file_id, safe='')
    print(f"前端编码后: {encoded_file_id}")
    
    # 测试API调用
    base_url = "http://127.0.0.1:8000"
    
    # 测试文件信息API
    info_url = f"{base_url}/api/v1/file-management/files/{encoded_file_id}"
    print(f"\n测试文件信息API: {info_url}")
    
    try:
        response = requests.get(info_url, timeout=10)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 文件信息API调用成功")
            data = response.json()
            print(f"文件名: {data.get('file_name', 'N/A')}")
        else:
            print(f"❌ 文件信息API调用失败: {response.text}")
    except Exception as e:
        print(f"❌ 文件信息API调用异常: {e}")
    
    # 测试文件内容API
    content_url = f"{base_url}/api/v1/file-management/files/{encoded_file_id}/content"
    print(f"\n测试文件内容API: {content_url}")
    
    try:
        response = requests.get(content_url, timeout=10)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 文件内容API调用成功")
            data = response.json()
            print(f"内容格式: {data.get('format', 'N/A')}")
        else:
            print(f"❌ 文件内容API调用失败: {response.text}")
    except Exception as e:
        print(f"❌ 文件内容API调用异常: {e}")
    
    # 测试调试API
    debug_url = f"{base_url}/api/v1/file-management/debug/file-id/{encoded_file_id}"
    print(f"\n测试调试API: {debug_url}")
    
    try:
        response = requests.get(debug_url, timeout=10)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 调试API调用成功")
            data = response.json()
            print(f"解析结果: {data}")
        else:
            print(f"❌ 调试API调用失败: {response.text}")
    except Exception as e:
        print(f"❌ 调试API调用异常: {e}")

if __name__ == '__main__':
    test_file_api()
