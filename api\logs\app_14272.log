2025-06-17 17:24:48.029 | INFO     | app.core.logging:setup_logging:110 | Logging configured for development environment
2025-06-17 17:24:48.030 | INFO     | app.core.middleware:setup_middleware:136 | Middleware setup completed
2025-06-17 17:24:48.059 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: auth v1.0.0
2025-06-17 17:24:48.060 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: file_manager v1.0.0
2025-06-17 17:24:48.062 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: rag_engine v1.0.0
2025-06-17 17:24:48.063 | INFO     | app.core.database:init_database:222 | Initializing database connection with URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:24:48.069 | INFO     | app.core.database:init_database:254 | Using async URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:24:48.069 | INFO     | app.core.database:init_database:255 | Using sync URL: postgresql+psycopg2://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:24:48.116 | INFO     | app.core.database:init_database:281 | Database connection initialized successfully for postgresql
2025-06-17 17:24:48.117 | INFO     | app.core.simple_migration:create_tables_directly:44 | Creating tables for database type: postgresql
2025-06-17 17:24:48.155 | INFO     | app.plugins.auth.plugin:initialize:36 | Auth plugin initialized
2025-06-17 17:24:48.155 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: auth
2025-06-17 17:24:48.156 | INFO     | app.plugins.file_manager.plugin:initialize:49 | File manager plugin initialized with upload dir: uploads
2025-06-17 17:24:48.156 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: file_manager
2025-06-17 17:24:48.156 | INFO     | app.plugins.rag_engine.plugin:initialize:64 | RAG engine plugin initialized
2025-06-17 17:24:48.156 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: rag_engine
2025-06-17 17:24:48.157 | INFO     | app.plugins.auth.plugin:startup:40 | Auth plugin started
2025-06-17 17:24:48.157 | INFO     | app.plugins.manager:startup:167 | Started plugin: auth
2025-06-17 17:24:48.157 | INFO     | app.plugins.file_manager.plugin:startup:57 | File manager plugin started
2025-06-17 17:24:48.157 | INFO     | app.plugins.manager:startup:167 | Started plugin: file_manager
2025-06-17 17:24:48.157 | INFO     | app.plugins.rag_engine.plugin:startup:69 | RAG engine plugin started
2025-06-17 17:24:48.158 | INFO     | app.plugins.manager:startup:167 | Started plugin: rag_engine
2025-06-17 17:24:48.158 | INFO     | app.services.upload_processor:start:34 | 上传处理器启动
2025-06-17 17:24:48.159 | INFO     | app.core.startup:startup_tasks:66 | 执行应用启动任务...
2025-06-17 17:24:48.159 | INFO     | app.core.startup:initialize_celery:30 | 自动启动Celery服务...
2025-06-17 17:24:48.160 | INFO     | app.core.startup:initialize_celery:55 | Celery指标收集器将在服务完全启动后手动启动
2025-06-17 17:24:48.160 | INFO     | app.core.startup:startup_tasks:71 | 应用启动任务完成
2025-06-17 17:24:48.202 | INFO     | app.core.celery_manager:_check_redis_connection:138 | Redis连接成功
2025-06-17 17:24:48.202 | INFO     | app.core.celery_manager:start_service:169 | 启动 worker 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=4 --queues=default,upload_queue,file_queue --hostname=worker@%h --logfile=logs/celery_worker.log
2025-06-17 17:24:50.212 | INFO     | app.core.celery_manager:start_service:191 | worker 服务启动成功 (PID: 22880)
2025-06-17 17:24:53.324 | INFO     | app.core.celery_manager:start_service:169 | 启动 beat 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app beat --loglevel=info --logfile=logs/celery_beat.log
2025-06-17 17:24:55.338 | INFO     | app.core.celery_manager:start_service:191 | beat 服务启动成功 (PID: 24024)
2025-06-17 17:24:57.392 | INFO     | app.core.celery_manager:_find_available_port:96 | 找到可用端口: 5555
2025-06-17 17:24:57.393 | INFO     | app.core.celery_manager:start_service:169 | 启动 flower 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app flower --port=5555 --basic_auth=admin:password --logfile=logs/celery_flower.log
2025-06-17 17:24:59.401 | INFO     | app.core.celery_manager:start_service:191 | flower 服务启动成功 (PID: 7136)
2025-06-17 17:25:00.402 | INFO     | app.core.startup:start_celery_background:40 | 所有Celery服务启动成功
2025-06-17 17:25:38.974 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:38.975 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:38.976 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:38.976 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:38.977 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:38.978 | WARNING  | app.core.security:decode_access_token:110 | Invalid access token: Not enough segments
2025-06-17 17:25:38.979 | ERROR    | app.core.dependencies:get_current_user:118 | 认证失败: Not enough segments
2025-06-17 17:25:38.980 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 401 - Could not validate credentials
2025-06-17 17:25:38.980 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:38.982 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:38.983 | WARNING  | app.core.security:decode_access_token:110 | Invalid access token: Not enough segments
2025-06-17 17:25:38.983 | ERROR    | app.core.dependencies:get_current_user:118 | 认证失败: Not enough segments
2025-06-17 17:25:38.984 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 401 - Could not validate credentials
2025-06-17 17:25:38.984 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:39.843 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:39.844 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:39.844 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:39.845 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:39.846 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:39.854 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:39.855 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:39.878 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:25:41.388 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:25:41.389 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:25:41.390 | INFO     | app.core.middleware:dispatch:30 | Request started: POST http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:25:41.641 | INFO     | app.repositories.user:update_last_login:143 | Updated last login for user 4
2025-06-17 17:25:41.655 | INFO     | app.core.middleware:dispatch:61 | Request completed: POST http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:25:43.798 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.798 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.800 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.800 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.801 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.806 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.807 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.810 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.820 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.824 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.825 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:43.827 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:45.897 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:45.902 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:45.903 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:45.908 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:25:45.912 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.913 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.913 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.914 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.915 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.950 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.952 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.956 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:25:45.957 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:25:45.958 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:25:45.977 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:25:45.977 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:25:45.980 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:25:45.981 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 404 - Not Found
2025-06-17 17:25:45.981 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:25:45.983 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.983 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:45.997 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:25:46.031 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:25:51.122 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:51.123 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:51.124 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.124 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:51.125 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.126 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:51.126 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.127 | INFO     | app.core.middleware:dispatch:30 | Request started: POST http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:51.127 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.129 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.129 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 404 - Not Found
2025-06-17 17:25:51.130 | INFO     | app.core.middleware:dispatch:30 | Request started: POST http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:51.131 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.132 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.132 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 404 - Not Found
2025-06-17 17:25:51.133 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage-management
2025-06-17 17:25:51.138 | INFO     | app.api.v1.file_management:get_batch_file_info:647 | 批量获取文件信息: 文件数量=3
2025-06-17 17:25:51.138 | INFO     | app.api.v1.file_management:get_batch_file_info:648 | 文件ID列表: ['local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx', 'local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx', 'local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx']
2025-06-17 17:25:51.139 | INFO     | app.api.v1.file_management:parse_file_id:519 | 使用原始文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.139 | INFO     | app.api.v1.file_management:get_batch_file_info:657 | 解析文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx -> type=local, storage_id=5, path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.141 | INFO     | app.api.v1.file_management:get_batch_file_info:647 | 批量获取文件信息: 文件数量=3
2025-06-17 17:25:51.142 | INFO     | app.api.v1.file_management:get_batch_file_info:648 | 文件ID列表: ['local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx', 'local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx', 'local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx']
2025-06-17 17:25:51.142 | INFO     | app.api.v1.file_management:parse_file_id:519 | 使用原始文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.143 | INFO     | app.api.v1.file_management:get_batch_file_info:657 | 解析文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx -> type=local, storage_id=5, path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.147 | INFO     | app.services.simple_file_manager:get_file_info:474 | 获取文件信息: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.148 | INFO     | app.api.v1.file_management:get_batch_file_info:670 | 成功获取文件信息: /1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.148 | INFO     | app.api.v1.file_management:parse_file_id:519 | 使用原始文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.148 | INFO     | app.api.v1.file_management:get_batch_file_info:657 | 解析文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx -> type=local, storage_id=5, path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.150 | INFO     | app.services.simple_file_manager:get_file_info:474 | 获取文件信息: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.150 | INFO     | app.api.v1.file_management:get_batch_file_info:670 | 成功获取文件信息: /1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.151 | INFO     | app.api.v1.file_management:parse_file_id:519 | 使用原始文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.151 | INFO     | app.api.v1.file_management:get_batch_file_info:657 | 解析文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx -> type=local, storage_id=5, path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.152 | INFO     | app.services.simple_file_manager:get_file_info:474 | 获取文件信息: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.153 | INFO     | app.api.v1.file_management:get_batch_file_info:670 | 成功获取文件信息: /1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.153 | INFO     | app.api.v1.file_management:get_batch_file_info:687 | 批量获取文件信息完成: 成功=3, 失败=0
2025-06-17 17:25:51.155 | INFO     | app.core.middleware:dispatch:61 | Request completed: POST http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:51.177 | INFO     | app.services.simple_file_manager:get_file_info:474 | 获取文件信息: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.178 | INFO     | app.api.v1.file_management:get_batch_file_info:670 | 成功获取文件信息: /1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:25:51.179 | INFO     | app.api.v1.file_management:parse_file_id:519 | 使用原始文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.179 | INFO     | app.api.v1.file_management:get_batch_file_info:657 | 解析文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx -> type=local, storage_id=5, path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.183 | INFO     | app.services.simple_file_manager:get_file_info:474 | 获取文件信息: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.184 | INFO     | app.api.v1.file_management:get_batch_file_info:670 | 成功获取文件信息: /1-深圳市政务云服务申请表-民生警务平台汇总20250605(1).docx
2025-06-17 17:25:51.184 | INFO     | app.api.v1.file_management:parse_file_id:519 | 使用原始文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.184 | INFO     | app.api.v1.file_management:get_batch_file_info:657 | 解析文件ID: local_5_/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx -> type=local, storage_id=5, path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.186 | INFO     | app.services.simple_file_manager:get_file_info:474 | 获取文件信息: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.186 | INFO     | app.api.v1.file_management:get_batch_file_info:670 | 成功获取文件信息: /1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx
2025-06-17 17:25:51.187 | INFO     | app.api.v1.file_management:get_batch_file_info:687 | 批量获取文件信息完成: 成功=3, 失败=0
2025-06-17 17:25:51.189 | INFO     | app.core.middleware:dispatch:61 | Request completed: POST http://127.0.0.1:8000/api/v1/file-management/batch-info
2025-06-17 17:25:55.900 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/document-segment/tasks
2025-06-17 17:25:55.900 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/document-segment/tasks
2025-06-17 17:25:55.901 | INFO     | app.core.middleware:dispatch:30 | Request started: POST http://127.0.0.1:8000/api/v1/document-segment/tasks
2025-06-17 17:25:56.067 | INFO     | app.core.celery_config:<module>:59 | 使用Redis URL: redis://:XHC12345@192.168.50.142:6379/10
2025-06-17 17:25:56.070 | INFO     | app.core.celery_config:<module>:119 | Celery配置已加载
2025-06-17 17:25:56.129 | INFO     | app.core.middleware:dispatch:61 | Request completed: POST http://127.0.0.1:8000/api/v1/document-segment/tasks
2025-06-17 17:25:58.170 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:25:58.170 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:25:58.171 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:25:58.208 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:00.137 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:00.153 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:02.138 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:02.151 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:04.141 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:04.156 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:06.136 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:06.149 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:08.137 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:08.152 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:10.139 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:10.150 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:12.135 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:12.153 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:14.136 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:14.153 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:16.139 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:16.152 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:18.135 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:18.145 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:20.140 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:20.150 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:22.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:22.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:24.582 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:24.597 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:26.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:26.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:28.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:28.591 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:30.575 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:30.586 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:32.132 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:32.145 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:34.140 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:34.155 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:36.134 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:36.147 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:38.143 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:38.167 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:40.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:40.596 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:42.133 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:42.145 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:44.572 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:44.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:46.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:46.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:48.570 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:48.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:50.588 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:50.602 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:52.575 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:52.586 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:54.585 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:54.600 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:56.582 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:56.599 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:58.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:26:58.598 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:00.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:00.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:02.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:02.597 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:04.575 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:04.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:06.574 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:06.592 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:08.577 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:08.596 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:10.575 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:10.591 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:12.133 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:12.145 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:14.137 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:14.147 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:16.143 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:16.153 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:18.133 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:18.143 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:20.133 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:20.143 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:22.135 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:22.145 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:24.133 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:24.143 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:26.138 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:26.149 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:28.144 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:28.156 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:30.139 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:30.148 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:32.133 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:32.144 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:34.147 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:34.156 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:36.135 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:36.146 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:38.134 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:38.150 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:40.136 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:40.150 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:42.137 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:42.148 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:44.142 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:44.154 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:46.144 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:46.157 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:48.148 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:48.162 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:50.133 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:50.144 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:52.584 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:52.597 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:54.587 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:54.599 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:56.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:56.590 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:58.574 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:27:58.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:00.581 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:00.594 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:02.579 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:02.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:04.140 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:04.151 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:06.402 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:06.412 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:08.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:08.593 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:10.572 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:10.585 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:12.581 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:12.595 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:14.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:14.584 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:16.577 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:16.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:18.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:18.592 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:20.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:20.585 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:22.584 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:22.598 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:24.579 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:24.590 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:26.579 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:26.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:28.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:28.591 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:30.577 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:30.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:32.577 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:32.587 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:34.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:34.582 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:36.576 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:36.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:38.570 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:38.583 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:40.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:40.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:42.576 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:42.587 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:44.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:44.593 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:46.567 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:46.578 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:48.576 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:48.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:50.579 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:50.592 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:52.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:52.585 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:54.576 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:54.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:56.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:56.586 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:58.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:28:58.591 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:00.583 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:00.595 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:02.583 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:02.597 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:04.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:04.584 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:06.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:06.583 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:08.581 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:08.592 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:10.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:10.585 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:12.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:12.583 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:14.572 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:14.583 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:16.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:16.582 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:18.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:18.584 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:20.570 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:20.584 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:22.579 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:22.589 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:24.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:24.594 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:26.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:26.592 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:28.574 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:28.591 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:30.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:30.593 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:32.570 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:32.579 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:34.572 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:34.585 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:36.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:36.590 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:38.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:38.584 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:40.581 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:40.593 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:42.582 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:42.595 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:44.577 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:44.587 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:46.583 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:46.594 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:51.582 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:29:51.593 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:18.392 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:18.404 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:20.581 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:20.591 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:22.576 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:22.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:24.576 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:24.586 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:26.571 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:26.583 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:28.578 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:28.592 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:30.572 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:30.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:32.583 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:32.595 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:34.574 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:34.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:36.580 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:36.591 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:38.573 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:38.590 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:40.575 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:40.588 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:42.310 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:42.324 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:44.135 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:44.148 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:46.140 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:46.153 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:48.142 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:48.162 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:49.760 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:49.766 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:49.766 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:49.769 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:49.771 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:49.793 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:49.795 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:49.798 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:30:49.800 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:30:49.809 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 404 - Not Found
2025-06-17 17:30:49.810 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:30:49.812 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:49.813 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:49.821 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:30:49.837 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:50.134 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:50.152 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:52.139 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:52.151 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/document-segment/tasks/2f9b655a-8dc1-49ff-8ce2-573d519cc819/progress
2025-06-17 17:30:53.798 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:53.803 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:53.804 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:53.808 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:30:53.824 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:53.858 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:53.860 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:53.865 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:30:53.867 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:30:53.868 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 404 - Not Found
2025-06-17 17:30:53.868 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:30:53.890 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:53.891 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:30:53.903 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:30:53.939 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:31:12.543 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:12.545 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:12.546 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:12.547 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:12.548 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:12.551 | INFO     | app.api.v1.file_management:get_file_content:217 | 解析文件内容: type=local, storage_id=5, encoded_path=%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx, decoded_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:12.553 | INFO     | app.services.simple_file_manager:get_file_content:411 | 获取文件内容: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:12.754 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:12.758 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:12.760 | INFO     | app.api.v1.file_management:get_file_content:217 | 解析文件内容: type=local, storage_id=5, encoded_path=%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx, decoded_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:12.765 | INFO     | app.services.simple_file_manager:get_file_content:411 | 获取文件内容: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:12.958 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:24.806 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:24.810 | INFO     | app.api.v1.file_management:get_file_content:217 | 解析文件内容: type=local, storage_id=5, encoded_path=%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx, decoded_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:24.812 | INFO     | app.services.simple_file_manager:get_file_content:411 | 获取文件内容: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:25.015 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:25.019 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:25.021 | INFO     | app.api.v1.file_management:get_file_content:217 | 解析文件内容: type=local, storage_id=5, encoded_path=%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx, decoded_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:25.023 | INFO     | app.services.simple_file_manager:get_file_content:411 | 获取文件内容: original_path=/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, clean_path=1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx, full_path=storage\1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx
2025-06-17 17:31:25.223 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%**********%281%29%281%29.docx/content
2025-06-17 17:31:56.508 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:31:56.510 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:31:56.510 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:31:56.511 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:31:56.511 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:31:56.514 | INFO     | app.api.v1.file_management:get_file_content:217 | 解析文件内容: type=local, storage_id=5, encoded_path=%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx, decoded_path=/【法狗狗】人民检察院人工智能平台和应用场景方案.pptx
2025-06-17 17:31:56.516 | INFO     | app.services.simple_file_manager:get_file_content:411 | 获取文件内容: original_path=/【法狗狗】人民检察院人工智能平台和应用场景方案.pptx, clean_path=【法狗狗】人民检察院人工智能平台和应用场景方案.pptx, full_path=storage\【法狗狗】人民检察院人工智能平台和应用场景方案.pptx
2025-06-17 17:31:56.754 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:31:56.755 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:31:56.757 | INFO     | app.api.v1.file_management:get_file_content:217 | 解析文件内容: type=local, storage_id=5, encoded_path=%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx, decoded_path=/【法狗狗】人民检察院人工智能平台和应用场景方案.pptx
2025-06-17 17:31:56.759 | INFO     | app.services.simple_file_manager:get_file_content:411 | 获取文件内容: original_path=/【法狗狗】人民检察院人工智能平台和应用场景方案.pptx, clean_path=【法狗狗】人民检察院人工智能平台和应用场景方案.pptx, full_path=storage\【法狗狗】人民检察院人工智能平台和应用场景方案.pptx
2025-06-17 17:31:57.027 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/files/local_5_%2F%E3%80%90%E6%B3%95%E7%8B%97%E7%8B%97%E3%80%91%E4%BA%BA%E6%B0%91%E6%A3%80%E5%AF%9F%E9%99%A2%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E5%B9%B3%E5%8F%B0%E5%92%8C%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF%E6%96%B9%E6%A1%88.pptx/content
2025-06-17 17:35:52.442 | INFO     | app.services.upload_processor:stop:42 | 上传处理器停止
2025-06-17 17:35:52.442 | INFO     | app.core.startup:shutdown_tasks:76 | 执行应用关闭任务...
2025-06-17 17:35:52.443 | INFO     | app.core.startup:shutdown_tasks:82 | Celery指标收集器已停止
2025-06-17 17:35:52.443 | INFO     | app.core.startup:shutdown_tasks:88 | 停止Celery服务...
2025-06-17 17:35:52.443 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 flower 服务 (PID: 7136)
2025-06-17 17:35:52.444 | INFO     | app.core.celery_manager:stop_service:236 | flower 服务已停止
2025-06-17 17:35:53.489 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 beat 服务 (PID: 24024)
2025-06-17 17:35:53.490 | INFO     | app.core.celery_manager:stop_service:236 | beat 服务已停止
2025-06-17 17:35:55.573 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 worker 服务 (PID: 22880)
2025-06-17 17:35:55.574 | INFO     | app.core.celery_manager:stop_service:236 | worker 服务已停止
2025-06-17 17:35:58.711 | INFO     | app.core.startup:shutdown_tasks:90 | Celery服务已停止
2025-06-17 17:35:58.711 | INFO     | app.core.startup:shutdown_tasks:94 | 应用关闭任务完成
2025-06-17 17:35:58.712 | INFO     | app.plugins.auth.plugin:shutdown:44 | Auth plugin shutdown
2025-06-17 17:35:58.712 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: auth
2025-06-17 17:35:58.712 | INFO     | app.plugins.file_manager.plugin:shutdown:61 | File manager plugin shutdown
2025-06-17 17:35:58.712 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: file_manager
2025-06-17 17:35:58.712 | INFO     | app.plugins.rag_engine.plugin:shutdown:74 | RAG engine plugin shutdown
2025-06-17 17:35:58.713 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: rag_engine
2025-06-17 17:35:58.718 | INFO     | app.core.database:close_database:348 | Async database connection closed
2025-06-17 17:35:58.719 | INFO     | app.core.database:close_database:352 | Sync database connection closed
