/**
 * 文件上传任务管理器 - 数据库持久化版本
 * 支持任务队列、数据库持久化、断点续传
 */

export interface UploadTask {
  id?: number;
  task_id: string;
  file_name: string;
  file_size: number;
  file_path: string;
  storage_id: number;
  upload_path: string;
  status: 'pending' | 'uploading' | 'success' | 'error' | 'paused';
  progress: number;
  uploaded_bytes: number;
  error_message?: string;
  retry_count: number;
  created_at?: string;
  updated_at?: string;
  started_at?: string;
  completed_at?: string;
}

export interface UploadProgress {
  taskId: string;
  progress: number;
  loaded: number;
  total: number;
}

export interface TaskListResponse {
  tasks: UploadTask[];
  total: number;
  pages: number;
  current_page: number;
  page_size: number;
}

class UploadTaskManager {
  private activeUploads: Set<string> = new Set();
  private maxConcurrent = 3;
  private listeners: Set<(tasks: UploadTask[]) => void> = new Set();
  private progressListeners: Set<(progress: UploadProgress) => void> = new Set();
  private apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

  constructor() {
    this.startProcessing();
  }

  /**
   * 获取认证头
   */
  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  /**
   * 添加上传任务
   */
  async addTask(
    file: File,
    storageId: number,
    uploadPath: string
  ): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('storage_id', storageId.toString());
      formData.append('upload_path', uploadPath);

      const response = await fetch(`${this.apiBaseUrl}/api/v1/upload-tasks/create-with-file`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '创建任务失败');
      }

      this.notifyListeners();
      return result.data.task_id;
    } catch (error: any) {
      console.error('添加任务失败:', error);
      throw error;
    }
  }

  /**
   * 批量添加任务
   */
  async addTasks(
    files: File[],
    storageId: number,
    basePath: string
  ): Promise<string[]> {
    const taskIds: string[] = [];

    for (const file of files) {
      try {
        const uploadPath = basePath === '/'
          ? `/${file.name}`
          : `${basePath}/${file.name}`;

        const taskId = await this.addTask(file, storageId, uploadPath);
        taskIds.push(taskId);
      } catch (error) {
        console.error(`添加文件 ${file.name} 失败:`, error);
      }
    }

    return taskIds;
  }

  /**
   * 获取分页任务列表
   */
  async getTasksPaginated(page: number = 1, pageSize: number = 20): Promise<{
    tasks: UploadTask[];
    total: number;
    pages: number;
    currentPage: number;
  }> {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}/api/v1/upload-tasks/list?page=${page}&page_size=${pageSize}`,
        {
          headers: this.getAuthHeaders(),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '获取任务列表失败');
      }

      return {
        tasks: result.data.tasks,
        total: result.data.total,
        pages: result.data.pages,
        currentPage: result.data.current_page
      };
    } catch (error: any) {
      console.error('获取任务列表失败:', error);
      return {
        tasks: [],
        total: 0,
        pages: 0,
        currentPage: page
      };
    }
  }

  /**
   * 重试任务
   */
  async retryTask(taskId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/upload-tasks/${taskId}/retry`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '重试任务失败');
      }

      this.notifyListeners();
      return true;
    } catch (error: any) {
      console.error('重试任务失败:', error);
      return false;
    }
  }

  /**
   * 删除任务
   */
  async removeTask(taskId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/upload-tasks/${taskId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '删除任务失败');
      }

      this.notifyListeners();
      return true;
    } catch (error: any) {
      console.error('删除任务失败:', error);
      return false;
    }
  }

  /**
   * 批量操作任务
   */
  async batchOperation(taskIds: string[], action: 'pause' | 'resume' | 'delete'): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/upload-tasks/batch-operation`, {
        method: 'POST',
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          task_ids: taskIds,
          action: action
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '批量操作失败');
      }

      this.notifyListeners();
      return true;
    } catch (error: any) {
      console.error('批量操作失败:', error);
      return false;
    }
  }

  /**
   * 批量删除任务
   */
  async removeTasks(taskIds: string[]): Promise<boolean> {
    return this.batchOperation(taskIds, 'delete');
  }

  /**
   * 批量暂停任务
   */
  async pauseTasks(taskIds: string[]): Promise<boolean> {
    return this.batchOperation(taskIds, 'pause');
  }

  /**
   * 批量恢复任务
   */
  async resumeTasks(taskIds: string[]): Promise<boolean> {
    return this.batchOperation(taskIds, 'resume');
  }

  /**
   * 清空所有已完成的任务
   */
  async clearCompletedTasks(): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/v1/upload-tasks/clear-completed`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '清理已完成任务失败');
      }

      this.notifyListeners();
      return true;
    } catch (error: any) {
      console.error('清理已完成任务失败:', error);
      return false;
    }
  }

  /**
   * 暂停所有上传
   */
  async pauseAll(): Promise<boolean> {
    // 获取所有待处理和上传中的任务
    const tasks = await this.getTasksPaginated(1, 1000);
    const activeTaskIds = tasks.tasks
      .filter(task => task.status === 'pending' || task.status === 'uploading')
      .map(task => task.task_id);

    if (activeTaskIds.length === 0) return true;

    return this.pauseTasks(activeTaskIds);
  }

  /**
   * 恢复所有上传
   */
  async resumeAll(): Promise<boolean> {
    // 获取所有暂停的任务
    const tasks = await this.getTasksPaginated(1, 1000);
    const pausedTaskIds = tasks.tasks
      .filter(task => task.status === 'paused')
      .map(task => task.task_id);

    if (pausedTaskIds.length === 0) return true;

    return this.resumeTasks(pausedTaskIds);
  }

  /**
   * 添加状态监听器
   */
  addListener(listener: (tasks: UploadTask[]) => void): void {
    this.listeners.add(listener);
  }

  /**
   * 移除状态监听器
   */
  removeListener(listener: (tasks: UploadTask[]) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * 添加进度监听器
   */
  addProgressListener(listener: (progress: UploadProgress) => void): void {
    this.progressListeners.add(listener);
  }

  /**
   * 移除进度监听器
   */
  removeProgressListener(listener: (progress: UploadProgress) => void): void {
    this.progressListeners.delete(listener);
  }

  /**
   * 开始处理队列 - 简化版本，主要用于通知
   */
  private startProcessing(): void {
    // 定期检查任务状态变化并通知监听器
    setInterval(() => {
      this.notifyListeners();
    }, 2000);
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        // 这里不传递具体任务，让组件自己获取
        listener([]);
      } catch (error) {
        console.error('Listener error:', error);
      }
    });
  }

  /**
   * 通知进度监听器
   */
  private notifyProgressListeners(progress: UploadProgress): void {
    this.progressListeners.forEach(listener => {
      try {
        listener(progress);
      } catch (error) {
        console.error('Progress listener error:', error);
      }
    });
  }
}

// 全局单例
export const uploadTaskManager = new UploadTaskManager();
