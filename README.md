# AI知识库

基于人工智能的智能知识管理与检索系统，采用现代化的前后端分离架构，提供企业级的知识管理解决方案。

## 🌟 项目特色

- 🧠 **AI驱动** - 基于先进的人工智能技术，提供智能知识检索和管理
- 🌍 **多语言支持** - 支持中文简体、中文繁体、英文、日文四种语言
- 🔐 **安全可靠** - 企业级安全保障，JWT认证，端到端加密
- ⚡ **高性能** - 基于FastAPI和Next.js，提供极速响应体验
- 🔌 **插件化** - 灵活的插件系统，支持功能扩展
- 📱 **响应式** - 完美适配各种设备，移动优先设计

## 🏗️ 技术架构

### 后端 (API)
- **框架**: FastAPI
- **语言**: Python 3.9+
- **数据库**: 多数据库支持 (PostgreSQL/MySQL/Oracle/SQLite) + Redis
- **认证**: JWT Token
- **任务队列**: Celery
- **包管理**: uv

### 前端 (Web)
- **框架**: Next.js 15
- **语言**: TypeScript
- **样式**: Tailwind CSS 4.0
- **动画**: Framer Motion
- **状态管理**: React Hook Form
- **包管理**: pnpm/npm

## 📁 项目结构

```
AI知识库/
├── api/                    # 后端API服务
│   ├── app/               # 应用核心代码
│   │   ├── api/          # API路由
│   │   ├── core/         # 核心配置
│   │   ├── plugins/      # 插件系统
│   │   └── cli.py        # 命令行工具
│   ├── main.py           # 应用入口
│   ├── pyproject.toml    # 项目配置
│   └── README.md         # 后端文档
├── web/                   # 前端Web应用
│   ├── app/              # Next.js应用
│   ├── components/       # React组件
│   ├── lib/              # 工具库
│   ├── contexts/         # React上下文
│   ├── package.json      # 前端配置
│   └── README.md         # 前端文档
└── README.md             # 项目总览
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.9+
- **Node.js**: 18+
- **PostgreSQL**: 13+
- **Redis**: 6+

### 1. 克隆项目

```bash
git clone <repository-url>
cd 知识库智能平台
```

### 2. 启动后端服务

```bash
# 进入后端目录
cd api

# 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
uv sync

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 配置数据库等信息

# 启动服务
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

### 3. 启动前端服务

```bash
# 进入前端目录
cd web

# 安装依赖
npm install
# 或使用 pnpm install

# 启动开发服务器
npm run dev
```

### 4. 访问应用

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🔧 配置说明

### 后端配置

主要配置文件位于 `api/.env.*`：

- `.env.development` - 开发环境
- `.env.testing` - 测试环境  
- `.env.production` - 生产环境

关键配置项：
```env
# 应用基础配置
APP_NAME="知识库智能平台 API"
ENVIRONMENT="development"
DEBUG=true

# 数据库配置
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/knowledge_platform"

# Redis配置
REDIS_URL="redis://localhost:6379/0"

# 安全配置
SECRET_KEY="your-secret-key"
```

### 前端配置

主要配置文件 `web/.env.local`：

```env
# API基础URL
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=知识库智能平台
```

## 🌍 多语言支持

系统采用模块化多语言架构，支持动态语言切换：

### 支持的语言
- 🇨🇳 **中文简体** (zh-CN) - 默认语言
- 🇹🇼 **中文繁体** (zh-TW)
- 🇺🇸 **英文** (en)
- 🇯🇵 **日文** (ja)

### 语言文件结构
```
web/lib/i18n/
├── index.ts              # 主配置文件
└── locales/              # 语言文件目录
    ├── zh-CN.ts         # 中文简体
    ├── zh-TW.ts         # 中文繁体
    ├── en.ts            # 英文
    └── ja.ts            # 日文
```

### 添加新语言
1. 在 `web/lib/i18n/locales/` 目录下创建新的语言文件
2. 在 `web/lib/i18n/index.ts` 中添加语言配置
3. 更新语言切换器组件

### 语言功能特性
- 🔄 **动态加载** - 按需加载语言文件
- 🧠 **智能检测** - 自动检测浏览器语言
- 💾 **本地存储** - 记住用户语言偏好
- ⚡ **缓存机制** - 提高语言切换性能

## 🗄️ 多数据库支持

系统支持多种主流数据库，可根据需求灵活选择：

### 支持的数据库
- 🐘 **PostgreSQL** (推荐) - 企业级关系数据库
- 🐬 **MySQL** - 流行的开源数据库
- 🏛️ **Oracle** - 企业级商业数据库
- 📁 **SQLite** - 轻量级文件数据库

### 数据库配置示例

**PostgreSQL:**
```env
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/ai_knowledge_base"
DATABASE_TYPE="postgresql"
```

**MySQL:**
```env
DATABASE_URL="mysql+aiomysql://user:password@localhost:3306/ai_knowledge_base"
DATABASE_TYPE="mysql"
```

**Oracle:**
```env
DATABASE_URL="oracle+oracledb://user:password@localhost:1521/ai_knowledge_base"
DATABASE_TYPE="oracle"
```

**SQLite:**
```env
DATABASE_URL="sqlite+aiosqlite:///./ai_knowledge_base.db"
DATABASE_TYPE="sqlite"
```

### 数据库兼容性特性
- 🔄 **自动适配** - 根据数据库类型自动调整配置
- 🛠️ **统一接口** - 提供统一的数据库操作接口
- ⚡ **连接池** - 智能连接池管理
- 🔧 **迁移支持** - 支持数据库结构迁移

## 🔌 插件系统

系统采用插件化架构，支持功能扩展：

### 内置插件

1. **认证插件** (`auth`) - JWT认证和授权
2. **文件管理插件** (`file_manager`) - 文件上传下载
3. **知识引擎插件** (`knowledge_engine`) - 智能知识检索

### 插件开发

```python
from app.plugins.base import BasePlugin

class MyPlugin(BasePlugin):
    @property
    def metadata(self):
        return PluginMetadata(
            name="my_plugin",
            version="1.0.0",
            description="我的自定义插件"
        )
    
    async def initialize(self, app_settings):
        # 初始化逻辑
        pass
```

## 📊 API接口

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `GET /api/v1/auth/me` - 获取用户信息

### 用户管理
- `GET /api/v1/users/` - 用户列表
- `POST /api/v1/users/` - 创建用户
- `PUT /api/v1/users/{id}` - 更新用户

### 文件管理
- `POST /api/v1/files/upload` - 文件上传
- `GET /api/v1/files/` - 文件列表
- `GET /api/v1/files/{id}/download` - 文件下载

### 系统管理
- `GET /api/v1/system/health` - 系统健康检查
- `GET /api/v1/system/info` - 系统信息

## 🛠️ 开发指南

### 后端开发

```bash
# 运行测试
uv run pytest

# 代码检查
uv run black .
uv run isort .
uv run flake8 .

# 启动开发服务器
uv run knowledge-api run --reload
```

### 前端开发

```bash
# 运行开发服务器
npm run dev

# 构建生产版本
npm run build

# 代码检查
npm run lint
```

## 🚀 部署指南

### Docker部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境

1. 配置生产环境变量
2. 设置数据库和Redis
3. 配置反向代理(Nginx)
4. 设置SSL证书
5. 配置监控和日志

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- **项目主页**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **邮箱**: <EMAIL>

---

**知识库智能平台** - 让知识更智能，让检索更精准 🚀
