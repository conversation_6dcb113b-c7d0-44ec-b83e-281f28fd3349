"""
统一响应格式工具
"""

from typing import Any, Dict, Optional, Union
from fastapi import status
from fastapi.responses import JSONResponse


def success_response(
    data: Any = None,
    message: str = "操作成功",
    code: int = 200
) -> Dict[str, Any]:
    """
    成功响应格式
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 状态码
    
    Returns:
        统一格式的成功响应
    """
    response = {
        "code": code,
        "message": message,
        "success": True
    }
    
    if data is not None:
        response["data"] = data
    
    return response


def error_response(
    message: str = "操作失败",
    code: int = 400,
    data: Any = None,
    errors: Optional[Union[str, list, dict]] = None
) -> Dict[str, Any]:
    """
    错误响应格式
    
    Args:
        message: 错误消息
        code: 错误码
        data: 响应数据
        errors: 详细错误信息
    
    Returns:
        统一格式的错误响应
    """
    response = {
        "code": code,
        "message": message,
        "success": False
    }
    
    if data is not None:
        response["data"] = data
    
    if errors is not None:
        response["errors"] = errors
    
    return response


def paginated_response(
    items: list,
    total: int,
    page: int = 1,
    page_size: int = 10,
    message: str = "获取成功"
) -> Dict[str, Any]:
    """
    分页响应格式
    
    Args:
        items: 数据列表
        total: 总数量
        page: 当前页码
        page_size: 每页大小
        message: 响应消息
    
    Returns:
        统一格式的分页响应
    """
    total_pages = (total + page_size - 1) // page_size
    
    return success_response(
        data={
            "items": items,
            "pagination": {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        },
        message=message
    )


def validation_error_response(errors: Union[str, list, dict]) -> JSONResponse:
    """
    验证错误响应
    
    Args:
        errors: 验证错误信息
    
    Returns:
        验证错误的JSON响应
    """
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response(
            message="数据验证失败",
            code=422,
            errors=errors
        )
    )


def unauthorized_response(message: str = "未授权访问") -> JSONResponse:
    """
    未授权响应
    
    Args:
        message: 错误消息
    
    Returns:
        未授权的JSON响应
    """
    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content=error_response(
            message=message,
            code=401
        )
    )


def forbidden_response(message: str = "权限不足") -> JSONResponse:
    """
    禁止访问响应
    
    Args:
        message: 错误消息
    
    Returns:
        禁止访问的JSON响应
    """
    return JSONResponse(
        status_code=status.HTTP_403_FORBIDDEN,
        content=error_response(
            message=message,
            code=403
        )
    )


def not_found_response(message: str = "资源不存在") -> JSONResponse:
    """
    资源不存在响应
    
    Args:
        message: 错误消息
    
    Returns:
        资源不存在的JSON响应
    """
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content=error_response(
            message=message,
            code=404
        )
    )


def server_error_response(message: str = "服务器内部错误") -> JSONResponse:
    """
    服务器错误响应
    
    Args:
        message: 错误消息
    
    Returns:
        服务器错误的JSON响应
    """
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response(
            message=message,
            code=500
        )
    )


class ResponseCode:
    """响应状态码常量"""
    
    # 成功状态码
    SUCCESS = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204
    
    # 客户端错误状态码
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    TOO_MANY_REQUESTS = 429
    
    # 服务器错误状态码
    INTERNAL_SERVER_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504


class ResponseMessage:
    """响应消息常量"""
    
    # 成功消息
    SUCCESS = "操作成功"
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    RETRIEVED = "获取成功"
    
    # 错误消息
    BAD_REQUEST = "请求参数错误"
    UNAUTHORIZED = "未授权访问"
    FORBIDDEN = "权限不足"
    NOT_FOUND = "资源不存在"
    CONFLICT = "资源冲突"
    VALIDATION_ERROR = "数据验证失败"
    INTERNAL_ERROR = "服务器内部错误"
    
    # 业务消息
    LOGIN_SUCCESS = "登录成功"
    LOGIN_FAILED = "登录失败"
    LOGOUT_SUCCESS = "退出成功"
    REGISTER_SUCCESS = "注册成功"
    PASSWORD_CHANGED = "密码修改成功"
    PROFILE_UPDATED = "个人信息更新成功"
