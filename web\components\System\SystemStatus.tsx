/**
 * 系统状态组件
 * 显示真实的系统监控数据
 */
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Cpu, 
  MemoryStick, 
  Wifi, 
  WifiOff,
  HardDrive,
  Activity,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Server,
  Monitor,
  Zap
} from 'lucide-react';
import apiClient from '@/lib/api';

interface SystemStatusData {
  cpu: {
    usage_percent: number;
    core_count: number;
    status: string;
  };
  memory: {
    total: number;
    used: number;
    usage_percent: number;
    total_formatted: string;
    used_formatted: string;
    status: string;
  };
  disk: {
    total: number;
    used: number;
    usage_percent: number;
    total_formatted: string;
    used_formatted: string;
    status: string;
  };
  network: {
    status: string;
    connections: number;
    bytes_sent_formatted: string;
    bytes_recv_formatted: string;
  };
  system: {
    platform: string;
    hostname: string;
    uptime_formatted: string;
  };
  timestamp: string;
}

interface SystemStatusProps {
  className?: string;
}

const SystemStatus: React.FC<SystemStatusProps> = ({ className = '' }) => {
  const [data, setData] = useState<SystemStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get('/api/v1/system-status/overview');
      
      if (response.data.success) {
        setData(response.data.data);
      } else {
        setError(response.data.message || '获取系统状态失败');
      }
    } catch (err) {
      console.error('Failed to fetch system status:', err);
      setError(err instanceof Error ? err.message : '获取系统状态失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemStatus();
    
    // 每30秒自动刷新
    const interval = setInterval(fetchSystemStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchSystemStatus();
  };

  const getStatusColor = (status: string, percentage?: number) => {
    if (status === 'offline') return 'text-red-500';
    if (percentage !== undefined) {
      if (percentage > 90) return 'text-red-500';
      if (percentage > 70) return 'text-yellow-500';
      return 'text-green-500';
    }
    return status === 'online' ? 'text-green-500' : 'text-gray-500';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage > 90) return 'bg-red-500';
    if (percentage > 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl p-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="flex items-center space-x-2 text-gray-500">
            <RefreshCw className="w-5 h-5 animate-spin" />
            <span>加载系统状态...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white/80 backdrop-blur-sm border border-red-200/50 rounded-xl p-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="flex items-center space-x-2 text-red-500">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
            <button
              onClick={handleRefresh}
              className="ml-2 px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200 transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <div className={`bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl p-6 ${className}`}>
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">系统状态</h3>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">
            更新于: {new Date(data.timestamp).toLocaleTimeString()}
          </span>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="刷新数据"
          >
            <RefreshCw className="w-4 h-4" />
          </motion.button>
        </div>
      </div>

      {/* 系统信息 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* CPU使用率 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <div className="flex items-center justify-between mb-2">
            <Cpu className="w-5 h-5 text-blue-600" />
            <span className={`text-sm font-medium ${getStatusColor(data.cpu.status, data.cpu.usage_percent)}`}>
              {data.cpu.usage_percent}%
            </span>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-blue-800">CPU使用率</p>
            <p className="text-xs text-blue-600">{data.cpu.core_count} 核心</p>
            <div className="w-full bg-blue-200 rounded-full h-1.5">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${data.cpu.usage_percent}%` }}
                transition={{ duration: 0.5 }}
                className={`h-1.5 rounded-full ${getProgressColor(data.cpu.usage_percent)}`}
              />
            </div>
          </div>
        </motion.div>

        {/* 内存使用 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="p-4 bg-green-50 border border-green-200 rounded-lg"
        >
          <div className="flex items-center justify-between mb-2">
            <MemoryStick className="w-5 h-5 text-green-600" />
            <span className={`text-sm font-medium ${getStatusColor(data.memory.status, data.memory.usage_percent)}`}>
              {data.memory.usage_percent}%
            </span>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-green-800">内存使用</p>
            <p className="text-xs text-green-600">
              {data.memory.used_formatted} / {data.memory.total_formatted}
            </p>
            <div className="w-full bg-green-200 rounded-full h-1.5">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${data.memory.usage_percent}%` }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className={`h-1.5 rounded-full ${getProgressColor(data.memory.usage_percent)}`}
              />
            </div>
          </div>
        </motion.div>

        {/* 磁盘使用 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="p-4 bg-purple-50 border border-purple-200 rounded-lg"
        >
          <div className="flex items-center justify-between mb-2">
            <HardDrive className="w-5 h-5 text-purple-600" />
            <span className={`text-sm font-medium ${getStatusColor(data.disk.status, data.disk.usage_percent)}`}>
              {data.disk.usage_percent}%
            </span>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-purple-800">磁盘使用</p>
            <p className="text-xs text-purple-600">
              {data.disk.used_formatted} / {data.disk.total_formatted}
            </p>
            <div className="w-full bg-purple-200 rounded-full h-1.5">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${data.disk.usage_percent}%` }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className={`h-1.5 rounded-full ${getProgressColor(data.disk.usage_percent)}`}
              />
            </div>
          </div>
        </motion.div>

        {/* 网络状态 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="p-4 bg-orange-50 border border-orange-200 rounded-lg"
        >
          <div className="flex items-center justify-between mb-2">
            {data.network.status === 'online' ? (
              <Wifi className="w-5 h-5 text-orange-600" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-600" />
            )}
            <span className={`text-sm font-medium ${getStatusColor(data.network.status)}`}>
              {data.network.status === 'online' ? '在线' : '离线'}
            </span>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-orange-800">网络状态</p>
            <p className="text-xs text-orange-600">
              {data.network.connections} 个连接
            </p>
            <div className="flex items-center space-x-1 text-xs text-orange-600">
              <span>↑{data.network.bytes_sent_formatted}</span>
              <span>↓{data.network.bytes_recv_formatted}</span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 系统信息 */}
      <div className="pt-4 border-t border-gray-200/50">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <Server className="w-4 h-4 text-gray-500" />
            <span className="text-gray-600">系统:</span>
            <span className="font-medium">{data.system.platform}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Monitor className="w-4 h-4 text-gray-500" />
            <span className="text-gray-600">主机:</span>
            <span className="font-medium">{data.system.hostname}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Zap className="w-4 h-4 text-gray-500" />
            <span className="text-gray-600">运行时间:</span>
            <span className="font-medium">{data.system.uptime_formatted}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemStatus;
