"""
应用启动时的初始化功能
"""
import json
import asyncio
import time
import threading
from pathlib import Path
from loguru import logger

from app.core.celery_manager import celery_manager


async def initialize_celery():
    """初始化Celery服务"""
    try:
        # 检查自动启动配置
        config_file = Path("celery_config.json")
        auto_start = True  # 默认启动
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    auto_start = config.get("auto_start", True)
            except Exception as e:
                logger.warning(f"读取Celery配置失败: {e}")
        
        if auto_start:
            logger.info("自动启动Celery服务...")
            
            # 在后台线程中启动Celery，避免阻塞主线程
            def start_celery_background():
                try:
                    results = celery_manager.start_all()
                    success_count = sum(1 for success in results.values() if success)
                    total_count = len(results)
                    
                    if success_count == total_count:
                        logger.info("所有Celery服务启动成功")
                    elif success_count > 0:
                        logger.warning(f"部分Celery服务启动成功 ({success_count}/{total_count})")
                    else:
                        logger.error("所有Celery服务启动失败")
                        
                except Exception as e:
                    logger.error(f"启动Celery服务失败: {e}")
            
            # 使用线程池执行，避免阻塞
            import threading
            thread = threading.Thread(target=start_celery_background, daemon=True)
            thread.start()

            # 暂时禁用自动启动指标收集，避免启动时的复杂依赖
            logger.info("Celery指标收集器将在服务完全启动后手动启动")

        else:
            logger.info("Celery自动启动已禁用")
            
    except Exception as e:
        logger.error(f"初始化Celery失败: {e}")


async def startup_tasks():
    """应用启动任务"""
    logger.info("执行应用启动任务...")
    
    # 初始化Celery
    await initialize_celery()
    
    logger.info("应用启动任务完成")


async def shutdown_tasks():
    """应用关闭任务"""
    logger.info("执行应用关闭任务...")
    
    try:
        # 停止指标收集
        from app.services.celery_metrics_collector import metrics_collector
        await metrics_collector.stop_collection()
        logger.info("Celery指标收集器已停止")
    except Exception as e:
        logger.warning(f"停止Celery指标收集器失败: {e}")

    try:
        # 停止Celery服务
        logger.info("停止Celery服务...")
        celery_manager.stop_all()
        logger.info("Celery服务已停止")
    except Exception as e:
        logger.error(f"停止Celery服务失败: {e}")
    
    logger.info("应用关闭任务完成")
