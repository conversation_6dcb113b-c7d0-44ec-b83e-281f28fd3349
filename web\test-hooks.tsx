/**
 * React Hooks 测试组件
 * 用于验证 Hooks 调用顺序是否正确
 */

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';

interface TestFormData {
  username: string;
  password: string;
}

const TestHooksComponent: React.FC = () => {
  // 所有 Hooks 必须在条件语句之前调用
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [data, setData] = useState<any>(null);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<TestFormData>();

  useEffect(() => {
    console.log('Component mounted');
  }, []);

  // 条件渲染应该在所有 Hooks 调用之后
  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!data) {
    return (
      <div>
        <h1>Test Hooks Component</h1>
        <p>All hooks are called before conditional rendering</p>
        <button onClick={() => setData({ test: true })}>
          Load Data
        </button>
      </div>
    );
  }

  return (
    <div>
      <h1>Data Loaded</h1>
      <form onSubmit={handleSubmit((data) => console.log(data))}>
        <input
          {...register('username', { required: true })}
          placeholder="Username"
        />
        {errors.username && <span>Username is required</span>}
        
        <input
          {...register('password', { required: true })}
          type={showPassword ? 'text' : 'password'}
          placeholder="Password"
        />
        {errors.password && <span>Password is required</span>}
        
        <button type="button" onClick={() => setShowPassword(!showPassword)}>
          {showPassword ? 'Hide' : 'Show'} Password
        </button>
        
        <button type="submit">Submit</button>
      </form>
    </div>
  );
};

export default TestHooksComponent;
