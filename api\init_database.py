#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表和初始数据
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 加载环境变量
env_files = ['.env.local', '.env.test', '.env', '.env.example']
for env_file in env_files:
    env_path = Path(env_file)
    if env_path.exists():
        print(f"📄 Loading environment from: {env_file}")
        load_dotenv(env_path)
        break

# 先导入配置以确保环境变量正确加载
from app.core.config import settings
from app.core.database import init_database, create_tables
from loguru import logger

# 验证配置加载
logger.info(f"Loaded config - DB Host: {settings.DB_HOST}, DB Port: {settings.DB_PORT}")
logger.info(f"Database URL: postgresql://{settings.DB_USERNAME}:***@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_DATABASE}")


async def check_database_connection():
    """检查数据库连接"""
    try:
        import asyncpg

        # 显示连接信息（隐藏密码）
        logger.info(f"Testing database connection: postgresql://{settings.DB_USERNAME}:***@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_DATABASE}")

        # 连接测试
        conn = await asyncpg.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            database=settings.DB_DATABASE,
            timeout=30  # 增加超时时间
        )

        # 测试查询
        result = await conn.fetchval("SELECT version()")
        logger.info(f"PostgreSQL version: {result[:50]}...")

        await conn.close()
        logger.success("✅ Database connection successful")
        return True

    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        logger.error(f"Connection details: host={settings.DB_HOST}, port={settings.DB_PORT}, user={settings.DB_USERNAME}, database={settings.DB_DATABASE}")
        return False


async def check_redis_connection():
    """检查Redis连接"""
    try:
        import redis.asyncio as redis
        
        # 创建Redis连接
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            socket_timeout=5
        )
        
        # 测试连接
        await r.ping()
        logger.success("✅ Redis connection successful")
        await r.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        logger.warning("⚠️  Redis is optional, continuing without it")
        return False


async def initialize_database():
    """初始化数据库"""
    try:
        logger.info("🔧 Initializing database...")

        # 直接使用SQL创建表（更可靠的方法）
        await create_tables_directly()
        logger.success("✅ Database initialized using direct SQL")
        return True

    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False


async def create_tables_directly():
    """直接使用SQL创建表结构"""
    try:
        import asyncpg

        logger.info("Creating database tables using direct SQL...")

        conn = await asyncpg.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD,
            database=settings.DB_DATABASE,
            timeout=30
        )

        # 创建用户表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                hashed_password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                is_superuser BOOLEAN DEFAULT FALSE,
                avatar_url VARCHAR(255),
                bio TEXT,
                last_login_at TIMESTAMP WITH TIME ZONE,
                login_count INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        logger.info("✅ Users table created")

        # 创建存储配置表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS storage_configs (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                storage_type VARCHAR(20) NOT NULL,
                config JSONB NOT NULL,
                is_default BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        logger.info("✅ Storage configs table created")

        # 创建文件记录表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS file_records (
                id SERIAL PRIMARY KEY,
                storage_config_id INTEGER REFERENCES storage_configs(id),
                file_path VARCHAR(500) NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_size BIGINT,
                file_hash VARCHAR(64),
                mime_type VARCHAR(100),
                metadata JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        logger.info("✅ File records table created")

        # 创建同步日志表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS sync_logs (
                id SERIAL PRIMARY KEY,
                storage_config_id INTEGER REFERENCES storage_configs(id),
                operation VARCHAR(20) NOT NULL,
                file_path VARCHAR(500),
                status VARCHAR(20) NOT NULL,
                message TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        logger.info("✅ Sync logs table created")

        # 创建索引
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_file_records_path ON file_records(file_path)')
        await conn.execute('CREATE INDEX IF NOT EXISTS idx_file_records_hash ON file_records(file_hash)')
        logger.info("✅ Indexes created")

        await conn.close()

    except Exception as e:
        logger.error(f"Failed to create tables directly: {e}")
        raise


async def create_default_storage():
    """创建默认存储配置"""
    try:
        logger.info("🗂️ Creating default storage configuration...")

        # 直接使用SQL创建默认存储配置
        await create_default_storage_directly()
        logger.success("✅ Default storage configuration created")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to create default storage: {e}")
        return False


async def create_default_storage_directly():
    """直接使用SQL创建默认存储配置"""
    import asyncpg

    conn = await asyncpg.connect(
        host=settings.DB_HOST,
        port=settings.DB_PORT,
        user=settings.DB_USERNAME,
        password=settings.DB_PASSWORD,
        database=settings.DB_DATABASE,
        timeout=30
    )

    # 检查是否已存在默认存储配置
    existing = await conn.fetchval(
        "SELECT id FROM storage_configs WHERE is_default = TRUE LIMIT 1"
    )

    if not existing:
        # 创建默认本地存储配置
        await conn.execute('''
            INSERT INTO storage_configs (name, storage_type, config, is_default, is_active)
            VALUES ($1, $2, $3, $4, $5)
        ''',
        "默认本地存储",
        "local",
        '{"base_path": "./storage", "max_file_size": 104857600}',
        True,
        True
        )
        logger.info("✅ Default storage config created")
    else:
        logger.info("✅ Default storage config already exists")

    await conn.close()


async def main():
    """主函数"""
    logger.info("🚀 AI知识库数据库初始化")
    logger.info("=" * 50)
    
    try:
        # 检查数据库连接
        logger.info("🔍 Checking database connection...")
        db_ok = await check_database_connection()
        if not db_ok:
            logger.error("❌ Database connection failed!")
            logger.info("💡 Please ensure PostgreSQL is running and accessible:")
            logger.info(f"   - Host: {settings.DB_HOST}:{settings.DB_PORT}")
            logger.info(f"   - Database: {settings.DB_DATABASE}")
            logger.info(f"   - User: {settings.DB_USERNAME}")
            sys.exit(1)
        
        # 检查Redis连接（可选）
        logger.info("🔍 Checking Redis connection...")
        redis_ok = await check_redis_connection()
        
        # 初始化数据库
        init_ok = await initialize_database()
        if not init_ok:
            logger.error("❌ Database initialization failed!")
            sys.exit(1)
        
        # 创建默认存储配置
        storage_ok = await create_default_storage()
        if not storage_ok:
            logger.warning("⚠️  Failed to create default storage configuration")
        
        logger.success("🎉 Database initialization completed successfully!")
        logger.info("📊 Summary:")
        logger.info(f"   - PostgreSQL: {'✅ Connected' if db_ok else '❌ Failed'}")
        logger.info(f"   - Redis: {'✅ Connected' if redis_ok else '⚠️ Optional'}")
        logger.info(f"   - Database: {'✅ Initialized' if init_ok else '❌ Failed'}")
        logger.info(f"   - Default Storage: {'✅ Created' if storage_ok else '⚠️ Skipped'}")
        
        logger.info("\n🚀 You can now start the server with:")
        logger.info("   python quick_start.py")
        logger.info("   or")
        logger.info("   uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
        
    except Exception as e:
        logger.error(f"❌ Initialization failed: {e}")
        logger.info("\n💡 Troubleshooting tips:")
        logger.info("1. Check PostgreSQL service is running")
        logger.info("2. Verify database credentials")
        logger.info("3. Ensure database 'xhc_rag' exists")
        logger.info("4. Check network connectivity to database server")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
