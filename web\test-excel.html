<!DOCTYPE html>
<html>
<head>
    <title>Test Excel Editor</title>
</head>
<body>
    <h1>Excel Editor Import Test</h1>
    <p>Testing if the Excel editor imports work correctly.</p>
    
    <script>
        // Test if we can access the page
        console.log('Page loaded successfully');
        
        // Try to access the file
        fetch('/file-manager/view/test')
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Response received');
            })
            .catch(error => {
                console.error('Error:', error);
            });
    </script>
</body>
</html>
