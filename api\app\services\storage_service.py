"""
存储服务
提供存储相关的业务逻辑
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.file_management import StorageConfig


class StorageService:
    """存储服务类"""
    
    def __init__(self):
        pass
    
    def get_storage_by_id(self, db: Session, storage_id: int) -> Optional[StorageConfig]:
        """根据ID获取存储配置"""
        return db.query(StorageConfig).filter(StorageConfig.id == storage_id).first()
    
    def get_all_storages(self, db: Session) -> List[StorageConfig]:
        """获取所有存储配置"""
        return db.query(StorageConfig).all()
    
    def get_active_storages(self, db: Session) -> List[StorageConfig]:
        """获取所有活跃的存储配置"""
        return db.query(StorageConfig).filter(StorageConfig.is_active == True).all()
