-- 分段任务管理功能数据库修复脚本
-- 适用于Windows系统，无外键约束
-- 2025-06-17

-- 1. 检查并添加任务管理相关字段（如果不存在）
DO $$
BEGIN
    -- 检查document_segment_tasks表是否存在segment_metadata字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segment_tasks' 
        AND column_name = 'segment_metadata'
    ) THEN
        ALTER TABLE document_segment_tasks 
        ADD COLUMN segment_metadata JSONB DEFAULT '{}'::jsonb;
        
        COMMENT ON COLUMN document_segment_tasks.segment_metadata IS '分段元数据，包含Celery任务ID等信息';
    END IF;

    -- 检查是否存在started_at字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segment_tasks' 
        AND column_name = 'started_at'
    ) THEN
        ALTER TABLE document_segment_tasks 
        ADD COLUMN started_at TIMESTAMP WITH TIME ZONE;
        
        COMMENT ON COLUMN document_segment_tasks.started_at IS '任务开始时间';
    END IF;

    -- 检查是否存在completed_at字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segment_tasks' 
        AND column_name = 'completed_at'
    ) THEN
        ALTER TABLE document_segment_tasks 
        ADD COLUMN completed_at TIMESTAMP WITH TIME ZONE;
        
        COMMENT ON COLUMN document_segment_tasks.completed_at IS '任务完成时间';
    END IF;

    -- 检查是否存在progress字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segment_tasks' 
        AND column_name = 'progress'
    ) THEN
        ALTER TABLE document_segment_tasks 
        ADD COLUMN progress FLOAT DEFAULT 0.0 CHECK (progress >= 0.0 AND progress <= 100.0);
        
        COMMENT ON COLUMN document_segment_tasks.progress IS '任务进度百分比';
    END IF;

    -- 检查是否存在error_message字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segment_tasks' 
        AND column_name = 'error_message'
    ) THEN
        ALTER TABLE document_segment_tasks 
        ADD COLUMN error_message TEXT;
        
        COMMENT ON COLUMN document_segment_tasks.error_message IS '错误信息';
    END IF;

    -- 检查document_segments表是否存在content_hash字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segments' 
        AND column_name = 'content_hash'
    ) THEN
        ALTER TABLE document_segments 
        ADD COLUMN content_hash VARCHAR(64);
        
        COMMENT ON COLUMN document_segments.content_hash IS '内容哈希值，用于去重';
    END IF;

    -- 检查是否存在quality_score字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segments' 
        AND column_name = 'quality_score'
    ) THEN
        ALTER TABLE document_segments 
        ADD COLUMN quality_score FLOAT DEFAULT 0.0 CHECK (quality_score >= 0.0 AND quality_score <= 1.0);
        
        COMMENT ON COLUMN document_segments.quality_score IS '分段质量评分';
    END IF;

    -- 检查是否存在readability_score字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segments' 
        AND column_name = 'readability_score'
    ) THEN
        ALTER TABLE document_segments 
        ADD COLUMN readability_score FLOAT DEFAULT 0.0 CHECK (readability_score >= 0.0 AND readability_score <= 1.0);
        
        COMMENT ON COLUMN document_segments.readability_score IS '分段可读性评分';
    END IF;

    -- 检查是否存在keywords字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_segments' 
        AND column_name = 'keywords'
    ) THEN
        ALTER TABLE document_segments 
        ADD COLUMN keywords JSONB DEFAULT '[]'::jsonb;
        
        COMMENT ON COLUMN document_segments.keywords IS '关键词列表';
    END IF;

END $$;

-- 2. 创建任务管理相关的索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status_created 
ON document_segment_tasks(status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_progress 
ON document_segment_tasks(progress) WHERE status = 'processing';

CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_metadata 
ON document_segment_tasks USING GIN(segment_metadata);

CREATE INDEX IF NOT EXISTS idx_document_segments_task_file 
ON document_segments(task_id, file_id);

CREATE INDEX IF NOT EXISTS idx_document_segments_quality 
ON document_segments(quality_score DESC) WHERE quality_score > 0;

CREATE INDEX IF NOT EXISTS idx_document_segments_keywords 
ON document_segments USING GIN(keywords);

-- 3. 创建任务统计视图
CREATE OR REPLACE VIEW task_statistics AS
SELECT 
    t.id,
    t.task_id,
    t.task_name,
    t.status,
    t.progress,
    t.total_files,
    t.processed_files,
    t.total_segments,
    t.total_vectors,
    t.created_at,
    t.started_at,
    t.completed_at,
    -- 计算字段
    CASE 
        WHEN t.started_at IS NOT NULL AND t.completed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (t.completed_at - t.started_at))
        ELSE NULL 
    END as duration_seconds,
    CASE 
        WHEN t.total_files > 0 
        THEN (t.processed_files::float / t.total_files::float) * 100 
        ELSE 0 
    END as success_rate,
    -- 实际分段统计
    COALESCE(seg_stats.actual_segments, 0) as actual_segments,
    COALESCE(seg_stats.actual_vectors, 0) as actual_vectors,
    COALESCE(seg_stats.avg_quality, 0) as avg_quality_score,
    COALESCE(seg_stats.avg_readability, 0) as avg_readability_score
FROM document_segment_tasks t
LEFT JOIN (
    SELECT 
        task_id,
        COUNT(*) as actual_segments,
        COUNT(CASE WHEN vectorize_status = 'completed' THEN 1 END) as actual_vectors,
        AVG(quality_score) as avg_quality,
        AVG(readability_score) as avg_readability
    FROM document_segments 
    GROUP BY task_id
) seg_stats ON t.id = seg_stats.task_id;

-- 4. 创建文件处理统计视图
CREATE OR REPLACE VIEW file_processing_statistics AS
SELECT 
    t.id as task_id,
    t.task_id as task_uuid,
    t.task_name,
    file_id,
    COUNT(s.id) as segments_count,
    COUNT(CASE WHEN s.vectorize_status = 'completed' THEN 1 END) as vectors_count,
    AVG(s.quality_score) as avg_quality,
    AVG(s.readability_score) as avg_readability,
    MIN(s.created_at) as first_segment_at,
    MAX(s.created_at) as last_segment_at
FROM document_segment_tasks t
CROSS JOIN LATERAL unnest(t.file_ids) as file_id
LEFT JOIN document_segments s ON s.task_id = t.id AND s.file_id = file_id
GROUP BY t.id, t.task_id, t.task_name, file_id;

-- 5. 创建系统性能监控表（用于存储Celery性能数据）
CREATE TABLE IF NOT EXISTS celery_performance_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    worker_count INTEGER DEFAULT 0,
    active_tasks INTEGER DEFAULT 0,
    processed_tasks INTEGER DEFAULT 0,
    queue_length INTEGER DEFAULT 0,
    cpu_usage FLOAT DEFAULT 0.0,
    memory_usage FLOAT DEFAULT 0.0,
    tasks_per_minute FLOAT DEFAULT 0.0,
    avg_task_duration FLOAT DEFAULT 0.0,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_celery_performance_logs_timestamp 
ON celery_performance_logs(timestamp DESC);

-- 6. 创建清理过期性能日志的函数
CREATE OR REPLACE FUNCTION cleanup_performance_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除30天前的性能日志
    DELETE FROM celery_performance_logs 
    WHERE timestamp < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 7. 插入一些示例性能数据（可选）
INSERT INTO celery_performance_logs (
    worker_count, active_tasks, processed_tasks, queue_length,
    cpu_usage, memory_usage, tasks_per_minute, avg_task_duration
) VALUES 
(2, 0, 0, 0, 15.5, 45.2, 0, 0),
(2, 1, 5, 2, 25.8, 52.1, 12.5, 8.2),
(2, 0, 12, 0, 18.3, 48.7, 8.7, 6.5)
ON CONFLICT DO NOTHING;

-- 8. 验证表结构
SELECT 
    'document_segment_tasks' as table_name,
    COUNT(*) as record_count,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'document_segment_tasks') as column_count
FROM document_segment_tasks
UNION ALL
SELECT 
    'document_segments' as table_name,
    COUNT(*) as record_count,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'document_segments') as column_count
FROM document_segments
UNION ALL
SELECT 
    'celery_performance_logs' as table_name,
    COUNT(*) as record_count,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'celery_performance_logs') as column_count
FROM celery_performance_logs;

-- 9. 显示创建的视图
SELECT 
    schemaname,
    viewname,
    definition
FROM pg_views 
WHERE viewname IN ('task_statistics', 'file_processing_statistics')
ORDER BY viewname;

-- 完成提示
SELECT '分段任务管理功能数据库修复完成！' as status;
