# 前端依赖修复指南

## 问题描述

遇到 `@tailwindcss/postcss` 包版本错误的问题：
```
ERR_PNPM_NO_MATCHING_VERSION  No matching version found for @tailwindcss/postcss@^3.4.0
```

## 解决方案

### 1. 问题原因
- `@tailwindcss/postcss` 这个包名是错误的
- 正确的配置应该使用 `postcss` 和 `autoprefixer`

### 2. 已修复的内容

#### package.json 修复
- 移除了错误的 `@tailwindcss/postcss` 依赖
- 添加了正确的 `postcss` 和 `autoprefixer` 依赖
- 降级了一些版本以确保兼容性

#### 配置文件创建
- 创建了 `postcss.config.js`
- 创建了 `tailwind.config.js`
- 修复了 `globals.css` 中的 Tailwind 导入

#### 版本调整
- React: 18.2.0 (更稳定的版本)
- Next.js: 14.0.0 (更稳定的版本)
- Tailwind CSS: 3.3.0 (稳定版本)

### 3. 安装步骤

#### 方法1：使用批处理文件（推荐）
```cmd
双击运行: web\fix-dependencies.bat
```

#### 方法2：手动安装
```cmd
cd web

# 清理旧依赖
rmdir /s /q node_modules
del pnpm-lock.yaml

# 重新安装
pnpm install
```

#### 方法3：使用 npm（备选）
```cmd
cd web

# 清理旧依赖
rmdir /s /q node_modules
del package-lock.json

# 重新安装
npm install
```

### 4. 验证安装

安装完成后，运行以下命令验证：

```cmd
cd web
pnpm dev
```

或者

```cmd
cd web
npm run dev
```

如果看到类似以下输出，说明安装成功：
```
- ready started server on 0.0.0.0:3000, url: http://localhost:3000
- event compiled client and server successfully
```

### 5. 配置文件说明

#### postcss.config.js
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

#### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    // ... 其他路径
  ],
  theme: {
    extend: {
      // 自定义主题配置
    },
  },
  plugins: [],
}
```

#### globals.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 其他样式 */
```

### 6. 常见问题

#### 问题1：pnpm 安装失败
**解决方案**：尝试使用 npm
```cmd
npm install
```

#### 问题2：网络连接问题
**解决方案**：使用国内镜像
```cmd
pnpm config set registry https://registry.npmmirror.com/
```

#### 问题3：权限问题
**解决方案**：以管理员身份运行命令提示符

#### 问题4：端口占用
**解决方案**：使用不同端口
```cmd
pnpm dev:3001
```

### 7. 依赖版本说明

| 包名 | 版本 | 说明 |
|------|------|------|
| react | ^18.2.0 | 稳定的React版本 |
| next | ^14.0.0 | 稳定的Next.js版本 |
| tailwindcss | ^3.3.0 | 稳定的Tailwind版本 |
| postcss | ^8.4.31 | PostCSS处理器 |
| autoprefixer | ^10.4.16 | CSS前缀自动添加 |

### 8. 成功标志

安装成功后，您应该能够：
- ✅ 运行 `pnpm dev` 或 `npm run dev`
- ✅ 访问 http://localhost:3000
- ✅ 看到应用正常启动
- ✅ Tailwind CSS 样式正常工作

### 9. 下一步

依赖安装成功后，可以：
1. 启动前端开发服务器
2. 启动后端API服务器
3. 测试真实数据API功能
4. 查看存储概览和系统状态

### 10. 联系支持

如果仍然遇到问题，请：
1. 检查 Node.js 版本（建议 16.0.0+）
2. 检查 pnpm 版本（建议 8.0.0+）
3. 清理所有缓存后重试
4. 查看详细错误日志
