"""
数据库迁移脚本
自动检查和更新数据库表结构
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, inspect, text, MetaData, Table, Column
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings
from app.core.database import Base, get_db
from app.models.base import BaseModel

# 导入所有模型以确保它们被注册
from app.models.user import User
from app.models.file_management import StorageConfig
from app.models.storage_stats import StorageStats, StorageStatsHistory


class DatabaseMigrator:
    """数据库迁移器"""
    
    def __init__(self):
        self.engine = create_engine(settings.DATABASE_URL)
        self.inspector = inspect(self.engine)
        self.metadata = MetaData()
        
    def get_existing_tables(self):
        """获取现有表列表"""
        return self.inspector.get_table_names()
    
    def get_table_columns(self, table_name):
        """获取表的列信息"""
        try:
            return self.inspector.get_columns(table_name)
        except Exception:
            return []
    
    def table_exists(self, table_name):
        """检查表是否存在"""
        return table_name in self.get_existing_tables()
    
    def column_exists(self, table_name, column_name):
        """检查列是否存在"""
        if not self.table_exists(table_name):
            return False
        
        columns = self.get_table_columns(table_name)
        return any(col['name'] == column_name for col in columns)
    
    def add_missing_columns(self):
        """添加缺失的列"""
        print("检查并添加缺失的列...")
        
        # 检查storage表是否需要添加stats关系
        if self.table_exists('storages'):
            print("✓ storages表已存在")
        
        # 检查storage_stats表
        if not self.table_exists('storage_stats'):
            print("✗ storage_stats表不存在，将创建")
        else:
            print("✓ storage_stats表已存在")
            
        # 检查storage_stats_history表
        if not self.table_exists('storage_stats_history'):
            print("✗ storage_stats_history表不存在，将创建")
        else:
            print("✓ storage_stats_history表已存在")
    
    def create_missing_tables(self):
        """创建缺失的表"""
        print("创建缺失的表...")
        
        try:
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            print("✓ 所有表创建完成")
            
        except SQLAlchemyError as e:
            print(f"✗ 创建表失败: {e}")
            raise
    
    def update_storage_model(self):
        """更新Storage模型以支持stats关系"""
        print("更新Storage模型...")
        
        # 检查是否需要添加外键约束
        try:
            with self.engine.connect() as conn:
                # 检查外键约束是否存在
                if self.table_exists('storage_stats'):
                    print("✓ storage_stats表外键关系已建立")
                    
        except Exception as e:
            print(f"更新Storage模型时出错: {e}")
    
    def add_indexes(self):
        """添加索引"""
        print("添加数据库索引...")
        
        indexes_to_add = [
            ("storage_stats", "storage_id"),
            ("storage_stats", "stats_date"),
            ("storage_stats_history", "storage_id"),
            ("storage_stats_history", "stats_date"),
        ]
        
        with self.engine.connect() as conn:
            for table_name, column_name in indexes_to_add:
                if self.table_exists(table_name):
                    try:
                        index_name = f"idx_{table_name}_{column_name}"
                        # 检查索引是否已存在
                        existing_indexes = self.inspector.get_indexes(table_name)
                        index_exists = any(idx['name'] == index_name for idx in existing_indexes)
                        
                        if not index_exists:
                            conn.execute(text(f"CREATE INDEX {index_name} ON {table_name} ({column_name})"))
                            print(f"✓ 添加索引: {index_name}")
                        else:
                            print(f"✓ 索引已存在: {index_name}")
                            
                    except Exception as e:
                        print(f"✗ 添加索引失败 {table_name}.{column_name}: {e}")
            
            conn.commit()
    
    def migrate(self):
        """执行迁移"""
        print("开始数据库迁移...")
        print(f"数据库URL: {settings.DATABASE_URL}")
        
        try:
            # 1. 检查现有表结构
            existing_tables = self.get_existing_tables()
            print(f"现有表: {existing_tables}")
            
            # 2. 添加缺失的列
            self.add_missing_columns()
            
            # 3. 创建缺失的表
            self.create_missing_tables()
            
            # 4. 更新模型关系
            self.update_storage_model()
            
            # 5. 添加索引
            self.add_indexes()
            
            print("✓ 数据库迁移完成")
            
        except Exception as e:
            print(f"✗ 数据库迁移失败: {e}")
            raise
    
    def verify_migration(self):
        """验证迁移结果"""
        print("验证迁移结果...")
        
        required_tables = [
            'users',
            'storages', 
            'files',
            'storage_stats',
            'storage_stats_history'
        ]
        
        missing_tables = []
        for table in required_tables:
            if not self.table_exists(table):
                missing_tables.append(table)
        
        if missing_tables:
            print(f"✗ 缺失表: {missing_tables}")
            return False
        else:
            print("✓ 所有必需的表都存在")
            
        # 验证storage_stats表结构
        if self.table_exists('storage_stats'):
            columns = self.get_table_columns('storage_stats')
            required_columns = [
                'id', 'storage_id', 'stats_date', 'total_files', 'total_folders',
                'total_size', 'used_size', 'available_size', 'health_score', 'is_healthy'
            ]
            
            existing_column_names = [col['name'] for col in columns]
            missing_columns = [col for col in required_columns if col not in existing_column_names]
            
            if missing_columns:
                print(f"✗ storage_stats表缺失列: {missing_columns}")
                return False
            else:
                print("✓ storage_stats表结构正确")
        
        return True


def main():
    """主函数"""
    print("=" * 50)
    print("数据库迁移工具")
    print("=" * 50)
    
    try:
        migrator = DatabaseMigrator()
        
        # 执行迁移
        migrator.migrate()
        
        # 验证迁移
        if migrator.verify_migration():
            print("\n✓ 数据库迁移成功完成！")
            return 0
        else:
            print("\n✗ 数据库迁移验证失败！")
            return 1
            
    except Exception as e:
        print(f"\n✗ 数据库迁移失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
