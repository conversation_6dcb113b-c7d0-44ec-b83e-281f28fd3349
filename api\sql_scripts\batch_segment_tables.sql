-- 批量分段功能相关表结构
-- 手动执行此SQL脚本来创建或更新表结构

-- 文档分段任务表
CREATE TABLE IF NOT EXISTS document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    config JSONB,
    total_files INTEGER DEFAULT 0,
    completed_files INTEGER DEFAULT 0,
    failed_files INTEGER DEFAULT 0,
    processing_files INTEGER DEFAULT 0,
    pending_files INTEGER DEFAULT 0,
    total_segments INTEGER DEFAULT 0
);

-- 文档分段任务文件关联表
CREATE TABLE IF NOT EXISTS document_segment_task_files (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    file_id VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000),
    file_name VARCHAR(255),
    file_size BIGINT DEFAULT 0,
    file_extension VARCHAR(50),
    storage_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    segments_count INTEGER DEFAULT 0,
    error_message TEXT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文档分段结果表
CREATE TABLE IF NOT EXISTS document_segments (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    file_id VARCHAR(500) NOT NULL,
    segment_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    start_position INTEGER,
    end_position INTEGER,
    word_count INTEGER,
    keywords TEXT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分段统计表（用于实时统计）
CREATE TABLE IF NOT EXISTS document_segment_statistics (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    total_files INTEGER DEFAULT 0,
    pending_files INTEGER DEFAULT 0,
    processing_files INTEGER DEFAULT 0,
    completed_files INTEGER DEFAULT 0,
    failed_files INTEGER DEFAULT 0,
    total_segments INTEGER DEFAULT 0,
    total_words INTEGER DEFAULT 0,
    average_segments_per_file DECIMAL(10,2) DEFAULT 0,
    processing_time_seconds INTEGER DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_created_by ON document_segment_tasks(created_by);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_created_at ON document_segment_tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_document_segment_task_files_task_id ON document_segment_task_files(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_task_files_status ON document_segment_task_files(status);
CREATE INDEX IF NOT EXISTS idx_document_segment_task_files_file_id ON document_segment_task_files(file_id);

CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_segment_index ON document_segments(segment_index);

CREATE INDEX IF NOT EXISTS idx_document_segment_statistics_task_id ON document_segment_statistics(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_statistics_updated_at ON document_segment_statistics(updated_at);

-- 添加注释
COMMENT ON TABLE document_segment_tasks IS '文档分段任务表';
COMMENT ON TABLE document_segment_task_files IS '文档分段任务文件关联表';
COMMENT ON TABLE document_segments IS '文档分段结果表';
COMMENT ON TABLE document_segment_statistics IS '分段统计表，用于实时统计显示';

COMMENT ON COLUMN document_segment_tasks.processing_files IS '正在处理的文件数量';
COMMENT ON COLUMN document_segment_tasks.pending_files IS '等待处理的文件数量';
COMMENT ON COLUMN document_segment_tasks.total_segments IS '总分段数量';

COMMENT ON COLUMN document_segment_task_files.file_size IS '文件大小（字节）';
COMMENT ON COLUMN document_segment_task_files.file_extension IS '文件扩展名';
COMMENT ON COLUMN document_segment_task_files.storage_type IS '存储类型';
COMMENT ON COLUMN document_segment_task_files.started_at IS '开始处理时间';
COMMENT ON COLUMN document_segment_task_files.completed_at IS '完成处理时间';

COMMENT ON COLUMN document_segment_statistics.total_words IS '总词数';
COMMENT ON COLUMN document_segment_statistics.average_segments_per_file IS '平均每个文件的分段数';
COMMENT ON COLUMN document_segment_statistics.processing_time_seconds IS '处理耗时（秒）';

-- 示例数据（可选）
-- INSERT INTO document_segment_tasks (task_name, description, status, created_by, config)
-- VALUES ('测试批量分段任务', '测试用的批量分段任务', 'pending', 1, '{"method": "paragraph", "max_length": 500}');
