'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { BarChart3, TrendingUp, PieChart, Activity } from 'lucide-react';
import MainLayout from '@/components/Layout/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';

const AnalyticsPage: React.FC = () => {
  const { t, isLoading: langLoading } = useLanguage();

  if (langLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <BarChart3 className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t?.navigation?.analytics || '数据分析'}
          </h1>
          <p className="text-gray-600 mb-8">
            深入了解您的数据使用情况和系统性能
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">使用趋势</h3>
              <p className="text-gray-600 text-sm">分析系统使用趋势和增长模式</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <PieChart className="w-8 h-8 text-blue-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">数据分布</h3>
              <p className="text-gray-600 text-sm">查看文档类型和用户行为分布</p>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-200"
            >
              <Activity className="w-8 h-8 text-purple-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">实时监控</h3>
              <p className="text-gray-600 text-sm">实时监控系统性能和用户活动</p>
            </motion.div>
          </div>
          
          <div className="mt-12 text-gray-500">
            <p>数据分析功能正在开发中，敬请期待...</p>
          </div>
        </motion.div>
        </div>
      </div>
    </MainLayout>
  );
};

export default AnalyticsPage;
