"""
文档分段任务模块
使用Celery管理文档分段任务队列，与文件上传任务分离
"""

import hashlib
import uuid
from datetime import datetime
from typing import List, Dict, Any
from celery import current_task
from loguru import logger

from app.core.celery_config import celery_app
from app.core.database import get_sync_session
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from app.models.document_segment import (
    DocumentSegmentTask, 
    DocumentSegment, 
    SegmentStatus, 
    VectorizeStatus
)


def get_db_session():
    """获取数据库会话的辅助函数"""
    try:
        return next(get_sync_session())
    except Exception as e:
        logger.error(f"获取数据库会话失败: {e}")
        raise


@celery_app.task(bind=True, queue='segment_queue')
def process_batch_segment_task(self, task_id: int):
    """
    处理批量分段任务
    每个批量任务包含多个文件，每个文件创建独立的子任务
    """
    db = get_db_session()

    try:
        # 获取任务记录
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.id == task_id
        ).first()
        
        if not task:
            logger.error(f"任务不存在: {task_id}")
            return {"status": "error", "message": "任务不存在"}
        
        logger.info(f"开始处理批量分段任务: {task.task_name} (ID: {task_id})")
        
        # 更新任务状态
        task.status = SegmentStatus.PROCESSING
        task.started_at = datetime.utcnow()
        db.commit()
        
        # 为每个文件创建独立的子任务
        file_tasks = []
        for i, file_id in enumerate(task.file_ids):
            # 创建文件分段子任务
            subtask = process_single_file_segment.delay(task_id, file_id, i)
            file_tasks.append({
                "file_id": file_id,
                "task_id": subtask.id,
                "index": i
            })
            logger.info(f"为文件 {file_id} 创建子任务: {subtask.id}")
        
        # 更新任务元数据，记录子任务信息
        task.segment_metadata = {
            "file_tasks": file_tasks,
            "total_files": len(task.file_ids),
            "created_at": datetime.utcnow().isoformat()
        }
        db.commit()
        
        # 监控所有子任务完成情况
        monitor_subtasks.delay(task_id, [ft["task_id"] for ft in file_tasks])
        
        return {
            "status": "processing", 
            "task_id": task.task_id,
            "file_tasks": file_tasks
        }
        
    except Exception as e:
        logger.error(f"批量分段任务失败: {e}")
        if 'task' in locals() and task:
            task.status = SegmentStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            db.commit()
        return {"status": "error", "message": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True, queue='segment_queue')
def process_single_file_segment(self, task_id: int, file_id: str, file_index: int):
    """
    处理单个文件的分段任务
    """
    db = get_db_session()
    
    try:
        # 获取主任务
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.id == task_id
        ).first()
        
        if not task:
            logger.error(f"主任务不存在: {task_id}")
            return {"status": "error", "message": "主任务不存在"}
        
        logger.info(f"开始处理文件分段: {file_id} (任务: {task_id})")
        
        # 更新任务进度
        current_task.update_state(
            state='PROGRESS',
            meta={
                'file_id': file_id,
                'status': 'processing',
                'progress': 0
            }
        )
        
        # 模拟获取文件内容和分段处理
        # 在实际实现中，这里会调用真实的文档解析和分段逻辑
        segments_data = simulate_document_segmentation(
            file_id=file_id,
            task_config=task,
            progress_callback=lambda p: current_task.update_state(
                state='PROGRESS',
                meta={
                    'file_id': file_id,
                    'status': 'processing',
                    'progress': p
                }
            )
        )
        
        # 存储分段数据到数据库
        segments_created = 0
        for i, segment_data in enumerate(segments_data):
            content_hash = hashlib.sha256(segment_data['content'].encode('utf-8')).hexdigest()
            
            segment = DocumentSegment(
                task_id=task.id,
                file_id=file_id,
                content=segment_data['content'],
                content_hash=content_hash,
                segment_index=i,
                start_position=segment_data.get('start_position', i * 100),
                end_position=segment_data.get('end_position', (i + 1) * 100),
                word_count=len(segment_data['content']),
                sentence_count=segment_data.get('sentence_count', 1),
                vectorize_status=VectorizeStatus.PENDING,
                quality_score=segment_data.get('quality_score', 0.8),
                readability_score=segment_data.get('readability_score', 0.7),
                keywords=segment_data.get('keywords', []) if task.extract_keywords else None
            )
            
            db.add(segment)
            segments_created += 1
            
            # 每10个分段提交一次
            if segments_created % 10 == 0:
                db.commit()
                current_task.update_state(
                    state='PROGRESS',
                    meta={
                        'file_id': file_id,
                        'status': 'processing',
                        'progress': int((i + 1) / len(segments_data) * 80)  # 80%用于分段，20%用于向量化
                    }
                )
        
        # 最终提交
        db.commit()
        
        # 向量化处理（如果启用）
        if task.enable_vectorization:
            vectorize_file_segments.delay(task_id, file_id)
        
        # 更新完成状态
        current_task.update_state(
            state='SUCCESS',
            meta={
                'file_id': file_id,
                'status': 'completed',
                'progress': 100,
                'segments_count': segments_created
            }
        )
        
        logger.info(f"文件分段完成: {file_id}, 创建 {segments_created} 个分段")
        
        return {
            "status": "completed",
            "file_id": file_id,
            "segments_count": segments_created
        }
        
    except Exception as e:
        logger.error(f"文件分段失败: {file_id}, 错误: {e}")
        current_task.update_state(
            state='FAILURE',
            meta={
                'file_id': file_id,
                'status': 'error',
                'error': str(e)
            }
        )
        return {"status": "error", "file_id": file_id, "message": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True, queue='segment_queue')
def vectorize_file_segments(self, task_id: int, file_id: str):
    """
    对文件的分段进行向量化处理
    """
    db = get_db_session()
    
    try:
        logger.info(f"开始向量化处理: 文件 {file_id} (任务: {task_id})")
        
        # 获取待向量化的分段
        segments = db.query(DocumentSegment).filter(
            DocumentSegment.task_id == task_id,
            DocumentSegment.file_id == file_id,
            DocumentSegment.vectorize_status == VectorizeStatus.PENDING
        ).all()
        
        for i, segment in enumerate(segments):
            # 模拟向量化处理
            # 在实际实现中，这里会调用真实的向量化服务
            embedding_vector = simulate_vectorization(segment.content)
            
            segment.vectorize_status = VectorizeStatus.COMPLETED
            segment.embedding_vector = embedding_vector
            segment.vector_id = f"vec_{segment.segment_id}"
            
            # 更新进度
            current_task.update_state(
                state='PROGRESS',
                meta={
                    'file_id': file_id,
                    'status': 'vectorizing',
                    'progress': int((i + 1) / len(segments) * 100)
                }
            )
        
        db.commit()
        
        logger.info(f"向量化完成: 文件 {file_id}, 处理 {len(segments)} 个分段")
        
        return {
            "status": "completed",
            "file_id": file_id,
            "vectorized_count": len(segments)
        }
        
    except Exception as e:
        logger.error(f"向量化失败: {file_id}, 错误: {e}")
        return {"status": "error", "file_id": file_id, "message": str(e)}
    finally:
        db.close()


@celery_app.task(bind=True, queue='segment_queue')
def monitor_subtasks(self, main_task_id: int, subtask_ids: List[str]):
    """
    监控所有子任务的完成情况，更新主任务状态
    """
    db = get_db_session()
    
    try:
        # 检查所有子任务状态
        completed_tasks = 0
        failed_tasks = 0
        total_segments = 0
        
        for subtask_id in subtask_ids:
            result = celery_app.AsyncResult(subtask_id)
            if result.state == 'SUCCESS':
                completed_tasks += 1
                if result.result and 'segments_count' in result.result:
                    total_segments += result.result['segments_count']
            elif result.state == 'FAILURE':
                failed_tasks += 1
        
        # 更新主任务状态
        main_task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.id == main_task_id
        ).first()
        
        if main_task:
            main_task.processed_files = completed_tasks
            main_task.total_segments = total_segments
            main_task.progress = (completed_tasks / len(subtask_ids)) * 100
            
            # 如果所有子任务完成
            if completed_tasks + failed_tasks == len(subtask_ids):
                if failed_tasks == 0:
                    main_task.status = SegmentStatus.COMPLETED
                else:
                    main_task.status = SegmentStatus.FAILED
                    main_task.error_message = f"{failed_tasks} 个文件处理失败"
                
                main_task.completed_at = datetime.utcnow()
            
            db.commit()
        
        logger.info(f"主任务 {main_task_id} 进度更新: {completed_tasks}/{len(subtask_ids)} 完成")
        
        return {
            "main_task_id": main_task_id,
            "completed": completed_tasks,
            "failed": failed_tasks,
            "total": len(subtask_ids)
        }
        
    except Exception as e:
        logger.error(f"监控子任务失败: {e}")
        return {"status": "error", "message": str(e)}
    finally:
        db.close()


def simulate_document_segmentation(file_id: str, task_config: DocumentSegmentTask, progress_callback=None) -> List[Dict[str, Any]]:
    """
    模拟文档分段处理
    在实际实现中，这里会调用真实的文档解析和分段逻辑
    """
    import time
    import random
    
    # 模拟不同文件的分段数量
    segments_count = random.randint(5, 20)
    segments = []
    
    for i in range(segments_count):
        # 模拟处理时间
        time.sleep(0.1)
        
        segment_data = {
            'content': f'这是文件 {file_id} 的第 {i+1} 个分段内容。' + 
                      f'分段方法: {task_config.segment_method}, ' +
                      f'最大长度: {task_config.max_length}。' +
                      '这里包含了文档的实际内容，经过智能分段算法处理后得到的语义完整的文本片段。',
            'start_position': i * task_config.max_length,
            'end_position': (i + 1) * task_config.max_length,
            'sentence_count': random.randint(1, 5),
            'quality_score': random.uniform(0.7, 0.95),
            'readability_score': random.uniform(0.6, 0.9),
            'keywords': [f'关键词{j}' for j in range(random.randint(2, 5))] if task_config.extract_keywords else []
        }
        
        segments.append(segment_data)
        
        # 更新进度
        if progress_callback:
            progress_callback(int((i + 1) / segments_count * 80))
    
    return segments


def simulate_vectorization(content: str) -> List[float]:
    """
    模拟向量化处理
    在实际实现中，这里会调用真实的向量化服务
    """
    import random
    
    # 模拟1536维向量（OpenAI text-embedding-ada-002的维度）
    return [random.uniform(-1, 1) for _ in range(1536)]
