"""
测试登录功能
验证登录相关的数据库操作是否正常
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_login_functionality():
    """测试登录功能"""
    try:
        print("测试登录功能...")
        
        # 导入必要的模块
        from app.core.database import get_db
        from app.models.user import User
        from app.repositories.user import UserRepository
        from app.core.security import verify_password
        
        print("✓ 模块导入成功")
        
        # 获取数据库会话
        db = next(get_db())
        user_repo = UserRepository()
        
        try:
            # 测试用户查询（这是登录时会执行的操作）
            print("测试用户查询...")
            user = user_repo.get_by_username_or_email(db, "admin")
            
            if user:
                print(f"✓ 找到用户: {user.username}")
                
                # 测试密码验证
                print("测试密码验证...")
                if verify_password("password", user.hashed_password):
                    print("✓ 密码验证成功")
                else:
                    print("✗ 密码验证失败")
                    
            else:
                print("✗ 未找到用户")
                
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 登录功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("登录功能测试")
    print("=" * 50)
    
    if test_login_functionality():
        print("\n🎉 登录功能测试通过！")
        return 0
    else:
        print("\n❌ 登录功能测试失败！")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
