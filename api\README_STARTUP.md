# AI知识库后端启动指南

## 🚀 快速启动

### 方式一：使用快速启动脚本（推荐）

```bash
cd api
python quick_start.py
```

这个脚本会自动：
- ✅ 检查并安装必要的依赖
- ✅ 创建环境配置文件
- ✅ 测试数据库和Redis连接
- ✅ 初始化数据库表（如果需要）
- ✅ 启动开发服务器

### 方式二：手动启动

```bash
cd api

# 1. 安装依赖
pip install fastapi uvicorn sqlalchemy asyncpg redis aioredis loguru pydantic

# 2. 初始化数据库（首次运行）
python init_database.py

# 3. 启动服务器
uv run uvicorn main:app --host 127.0.0.1 --port 8000 --reload
```

### 方式三：Windows批处理启动

```bash
cd api
start_manual.bat
```

这种方式可以避免日志文件冲突问题。

## 🔧 环境配置

### 环境变量配置

系统使用环境变量进行配置，支持变量替换。主要配置项：

```bash
# PostgreSQL 数据库配置
DB_USERNAME=postgres
DB_PASSWORD=XHC12345
DB_HOST=**************
DB_PORT=5432
DB_DATABASE=xhc_rag
DATABASE_URL=postgresql+asyncpg://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}

# Redis 缓存配置
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_PASSWORD=XHC12345
REDIS_DB=10
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}
```

### 环境变量验证

运行环境检查脚本验证配置：

```bash
cd api
python check_env.py
```

## 📁 配置文件

- `.env.local` - 开发环境配置（自动生成）
- `.env.test` - 测试环境配置
- `.env.production` - 生产环境配置
- `.env.example` - 配置模板

配置文件加载优先级：`.env.local` > `.env` > `.env.example`

### 环境变量替换

支持在配置文件中使用 `${VARIABLE_NAME}` 格式引用其他环境变量：

```bash
# 分离的配置
DB_USERNAME=postgres
DB_PASSWORD=XHC12345
DB_HOST=**************

# 使用变量替换
DATABASE_URL=postgresql+asyncpg://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:5432/xhc_rag
```

## 🌐 服务地址

启动成功后，可以访问：

- **API 服务**: http://127.0.0.1:8000
- **API 文档**: http://127.0.0.1:8000/docs
- **健康检查**: http://127.0.0.1:8000/health
- **文件管理**: http://localhost:3000/file-manager

## 🔍 故障排除

### 数据库连接失败
```bash
# 检查PostgreSQL服务状态
# 确认数据库服务器可访问
ping **************

# 测试数据库连接
psql -h ************** -p 5432 -U postgres -d xhc_rag
```

### Redis连接失败
```bash
# 检查Redis服务状态
redis-cli -h ************** -p 6379 -a XHC12345 ping
```

### 端口占用
```bash
# 检查端口8000是否被占用
netstat -an | grep 8000

# 或使用其他端口启动
uvicorn main:app --host 127.0.0.1 --port 8001 --reload
```

### 依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ fastapi uvicorn sqlalchemy asyncpg redis aioredis
```

### 日志文件权限错误
```bash
# 如果遇到日志文件权限错误，尝试：
# 1. 重启终端/IDE
# 2. 使用Windows批处理启动
start_manual.bat

# 3. 或手动启动
python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 4. 删除logs目录（如果需要）
rmdir /s logs  # Windows
rm -rf logs    # Linux/Mac
```

## 📊 功能模块

启动后可使用以下功能：

- ✅ **用户认证**: 登录/注册/权限管理
- ✅ **文件管理**: 多存储后端支持（本地/MinIO/FTP/SFTP）
- ✅ **存储管理**: 存储配置的增删改查
- ✅ **数据缓存**: Redis缓存优化
- ✅ **API文档**: 自动生成的Swagger文档

## 🔄 开发模式

开发模式下的特性：
- 🔄 **热重载**: 代码修改自动重启
- 📝 **详细日志**: DEBUG级别日志输出
- 🔍 **SQL调试**: 显示SQL查询语句
- 🌐 **CORS开放**: 允许跨域请求

## 🚀 生产部署

生产环境部署建议：

```bash
# 使用生产配置
cp .env.production .env.local

# 使用Gunicorn启动
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 或使用Docker
docker build -t ai-knowledge-api .
docker run -p 8000:8000 ai-knowledge-api
```

## 📞 技术支持

如遇问题，请检查：
1. Python版本 >= 3.9
2. 数据库服务正常运行
3. 网络连接正常
4. 配置文件正确

更多信息请参考项目文档或联系技术支持。
