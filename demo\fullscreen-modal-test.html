<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏弹窗测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-button {
            padding: 20px 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        /* 全屏弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
            overflow-y: auto;
        }

        .modal-content {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: transparent;
            padding: 0;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .modal-header-left {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .modal-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 30px;
            color: white;
            flex-shrink: 0;
        }

        .modal-title-section {
            flex: 1;
            min-width: 0;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .modal-meta {
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.2;
        }

        .modal-company {
            font-weight: 600;
            color: #6366f1;
        }

        .modal-creator {
            color: #8b5cf6;
            font-weight: 500;
        }

        .modal-body {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        .modal-description {
            font-size: 1.1rem;
            color: #4b5563;
            line-height: 1.7;
            margin-bottom: 40px;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn {
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            min-width: 120px;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #6b7280;
            border: 2px solid #e5e7eb;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #d1d5db;
        }

        .close {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .close:hover {
            background: rgba(255, 255, 255, 1);
            color: #374151;
            transform: scale(1.1);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .modal-header {
                padding: 15px 20px;
            }
            
            .modal-header-left {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .modal-icon {
                width: 50px;
                height: 50px;
                font-size: 24px;
                margin-right: 0;
                margin-bottom: 12px;
                align-self: center;
            }
            
            .modal-title-section {
                text-align: center;
                width: 100%;
            }
            
            .modal-title {
                font-size: 1.3rem;
            }
            
            .modal-meta {
                font-size: 0.8rem;
            }
            
            .modal-body {
                padding: 20px;
            }
            
            .modal-description {
                font-size: 1rem;
                padding: 20px;
                margin-bottom: 30px;
            }
            
            .modal-actions {
                flex-direction: column;
                gap: 12px;
                padding: 0 20px 20px;
            }
            
            .btn {
                width: 100%;
                padding: 16px 20px;
                font-size: 1rem;
            }
            
            .close {
                width: 36px;
                height: 36px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <button class="test-button" onclick="openModal()">
        🤖 点击测试全屏弹窗
    </button>

    <!-- 全屏弹窗 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-header-left">
                    <div class="modal-icon">🤖</div>
                    <div class="modal-title-section">
                        <div class="modal-title">ChatGPT</div>
                        <div class="modal-meta">
                            <span class="modal-company">OpenAI</span>
                            <span> • </span>
                            <span class="modal-creator">Sam Altman</span>
                        </div>
                    </div>
                </div>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="modal-description">
                    OpenAI开发的强大对话AI，支持文本生成、问答、编程等多种任务，是目前最受欢迎的AI助手之一。它能够理解上下文，进行深度对话，协助完成写作、编程、分析等各种复杂任务。通过不断的训练和优化，ChatGPT在自然语言处理方面达到了前所未有的水平，为用户提供了智能、高效的AI助手体验。
                </div>
                <div class="modal-actions">
                    <button class="btn btn-secondary" onclick="closeModal()">返回</button>
                    <a class="btn btn-primary" href="https://chat.openai.com" target="_blank">访问工具</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openModal() {
            const modal = document.getElementById('modal');
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // 点击弹窗外部关闭（可选）
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
