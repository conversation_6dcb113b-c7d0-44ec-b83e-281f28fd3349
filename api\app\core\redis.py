"""
Redis配置和连接管理
"""

import json
import pickle
from typing import Any, Optional, Union
from datetime import timedelta

import redis.asyncio as redis
from redis.asyncio import Redis
from app.core.config import settings
from loguru import logger


class RedisManager:
    """Redis连接管理器"""
    
    def __init__(self):
        self._redis: Optional[Redis] = None
        self._connection_pool = None
    
    async def connect(self) -> Redis:
        """连接到Redis"""
        if self._redis is None:
            try:
                # 创建连接池
                self._connection_pool = redis.ConnectionPool.from_url(
                    settings.redis_url,  # 使用动态生成的URL
                    encoding="utf-8",
                    decode_responses=False,  # 保持二进制数据
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                    health_check_interval=30
                )
                
                # 创建Redis客户端
                self._redis = redis.Redis(
                    connection_pool=self._connection_pool,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
                
                # 测试连接
                await self._redis.ping()
                logger.info("Redis连接成功")
                
            except Exception as e:
                logger.error(f"Redis连接失败: {e}")
                raise
        
        return self._redis
    
    async def disconnect(self):
        """断开Redis连接"""
        if self._redis:
            await self._redis.close()
            self._redis = None
        
        if self._connection_pool:
            await self._connection_pool.disconnect()
            self._connection_pool = None
        
        logger.info("Redis连接已断开")
    
    async def get_redis(self) -> Redis:
        """获取Redis客户端"""
        if self._redis is None:
            await self.connect()
        return self._redis


# 全局Redis管理器实例
redis_manager = RedisManager()


class RedisCache:
    """Redis缓存操作类"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        try:
            value = await self.redis.get(key)
            if value is None:
                return default
            
            # 尝试反序列化
            try:
                return pickle.loads(value)
            except (pickle.PickleError, TypeError):
                # 如果不是pickle序列化的，尝试JSON
                try:
                    return json.loads(value.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # 返回原始字符串
                    return value.decode('utf-8')
        except Exception as e:
            logger.error(f"Redis获取缓存失败 {key}: {e}")
            return default
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """设置缓存值"""
        try:
            # 序列化值
            if isinstance(value, (str, int, float, bool)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = pickle.dumps(value)
            
            # 设置过期时间
            if isinstance(expire, timedelta):
                expire = int(expire.total_seconds())
            
            await self.redis.set(key, serialized_value, ex=expire)
            return True
        except Exception as e:
            logger.error(f"Redis设置缓存失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis检查键存在失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置键的过期时间"""
        try:
            return await self.redis.expire(key, seconds)
        except Exception as e:
            logger.error(f"Redis设置过期时间失败 {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取键的剩余生存时间"""
        try:
            return await self.redis.ttl(key)
        except Exception as e:
            logger.error(f"Redis获取TTL失败 {key}: {e}")
            return -1
    
    async def keys(self, pattern: str = "*") -> list:
        """获取匹配模式的所有键"""
        try:
            keys = await self.redis.keys(pattern)
            return [key.decode('utf-8') if isinstance(key, bytes) else key for key in keys]
        except Exception as e:
            logger.error(f"Redis获取键列表失败 {pattern}: {e}")
            return []
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的所有键"""
        try:
            keys = await self.redis.keys(pattern)
            if keys:
                return await self.redis.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Redis清除模式键失败 {pattern}: {e}")
            return 0
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """递增计数器"""
        try:
            return await self.redis.incrby(key, amount)
        except Exception as e:
            logger.error(f"Redis递增失败 {key}: {e}")
            return 0
    
    async def hash_get(self, name: str, key: str, default: Any = None) -> Any:
        """获取哈希表字段值"""
        try:
            value = await self.redis.hget(name, key)
            if value is None:
                return default
            
            try:
                return pickle.loads(value)
            except (pickle.PickleError, TypeError):
                try:
                    return json.loads(value.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return value.decode('utf-8')
        except Exception as e:
            logger.error(f"Redis哈希获取失败 {name}.{key}: {e}")
            return default
    
    async def hash_set(self, name: str, key: str, value: Any) -> bool:
        """设置哈希表字段值"""
        try:
            if isinstance(value, (str, int, float, bool)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = pickle.dumps(value)
            
            await self.redis.hset(name, key, serialized_value)
            return True
        except Exception as e:
            logger.error(f"Redis哈希设置失败 {name}.{key}: {e}")
            return False
    
    async def hash_delete(self, name: str, key: str) -> bool:
        """删除哈希表字段"""
        try:
            result = await self.redis.hdel(name, key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis哈希删除失败 {name}.{key}: {e}")
            return False
    
    async def hash_get_all(self, name: str) -> dict:
        """获取哈希表所有字段"""
        try:
            data = await self.redis.hgetall(name)
            result = {}
            for key, value in data.items():
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                try:
                    result[key_str] = pickle.loads(value)
                except (pickle.PickleError, TypeError):
                    try:
                        result[key_str] = json.loads(value.decode('utf-8'))
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        result[key_str] = value.decode('utf-8')
            return result
        except Exception as e:
            logger.error(f"Redis哈希获取全部失败 {name}: {e}")
            return {}


async def get_redis_cache() -> RedisCache:
    """获取Redis缓存实例"""
    redis_client = await redis_manager.get_redis()
    return RedisCache(redis_client)


# 缓存键前缀
CACHE_PREFIX = "ai_knowledge:"

def make_cache_key(*parts: str) -> str:
    """生成缓存键"""
    return CACHE_PREFIX + ":".join(str(part) for part in parts)
