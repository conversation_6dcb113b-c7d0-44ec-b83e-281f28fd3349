'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, Scissors, Brain, Zap, Loader2, CheckCircle, 
  AlertCircle, FileText, Settings, Plus, X, Search, FolderOpen
} from 'lucide-react';

// 测试数据
const mockFiles = [
  {
    file_id: 'file_1',
    file_name: '深圳市政务云服务申请表-民生警务平台汇总20250605.docx',
    file_size: 1024000,
    file_size_formatted: '1.0 MB',
    file_extension: 'docx',
    storage_type: 'local',
    created_at: '2025-06-16T14:30:00Z'
  },
  {
    file_id: 'file_2', 
    file_name: '技术文档说明书v2.1.pdf',
    file_size: 2048000,
    file_size_formatted: '2.0 MB',
    file_extension: 'pdf',
    storage_type: 'minio',
    created_at: '2025-06-15T10:20:00Z'
  },
  {
    file_id: 'file_3',
    file_name: '数据分析报告.xlsx',
    file_size: 512000,
    file_size_formatted: '512 KB',
    file_extension: 'xlsx',
    storage_type: 'local',
    created_at: '2025-06-14T16:45:00Z'
  }
];

const TestNewSegmentPage: React.FC = () => {
  const [files, setFiles] = useState(mockFiles);
  const [taskName, setTaskName] = useState('批量分段任务 - 2025/6/16 22:46:35');
  const [taskStatus, setTaskStatus] = useState<'idle' | 'running' | 'completed'>('idle');
  const [showFileSelector, setShowFileSelector] = useState(false);
  
  const [config, setConfig] = useState({
    method: 'paragraph',
    max_length: 500,
    overlap: 50,
    preserve_formatting: true,
    normalize_text: true,
    extract_keywords: true,
    remove_stopwords: false,
    language: 'zh'
  });

  const fileProgress = {
    'file_1': { status: 'completed', progress: 100, segments: 15 },
    'file_2': { status: 'processing', progress: 60, segments: 8 },
    'file_3': { status: 'pending', progress: 0, segments: 0 }
  };

  const removeFile = (fileId: string) => {
    setFiles(files.filter(f => f.file_id !== fileId));
  };

  const startTask = () => {
    setTaskStatus('running');
    // 模拟任务完成
    setTimeout(() => {
      setTaskStatus('completed');
    }, 3000);
  };

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex flex-col">
      {/* 头部导航 */}
      <div className="bg-white/60 backdrop-blur-lg shadow-sm border-b border-white/30 flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200">
                <ArrowLeft className="w-5 h-5" />
              </button>

              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Scissors className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    AI智能批量分段
                  </h1>
                  <p className="text-sm text-gray-500 flex items-center space-x-1">
                    <Brain className="w-3 h-3 text-purple-500" />
                    <span>{files.length} 个文件待处理</span>
                    {taskStatus === 'running' && (
                      <>
                        <span>•</span>
                        <span className="text-blue-600 font-medium">处理中...</span>
                      </>
                    )}
                    {taskStatus === 'completed' && (
                      <>
                        <span>•</span>
                        <span className="text-green-600 font-medium">已完成</span>
                      </>
                    )}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {taskStatus === 'running' && (
                <div className="flex items-center space-x-2 px-4 py-2 bg-blue-100/50 rounded-xl">
                  <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                  <span className="text-sm font-medium text-blue-700">AI处理中</span>
                </div>
              )}
              
              {taskStatus === 'completed' && (
                <div className="flex items-center space-x-2 px-4 py-2 bg-green-100/50 rounded-xl">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-700">处理完成</span>
                </div>
              )}

              {taskStatus === 'idle' && (
                <button
                  onClick={startTask}
                  className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Zap className="w-4 h-4" />
                  <span>开始AI分段</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：任务配置和分段配置 */}
        <div className="w-96 flex-shrink-0 bg-white/60 backdrop-blur-lg border-r border-white/30 overflow-y-auto">
          {/* 任务配置区域 */}
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Settings className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">任务配置</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">任务名称 *</label>
                <input
                  type="text"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
            </div>
          </div>

          {/* 分段配置区域 */}
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <Scissors className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">分段配置</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">分段方式</label>
                <select
                  value={config.method}
                  onChange={(e) => setConfig({...config, method: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                >
                  <option value="paragraph">按段落分段</option>
                  <option value="sentence">按句子分段</option>
                  <option value="fixed_length">固定长度分段</option>
                  <option value="semantic">语义分段</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">最大长度</label>
                  <input
                    type="number"
                    value={config.max_length}
                    onChange={(e) => setConfig({...config, max_length: parseInt(e.target.value) || 500})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">重叠长度</label>
                  <input
                    type="number"
                    value={config.overlap}
                    onChange={(e) => setConfig({...config, overlap: parseInt(e.target.value) || 50})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">语言</label>
                <select
                  value={config.language}
                  onChange={(e) => setConfig({...config, language: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                >
                  <option value="zh">中文</option>
                  <option value="en">英文</option>
                  <option value="auto">自动检测</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.preserve_formatting}
                    onChange={(e) => setConfig({...config, preserve_formatting: e.target.checked})}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">保留格式</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.normalize_text}
                    onChange={(e) => setConfig({...config, normalize_text: e.target.checked})}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">文本标准化</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.extract_keywords}
                    onChange={(e) => setConfig({...config, extract_keywords: e.target.checked})}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">提取关键词</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.remove_stopwords}
                    onChange={(e) => setConfig({...config, remove_stopwords: e.target.checked})}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">移除停用词</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：文件列表 */}
        <div className="flex-1 flex flex-col bg-white/40 backdrop-blur-lg overflow-hidden">
          {/* 文件列表头部 */}
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">待分段文件</h3>
                  <p className="text-sm text-gray-500">
                    {files.length} 个文件 • 点击添加更多文件
                  </p>
                </div>
              </div>
              
              <button
                onClick={() => setShowFileSelector(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Plus className="w-4 h-4" />
                <span>添加文件</span>
              </button>
            </div>
          </div>

          {/* 文件列表内容 */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {files.map((file) => {
                const progress = fileProgress[file.file_id];
                
                return (
                  <motion.div
                    key={file.file_id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                          <FileText className="w-5 h-5 text-white" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-gray-900 truncate" title={file.file_name}>
                            {file.file_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {file.file_size_formatted} • {file.file_extension.toUpperCase()}
                          </p>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => removeFile(file.file_id)}
                        className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors"
                        title="移除文件"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="mb-3">
                      <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                        <span>存储类型: {file.storage_type}</span>
                        <span>{new Date(file.created_at).toLocaleDateString('zh-CN')}</span>
                      </div>
                    </div>

                    {progress && (
                      <div className="mt-3 pt-3 border-t border-gray-200/50">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-gray-700">
                            {progress.status === 'pending' && '等待处理'}
                            {progress.status === 'processing' && '处理中...'}
                            {progress.status === 'completed' && `已完成 (${progress.segments} 段)`}
                          </span>
                          <span className="text-xs text-gray-500">{progress.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full transition-all duration-300 ${
                              progress.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${progress.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 文件选择弹窗 */}
      {showFileSelector && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[60vh] overflow-hidden"
          >
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <FolderOpen className="w-6 h-6 text-blue-600" />
                  <h3 className="text-lg font-bold text-gray-900">选择文件</h3>
                </div>
                <button
                  onClick={() => setShowFileSelector(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                <p className="text-gray-500">文件选择功能演示</p>
                <p className="text-sm text-gray-400 mt-1">实际使用时会显示存储中的文件列表</p>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default TestNewSegmentPage;
