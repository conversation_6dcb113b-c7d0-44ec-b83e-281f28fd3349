#!/usr/bin/env python3
"""
清理首次设置数据
用于测试首次设置功能
"""
import asyncio
from sqlalchemy import text
from loguru import logger

from app.core.database import get_async_session


async def clear_setup_data():
    """清理首次设置相关数据"""
    
    try:
        async for session in get_async_session():
            logger.info("开始清理首次设置数据...")
            
            # 清理存储配置
            result = await session.execute(text("DELETE FROM storage_configs"))
            storage_deleted = result.rowcount
            logger.info("删除了 {} 个存储配置".format(storage_deleted))
            
            # 清理管理员用户
            result = await session.execute(text("DELETE FROM users WHERE is_superuser = true"))
            admin_deleted = result.rowcount
            logger.info("删除了 {} 个管理员用户".format(admin_deleted))
            
            # 提交更改
            await session.commit()
            
            logger.info("✅ 首次设置数据清理完成！")
            logger.info("现在可以重新进行首次设置测试")
            
            break  # 退出async for循环
            
    except Exception as e:
        logger.error("清理首次设置数据失败: {}".format(str(e)))
        raise


if __name__ == "__main__":
    asyncio.run(clear_setup_data())
