'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDown, 
  HardDrive, 
  Cloud, 
  Server, 
  Database,
  Check,
  Plus,
  Settings
} from 'lucide-react';


interface Storage {
  id: number;
  name: string;
  storage_type: 'local' | 'minio' | 'ftp' | 'sftp';
  is_default: boolean;
  is_active: boolean;
  config: any;
}

interface StorageSelectorProps {
  currentStorage: number | null;
  onStorageChange: (storageId: number) => void;
  showPath?: boolean;
}

const StorageSelector: React.FC<StorageSelectorProps> = ({
  currentStorage,
  onStorageChange,
  showPath = false
}) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [storages, setStorages] = useState<Storage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 获取存储图标
  const getStorageIcon = (type: string) => {
    switch (type) {
      case 'local':
        return <HardDrive className="w-4 h-4" />;
      case 'minio':
        return <Cloud className="w-4 h-4" />;
      case 'ftp':
      case 'sftp':
        return <Server className="w-4 h-4" />;
      default:
        return <Database className="w-4 h-4" />;
    }
  };

  // 获取存储类型显示名称
  const getStorageTypeName = (type: string) => {
    switch (type) {
      case 'local':
        return '本地存储';
      case 'minio':
        return 'MinIO';
      case 'ftp':
        return 'FTP';
      case 'sftp':
        return 'SFTP';
      default:
        return type.toUpperCase();
    }
  };

  // 获取存储路径信息
  const getStoragePath = (storage: Storage) => {
    if (!storage.config) return '';

    switch (storage.storage_type) {
      case 'local':
        return storage.config.base_path || '/';
      case 'minio':
        return `${storage.config.endpoint}/${storage.config.bucket}`;
      case 'ftp':
      case 'sftp':
        return `${storage.config.host}:${storage.config.port}${storage.config.base_path || '/'}`;
      default:
        return '';
    }
  };

  // 加载存储列表
  const loadStorages = async () => {
    try {
      setIsLoading(true);
      // 使用统一的API客户端
      const { storageApi } = await import('@/lib/api');
      const data = await storageApi.getStorages();
      setStorages(data);

      // 如果没有选中的存储，选择默认存储
      if (!currentStorage && data.length > 0) {
        const defaultStorage = data.find((s: Storage) => s.is_default) || data[0];
        onStorageChange(defaultStorage.id);
      }
    } catch (error) {
      console.error('Failed to load storages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStorages();
  }, [currentStorage, onStorageChange]);

  const currentStorageData = storages.find(s => s.id === currentStorage);

  return (
    <div className="relative">
      {/* 存储选择按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        disabled={isLoading}
      >
        {isLoading ? (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full"
          />
        ) : (
          currentStorageData && getStorageIcon(currentStorageData.storage_type)
        )}
        
        <div className="text-left">
          <div className="text-sm font-medium text-gray-700">
            {isLoading ? '加载中...' : (currentStorageData?.name || '选择存储')}
          </div>
          {showPath && currentStorageData && (
            <div className="text-xs text-gray-500 truncate max-w-32">
              {getStoragePath(currentStorageData)}
            </div>
          )}
        </div>
        
        <ChevronDown 
          className={`w-4 h-4 text-gray-500 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {/* 下拉菜单 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
          >
            <div className="p-2">
              {/* 存储列表 */}
              <div className="space-y-1">
                {storages.map((storage) => (
                  <button
                    key={storage.id}
                    onClick={() => {
                      onStorageChange(storage.id);
                      setIsOpen(false);
                    }}
                    className={`w-full flex items-center justify-between px-3 py-2 rounded-md transition-colors ${
                      storage.id === currentStorage
                        ? 'bg-blue-50 text-blue-700'
                        : 'hover:bg-gray-50 text-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      {getStorageIcon(storage.storage_type)}
                      <div className="text-left">
                        <div className="font-medium">{storage.name}</div>
                        <div className="text-xs text-gray-500">
                          {getStorageTypeName(storage.storage_type)}
                          {storage.is_default && ' (默认)'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {!storage.is_active && (
                        <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded">
                          未启用
                        </span>
                      )}
                      {storage.id === currentStorage && (
                        <Check className="w-4 h-4 text-blue-600" />
                      )}
                    </div>
                  </button>
                ))}
              </div>

              {/* 分隔线 */}
              {storages.length > 0 && (
                <div className="border-t border-gray-200 my-2"></div>
              )}

              {/* 管理选项 */}
              <div className="space-y-1">
                <button
                  onClick={() => {
                    router.push('/storage/create');
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span className="text-sm">添加存储</span>
                </button>

                <button
                  onClick={() => {
                    router.push('/storage');
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  <Settings className="w-4 h-4" />
                  <span className="text-sm">管理存储</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 点击外部关闭 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}


    </div>
  );
};

export default StorageSelector;
