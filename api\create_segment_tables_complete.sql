-- 创建文档分段相关数据库表（完整版本）
-- 包含所有模型中定义的字段
-- 使用VARCHAR类型替代枚举类型以避免类型不存在错误

-- 创建文档分段任务表
CREATE TABLE IF NOT EXISTS document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    file_ids JSONB NOT NULL,
    total_files INTEGER DEFAULT 0,
    processed_files INTEGER DEFAULT 0,
    
    -- 分段配置
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BO<PERSON><PERSON>N DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    
    -- 任务状态
    status VARCHAR(20) DEFAULT 'pending',
    progress FLOAT DEFAULT 0.0,
    error_message TEXT,
    
    -- 统计信息
    total_segments INTEGER DEFAULT 0,
    total_vectors INTEGER DEFAULT 0,
    total_size_bytes BIGINT DEFAULT 0,
    avg_segment_length FLOAT DEFAULT 0.0,
    
    -- 时间信息
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    estimated_duration INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档分段表（包含所有字段）
CREATE TABLE IF NOT EXISTS document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id INTEGER NOT NULL,  -- 改为INTEGER类型，对应task表的id
    file_id VARCHAR(36) NOT NULL,
    
    -- 分段内容
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    
    -- 位置信息
    segment_index INTEGER NOT NULL,
    start_position INTEGER DEFAULT 0,
    end_position INTEGER DEFAULT 0,
    
    -- 统计信息
    word_count INTEGER DEFAULT 0,
    sentence_count INTEGER DEFAULT 0,
    
    -- 元数据
    segment_metadata JSONB,
    keywords JSONB,
    
    -- 向量化信息
    vectorize_status VARCHAR(20) DEFAULT 'pending',
    vector_id VARCHAR(100),
    embedding_vector JSONB,
    
    -- 质量评分
    quality_score FLOAT DEFAULT 0.0,
    readability_score FLOAT DEFAULT 0.0,
    
    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建分段模板表（包含所有字段）
CREATE TABLE IF NOT EXISTS segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 分段配置
    segment_method VARCHAR(50) NOT NULL,
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    
    -- 模板属性
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    
    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量索引表（包含所有字段）
CREATE TABLE IF NOT EXISTS vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 索引配置
    embedding_model VARCHAR(100) NOT NULL,
    vector_dimension INTEGER NOT NULL,
    similarity_metric VARCHAR(20) DEFAULT 'cosine',
    
    -- 统计信息
    total_vectors INTEGER DEFAULT 0,
    total_documents INTEGER DEFAULT 0,
    index_size_mb FLOAT DEFAULT 0.0,
    
    -- 状态信息
    is_active BOOLEAN DEFAULT true,
    last_updated_at TIMESTAMP,
    
    -- 时间信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引（提升查询性能）
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_created_at ON document_segment_tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_segment_id ON document_segments(segment_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_content_hash ON document_segments(content_hash);
CREATE INDEX IF NOT EXISTS idx_document_segments_vectorize_status ON document_segments(vectorize_status);

CREATE INDEX IF NOT EXISTS idx_segment_templates_template_name ON segment_templates(template_name);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_system ON segment_templates(is_system);

CREATE INDEX IF NOT EXISTS idx_vector_indexes_index_name ON vector_indexes(index_name);
CREATE INDEX IF NOT EXISTS idx_vector_indexes_is_active ON vector_indexes(is_active);

-- 插入默认分段模板（完整字段）
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
(
    '通用文档分段', 
    '适用于大多数文档的通用分段配置，平衡分段质量和处理效率', 
    'paragraph', 500, 50,
    true, true, 'text-embedding-ada-002', 1536,
    1000, 'zh', false, true, true,
    true, true
),
(
    '长文档分段', 
    '适用于长文档的分段配置，使用较大的分段长度以保持上下文完整性', 
    'paragraph', 1000, 100,
    true, true, 'text-embedding-ada-002', 1536,
    1500, 'zh', false, true, true,
    false, true
),
(
    '精细分段', 
    '适用于需要精细分段的文档，使用较小的分段长度以提高检索精度', 
    'sentence', 200, 20,
    true, true, 'text-embedding-ada-002', 1536,
    500, 'zh', true, true, true,
    false, true
),
(
    '语义分段', 
    '使用AI语义理解进行智能分段，适合复杂文档结构', 
    'semantic', 800, 80,
    true, true, 'text-embedding-3-small', 1536,
    1200, 'zh', false, true, true,
    false, true
)
ON CONFLICT (template_name) DO NOTHING;

-- 插入默认向量索引
INSERT INTO vector_indexes (
    index_name, description, embedding_model, vector_dimension,
    similarity_metric, is_active
) VALUES 
(
    'default_index', 
    '默认向量索引，使用OpenAI text-embedding-ada-002模型', 
    'text-embedding-ada-002', 1536,
    'cosine', true
),
(
    'high_precision_index', 
    '高精度向量索引，使用OpenAI text-embedding-3-large模型', 
    'text-embedding-3-large', 3072,
    'cosine', true
)
ON CONFLICT (index_name) DO NOTHING;

-- 验证表创建
SELECT 'Tables created successfully!' as status;

-- 显示创建的表
SELECT table_name, 
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t
WHERE table_schema = 'public' 
AND table_name IN ('document_segment_tasks', 'document_segments', 'segment_templates', 'vector_indexes')
ORDER BY table_name;

-- 显示模板数量
SELECT COUNT(*) as template_count FROM segment_templates;

-- 显示索引数量
SELECT COUNT(*) as index_count FROM vector_indexes;

-- 显示默认模板
SELECT template_name, description, segment_method, is_default 
FROM segment_templates 
ORDER BY is_default DESC, template_name;
