'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Download, 
  Eye, 
  MoreHorizontal, 
  Check,
  Edit3,
  Scissors,
  FileText,
  Image,
  Video,
  Music,
  Archive,
  Code,
  Sparkles,
  Zap,
  Brain,
  ChevronRight,
  Calendar,
  HardDrive,
  Star,
  Share2,
  Copy
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { isSegmentableFile, isEditableFile, isOfficeFile } from '@/utils/fileTypeUtils';

interface FileItem {
  file_id: string;
  file_path: string;
  file_name: string;
  file_type: 'file' | 'directory';
  file_size: number;
  file_size_formatted: string;
  mime_type?: string;
  file_extension: string;
  created_at: string;
  modified_at: string;
  is_directory: boolean;
  is_image: boolean;
  is_video: boolean;
  is_document: boolean;
}

interface ModernFileListViewProps {
  files: FileItem[];
  selectedFiles: string[];
  onFileSelect: (file: FileItem, isSelected: boolean) => void;
  onFileDoubleClick: (file: FileItem) => void;
  onSelectAll: () => void;
  getFileIcon: (file: FileItem) => React.ReactNode;
  onFileDownload: (file: FileItem) => void;
  onFilePreview: (file: FileItem) => void;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  onBatchSegment?: (fileIds: string[]) => void;
}

const ModernFileListView: React.FC<ModernFileListViewProps> = ({
  files,
  selectedFiles,
  onFileSelect,
  onFileDoubleClick,
  onSelectAll,
  getFileIcon,
  onFileDownload,
  onFilePreview,
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  onBatchSegment
}) => {
  const router = useRouter();
  const [hoveredFile, setHoveredFile] = useState<string | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    file: FileItem;
  } | null>(null);





  // 处理文件查看 - 新窗口打开
  const handleFileView = (file: FileItem) => {
    // 直接使用base64编码file_id，不需要额外的URL编码
    const encodedFileId = btoa(file.file_id);

    // 构建查看URL
    let viewUrl;
    if (isOfficeFile(file)) {
      viewUrl = `/file-manager/office/view/${encodedFileId}`;
    } else {
      viewUrl = `/file-manager/view/${encodedFileId}`;
    }

    // 在新窗口中打开
    window.open(viewUrl, '_blank', 'noopener,noreferrer');
  };

  // 处理文件编辑 - 在新浏览器标签页中打开
  const handleFileEdit = (file: FileItem) => {
    // 直接使用base64编码file_id，不需要额外的URL编码
    const encodedFileId = btoa(file.file_id);

    // 构建编辑URL
    let editUrl;
    if (isOfficeFile(file)) {
      editUrl = `/file-manager/office/edit/${encodedFileId}`;
    } else {
      editUrl = `/file-manager/edit/${encodedFileId}`;
    }

    // 在新浏览器标签页中打开编辑器
    window.open(editUrl, '_blank', 'noopener,noreferrer');
  };

  // 处理文件分段
  const handleFileSegment = (file: FileItem) => {
    const encodedFileId = btoa(file.file_id);
    router.push(`/file-manager/segment/${encodedFileId}`);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '今天';
    if (diffDays === 2) return '昨天';
    if (diffDays <= 7) return `${diffDays}天前`;
    
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取文件类型标签颜色
  const getFileTypeColor = (file: FileItem) => {
    if (file.is_directory) return 'bg-blue-100 text-blue-800';
    if (file.is_image) return 'bg-green-100 text-green-800';
    if (file.is_video) return 'bg-purple-100 text-purple-800';
    if (file.is_document) return 'bg-orange-100 text-orange-800';
    return 'bg-gray-100 text-gray-800';
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, file: FileItem) => {
    e.preventDefault();
    e.stopPropagation();

    // 获取鼠标位置
    const mouseX = e.clientX;
    const mouseY = e.clientY;

    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = 192; // 菜单实际宽度 (min-w-48 = 12rem = 192px)
    const menuHeight = 200; // 菜单预估高度

    // 计算菜单位置，确保不超出视口
    let x = mouseX;
    let y = mouseY;

    // 右侧边界检查
    if (x + menuWidth > viewportWidth) {
      x = mouseX - menuWidth; // 显示在鼠标左侧
    }

    // 底部边界检查
    if (y + menuHeight > viewportHeight) {
      y = mouseY - menuHeight; // 显示在鼠标上方
    }

    // 确保菜单不会超出屏幕边界
    x = Math.max(8, Math.min(x, viewportWidth - menuWidth - 8));
    y = Math.max(8, Math.min(y, viewportHeight - menuHeight - 8));

    setContextMenu({
      x,
      y,
      file
    });
  };

  const allSelected = files.length > 0 && selectedFiles.length === files.length;
  const someSelected = selectedFiles.length > 0 && selectedFiles.length < files.length;





  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden flex flex-col h-full">

      {/* 固定AI风格表头 */}
      <div className="bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center px-6 py-4 text-sm font-semibold text-gray-700">
          <div className="w-10 flex items-center justify-center">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={onSelectAll}
              className={`flex items-center justify-center w-5 h-5 rounded border-2 transition-all duration-200 ${
                allSelected
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 border-transparent'
                  : someSelected
                  ? 'bg-gradient-to-r from-blue-400 to-purple-500 border-transparent'
                  : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
              }`}
            >
              {allSelected && <Check className="w-3 h-3 text-white" />}
              {someSelected && !allSelected && (
                <div className="w-2 h-2 bg-white rounded-sm" />
              )}
            </motion.button>
          </div>
          <div className="flex-1 flex items-center space-x-2 min-w-0">
            <Sparkles className="w-4 h-4 text-indigo-500" />
            <span>文件名称</span>
          </div>
          <div className="w-24 flex items-center space-x-2">
            <HardDrive className="w-4 h-4 text-purple-500" />
            <span>大小</span>
          </div>
          <div className="w-32 flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-pink-500" />
            <span>修改时间</span>
          </div>
          <div className="w-20 flex items-center space-x-2">
            <Brain className="w-4 h-4 text-indigo-500" />
            <span>类型</span>
          </div>
          <div className="w-32 flex items-center justify-center">
            <Zap className="w-4 h-4 text-yellow-500" />
          </div>
        </div>
      </div>

      {/* 可滚动的文件列表 */}
      <div className="flex-1 overflow-y-auto divide-y divide-gray-100">
        <AnimatePresence>
          {files.map((file, index) => {
            const isSelected = selectedFiles.includes(file.file_path);
            const isHovered = hoveredFile === file.file_id;
            
            return (
              <motion.div
                key={file.file_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className={`flex items-center px-6 py-4 cursor-pointer transition-all duration-200 group ${
                  isSelected
                    ? 'bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 border-l-4 border-blue-500'
                    : isHovered
                    ? 'bg-gradient-to-r from-gray-50 to-blue-50'
                    : 'hover:bg-gray-50'
                }`}
                onMouseEnter={() => setHoveredFile(file.file_id)}
                onMouseLeave={() => setHoveredFile(null)}
                onClick={() => {
                  const isSelected = selectedFiles.includes(file.file_path);
                  onFileSelect(file, !isSelected);
                }}
                onDoubleClick={() => onFileDoubleClick(file)}
                onContextMenu={(e) => handleContextMenu(e, file)}
              >
                {/* 选择框 */}
                <div className="w-10 flex items-center justify-center">
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className={`flex items-center justify-center w-5 h-5 rounded border-2 transition-all duration-200 ${
                      isSelected
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 border-transparent shadow-lg'
                        : 'border-gray-300 group-hover:border-blue-400 group-hover:bg-blue-50'
                    }`}
                  >
                    {isSelected && <Check className="w-3 h-3 text-white" />}
                  </motion.div>
                </div>

                {/* 文件名 */}
                <div className="flex-1 flex items-center space-x-3 min-w-0">
                  <div className="flex-shrink-0">
                    {getFileIcon(file)}
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                      {file.file_name}
                    </p>
                    {isOfficeFile(file) && (
                      <p className="text-xs text-blue-500 truncate flex items-center">
                        <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                        支持Office在线编辑
                      </p>
                    )}
                    {file.is_document && !isOfficeFile(file) && (
                      <p className="text-xs text-gray-500 truncate">
                        支持在线编辑和AI分析
                      </p>
                    )}
                  </div>
                </div>

                {/* 文件大小 */}
                <div className="w-24 flex items-center">
                  <span className="text-sm text-gray-600 font-mono">
                    {file.is_directory ? '-' : file.file_size_formatted}
                  </span>
                </div>

                {/* 修改时间 */}
                <div className="w-32 flex items-center">
                  <span className="text-sm text-gray-600">
                    {formatDate(file.modified_at)}
                  </span>
                </div>

                {/* 文件类型 */}
                <div className="w-20 flex items-center">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getFileTypeColor(file)}`}>
                    {file.is_directory ? '文件夹' : file.file_extension.toUpperCase()}
                  </span>
                </div>

                {/* 操作按钮 */}
                <div className="w-32 flex items-center justify-center">
                  <div className={`flex items-center space-x-1 transition-all duration-200 ${
                    isHovered || isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                  }`}>
                    {!file.is_directory && (
                      <>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleFileView(file);
                          }}
                          className="p-1.5 text-blue-500 hover:bg-blue-100 rounded-lg transition-colors"
                          title="查看"
                        >
                          <Eye className="w-4 h-4" />
                        </motion.button>

                        {/* 编辑按钮 - 优化显示，支持所有可编辑文件 */}
                        {isEditableFile(file) && (
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFileEdit(file);
                            }}
                            className="p-1.5 text-green-500 hover:bg-green-100 rounded-lg transition-colors border border-green-200 hover:border-green-300"
                            title={`编辑 ${file.file_name}`}
                          >
                            <Edit3 className="w-4 h-4" />
                          </motion.button>
                        )}

                        {!file.is_image && !file.is_video && isSegmentableFile(file) && (
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleFileSegment(file);
                            }}
                            className="p-1.5 text-purple-500 hover:bg-purple-100 rounded-lg transition-colors"
                            title="分段"
                          >
                            <Scissors className="w-4 h-4" />
                          </motion.button>
                        )}
                      </>
                    )}

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleContextMenu(e, file);
                      }}
                      className="p-1.5 text-gray-500 hover:bg-gray-100 rounded-lg transition-colors"
                      title="更多操作"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* 右键菜单 */}
      {contextMenu && (
        <>
          {/* 点击其他地方关闭菜单 */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setContextMenu(null)}
          />

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-xl py-2 min-w-48"
            style={{
              left: `${contextMenu.x}px`,
              top: `${contextMenu.y}px`,
            }}
            onMouseLeave={() => setContextMenu(null)}
          >
          <button
            onClick={() => {
              handleFileView(contextMenu.file);
              setContextMenu(null);
            }}
            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
          >
            <Eye className="w-4 h-4" />
            <span>查看</span>
          </button>

          {isEditableFile(contextMenu.file) && (
            <button
              onClick={() => {
                handleFileEdit(contextMenu.file);
                setContextMenu(null);
              }}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
            >
              <Edit3 className="w-4 h-4" />
              <span>编辑</span>
            </button>
          )}

          <button
            onClick={() => {
              // 下载逻辑
              setContextMenu(null);
            }}
            className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>下载</span>
          </button>

          {!contextMenu.file.is_image && !contextMenu.file.is_video && isSegmentableFile(contextMenu.file) && (
            <button
              onClick={() => {
                handleFileSegment(contextMenu.file);
                setContextMenu(null);
              }}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
            >
              <Scissors className="w-4 h-4" />
              <span>分段</span>
            </button>
          )}
          </motion.div>
        </>
      )}
    </div>
  );
};

export default ModernFileListView;
