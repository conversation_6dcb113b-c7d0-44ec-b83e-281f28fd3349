"""
存储统计模型
"""
from datetime import datetime
from typing import Dict, Any
from sqlalchemy import Column, Integer, BigInteger, Float, String, DateTime, ForeignKey, Text, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.models.base import BaseModel


class StorageStats(BaseModel):
    """存储统计表"""
    __tablename__ = 'storage_stats'

    storage_id = Column(Integer, ForeignKey('storage_configs.id'), nullable=False, index=True, comment='存储配置ID')
    
    # 统计时间
    stats_date = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), index=True, comment='统计时间')
    
    # 文件统计
    total_files = Column(Integer, default=0, comment='总文件数')
    total_folders = Column(Integer, default=0, comment='总文件夹数')
    
    # 大小统计
    total_size = Column(BigInteger, default=0, comment='总大小(字节)')
    used_size = Column(BigInteger, default=0, comment='已使用大小(字节)')
    available_size = Column(BigInteger, default=0, comment='可用大小(字节)')
    
    # 文件类型统计
    document_count = Column(Integer, default=0, comment='文档文件数')
    image_count = Column(Integer, default=0, comment='图片文件数')
    video_count = Column(Integer, default=0, comment='视频文件数')
    audio_count = Column(Integer, default=0, comment='音频文件数')
    archive_count = Column(Integer, default=0, comment='压缩文件数')
    other_count = Column(Integer, default=0, comment='其他文件数')
    
    # 大小分布统计
    document_size = Column(BigInteger, default=0, comment='文档文件总大小')
    image_size = Column(BigInteger, default=0, comment='图片文件总大小')
    video_size = Column(BigInteger, default=0, comment='视频文件总大小')
    audio_size = Column(BigInteger, default=0, comment='音频文件总大小')
    archive_size = Column(BigInteger, default=0, comment='压缩文件总大小')
    other_size = Column(BigInteger, default=0, comment='其他文件总大小')
    
    # 性能指标
    avg_file_size = Column(Float, default=0.0, comment='平均文件大小')
    largest_file_size = Column(BigInteger, default=0, comment='最大文件大小')
    smallest_file_size = Column(BigInteger, default=0, comment='最小文件大小')
    
    # 访问统计
    read_operations = Column(Integer, default=0, comment='读操作次数')
    write_operations = Column(Integer, default=0, comment='写操作次数')
    delete_operations = Column(Integer, default=0, comment='删除操作次数')
    
    # 错误统计
    error_count = Column(Integer, default=0, comment='错误次数')
    last_error = Column(Text, comment='最后一次错误信息')
    
    # 健康状态
    health_score = Column(Float, default=100.0, comment='健康评分(0-100)')
    is_healthy = Column(Boolean, default=True, comment='是否健康')
    
    # 关系
    storage = relationship("StorageConfig", back_populates="stats")

    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        
        # 添加格式化字段
        result['total_size_formatted'] = self.format_file_size(self.total_size)
        result['used_size_formatted'] = self.format_file_size(self.used_size)
        result['available_size_formatted'] = self.format_file_size(self.available_size)
        result['avg_file_size_formatted'] = self.format_file_size(self.avg_file_size)
        result['largest_file_size_formatted'] = self.format_file_size(self.largest_file_size)
        
        # 计算使用率
        if self.used_size and self.available_size:
            total_space = self.used_size + self.available_size
            result['usage_percentage'] = (self.used_size / total_space) * 100
        else:
            result['usage_percentage'] = 0
        
        return result
    
    @staticmethod
    def format_file_size(size_bytes: float) -> str:
        """格式化文件大小"""
        if not size_bytes:
            return "0 B"
        
        size = float(size_bytes)
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"


class StorageStatsHistory(BaseModel):
    """存储统计历史表"""
    __tablename__ = 'storage_stats_history'

    storage_id = Column(Integer, ForeignKey('storage_configs.id'), nullable=False, index=True, comment='存储配置ID')
    
    # 统计时间
    stats_date = Column(DateTime(timezone=True), nullable=False, index=True, comment='统计时间')
    
    # 快照数据(JSON格式存储完整统计信息)
    stats_snapshot = Column(Text, comment='统计快照数据')
    
    # 变化量
    files_change = Column(Integer, default=0, comment='文件数变化')
    size_change = Column(BigInteger, default=0, comment='大小变化')
    
    # 关系
    storage = relationship("StorageConfig")

    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        
        # 解析快照数据
        if self.stats_snapshot:
            import json
            try:
                result['snapshot_data'] = json.loads(self.stats_snapshot)
            except:
                result['snapshot_data'] = {}
        
        return result
