-- 完整修复文档分段功能的SQL脚本
-- 彻底解决字段缺失和枚举值不匹配问题
-- 适用于PostgreSQL数据库

-- 1. 检查并显示当前表结构
DO $$
DECLARE
    table_exists boolean;
BEGIN
    -- 检查表是否存在
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'document_segment_tasks'
    ) INTO table_exists;
    
    IF table_exists THEN
        RAISE NOTICE '表 document_segment_tasks 已存在，将进行结构检查和修复';
    ELSE
        RAISE NOTICE '表 document_segment_tasks 不存在，将创建新表';
    END IF;
END $$;

-- 2. 备份现有数据（如果表存在且有数据）
DO $$
BEGIN
    -- 创建备份表
    DROP TABLE IF EXISTS document_segment_tasks_backup_complete;
    DROP TABLE IF EXISTS document_segments_backup_complete;
    
    -- 如果原表存在，备份数据
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'document_segment_tasks') THEN
        CREATE TABLE document_segment_tasks_backup_complete AS SELECT * FROM document_segment_tasks;
        RAISE NOTICE '已备份 document_segment_tasks 表数据到 document_segment_tasks_backup_complete';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'document_segments') THEN
        CREATE TABLE document_segments_backup_complete AS SELECT * FROM document_segments;
        RAISE NOTICE '已备份 document_segments 表数据到 document_segments_backup_complete';
    END IF;
END $$;

-- 3. 删除现有表和枚举类型
DROP TABLE IF EXISTS document_segments CASCADE;
DROP TABLE IF EXISTS document_segment_tasks CASCADE;
DROP TABLE IF EXISTS segment_templates CASCADE;
DROP TABLE IF EXISTS vector_indexes CASCADE;

-- 删除枚举类型
DROP TYPE IF EXISTS segment_method_enum CASCADE;
DROP TYPE IF EXISTS segment_status_enum CASCADE;
DROP TYPE IF EXISTS vectorize_status_enum CASCADE;

-- 4. 创建枚举类型（使用大写值匹配Python模型）
CREATE TYPE segment_method_enum AS ENUM ('PARAGRAPH', 'SENTENCE', 'FIXED_LENGTH', 'SEMANTIC');
CREATE TYPE segment_status_enum AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');
CREATE TYPE vectorize_status_enum AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');

-- 5. 创建完整的文档分段任务表
CREATE TABLE document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- 文件信息
    file_ids JSONB NOT NULL,
    total_files INTEGER DEFAULT 0,
    processed_files INTEGER DEFAULT 0,
    
    -- 分段配置
    segment_method segment_method_enum NOT NULL,
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    
    -- 任务状态
    status segment_status_enum DEFAULT 'PENDING',
    progress FLOAT DEFAULT 0.0,
    error_message TEXT,
    
    -- 统计信息
    total_segments INTEGER DEFAULT 0,
    total_vectors INTEGER DEFAULT 0,
    
    -- 元数据（重要：这个字段在Python模型中被使用）
    segment_metadata JSONB,
    
    -- 时间信息
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 知识库相关字段
    kb_id VARCHAR(255),
    enable_vectorization_kb BOOLEAN DEFAULT false
);

-- 6. 创建完整的文档分段表
CREATE TABLE document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    task_id INTEGER NOT NULL,  -- 不创建外键约束
    file_id VARCHAR(36) NOT NULL,
    
    -- 分段内容
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    
    -- 位置信息
    segment_index INTEGER NOT NULL,
    start_position INTEGER DEFAULT 0,
    end_position INTEGER DEFAULT 0,
    
    -- 统计信息
    word_count INTEGER DEFAULT 0,
    sentence_count INTEGER DEFAULT 0,
    
    -- 元数据
    segment_metadata JSONB,
    keywords JSONB,
    
    -- 向量化信息
    vectorize_status vectorize_status_enum DEFAULT 'PENDING',
    vector_id VARCHAR(100),
    embedding_vector JSONB,
    
    -- 质量评分
    quality_score FLOAT DEFAULT 0.0,
    readability_score FLOAT DEFAULT 0.0,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 知识库相关字段
    kb_id VARCHAR(255),
    vector_status VARCHAR(50) DEFAULT 'PENDING',
    embedding_model VARCHAR(255),
    vector_dimension INTEGER
);

-- 7. 创建分段模板表
CREATE TABLE segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 分段配置
    segment_method segment_method_enum NOT NULL,
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    
    -- 模板属性
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 8. 创建向量索引表
CREATE TABLE vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 索引配置
    embedding_model VARCHAR(100) NOT NULL,
    vector_dimension INTEGER NOT NULL,
    similarity_metric VARCHAR(20) DEFAULT 'cosine',
    
    -- 统计信息
    total_vectors INTEGER DEFAULT 0,
    total_documents INTEGER DEFAULT 0,
    index_size_mb FLOAT DEFAULT 0.0,
    
    -- 状态信息
    is_active BOOLEAN DEFAULT true,
    last_updated_at TIMESTAMP WITH TIME ZONE,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 9. 创建所有必要的索引
-- document_segment_tasks 表索引
CREATE INDEX idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX idx_document_segment_tasks_created_at ON document_segment_tasks(created_at);
CREATE INDEX idx_document_segment_tasks_kb_id ON document_segment_tasks(kb_id);

-- document_segments 表索引
CREATE INDEX idx_document_segments_segment_id ON document_segments(segment_id);
CREATE INDEX idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX idx_document_segments_vectorize_status ON document_segments(vectorize_status);
CREATE INDEX idx_document_segments_content_hash ON document_segments(content_hash);
CREATE INDEX idx_document_segments_kb_id ON document_segments(kb_id);
CREATE INDEX idx_document_segments_vector_id ON document_segments(vector_id);

-- segment_templates 表索引
CREATE INDEX idx_segment_templates_template_name ON segment_templates(template_name);
CREATE INDEX idx_segment_templates_is_default ON segment_templates(is_default);
CREATE INDEX idx_segment_templates_is_system ON segment_templates(is_system);

-- vector_indexes 表索引
CREATE INDEX idx_vector_indexes_index_name ON vector_indexes(index_name);
CREATE INDEX idx_vector_indexes_embedding_model ON vector_indexes(embedding_model);
CREATE INDEX idx_vector_indexes_is_active ON vector_indexes(is_active);

-- 10. 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 11. 添加更新时间触发器
CREATE TRIGGER update_document_segment_tasks_updated_at
    BEFORE UPDATE ON document_segment_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_segments_updated_at
    BEFORE UPDATE ON document_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_segment_templates_updated_at
    BEFORE UPDATE ON segment_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vector_indexes_updated_at
    BEFORE UPDATE ON vector_indexes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 12. 插入默认分段模板
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
(
    '默认分段模板', '系统默认的文档分段模板，适用于大多数文档类型',
    'PARAGRAPH', 1000, 100, true, false, 'text-embedding-ada-002', 1536,
    'zh', false, true, true, true, true
),
(
    '短文本分段', '适用于短文档或需要精细分段的场景',
    'SENTENCE', 500, 50, true, false, 'text-embedding-ada-002', 1536,
    'zh', false, true, true, false, true
),
(
    '长文档分段', '适用于长文档的快速分段处理',
    'FIXED_LENGTH', 2000, 200, false, false, 'text-embedding-ada-002', 1536,
    'zh', true, true, false, false, true
);

-- 13. 添加表和列注释
COMMENT ON TABLE document_segment_tasks IS '文档分段任务表';
COMMENT ON TABLE document_segments IS '文档分段表';
COMMENT ON TABLE segment_templates IS '分段模板表';
COMMENT ON TABLE vector_indexes IS '向量索引表';

-- 重要字段注释
COMMENT ON COLUMN document_segment_tasks.segment_metadata IS '分段元数据(JSON格式) - 存储Celery任务信息等';
COMMENT ON COLUMN document_segment_tasks.task_id IS '任务唯一ID';
COMMENT ON COLUMN document_segment_tasks.file_ids IS '文件ID列表(JSON格式)';
COMMENT ON COLUMN document_segment_tasks.segment_method IS '分段方法：PARAGRAPH, SENTENCE, FIXED_LENGTH, SEMANTIC';
COMMENT ON COLUMN document_segment_tasks.status IS '任务状态：PENDING, PROCESSING, COMPLETED, FAILED';

COMMENT ON COLUMN document_segments.segment_id IS '分段唯一ID';
COMMENT ON COLUMN document_segments.task_id IS '关联的任务ID（无外键约束）';
COMMENT ON COLUMN document_segments.vectorize_status IS '向量化状态：PENDING, PROCESSING, COMPLETED, FAILED';
COMMENT ON COLUMN document_segments.embedding_vector IS '嵌入向量(JSON格式)';

-- 14. 验证表结构
DO $$
DECLARE
    task_columns_count integer;
    segment_columns_count integer;
    enum_count integer;
BEGIN
    -- 检查 document_segment_tasks 表字段数量
    SELECT COUNT(*) INTO task_columns_count
    FROM information_schema.columns 
    WHERE table_name = 'document_segment_tasks';
    
    -- 检查 document_segments 表字段数量
    SELECT COUNT(*) INTO segment_columns_count
    FROM information_schema.columns 
    WHERE table_name = 'document_segments';
    
    -- 检查枚举类型数量
    SELECT COUNT(*) INTO enum_count
    FROM pg_type 
    WHERE typname IN ('segment_method_enum', 'segment_status_enum', 'vectorize_status_enum');
    
    RAISE NOTICE '=== 表结构验证结果 ===';
    RAISE NOTICE 'document_segment_tasks 表字段数: %', task_columns_count;
    RAISE NOTICE 'document_segments 表字段数: %', segment_columns_count;
    RAISE NOTICE '枚举类型数量: %', enum_count;
    
    -- 检查关键字段是否存在
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'document_segment_tasks' AND column_name = 'segment_metadata') THEN
        RAISE NOTICE '✅ segment_metadata 字段已创建';
    ELSE
        RAISE NOTICE '❌ segment_metadata 字段缺失';
    END IF;
    
    RAISE NOTICE '=== 修复完成 ===';
    RAISE NOTICE '现在可以正常使用文档分段功能了！';
END $$;
