'use client';

import React, { useState, useEffect } from 'react';

const CeleryDebug: React.FC = () => {
  const [status, setStatus] = useState<any>(null);
  const [config, setConfig] = useState<any>(null);
  const [metrics, setMetrics] = useState<any>(null);
  const [autoStart, setAutoStart] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState<string[]>([]);

  const addError = (error: string) => {
    setErrors(prev => [...prev, error]);
  };

  const fetchStatus = async () => {
    try {
      console.log('Fetching status...');
      const token = localStorage.getItem('token');
      console.log('Token:', token ? `exists (${token.substring(0, 20)}...)` : 'missing');

      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Request headers:', headers);

      const response = await fetch('/api/v1/celery/status', {
        headers
      });
      
      console.log('Status response:', response.status, response.statusText);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Status data:', data);
        setStatus(data);
      } else {
        const errorText = await response.text();
        addError(`Status API error: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('Status fetch error:', error);
      addError(`Status fetch error: ${error}`);
    }
  };

  const fetchConfig = async () => {
    try {
      console.log('Fetching config...');
      const token = localStorage.getItem('token');

      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch('/api/v1/celery/config', {
        headers
      });
      
      console.log('Config response:', response.status, response.statusText);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Config data:', data);
        setConfig(data);
      } else {
        const errorText = await response.text();
        addError(`Config API error: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('Config fetch error:', error);
      addError(`Config fetch error: ${error}`);
    }
  };

  const fetchMetrics = async () => {
    try {
      console.log('Fetching metrics...');
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/v1/celery/metrics?hours=1', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('Metrics response:', response.status, response.statusText);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Metrics data:', data);
        setMetrics(data);
      } else {
        const errorText = await response.text();
        addError(`Metrics API error: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('Metrics fetch error:', error);
      addError(`Metrics fetch error: ${error}`);
    }
  };

  const fetchAutoStart = async () => {
    try {
      console.log('Fetching auto-start...');
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/v1/celery/auto-start', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('Auto-start response:', response.status, response.statusText);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Auto-start data:', data);
        setAutoStart(data.auto_start);
      } else {
        const errorText = await response.text();
        addError(`Auto-start API error: ${response.status} - ${errorText}`);
      }
    } catch (error) {
      console.error('Auto-start fetch error:', error);
      addError(`Auto-start fetch error: ${error}`);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setErrors([]);
      
      await Promise.all([
        fetchStatus(),
        fetchConfig(),
        fetchMetrics(),
        fetchAutoStart()
      ]);
      
      setLoading(false);
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Celery调试信息</h3>
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-lg font-semibold mb-4">Celery调试信息</h3>
      
      {errors.length > 0 && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
          <h4 className="font-medium text-red-800 mb-2">错误信息:</h4>
          {errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600">{error}</p>
          ))}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">状态数据</h4>
          <pre className="text-xs overflow-auto max-h-40">
            {status ? JSON.stringify(status, null, 2) : '未加载'}
          </pre>
        </div>

        <div className="p-4 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">配置数据</h4>
          <pre className="text-xs overflow-auto max-h-40">
            {config ? JSON.stringify(config, null, 2) : '未加载'}
          </pre>
        </div>

        <div className="p-4 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">指标数据</h4>
          <pre className="text-xs overflow-auto max-h-40">
            {metrics ? JSON.stringify(metrics, null, 2) : '未加载'}
          </pre>
        </div>

        <div className="p-4 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">自动启动</h4>
          <p className="text-sm">{autoStart ? '已启用' : '已禁用'}</p>
        </div>
      </div>

      <div className="mt-4">
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重新加载
        </button>
      </div>
    </div>
  );
};

export default CeleryDebug;
