#!/usr/bin/env python3
"""
测试数据库连接
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from app.core.config import get_settings

async def test_connection():
    """测试数据库连接"""
    try:
        print("开始测试数据库连接...")
        
        # 获取设置
        settings = get_settings()
        database_url = settings.database_url
        print(f"数据库URL: {database_url}")
        
        # 创建异步引擎
        engine = create_async_engine(database_url, echo=False)
        
        # 测试连接
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            row = result.fetchone()
            print(f"连接测试成功: {row}")
        
        # 检查现有表
        async with engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            print(f"现有表: {tables}")
        
        await engine.dispose()
        print("✅ 数据库连接测试成功")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_connection())
