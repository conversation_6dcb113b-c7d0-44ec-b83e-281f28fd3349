"""
测试配置和夹具
"""

import pytest
from fastapi.testclient import TestClient
from app.core.app_factory import create_app
from app.core.config import get_settings


@pytest.fixture
def settings():
    """测试配置夹具"""
    return get_settings()


@pytest.fixture
def app():
    """FastAPI应用夹具"""
    return create_app()


@pytest.fixture
def client(app):
    """测试客户端夹具"""
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """认证头夹具"""
    return {"Authorization": "Bearer fake-access-token"}
