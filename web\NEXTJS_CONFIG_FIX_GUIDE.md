# Next.js 配置修复指南

## 🚨 问题描述

遇到以下错误：
```
Error: Configuring Next.js via 'next.config.ts' is not supported. 
Please replace the file with 'next.config.js' or 'next.config.mjs'.
```

## 🔧 问题原因

1. **Next.js 14 不支持 TypeScript 配置文件**
   - `next.config.ts` 在 Next.js 14 中不被支持
   - 需要使用 `next.config.js` 或 `next.config.mjs`

2. **PostCSS 配置冲突**
   - 存在错误的 `postcss.config.mjs` 文件
   - 使用了不存在的 `@tailwindcss/postcss` 插件

## ✅ 已修复的问题

### 1. 配置文件修复

#### 删除的文件
- ❌ `next.config.ts` (不支持)
- ❌ `postcss.config.mjs` (错误配置)

#### 创建的文件
- ✅ `next.config.js` (正确的 JavaScript 配置)
- ✅ `postcss.config.js` (正确的 PostCSS 配置)
- ✅ `tailwind.config.js` (Tailwind CSS 配置)

### 2. 配置文件内容

#### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    APP_ENV: process.env.NODE_ENV || 'development',
  },
  experimental: {
    esmExternals: true,
  },
  swcMinify: true,
  images: {
    domains: [],
    unoptimized: false,
  },
  output: 'standalone',
  reactStrictMode: true,
};

module.exports = nextConfig;
```

#### postcss.config.js
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

#### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    // ... 其他路径
  ],
  theme: {
    extend: {
      // 自定义主题配置
    },
  },
  plugins: [],
}
```

### 3. package.json 依赖

```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "next": "^14.0.0",
    "framer-motion": "^10.16.0",
    "lucide-react": "^0.300.0",
    "react-hook-form": "^7.48.0",
    "axios": "^1.6.0",
    "js-cookie": "^3.0.5",
    "react-hot-toast": "^2.4.1"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@types/js-cookie": "^3.0.6",
    "postcss": "^8.4.31",
    "autoprefixer": "^10.4.16",
    "tailwindcss": "^3.3.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0"
  }
}
```

## 🚀 修复步骤

### 方法1：使用自动修复脚本（推荐）

```cmd
cd web
双击运行: fix-dependencies.bat
```

### 方法2：手动修复

```cmd
cd web

# 1. 清理旧文件
rmdir /s /q node_modules
rmdir /s /q .next
del pnpm-lock.yaml
del package-lock.json

# 2. 删除错误的配置文件
del next.config.ts
del postcss.config.mjs

# 3. 确认正确的配置文件存在
# - next.config.js
# - postcss.config.js  
# - tailwind.config.js

# 4. 重新安装依赖
pnpm install

# 5. 启动开发服务器
pnpm dev
```

### 方法3：测试启动

```cmd
cd web
双击运行: test-nextjs-start.bat
```

## 🔍 验证修复

### 1. 检查配置文件

确保以下文件存在且内容正确：
- ✅ `next.config.js` (不是 .ts)
- ✅ `postcss.config.js` (不是 .mjs)
- ✅ `tailwind.config.js`
- ✅ `package.json`

### 2. 启动测试

运行以下命令：
```cmd
cd web
pnpm dev
```

成功的输出应该类似：
```
> ai-knowledge-base-web@0.1.0 dev
> next dev

- ready started server on 0.0.0.0:3000, url: http://localhost:3000
- event compiled client and server successfully
```

### 3. 浏览器测试

访问 `http://localhost:3000` 应该能看到应用正常加载。

## 🐛 常见问题排查

### 问题1：仍然报 next.config.ts 错误
**解决方案**：
```cmd
# 确保完全删除 TypeScript 配置文件
del next.config.ts
# 确认 JavaScript 配置文件存在
dir next.config.js
```

### 问题2：PostCSS 插件错误
**解决方案**：
```cmd
# 删除错误的配置文件
del postcss.config.mjs
# 确认正确的配置文件存在
dir postcss.config.js
```

### 问题3：依赖版本冲突
**解决方案**：
```cmd
# 完全清理并重新安装
rmdir /s /q node_modules
del pnpm-lock.yaml
pnpm install
```

### 问题4：端口占用
**解决方案**：
```cmd
# 使用不同端口
pnpm dev:3001
# 或者杀死占用进程
netstat -ano | findstr :3000
taskkill /PID <PID> /F
```

### 问题5：权限问题
**解决方案**：
- 以管理员身份运行命令提示符
- 检查文件夹权限
- 确保没有文件被其他程序占用

## 📋 修复检查清单

- [ ] 删除 `next.config.ts`
- [ ] 删除 `postcss.config.mjs`
- [ ] 确认 `next.config.js` 存在
- [ ] 确认 `postcss.config.js` 存在
- [ ] 确认 `tailwind.config.js` 存在
- [ ] 清理 `node_modules`
- [ ] 清理 `.next`
- [ ] 重新安装依赖
- [ ] 测试启动 `pnpm dev`
- [ ] 验证浏览器访问

## 🎯 成功标志

修复成功后，您应该能够：
- ✅ 运行 `pnpm dev` 无错误
- ✅ 看到 "ready started server" 消息
- ✅ 访问 http://localhost:3000
- ✅ 页面正常加载和渲染
- ✅ Tailwind CSS 样式正常工作

## 📞 进一步支持

如果仍然遇到问题：

1. **检查环境**：
   - Node.js 版本：`node --version` (建议 16.0.0+)
   - pnpm 版本：`pnpm --version` (建议 8.0.0+)

2. **查看详细错误**：
   ```cmd
   pnpm dev --verbose
   ```

3. **清理所有缓存**：
   ```cmd
   pnpm store prune
   npm cache clean --force
   ```

4. **重新创建项目**：
   如果问题持续，考虑重新创建 Next.js 项目并迁移代码。
