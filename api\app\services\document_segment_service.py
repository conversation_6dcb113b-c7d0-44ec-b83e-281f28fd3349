"""
文档分段服务 - 增强版
处理文档内容读取、分段算法、向量化等核心功能
"""

import os
import hashlib
import re
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from loguru import logger

from app.services.file_parser_service import file_parser_service
from app.services.storage_service import storage_service


class DocumentSegmentService:
    """文档分段服务"""
    
    def __init__(self):
        self.file_parser = file_parser_service
        self.storage_service = storage_service
    
    def get_file_content(self, file_id: str) -> Dict[str, Any]:
        """
        获取文件内容
        
        Args:
            file_id: 文件ID
            
        Returns:
            包含文件内容和元数据的字典
        """
        try:
            # 从文件管理系统获取文件信息
            file_info = self.storage_service.get_file_info(file_id)
            if not file_info:
                return {
                    'success': False,
                    'error': f'文件不存在: {file_id}',
                    'content': '',
                    'metadata': {}
                }
            
            # 获取文件路径
            file_path = file_info.get('file_path') or file_info.get('path')
            if not file_path:
                return {
                    'success': False,
                    'error': '文件路径不存在',
                    'content': '',
                    'metadata': {}
                }
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': f'文件不存在于磁盘: {file_path}',
                    'content': '',
                    'metadata': {}
                }
            
            # 解析文件内容
            parse_result = self.file_parser.parse_file(file_path)
            
            if not parse_result.get('success', False):
                return {
                    'success': False,
                    'error': parse_result.get('error', '文件解析失败'),
                    'content': '',
                    'metadata': {}
                }
            
            return {
                'success': True,
                'content': parse_result.get('content', ''),
                'raw_content': parse_result.get('raw_content', ''),
                'format': parse_result.get('format', 'text'),
                'metadata': {
                    **file_info,
                    **parse_result.get('metadata', {})
                }
            }
            
        except Exception as e:
            logger.error(f"获取文件内容失败 {file_id}: {e}")
            return {
                'success': False,
                'error': f'获取文件内容失败: {str(e)}',
                'content': '',
                'metadata': {}
            }
    
    def segment_document(self, content: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        分段文档内容
        
        Args:
            content: 文档内容
            config: 分段配置
            
        Returns:
            分段结果列表
        """
        try:
            method = config.get('segment_method', 'paragraph')
            max_length = config.get('max_length', 500)
            overlap = config.get('overlap', 50)
            preserve_formatting = config.get('preserve_formatting', True)
            normalize_text = config.get('normalize_text', True)
            extract_keywords = config.get('extract_keywords', True)
            language = config.get('language', 'zh')
            
            # 预处理文本
            processed_content = self._preprocess_text(content, normalize_text, preserve_formatting)
            
            # 根据方法进行分段
            if method == 'paragraph':
                segments = self._segment_by_paragraph(processed_content, max_length, overlap)
            elif method == 'sentence':
                segments = self._segment_by_sentence(processed_content, max_length, overlap)
            elif method == 'fixed_length':
                segments = self._segment_by_fixed_length(processed_content, max_length, overlap)
            elif method == 'semantic':
                segments = self._segment_by_semantic(processed_content, max_length, overlap, language)
            else:
                raise ValueError(f"不支持的分段方法: {method}")
            
            # 后处理分段
            result_segments = []
            for i, segment_text in enumerate(segments):
                segment_data = {
                    'content': segment_text,
                    'segment_index': i,
                    'start_position': 0,  # 将在后续计算
                    'end_position': 0,    # 将在后续计算
                    'word_count': len(segment_text),
                    'sentence_count': self._count_sentences(segment_text, language),
                    'quality_score': self._calculate_quality_score(segment_text),
                    'readability_score': self._calculate_readability_score(segment_text, language),
                    'keywords': self._extract_keywords(segment_text, language) if extract_keywords else []
                }
                result_segments.append(segment_data)
            
            # 计算位置信息
            self._calculate_positions(result_segments, processed_content)
            
            logger.info(f"文档分段完成: {len(result_segments)} 个分段")
            return result_segments
            
        except Exception as e:
            logger.error(f"文档分段失败: {e}")
            raise
    
    def _preprocess_text(self, content: str, normalize_text: bool, preserve_formatting: bool) -> str:
        """预处理文本"""
        if not content:
            return ""
        
        # 移除HTML标签但保留内容
        text = re.sub(r'<[^>]+>', '', content)
        
        if normalize_text:
            # 标准化空白字符
            text = re.sub(r'\s+', ' ', text)
            # 移除多余的空行
            text = re.sub(r'\n\s*\n', '\n\n', text)
        
        if not preserve_formatting:
            # 移除格式化字符
            text = text.replace('\n', ' ').replace('\r', ' ')
            text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _segment_by_paragraph(self, content: str, max_length: int, overlap: int) -> List[str]:
        """按段落分段"""
        paragraphs = content.split('\n\n')
        segments = []
        current_segment = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前段落加上新段落超过最大长度
            if len(current_segment) + len(paragraph) + 2 > max_length and current_segment:
                segments.append(current_segment.strip())
                # 处理重叠
                if overlap > 0 and len(current_segment) > overlap:
                    current_segment = current_segment[-overlap:] + "\n\n" + paragraph
                else:
                    current_segment = paragraph
            else:
                if current_segment:
                    current_segment += "\n\n" + paragraph
                else:
                    current_segment = paragraph
        
        # 添加最后一个分段
        if current_segment.strip():
            segments.append(current_segment.strip())
        
        return segments
    
    def _segment_by_sentence(self, content: str, max_length: int, overlap: int) -> List[str]:
        """按句子分段"""
        # 简单的句子分割（可以使用更复杂的NLP库）
        sentences = re.split(r'[.!?。！？]+', content)
        segments = []
        current_segment = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 如果当前句子加上新句子超过最大长度
            if len(current_segment) + len(sentence) + 1 > max_length and current_segment:
                segments.append(current_segment.strip())
                # 处理重叠
                if overlap > 0 and len(current_segment) > overlap:
                    current_segment = current_segment[-overlap:] + " " + sentence
                else:
                    current_segment = sentence
            else:
                if current_segment:
                    current_segment += " " + sentence
                else:
                    current_segment = sentence
        
        # 添加最后一个分段
        if current_segment.strip():
            segments.append(current_segment.strip())
        
        return segments
    
    def _segment_by_fixed_length(self, content: str, max_length: int, overlap: int) -> List[str]:
        """按固定长度分段"""
        segments = []
        start = 0
        
        while start < len(content):
            end = start + max_length
            segment = content[start:end]
            
            # 尝试在单词边界分割
            if end < len(content) and not content[end].isspace():
                # 向前查找最近的空格
                space_pos = segment.rfind(' ')
                if space_pos > start + max_length // 2:  # 确保分段不会太短
                    end = start + space_pos
                    segment = content[start:end]
            
            segments.append(segment.strip())
            start = end - overlap if overlap > 0 else end
        
        return [s for s in segments if s.strip()]
    
    def _segment_by_semantic(self, content: str, max_length: int, overlap: int, language: str) -> List[str]:
        """语义分段（简化版本）"""
        # 这里使用段落分段作为语义分段的简化实现
        # 在实际项目中，可以集成更复杂的NLP模型
        return self._segment_by_paragraph(content, max_length, overlap)
    
    def _count_sentences(self, text: str, language: str) -> int:
        """计算句子数量"""
        if language == 'zh':
            sentences = re.split(r'[.!?。！？]+', text)
        else:
            sentences = re.split(r'[.!?]+', text)
        return len([s for s in sentences if s.strip()])
    
    def _calculate_quality_score(self, text: str) -> float:
        """计算质量评分"""
        if not text.strip():
            return 0.0
        
        score = 0.8  # 基础分数
        
        # 长度评分
        length = len(text)
        if 100 <= length <= 1000:
            score += 0.1
        elif length < 50:
            score -= 0.2
        
        # 完整性评分（是否以句号结尾）
        if text.strip().endswith(('.', '。', '!', '！', '?', '？')):
            score += 0.1
        
        return min(max(score, 0.0), 1.0)
    
    def _calculate_readability_score(self, text: str, language: str) -> float:
        """计算可读性评分"""
        if not text.strip():
            return 0.0
        
        # 简化的可读性评分
        words = text.split()
        sentences = self._count_sentences(text, language)
        
        if sentences == 0:
            return 0.5
        
        avg_words_per_sentence = len(words) / sentences
        
        # 理想的每句话单词数
        ideal_words = 15 if language == 'zh' else 20
        
        if avg_words_per_sentence <= ideal_words:
            score = 0.9
        else:
            score = max(0.3, 0.9 - (avg_words_per_sentence - ideal_words) * 0.02)
        
        return min(max(score, 0.0), 1.0)
    
    def _extract_keywords(self, text: str, language: str) -> List[str]:
        """提取关键词（简化版本）"""
        # 简化的关键词提取
        # 在实际项目中，可以使用jieba、NLTK等库
        
        # 移除标点符号
        clean_text = re.sub(r'[^\w\s]', '', text)
        words = clean_text.split()
        
        # 简单的词频统计
        word_freq = {}
        for word in words:
            if len(word) > 1:  # 忽略单字符
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 返回频率最高的前5个词
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        return [word for word, freq in keywords]
    
    def _calculate_positions(self, segments: List[Dict[str, Any]], content: str):
        """计算分段在原文中的位置"""
        current_pos = 0
        
        for segment in segments:
            segment_text = segment['content']
            start_pos = content.find(segment_text, current_pos)
            
            if start_pos != -1:
                segment['start_position'] = start_pos
                segment['end_position'] = start_pos + len(segment_text)
                current_pos = start_pos + len(segment_text)
            else:
                # 如果找不到精确匹配，使用估算位置
                segment['start_position'] = current_pos
                segment['end_position'] = current_pos + len(segment_text)
                current_pos += len(segment_text)


# 全局实例
document_segment_service = DocumentSegmentService()
