'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/Auth/AuthGuard';
import MainLayout from '@/components/Layout/MainLayout';
import { 
  Plus, 
  Settings, 
  Database, 
  HardDrive, 
  Cloud, 
  Server,
  Edit,
  Trash2,
  TestTube,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowLeft
} from 'lucide-react';

interface StorageConfig {
  id: number;
  name: string;
  storage_type: string;
  is_default: boolean;
  is_active: boolean;
  config: any;
  total_files: number;
  total_size: number;
  last_sync_at: string | null;
  created_at: string;
  updated_at: string;
}

export default function StorageManagementPage() {
  const router = useRouter();
  const [storages, setStorages] = useState<StorageConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [testingStorage, setTestingStorage] = useState<number | null>(null);

  // 获取存储配置列表
  const fetchStorages = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/storage/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStorages(data);
      }
    } catch (error) {
      console.error('获取存储配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStorages();
  }, []);

  // 测试存储连接
  const testStorage = async (storageId: number) => {
    setTestingStorage(storageId);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/storage/${storageId}/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      const result = await response.json();
      if (result.success) {
        alert('连接测试成功！');
      } else {
        alert(`连接测试失败: ${result.message}`);
      }
    } catch (error) {
      alert('连接测试失败');
    } finally {
      setTestingStorage(null);
    }
  };

  // 删除存储配置
  const deleteStorage = async (storageId: number, storageName: string) => {
    if (!confirm(`确定要删除存储配置 "${storageName}" 吗？`)) {
      return;
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/storage/${storageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        alert('删除成功');
        fetchStorages();
      } else {
        const error = await response.json();
        alert(`删除失败: ${error.detail}`);
      }
    } catch (error) {
      alert('删除失败');
    }
  };

  // 获取存储类型图标
  const getStorageIcon = (type: string) => {
    switch (type.toUpperCase()) {
      case 'LOCAL':
        return <HardDrive className="w-6 h-6" />;
      case 'FTP':
        return <Server className="w-6 h-6" />;
      case 'MINIO':
        return <Cloud className="w-6 h-6" />;
      default:
        return <Database className="w-6 h-6" />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
          <span className="text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard>
      <MainLayout>
        <div className="min-h-full bg-gradient-to-br from-blue-50 via-white to-purple-50">
          {/* 页面头部 */}
          <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50">
            <div className="mx-auto px-6 lg:px-12">
              <div className="flex items-center justify-between h-16">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => router.back()}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors group"
                    title="返回上一页"
                  >
                    <ArrowLeft className="w-5 h-5 text-gray-600 group-hover:text-gray-800" />
                  </button>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                      <Database className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h1 className="text-xl font-bold text-gray-900">存储管理</h1>
                      <p className="text-sm text-gray-500">管理文件存储配置</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => router.push('/file-manager')}
                    className="flex items-center space-x-2 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    <span>文件管理</span>
                  </button>
                  <button
                    onClick={() => router.push('/storage/create')}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl"
                  >
                    <Plus className="w-4 h-4" />
                    <span>添加存储</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 主要内容 */}
          <div className="mx-auto px-6 lg:px-12 py-8">
        {storages.length === 0 ? (
          <div className="text-center py-16">
            <div className="mx-auto w-24 h-24 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
              <Database className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无存储配置</h3>
            <p className="text-gray-500 mb-6">开始添加您的第一个存储配置</p>
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={() => router.push('/file-manager')}
                className="inline-flex items-center space-x-2 px-6 py-3 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span>返回文件管理</span>
              </button>
              <button
                onClick={() => router.push('/storage/create')}
                className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl"
              >
                <Plus className="w-5 h-5" />
                <span>添加存储配置</span>
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {storages.map((storage) => (
              <div
                key={storage.id}
                className="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 p-6 hover:shadow-lg transition-all duration-300"
              >
                {/* 存储头部 */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${
                      storage.storage_type === 'LOCAL' ? 'bg-green-100 text-green-600' :
                      storage.storage_type === 'FTP' ? 'bg-blue-100 text-blue-600' :
                      'bg-purple-100 text-purple-600'
                    }`}>
                      {getStorageIcon(storage.storage_type)}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{storage.name}</h3>
                      <p className="text-sm text-gray-500">{storage.storage_type}</p>
                    </div>
                  </div>
                  
                  {/* 状态标识 */}
                  <div className="flex items-center space-x-2">
                    {storage.is_default && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full">
                        默认
                      </span>
                    )}
                    <div className={`w-3 h-3 rounded-full ${
                      storage.is_active ? 'bg-green-400' : 'bg-gray-300'
                    }`} />
                  </div>
                </div>

                {/* 统计信息 */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-gray-50/50 rounded-lg">
                    <p className="text-2xl font-bold text-gray-900">{storage.total_files || 0}</p>
                    <p className="text-xs text-gray-500">文件数</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50/50 rounded-lg">
                    <p className="text-2xl font-bold text-gray-900">{formatFileSize(storage.total_size || 0)}</p>
                    <p className="text-xs text-gray-500">总大小</p>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => testStorage(storage.id)}
                      disabled={testingStorage === storage.id}
                      className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="测试连接"
                    >
                      {testingStorage === storage.id ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <TestTube className="w-4 h-4" />
                      )}
                    </button>
                    
                    <button
                      onClick={() => router.push(`/storage/edit/${storage.id}`)}
                      className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="编辑"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => deleteStorage(storage.id, storage.name)}
                      className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="删除"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <span className="text-xs text-gray-400">
                    {new Date(storage.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
