<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Celery任务队列管理功能展示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        .feature-card {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border: 2px solid #86efac;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #16a34a;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .feature-title::before {
            content: "⚡";
            margin-right: 10px;
            font-size: 1.1rem;
        }
        .feature-content {
            color: #047857;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        .tabs-demo {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
        }
        .tabs-header {
            display: flex;
            background: #e5e7eb;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 20px;
        }
        .tab-button {
            flex: 1;
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .tab-button.active {
            background: white;
            color: #3b82f6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .status-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .status-running {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
        }
        .status-stopped {
            border-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 5px 0;
        }
        .metric-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .config-item {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 10px;
        }
        .config-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 5px;
        }
        .config-value {
            font-weight: 500;
            color: #1f2937;
        }
        .button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .control-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            justify-content: center;
        }
        .btn-start {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .btn-restart {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }
        .btn-stop {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .highlight {
            background: rgba(59, 130, 246, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Celery任务队列管理</h1>
            <p class="subtitle">完整的异步任务处理服务管理功能</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3 class="feature-title">服务状态标签页</h3>
                <div class="feature-content">
                    <p><strong>功能特性：</strong></p>
                    <ul>
                        <li>整体状态显示：运行中/部分运行/已停止</li>
                        <li>服务详情：Worker、Beat、Flower状态</li>
                        <li>控制按钮：启动、停止、重启</li>
                        <li>自动启动开关配置</li>
                        <li>Redis连接状态监控</li>
                    </ul>
                </div>
            </div>

            <div class="feature-card">
                <h3 class="feature-title">性能指标标签页</h3>
                <div class="feature-content">
                    <p><strong>功能特性：</strong></p>
                    <ul>
                        <li>关键指标卡片：吞吐量、活跃任务、Worker数、Redis内存</li>
                        <li>队列状态：各队列任务数量和状态</li>
                        <li>性能统计：处理统计、运行时统计</li>
                        <li>实时更新：30秒自动刷新</li>
                        <li>成功率计算和显示</li>
                    </ul>
                </div>
            </div>

            <div class="feature-card">
                <h3 class="feature-title">配置管理标签页</h3>
                <div class="feature-content">
                    <p><strong>功能特性：</strong></p>
                    <ul>
                        <li>Redis配置：主机、端口、数据库、密码</li>
                        <li>Worker配置：并发数、预取倍数</li>
                        <li>任务配置：超时时间、重试次数</li>
                        <li>只读显示：当前配置参数查看</li>
                        <li>Flower监控端口配置</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tabs-demo">
            <h3 style="text-align: center; margin-bottom: 20px; color: #1e293b;">功能演示</h3>
            
            <div class="tabs-header">
                <button class="tab-button active" onclick="showTab('status')">⚡ 服务状态</button>
                <button class="tab-button" onclick="showTab('metrics')">📊 性能指标</button>
                <button class="tab-button" onclick="showTab('config')">⚙️ 配置管理</button>
            </div>

            <div id="status-tab" class="tab-content active">
                <h4 style="color: #1e293b; margin-bottom: 15px;">服务状态管理</h4>
                
                <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <p><strong>整体状态：</strong> <span class="highlight">已停止</span></p>
                    <p><strong>Redis连接：</strong> <span style="color: #ef4444;">断开</span></p>
                </div>

                <div class="status-grid">
                    <div class="status-item status-stopped">
                        <h5 style="margin: 0 0 10px 0; color: #dc2626;">Worker</h5>
                        <p style="margin: 0; font-size: 0.9rem;">状态: 已停止</p>
                        <p style="margin: 0; font-size: 0.8rem; color: #6b7280;">PID: -</p>
                    </div>
                    <div class="status-item status-stopped">
                        <h5 style="margin: 0 0 10px 0; color: #dc2626;">Beat</h5>
                        <p style="margin: 0; font-size: 0.9rem;">状态: 已停止</p>
                        <p style="margin: 0; font-size: 0.8rem; color: #6b7280;">PID: -</p>
                    </div>
                    <div class="status-item status-stopped">
                        <h5 style="margin: 0 0 10px 0; color: #dc2626;">Flower</h5>
                        <p style="margin: 0; font-size: 0.9rem;">状态: 已停止</p>
                        <p style="margin: 0; font-size: 0.8rem; color: #6b7280;">PID: -</p>
                    </div>
                </div>

                <div class="control-buttons">
                    <button class="button btn-start" onclick="simulateAction('启动')">▶️ 启动</button>
                    <button class="button btn-restart" onclick="simulateAction('重启')">🔄 重启</button>
                    <button class="button btn-stop" onclick="simulateAction('停止')">⏹️ 停止</button>
                </div>
            </div>

            <div id="metrics-tab" class="tab-content">
                <h4 style="color: #1e293b; margin-bottom: 15px;">性能指标监控</h4>
                
                <div class="metrics-grid">
                    <div class="metric-card" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <div class="metric-value">0.0</div>
                        <div class="metric-label">任务吞吐量 (任务/秒)</div>
                    </div>
                    <div class="metric-card" style="background: linear-gradient(135deg, #3b82f6, #2563eb);">
                        <div class="metric-value">0</div>
                        <div class="metric-label">活跃任务 (个)</div>
                    </div>
                    <div class="metric-card" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <div class="metric-value">0/4</div>
                        <div class="metric-label">活跃Worker (个)</div>
                    </div>
                    <div class="metric-card" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <div class="metric-value">15.6</div>
                        <div class="metric-label">Redis内存 (MB)</div>
                    </div>
                </div>

                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5 style="margin: 0 0 10px 0; color: #1e293b;">队列状态</h5>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                        <div style="background: #f3f4f6; padding: 10px; border-radius: 6px;">
                            <strong>default:</strong> <span style="color: #10b981;">0 任务</span>
                        </div>
                        <div style="background: #f3f4f6; padding: 10px; border-radius: 6px;">
                            <strong>high_priority:</strong> <span style="color: #10b981;">0 任务</span>
                        </div>
                        <div style="background: #f3f4f6; padding: 10px; border-radius: 6px;">
                            <strong>low_priority:</strong> <span style="color: #10b981;">0 任务</span>
                        </div>
                    </div>
                </div>
            </div>

            <div id="config-tab" class="tab-content">
                <h4 style="color: #1e293b; margin-bottom: 15px;">配置参数查看</h4>
                
                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <h5 style="margin: 0 0 10px 0; color: #1e293b;">Redis配置</h5>
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-label">主机地址</div>
                            <div class="config-value">192.168.50.142</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">端口</div>
                            <div class="config-value">6379</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">数据库</div>
                            <div class="config-value">10</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">密码</div>
                            <div class="config-value">未设置</div>
                        </div>
                    </div>
                </div>

                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <h5 style="margin: 0 0 10px 0; color: #1e293b;">Worker配置</h5>
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-label">并发数</div>
                            <div class="config-value">4</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">预取倍数</div>
                            <div class="config-value">1</div>
                        </div>
                    </div>
                </div>

                <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px;">
                    <h5 style="margin: 0 0 10px 0; color: #1e293b;">任务配置</h5>
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-label">软超时(秒)</div>
                            <div class="config-value">300</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">硬超时(秒)</div>
                            <div class="config-value">600</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">最大重试次数</div>
                            <div class="config-value">3</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.2); border-radius: 10px; padding: 20px; margin: 20px 0;">
            <h4 style="margin: 0 0 15px 0; color: #1e40af; font-size: 1.1rem;">🎯 功能说明</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li style="margin: 8px 0; color: #1e3a8a;"><strong>模拟数据：</strong>当前显示的是模拟数据，后端API接口开发中</li>
                <li style="margin: 8px 0; color: #1e3a8a;"><strong>实时更新：</strong>状态每10秒刷新，指标每30秒刷新</li>
                <li style="margin: 8px 0; color: #1e3a8a;"><strong>控制操作：</strong>启动、停止、重启功能已实现前端逻辑</li>
                <li style="margin: 8px 0; color: #1e3a8a;"><strong>配置管理：</strong>当前为只读模式，编辑功能开发中</li>
                <li style="margin: 8px 0; color: #1e3a8a;"><strong>日志查看：</strong>支持查看各服务的运行日志</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="button" onclick="showImplementation()">
                💻 查看实现细节
            </button>
            <button class="button" onclick="confirmFeatures()">
                ✅ 确认功能完整
            </button>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有按钮的活跃状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        function simulateAction(action) {
            alert(`🎯 ${action}操作演示\n\n这是Celery服务${action}功能的演示。\n\n实际使用时会：\n• 发送控制命令到后端\n• 更新服务状态\n• 显示操作结果\n• 刷新监控数据`);
        }

        function showImplementation() {
            alert(`💻 Celery管理功能实现细节\n\n前端组件：\n• CeleryControl.tsx - 主控制组件\n• 三个标签页：状态、指标、配置\n• 实时数据刷新机制\n• 模拟数据支持\n\n后端API（开发中）：\n• /api/v1/celery/status - 状态查询\n• /api/v1/celery/control - 服务控制\n• /api/v1/celery/metrics - 性能指标\n• /api/v1/celery/config - 配置管理\n\n当前状态：\n• 前端界面完整\n• 模拟数据展示\n• 等待后端API开发`);
        }

        function confirmFeatures() {
            alert(`✅ Celery管理功能确认\n\n已实现功能：\n✅ 服务状态标签页\n✅ 性能指标标签页\n✅ 配置管理标签页\n✅ 控制按钮（启动/停止/重启）\n✅ 自动启动开关\n✅ 实时数据刷新\n✅ 模拟数据支持\n✅ 响应式设计\n\n所有前端功能已完整实现！`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Celery管理功能演示页面已加载');
        });
    </script>
</body>
</html>
