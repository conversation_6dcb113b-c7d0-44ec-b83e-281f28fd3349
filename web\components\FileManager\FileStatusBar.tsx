'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Folder, 
  File, 
  HardDrive,
  Wifi,
  WifiOff
} from 'lucide-react';

interface FileStatusBarProps {
  selectedFiles: string[];
  totalFiles: number;
  currentPath: string;
  storageInfo?: {
    name: string;
    type: string;
    connected: boolean;
    totalSpace?: string;
    usedSpace?: string;
    freeSpace?: string;
  };
}

const FileStatusBar: React.FC<FileStatusBarProps> = ({
  selectedFiles,
  totalFiles,
  currentPath,
  storageInfo
}) => {
  return (
    <div className="bg-white border-t border-gray-200 px-4 py-2">
      <div className="flex items-center justify-between text-sm text-gray-600">
        {/* 左侧：选择和路径信息 */}
        <div className="flex items-center space-x-6">
          {/* 选择状态 */}
          <div className="flex items-center space-x-2">
            {selectedFiles.length > 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center space-x-1"
              >
                <File className="w-4 h-4" />
                <span>已选择 {selectedFiles.length} 项</span>
              </motion.div>
            ) : (
              <div className="flex items-center space-x-1">
                <Folder className="w-4 h-4" />
                <span>{totalFiles} 项</span>
              </div>
            )}
          </div>

          {/* 当前路径 */}
          <div className="flex items-center space-x-1 text-gray-500">
            <span>路径:</span>
            <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
              {currentPath || '/'}
            </span>
          </div>
        </div>

        {/* 右侧：存储信息 */}
        <div className="flex items-center space-x-6">
          {/* 存储连接状态 */}
          {storageInfo && (
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                {storageInfo.connected ? (
                  <Wifi className="w-4 h-4 text-green-500" />
                ) : (
                  <WifiOff className="w-4 h-4 text-red-500" />
                )}
                <span className={storageInfo.connected ? 'text-green-600' : 'text-red-600'}>
                  {storageInfo.name}
                </span>
              </div>
              
              <span className="text-gray-400">|</span>
              
              <span className="text-gray-500">
                {storageInfo.type.toUpperCase()}
              </span>
            </div>
          )}

          {/* 存储空间信息 */}
          {storageInfo?.totalSpace && (
            <div className="flex items-center space-x-2">
              <HardDrive className="w-4 h-4" />
              <div className="flex items-center space-x-1">
                <span>可用:</span>
                <span className="font-medium">{storageInfo.freeSpace}</span>
                <span className="text-gray-400">/</span>
                <span>{storageInfo.totalSpace}</span>
              </div>
            </div>
          )}

          {/* 同步状态指示器 */}
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-green-600">已同步</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileStatusBar;
