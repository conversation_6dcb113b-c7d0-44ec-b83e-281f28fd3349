/**
 * 文件类型检查工具函数
 */

// 支持分段的文件扩展名
export const SEGMENTABLE_EXTENSIONS = [
  'txt', 'md', 'markdown', 'doc', 'docx', 'pdf', 'ppt', 'pptx', 'xls', 'xlsx'
];

// 支持在线编辑的文件扩展名
export const EDITABLE_EXTENSIONS = [
  'txt', 'md', 'markdown', 'doc', 'docx', 'pdf', 'ppt', 'pptx', 'xls', 'xlsx'
];

// 图片文件扩展名
export const IMAGE_EXTENSIONS = [
  'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'
];

// 视频文件扩展名
export const VIDEO_EXTENSIONS = [
  'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'
];

// 音频文件扩展名
export const AUDIO_EXTENSIONS = [
  'mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'
];

// Office文件扩展名
export const OFFICE_EXTENSIONS = [
  'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'
];

/**
 * 从文件名或文件对象中提取扩展名
 */
export function getFileExtension(file: any): string {
  let extension = '';
  
  // 尝试从 file_extension 字段获取
  if (file.file_extension) {
    extension = file.file_extension;
  }
  // 尝试从 file_name 字段获取
  else if (file.file_name) {
    const parts = file.file_name.split('.');
    if (parts.length > 1) {
      extension = parts[parts.length - 1];
    }
  }
  // 如果是字符串，直接处理
  else if (typeof file === 'string') {
    const parts = file.split('.');
    if (parts.length > 1) {
      extension = parts[parts.length - 1];
    }
  }
  
  // 移除可能的点号前缀并转为小写
  extension = extension.replace(/^\./, '').toLowerCase();
  
  return extension;
}

/**
 * 检查文件是否支持分段
 */
export function isSegmentableFile(file: any): boolean {
  if (file.is_directory) return false;
  
  const extension = getFileExtension(file);
  const isSupported = SEGMENTABLE_EXTENSIONS.includes(extension);
  
  console.log(`检查文件 ${file.file_name || file} 分段支持: 扩展名=${extension}, 支持=${isSupported}`);
  
  return isSupported;
}

/**
 * 检查文件是否支持在线编辑
 */
export function isEditableFile(file: any): boolean {
  if (file.is_directory) return false;
  
  const extension = getFileExtension(file);
  return EDITABLE_EXTENSIONS.includes(extension);
}

/**
 * 检查是否为图片文件
 */
export function isImageFile(file: any): boolean {
  if (file.is_directory) return false;
  
  const extension = getFileExtension(file);
  return IMAGE_EXTENSIONS.includes(extension);
}

/**
 * 检查是否为视频文件
 */
export function isVideoFile(file: any): boolean {
  if (file.is_directory) return false;
  
  const extension = getFileExtension(file);
  return VIDEO_EXTENSIONS.includes(extension);
}

/**
 * 检查是否为音频文件
 */
export function isAudioFile(file: any): boolean {
  if (file.is_directory) return false;
  
  const extension = getFileExtension(file);
  return AUDIO_EXTENSIONS.includes(extension);
}

/**
 * 检查是否为Office文件
 */
export function isOfficeFile(file: any): boolean {
  if (file.is_directory) return false;
  
  const extension = getFileExtension(file);
  return OFFICE_EXTENSIONS.includes(extension);
}

/**
 * 获取文件类型描述
 */
export function getFileTypeDescription(file: any): string {
  if (file.is_directory) return '文件夹';
  
  const extension = getFileExtension(file);
  
  if (IMAGE_EXTENSIONS.includes(extension)) return '图片文件';
  if (VIDEO_EXTENSIONS.includes(extension)) return '视频文件';
  if (AUDIO_EXTENSIONS.includes(extension)) return '音频文件';
  if (OFFICE_EXTENSIONS.includes(extension)) return 'Office文档';
  if (['txt', 'md', 'markdown'].includes(extension)) return '文本文档';
  if (extension === 'pdf') return 'PDF文档';
  
  return '其他文件';
}

/**
 * 获取文件图标类名（用于显示图标）
 */
export function getFileIconClass(file: any): string {
  if (file.is_directory) return 'folder';
  
  const extension = getFileExtension(file);
  
  if (IMAGE_EXTENSIONS.includes(extension)) return 'image';
  if (VIDEO_EXTENSIONS.includes(extension)) return 'video';
  if (AUDIO_EXTENSIONS.includes(extension)) return 'audio';
  if (['doc', 'docx'].includes(extension)) return 'word';
  if (['xls', 'xlsx'].includes(extension)) return 'excel';
  if (['ppt', 'pptx'].includes(extension)) return 'powerpoint';
  if (extension === 'pdf') return 'pdf';
  if (['txt', 'md', 'markdown'].includes(extension)) return 'text';
  
  return 'file';
}

/**
 * 检查文件是否支持预览
 */
export function isPreviewableFile(file: any): boolean {
  if (file.is_directory) return false;
  
  const extension = getFileExtension(file);
  
  // 支持预览的文件类型
  const previewableExtensions = [
    ...IMAGE_EXTENSIONS,
    'txt', 'md', 'markdown', 'pdf'
  ];
  
  return previewableExtensions.includes(extension);
}

/**
 * 获取支持的文件类型列表（用于显示给用户）
 */
export function getSupportedFileTypes(): {
  segmentable: string[];
  editable: string[];
  previewable: string[];
} {
  return {
    segmentable: SEGMENTABLE_EXTENSIONS,
    editable: EDITABLE_EXTENSIONS,
    previewable: [...IMAGE_EXTENSIONS, 'txt', 'md', 'markdown', 'pdf']
  };
}
