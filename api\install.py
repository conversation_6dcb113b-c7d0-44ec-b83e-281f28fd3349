#!/usr/bin/env python3
"""
AI知识库安装脚本
自动检测系统环境并安装合适的依赖
"""

import sys
import subprocess
import platform
import os
from pathlib import Path


def run_command(command: list, check: bool = True) -> subprocess.CompletedProcess:
    """运行命令"""
    print(f"Running: {' '.join(command)}")
    return subprocess.run(command, check=check, capture_output=True, text=True)


def check_uv_installed() -> bool:
    """检查 uv 是否已安装"""
    try:
        result = run_command(["uv", "--version"], check=False)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def install_uv():
    """安装 uv 包管理器"""
    print("📦 Installing uv package manager...")
    
    system = platform.system().lower()
    
    if system == "windows":
        # Windows 使用 PowerShell 安装
        command = [
            "powershell", "-c",
            "irm https://astral.sh/uv/install.ps1 | iex"
        ]
    else:
        # Unix-like 系统使用 curl
        command = [
            "sh", "-c",
            "curl -LsSf https://astral.sh/uv/install.sh | sh"
        ]
    
    try:
        run_command(command)
        print("✅ uv installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install uv: {e}")
        print("Please install uv manually: https://github.com/astral-sh/uv")
        sys.exit(1)


def detect_database_preference() -> str:
    """检测用户的数据库偏好"""
    print("\n🗄️ Database Selection")
    print("Please choose your preferred database:")
    print("1. SQLite (Default - No setup required)")
    print("2. PostgreSQL (Recommended for production)")
    print("3. MySQL")
    print("4. Oracle")
    print("5. Install all database drivers")
    
    while True:
        choice = input("Enter your choice (1-5) [1]: ").strip() or "1"
        
        if choice == "1":
            return "sqlite"
        elif choice == "2":
            return "postgresql"
        elif choice == "3":
            return "mysql"
        elif choice == "4":
            return "oracle"
        elif choice == "5":
            return "all-databases"
        else:
            print("Invalid choice. Please enter 1-5.")


def check_visual_cpp_tools() -> bool:
    """检查 Visual C++ Build Tools 是否已安装 (Windows)"""
    if platform.system().lower() != "windows":
        return True
    
    try:
        # 检查常见的 Visual Studio 安装路径
        vs_paths = [
            r"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools",
            r"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools",
            r"C:\Program Files\Microsoft Visual Studio\2019\BuildTools",
            r"C:\Program Files\Microsoft Visual Studio\2022\BuildTools",
        ]
        
        for path in vs_paths:
            if os.path.exists(path):
                return True
        
        # 检查环境变量
        if os.environ.get("VS140COMNTOOLS") or os.environ.get("VS150COMNTOOLS"):
            return True
            
        return False
    except Exception:
        return False


def install_dependencies(database_type: str):
    """安装项目依赖"""
    print(f"\n📦 Installing dependencies for {database_type}...")
    
    try:
        # 安装基础依赖
        print("Installing base dependencies...")
        run_command(["uv", "sync"])
        
        # 安装数据库特定依赖
        if database_type != "sqlite":
            print(f"Installing {database_type} dependencies...")
            
            if database_type == "oracle":
                # 检查 Windows 上的编译工具
                if platform.system().lower() == "windows" and not check_visual_cpp_tools():
                    print("\n⚠️  Warning: Oracle cx_Oracle driver requires Visual C++ Build Tools")
                    print("📥 Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/")
                    
                    choice = input("Continue with oracledb driver only? (y/n) [y]: ").strip().lower()
                    if choice in ("", "y", "yes"):
                        # 只安装 oracledb，不安装 cx_Oracle
                        run_command(["uv", "add", "oracledb>=1.4.0"])
                    else:
                        print("Please install Visual C++ Build Tools and run this script again.")
                        sys.exit(1)
                else:
                    run_command(["uv", "add", "--optional", database_type])
            else:
                run_command(["uv", "add", "--optional", database_type])
        
        print("✅ Dependencies installed successfully")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        if "cx-oracle" in str(e).lower():
            print("\n💡 Tip: If you're on Windows and getting cx-oracle errors:")
            print("1. Install Visual C++ Build Tools")
            print("2. Or use 'uv add oracledb' instead of cx-oracle")
        sys.exit(1)


def create_env_file(database_type: str):
    """创建环境配置文件"""
    print(f"\n⚙️ Creating environment configuration for {database_type}...")
    
    env_content = {
        "sqlite": """# AI知识库 - 开发环境配置
ENVIRONMENT="development"
DEBUG=true
HOST="127.0.0.1"
PORT=8000
LOG_LEVEL="DEBUG"

# 应用配置
APP_NAME="AI知识库 API"
APP_VERSION="0.1.0"
APP_DESCRIPTION="AI知识库 - 基于人工智能的智能知识管理与检索系统"

# 安全配置
SECRET_KEY="dev-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# SQLite 数据库配置
DATABASE_URL="sqlite+aiosqlite:///./ai_knowledge_base.db"
DATABASE_TYPE="sqlite"
DATABASE_ECHO=true

# Redis 配置
REDIS_URL="redis://localhost:6379/0"

# 插件配置
ENABLED_PLUGINS="auth,file_manager,rag_engine"

# CORS 配置
CORS_ORIGINS="*"
""",
        
        "postgresql": """# AI知识库 - PostgreSQL 配置
ENVIRONMENT="development"
DEBUG=true
HOST="127.0.0.1"
PORT=8000
LOG_LEVEL="DEBUG"

# 应用配置
APP_NAME="AI知识库 API"
APP_VERSION="0.1.0"
APP_DESCRIPTION="AI知识库 - 基于人工智能的智能知识管理与检索系统"

# 安全配置
SECRET_KEY="dev-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# PostgreSQL 数据库配置
DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/ai_knowledge_base"
DATABASE_TYPE="postgresql"
DATABASE_ECHO=true
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis 配置
REDIS_URL="redis://localhost:6379/0"

# 插件配置
ENABLED_PLUGINS="auth,file_manager,rag_engine"

# CORS 配置
CORS_ORIGINS="*"
""",
        
        "mysql": """# AI知识库 - MySQL 配置
ENVIRONMENT="development"
DEBUG=true
HOST="127.0.0.1"
PORT=8000
LOG_LEVEL="DEBUG"

# 应用配置
APP_NAME="AI知识库 API"
APP_VERSION="0.1.0"
APP_DESCRIPTION="AI知识库 - 基于人工智能的智能知识管理与检索系统"

# 安全配置
SECRET_KEY="dev-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# MySQL 数据库配置
DATABASE_URL="mysql+aiomysql://username:password@localhost:3306/ai_knowledge_base"
DATABASE_TYPE="mysql"
DATABASE_ECHO=true
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis 配置
REDIS_URL="redis://localhost:6379/0"

# 插件配置
ENABLED_PLUGINS="auth,file_manager,rag_engine"

# CORS 配置
CORS_ORIGINS="*"
""",
        
        "oracle": """# AI知识库 - Oracle 配置
ENVIRONMENT="development"
DEBUG=true
HOST="127.0.0.1"
PORT=8000
LOG_LEVEL="DEBUG"

# 应用配置
APP_NAME="AI知识库 API"
APP_VERSION="0.1.0"
APP_DESCRIPTION="AI知识库 - 基于人工智能的智能知识管理与检索系统"

# 安全配置
SECRET_KEY="dev-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Oracle 数据库配置
DATABASE_URL="oracle+oracledb://username:password@localhost:1521/ai_knowledge_base"
DATABASE_TYPE="oracle"
DATABASE_ECHO=true
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis 配置
REDIS_URL="redis://localhost:6379/0"

# 插件配置
ENABLED_PLUGINS="auth,file_manager,rag_engine"

# CORS 配置
CORS_ORIGINS="*"
""",
        
        "all-databases": """# AI知识库 - 多数据库支持配置
ENVIRONMENT="development"
DEBUG=true
HOST="127.0.0.1"
PORT=8000
LOG_LEVEL="DEBUG"

# 应用配置
APP_NAME="AI知识库 API"
APP_VERSION="0.1.0"
APP_DESCRIPTION="AI知识库 - 基于人工智能的智能知识管理与检索系统"

# 安全配置
SECRET_KEY="dev-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置 - 选择一个取消注释
# SQLite (默认)
DATABASE_URL="sqlite+aiosqlite:///./ai_knowledge_base.db"
DATABASE_TYPE="sqlite"

# PostgreSQL
# DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/ai_knowledge_base"
# DATABASE_TYPE="postgresql"

# MySQL
# DATABASE_URL="mysql+aiomysql://username:password@localhost:3306/ai_knowledge_base"
# DATABASE_TYPE="mysql"

# Oracle
# DATABASE_URL="oracle+oracledb://username:password@localhost:1521/ai_knowledge_base"
# DATABASE_TYPE="oracle"

DATABASE_ECHO=true
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis 配置
REDIS_URL="redis://localhost:6379/0"

# 插件配置
ENABLED_PLUGINS="auth,file_manager,rag_engine"

# CORS 配置
CORS_ORIGINS="*"
"""
    }
    
    env_file = Path(".env.local")
    env_file.write_text(env_content[database_type])
    print(f"✅ Created {env_file}")


def main():
    """主函数"""
    print("🚀 AI知识库安装向导")
    print("=" * 50)
    
    # 检查 uv 是否已安装
    if not check_uv_installed():
        install_uv()
    else:
        print("✅ uv is already installed")
    
    # 检测数据库偏好
    database_type = detect_database_preference()
    
    # 安装依赖
    install_dependencies(database_type)
    
    # 创建环境配置文件
    create_env_file(database_type)
    
    print("\n🎉 Installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Review and update .env.local with your database credentials")
    print("2. Initialize the database: uv run ai-knowledge-api init-db")
    print("3. Start the server: uv run ai-knowledge-api run")
    print("\n📚 Documentation:")
    print("- Database guide: docs/database-guide.md")
    print("- API docs: http://localhost:8000/docs (after starting)")


if __name__ == "__main__":
    main()
