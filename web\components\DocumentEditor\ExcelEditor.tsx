'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Trash2,
  Copy,
  Scissors,
  Clipboard,
  Bold,
  Italic,
  AlignLeft,
  AlignCenter,
  AlignRight,
  BarChart3,
  PieChart,
  TrendingUp,
  Filter,
  SortAsc,
  SortDesc,
  Sigma,
  Percent,
  DollarSign,
  Save,
  Download,
  FileSpreadsheet,
  Grid3X3,
  Merge,
  Split,
  Eye,
  Edit3,
  Printer
} from 'lucide-react';

interface Cell {
  value: string;
  formula?: string;
  format?: {
    bold?: boolean;
    italic?: boolean;
    textAlign?: 'left' | 'center' | 'right';
    backgroundColor?: string;
    textColor?: string;
    fontSize?: number;
    numberFormat?: 'general' | 'number' | 'currency' | 'percentage' | 'date';
  };
  merged?: { rowSpan: number; colSpan: number };
}

interface Worksheet {
  id: string;
  name: string;
  data: Cell[][];
  selectedRange?: {
    startRow: number;
    startCol: number;
    endRow: number;
    endCol: number;
  };
}

interface ExcelEditorProps {
  fileId: string;
  fileName: string;
  worksheets: Worksheet[];
  isReadOnly?: boolean;
  onSave?: (worksheets: Worksheet[]) => Promise<void>;
  onClose?: () => void;
}

const ExcelEditor: React.FC<ExcelEditorProps> = ({
  fileId,
  fileName,
  worksheets: initialWorksheets,
  isReadOnly = false,
  onSave,
  onClose
}) => {
  const [worksheets, setWorksheets] = useState<Worksheet[]>(initialWorksheets);
  const [activeWorksheetIndex, setActiveWorksheetIndex] = useState(0);
  const [selectedCell, setSelectedCell] = useState<{ row: number; col: number } | null>(null);
  const [selectedRange, setSelectedRange] = useState<{ startRow: number; startCol: number; endRow: number; endCol: number } | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [selectedCols, setSelectedCols] = useState<number[]>([]);
  const [editingCell, setEditingCell] = useState<{ row: number; col: number } | null>(null);
  const [formulaBarValue, setFormulaBarValue] = useState('');
  const [zoom, setZoom] = useState(100);
  const [columnWidths, setColumnWidths] = useState<{ [key: number]: number }>({});
  const [rowHeights, setRowHeights] = useState<{ [key: number]: number }>({});
  const [isResizing, setIsResizing] = useState<{ type: 'row' | 'col'; index: number } | null>(null);
  const [clipboard, setClipboard] = useState<{ data: Cell[][]; type: 'copy' | 'cut' } | null>(null);

  const activeWorksheet = worksheets[activeWorksheetIndex];
  const tableRef = useRef<HTMLTableElement>(null);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'c':
            e.preventDefault();
            handleCopy();
            break;
          case 'x':
            e.preventDefault();
            handleCut();
            break;
          case 'v':
            e.preventDefault();
            handlePaste();
            break;
          case 'z':
            e.preventDefault();
            // TODO: 撤销功能
            break;
          case 'y':
            e.preventDefault();
            // TODO: 重做功能
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [clipboard, selectedCell, selectedRows, selectedCols]);

  // 生成列标题 (A, B, C, ...)
  const getColumnLabel = (index: number): string => {
    let result = '';
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result;
      index = Math.floor(index / 26) - 1;
    }
    return result;
  };

  // 确保工作表有足够的行和列
  const ensureWorksheetSize = (worksheet: Worksheet, minRows: number, minCols: number) => {
    const newData = [...worksheet.data];
    
    // 确保有足够的行
    while (newData.length < minRows) {
      newData.push([]);
    }
    
    // 确保每行有足够的列
    for (let i = 0; i < newData.length; i++) {
      while (newData[i].length < minCols) {
        newData[i].push({ value: '' });
      }
    }
    
    return { ...worksheet, data: newData };
  };

  // 更新单元格值
  const updateCell = (row: number, col: number, updates: Partial<Cell>) => {
    const newWorksheets = [...worksheets];
    const worksheet = ensureWorksheetSize(newWorksheets[activeWorksheetIndex], row + 1, col + 1);
    
    worksheet.data[row][col] = {
      ...worksheet.data[row][col],
      ...updates
    };
    
    newWorksheets[activeWorksheetIndex] = worksheet;
    setWorksheets(newWorksheets);
  };

  // 处理单元格点击
  const handleCellClick = (row: number, col: number, event?: React.MouseEvent) => {
    if (event?.ctrlKey || event?.metaKey) {
      // Ctrl+点击：多选
      if (selectedRows.includes(row)) {
        setSelectedRows(selectedRows.filter(r => r !== row));
      } else if (selectedCols.includes(col)) {
        setSelectedCols(selectedCols.filter(c => c !== col));
      } else {
        setSelectedCell({ row, col });
      }
    } else {
      // 普通点击：单选
      setSelectedCell({ row, col });
      setSelectedRows([]);
      setSelectedCols([]);
      setSelectedRange(null);
    }
    setEditingCell(null);

    const cell = activeWorksheet.data[row]?.[col];
    setFormulaBarValue(cell?.formula || cell?.value || '');
  };

  // 处理行头点击 - 选择整行
  const handleRowHeaderClick = (row: number, event?: React.MouseEvent) => {
    if (event?.ctrlKey || event?.metaKey) {
      // Ctrl+点击：多选行
      if (selectedRows.includes(row)) {
        setSelectedRows(selectedRows.filter(r => r !== row));
      } else {
        setSelectedRows([...selectedRows, row]);
      }
    } else if (event?.shiftKey && selectedRows.length > 0) {
      // Shift+点击：范围选择
      const lastRow = selectedRows[selectedRows.length - 1];
      const start = Math.min(lastRow, row);
      const end = Math.max(lastRow, row);
      const newRows = Array.from({ length: end - start + 1 }, (_, i) => start + i);
      setSelectedRows(newRows);
    } else {
      // 普通点击：选择单行
      setSelectedRows([row]);
    }
    setSelectedCell(null);
    setSelectedCols([]);
    setSelectedRange(null);
  };

  // 处理列头点击 - 选择整列
  const handleColHeaderClick = (col: number, event?: React.MouseEvent) => {
    if (event?.ctrlKey || event?.metaKey) {
      // Ctrl+点击：多选列
      if (selectedCols.includes(col)) {
        setSelectedCols(selectedCols.filter(c => c !== col));
      } else {
        setSelectedCols([...selectedCols, col]);
      }
    } else if (event?.shiftKey && selectedCols.length > 0) {
      // Shift+点击：范围选择
      const lastCol = selectedCols[selectedCols.length - 1];
      const start = Math.min(lastCol, col);
      const end = Math.max(lastCol, col);
      const newCols = Array.from({ length: end - start + 1 }, (_, i) => start + i);
      setSelectedCols(newCols);
    } else {
      // 普通点击：选择单列
      setSelectedCols([col]);
    }
    setSelectedCell(null);
    setSelectedRows([]);
    setSelectedRange(null);
  };

  // 处理单元格双击
  const handleCellDoubleClick = (row: number, col: number) => {
    if (isReadOnly) return;
    setEditingCell({ row, col });
    setSelectedCell({ row, col });
  };

  // 处理单元格值变化
  const handleCellValueChange = (row: number, col: number, value: string) => {
    const isFormula = value.startsWith('=');
    updateCell(row, col, {
      value: isFormula ? '' : value,
      formula: isFormula ? value : undefined
    });
  };

  // 添加新工作表
  const addWorksheet = () => {
    const newWorksheet: Worksheet = {
      id: `sheet-${Date.now()}`,
      name: `Sheet${worksheets.length + 1}`,
      data: Array(20).fill(null).map(() => Array(10).fill(null).map(() => ({ value: '' })))
    };
    setWorksheets([...worksheets, newWorksheet]);
    setActiveWorksheetIndex(worksheets.length);
  };

  // 删除工作表
  const deleteWorksheet = (index: number) => {
    if (worksheets.length <= 1) return;
    
    const newWorksheets = worksheets.filter((_, i) => i !== index);
    setWorksheets(newWorksheets);
    
    if (activeWorksheetIndex >= newWorksheets.length) {
      setActiveWorksheetIndex(newWorksheets.length - 1);
    } else if (activeWorksheetIndex > index) {
      setActiveWorksheetIndex(activeWorksheetIndex - 1);
    }
  };

  // 格式化工具栏按钮
  const FormatButton: React.FC<{
    icon: React.ReactNode;
    title: string;
    active?: boolean;
    onClick: () => void;
  }> = ({ icon, title, active, onClick }) => (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className={`p-2 rounded transition-colors ${
        active ? 'bg-blue-500 text-white' : 'text-gray-700 hover:bg-gray-100'
      }`}
      title={title}
    >
      {icon}
    </motion.button>
  );

  // 应用格式到选中单元格、行或列
  const applyFormat = (format: Partial<Cell['format']>) => {
    if (selectedRows.length > 0) {
      // 应用到选中的行
      selectedRows.forEach(row => {
        for (let col = 0; col < 26; col++) {
          const currentCell = activeWorksheet.data[row]?.[col];
          updateCell(row, col, {
            format: { ...currentCell?.format, ...format }
          });
        }
      });
    } else if (selectedCols.length > 0) {
      // 应用到选中的列
      selectedCols.forEach(col => {
        for (let row = 0; row < 50; row++) {
          const currentCell = activeWorksheet.data[row]?.[col];
          updateCell(row, col, {
            format: { ...currentCell?.format, ...format }
          });
        }
      });
    } else if (selectedCell) {
      // 应用到选中的单元格
      const currentCell = activeWorksheet.data[selectedCell.row]?.[selectedCell.col];
      updateCell(selectedCell.row, selectedCell.col, {
        format: { ...currentCell?.format, ...format }
      });
    }
  };

  // 处理列宽调整
  const handleColumnResize = (colIndex: number, newWidth: number) => {
    setColumnWidths(prev => ({
      ...prev,
      [colIndex]: Math.max(60, newWidth) // 最小宽度60px
    }));
  };

  // 处理行高调整
  const handleRowResize = (rowIndex: number, newHeight: number) => {
    setRowHeights(prev => ({
      ...prev,
      [rowIndex]: Math.max(24, newHeight) // 最小高度24px
    }));
  };

  // 开始调整大小
  const startResize = (type: 'row' | 'col', index: number, event: React.MouseEvent) => {
    event.preventDefault();
    setIsResizing({ type, index });

    const startPos = type === 'row' ? event.clientY : event.clientX;
    const startSize = type === 'row'
      ? (rowHeights[index] || 32)
      : (columnWidths[index] || 80);

    const handleMouseMove = (e: MouseEvent) => {
      const currentPos = type === 'row' ? e.clientY : e.clientX;
      const delta = currentPos - startPos;
      const newSize = startSize + delta;

      if (type === 'row') {
        handleRowResize(index, newSize);
      } else {
        handleColumnResize(index, newSize);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 复制功能
  const handleCopy = () => {
    const data: Cell[][] = [];

    if (selectedRows.length > 0) {
      // 复制选中的行
      selectedRows.forEach(row => {
        const rowData: Cell[] = [];
        for (let col = 0; col < 26; col++) {
          rowData.push(activeWorksheet.data[row]?.[col] || { value: '' });
        }
        data.push(rowData);
      });
    } else if (selectedCols.length > 0) {
      // 复制选中的列
      for (let row = 0; row < 50; row++) {
        const rowData: Cell[] = [];
        selectedCols.forEach(col => {
          rowData.push(activeWorksheet.data[row]?.[col] || { value: '' });
        });
        data.push(rowData);
      }
    } else if (selectedCell) {
      // 复制选中的单元格
      const cell = activeWorksheet.data[selectedCell.row]?.[selectedCell.col] || { value: '' };
      data.push([cell]);
    }

    setClipboard({ data, type: 'copy' });
  };

  // 剪切功能
  const handleCut = () => {
    handleCopy(); // 先复制

    // 然后清空选中的内容
    if (selectedRows.length > 0) {
      selectedRows.forEach(row => {
        for (let col = 0; col < 26; col++) {
          updateCell(row, col, { value: '', formula: undefined });
        }
      });
    } else if (selectedCols.length > 0) {
      selectedCols.forEach(col => {
        for (let row = 0; row < 50; row++) {
          updateCell(row, col, { value: '', formula: undefined });
        }
      });
    } else if (selectedCell) {
      updateCell(selectedCell.row, selectedCell.col, { value: '', formula: undefined });
    }

    if (clipboard) {
      setClipboard({ ...clipboard, type: 'cut' });
    }
  };

  // 粘贴功能
  const handlePaste = () => {
    if (!clipboard || !selectedCell) return;

    const startRow = selectedCell.row;
    const startCol = selectedCell.col;

    clipboard.data.forEach((rowData, rowOffset) => {
      rowData.forEach((cell, colOffset) => {
        const targetRow = startRow + rowOffset;
        const targetCol = startCol + colOffset;

        if (targetRow < 50 && targetCol < 26) {
          updateCell(targetRow, targetCol, {
            value: cell.value,
            formula: cell.formula,
            format: cell.format
          });
        }
      });
    });

    // 如果是剪切操作，清空剪贴板
    if (clipboard.type === 'cut') {
      setClipboard(null);
    }
  };

  // 排序功能
  const handleSort = (ascending: boolean = true) => {
    if (selectedCols.length === 0) return;

    const sortCol = selectedCols[0];
    const newWorksheets = [...worksheets];
    const worksheet = newWorksheets[activeWorksheetIndex];

    // 获取有数据的行
    const dataRows = worksheet.data.filter(row =>
      row.some(cell => cell && cell.value && cell.value.trim() !== '')
    );

    // 排序
    dataRows.sort((a, b) => {
      const aValue = a[sortCol]?.value || '';
      const bValue = b[sortCol]?.value || '';

      // 尝试数字比较
      const aNum = parseFloat(aValue);
      const bNum = parseFloat(bValue);

      if (!isNaN(aNum) && !isNaN(bNum)) {
        return ascending ? aNum - bNum : bNum - aNum;
      }

      // 字符串比较
      return ascending
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    });

    // 更新数据
    worksheet.data = dataRows;
    setWorksheets(newWorksheets);
  };

  // 筛选功能
  const handleFilter = () => {
    // 简单的筛选实现 - 隐藏空行
    const newWorksheets = [...worksheets];
    const worksheet = newWorksheets[activeWorksheetIndex];

    worksheet.data = worksheet.data.filter(row =>
      row.some(cell => cell && cell.value && cell.value.trim() !== '')
    );

    setWorksheets(newWorksheets);
  };

  // 求和功能
  const handleSum = () => {
    if (!selectedCell) return;

    let sum = 0;
    let hasNumbers = false;

    if (selectedRows.length > 0) {
      // 对选中行求和
      selectedRows.forEach(row => {
        for (let col = 0; col < 26; col++) {
          const value = activeWorksheet.data[row]?.[col]?.value;
          const num = parseFloat(value || '');
          if (!isNaN(num)) {
            sum += num;
            hasNumbers = true;
          }
        }
      });
    } else if (selectedCols.length > 0) {
      // 对选中列求和
      selectedCols.forEach(col => {
        for (let row = 0; row < 50; row++) {
          const value = activeWorksheet.data[row]?.[col]?.value;
          const num = parseFloat(value || '');
          if (!isNaN(num)) {
            sum += num;
            hasNumbers = true;
          }
        }
      });
    }

    if (hasNumbers) {
      updateCell(selectedCell.row, selectedCell.col, {
        value: sum.toString(),
        formula: `=SUM(${selectedRows.length > 0 ? 'rows' : 'cols'})`
      });
    }
  };

  // 插入行
  const handleInsertRow = () => {
    if (!selectedCell && selectedRows.length === 0) return;

    const insertIndex = selectedCell ? selectedCell.row : selectedRows[0];
    const newWorksheets = [...worksheets];
    const worksheet = newWorksheets[activeWorksheetIndex];

    // 插入空行
    const newRow = Array(26).fill(null).map(() => ({ value: '' }));
    worksheet.data.splice(insertIndex, 0, newRow);

    setWorksheets(newWorksheets);
  };

  // 删除行
  const handleDeleteRow = () => {
    if (selectedRows.length === 0) return;

    const newWorksheets = [...worksheets];
    const worksheet = newWorksheets[activeWorksheetIndex];

    // 从后往前删除，避免索引问题
    selectedRows.sort((a, b) => b - a).forEach(rowIndex => {
      worksheet.data.splice(rowIndex, 1);
    });

    setWorksheets(newWorksheets);
    setSelectedRows([]);
  };

  const currentCellFormat = selectedCell 
    ? activeWorksheet.data[selectedCell.row]?.[selectedCell.col]?.format 
    : undefined;

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        {/* Excel风格主工具栏 */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <FileSpreadsheet className="w-6 h-6" />
              <span className="text-lg font-semibold">{fileName}</span>
            </div>

            <div className="flex items-center space-x-3">
              {/* 模式切换 */}
              <div className="flex bg-white bg-opacity-20 rounded-lg p-1 mr-4">
                <button
                  onClick={() => window.location.href = window.location.href.replace('/edit/', '/view/')}
                  className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
                    isReadOnly
                      ? 'bg-white text-green-600 shadow-sm'
                      : 'text-white hover:bg-white hover:bg-opacity-20'
                  }`}
                >
                  <Eye className="w-4 h-4" />
                  <span>查看</span>
                </button>
                <button
                  onClick={() => window.location.href = window.location.href.replace('/view/', '/edit/')}
                  className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
                    !isReadOnly
                      ? 'bg-white text-green-600 shadow-sm'
                      : 'text-white hover:bg-white hover:bg-opacity-20'
                  }`}
                >
                  <Edit3 className="w-4 h-4" />
                  <span>编辑</span>
                </button>
              </div>

              {/* 操作按钮 - 优化文字可见性 */}
              {!isReadOnly && onSave && (
                <button
                  onClick={() => onSave(worksheets)}
                  className="flex items-center space-x-2 px-4 py-2 bg-white text-green-600 rounded-lg hover:bg-gray-50 transition-colors shadow-sm border border-white/30 font-medium"
                >
                  <Save className="w-4 h-4" />
                  <span>保存</span>
                </button>
              )}

              <button className="flex items-center space-x-2 px-4 py-2 bg-white text-green-600 rounded-lg hover:bg-gray-50 transition-colors shadow-sm border border-white/30 font-medium">
                <Download className="w-4 h-4" />
                <span>下载</span>
              </button>

              <button
                onClick={() => window.print()}
                className="flex items-center space-x-2 px-4 py-2 bg-white text-green-600 rounded-lg hover:bg-gray-50 transition-colors shadow-sm border border-white/30 font-medium"
              >
                <Printer className="w-4 h-4" />
                <span>打印</span>
              </button>
            </div>
          </div>
        </div>

        {/* 增强的格式工具栏 - Microsoft Office风格 */}
        {!isReadOnly && (
          <div className="bg-gray-50 border-t border-gray-100">
            {/* 主要格式工具栏 */}
            <div className="flex items-center justify-between px-4 py-2">
              <div className="flex items-center space-x-1">
                {/* 剪贴板操作 */}
                <div className="flex items-center space-x-1 mr-2">
                  <FormatButton
                    icon={<Copy className="w-4 h-4" />}
                    title="复制 (Ctrl+C)"
                    onClick={handleCopy}
                  />
                  <FormatButton
                    icon={<Scissors className="w-4 h-4" />}
                    title="剪切 (Ctrl+X)"
                    onClick={handleCut}
                  />
                  <FormatButton
                    icon={<Clipboard className="w-4 h-4" />}
                    title="粘贴 (Ctrl+V)"
                    onClick={handlePaste}
                    active={!!clipboard}
                  />
                </div>

                <div className="w-px h-6 bg-gray-300 mx-2" />

                {/* 字体格式 */}
                <div className="flex items-center space-x-1">
                  <select
                    value={currentCellFormat?.fontSize || 12}
                    onChange={(e) => applyFormat({ fontSize: parseInt(e.target.value) })}
                    className="px-2 py-1 border border-gray-300 rounded text-sm w-16"
                  >
                    <option value="8">8</option>
                    <option value="9">9</option>
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                    <option value="14">14</option>
                    <option value="16">16</option>
                    <option value="18">18</option>
                    <option value="20">20</option>
                    <option value="24">24</option>
                  </select>

                  <FormatButton
                    icon={<Bold className="w-4 h-4" />}
                    title="粗体 (Ctrl+B)"
                    active={currentCellFormat?.bold}
                    onClick={() => applyFormat({ bold: !currentCellFormat?.bold })}
                  />
                  <FormatButton
                    icon={<Italic className="w-4 h-4" />}
                    title="斜体 (Ctrl+I)"
                    active={currentCellFormat?.italic}
                    onClick={() => applyFormat({ italic: !currentCellFormat?.italic })}
                  />
                </div>

                <div className="w-px h-6 bg-gray-300 mx-2" />

                {/* 对齐方式 */}
                <div className="flex items-center space-x-1">
                  <FormatButton
                    icon={<AlignLeft className="w-4 h-4" />}
                    title="左对齐"
                    active={currentCellFormat?.textAlign === 'left'}
                    onClick={() => applyFormat({ textAlign: 'left' })}
                  />
                  <FormatButton
                    icon={<AlignCenter className="w-4 h-4" />}
                    title="居中对齐"
                    active={currentCellFormat?.textAlign === 'center'}
                    onClick={() => applyFormat({ textAlign: 'center' })}
                  />
                  <FormatButton
                    icon={<AlignRight className="w-4 h-4" />}
                    title="右对齐"
                    active={currentCellFormat?.textAlign === 'right'}
                    onClick={() => applyFormat({ textAlign: 'right' })}
                  />
                  <FormatButton
                    icon={<Merge className="w-4 h-4" />}
                    title="合并单元格"
                    onClick={() => {/* 合并单元格逻辑 */}}
                  />
                </div>

                <div className="w-px h-6 bg-gray-300 mx-2" />

                {/* 数字格式 */}
                <div className="flex items-center space-x-1">
                  <select
                    value={currentCellFormat?.numberFormat || 'general'}
                    onChange={(e) => applyFormat({ numberFormat: e.target.value as any })}
                    className="px-2 py-1 border border-gray-300 rounded text-sm w-20"
                  >
                    <option value="general">常规</option>
                    <option value="number">数字</option>
                    <option value="currency">货币</option>
                    <option value="percentage">百分比</option>
                    <option value="date">日期</option>
                  </select>

                  <FormatButton
                    icon={<Percent className="w-4 h-4" />}
                    title="百分比"
                    onClick={() => applyFormat({ numberFormat: 'percentage' })}
                  />
                  <FormatButton
                    icon={<DollarSign className="w-4 h-4" />}
                    title="货币"
                    onClick={() => applyFormat({ numberFormat: 'currency' })}
                  />
                </div>

                <div className="w-px h-6 bg-gray-300 mx-2" />

                {/* 数据操作 */}
                <div className="flex items-center space-x-1">
                  <FormatButton
                    icon={<SortAsc className="w-4 h-4" />}
                    title="升序排序"
                    onClick={() => handleSort(true)}
                  />
                  <FormatButton
                    icon={<SortDesc className="w-4 h-4" />}
                    title="降序排序"
                    onClick={() => handleSort(false)}
                  />
                  <FormatButton
                    icon={<Filter className="w-4 h-4" />}
                    title="筛选"
                    onClick={handleFilter}
                  />
                  <FormatButton
                    icon={<Sigma className="w-4 h-4" />}
                    title="求和"
                    onClick={handleSum}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* 颜色选择 */}
                <div className="flex items-center space-x-1">
                  <label className="text-xs text-gray-600">字体:</label>
                  <input
                    type="color"
                    value={currentCellFormat?.textColor || '#000000'}
                    onChange={(e) => applyFormat({ textColor: e.target.value })}
                    className="w-6 h-6 border border-gray-300 rounded cursor-pointer"
                  />
                </div>
                <div className="flex items-center space-x-1">
                  <label className="text-xs text-gray-600">背景:</label>
                  <input
                    type="color"
                    value={currentCellFormat?.backgroundColor || '#ffffff'}
                    onChange={(e) => applyFormat({ backgroundColor: e.target.value })}
                    className="w-6 h-6 border border-gray-300 rounded cursor-pointer"
                  />
                </div>
              </div>
            </div>

            {/* 图表和高级功能工具栏 */}
            <div className="flex items-center justify-between px-4 py-2 border-t border-gray-200 bg-white">
              <div className="flex items-center space-x-1">
                <span className="text-xs text-gray-600 mr-2">插入:</span>

                {/* 图表 */}
                <FormatButton
                  icon={<BarChart3 className="w-4 h-4" />}
                  title="柱状图"
                  onClick={() => {/* 插入图表逻辑 */}}
                />
                <FormatButton
                  icon={<PieChart className="w-4 h-4" />}
                  title="饼图"
                  onClick={() => {/* 插入图表逻辑 */}}
                />
                <FormatButton
                  icon={<TrendingUp className="w-4 h-4" />}
                  title="折线图"
                  onClick={() => {/* 插入图表逻辑 */}}
                />

                <div className="w-px h-6 bg-gray-300 mx-2" />

                {/* 表格操作 */}
                <FormatButton
                  icon={<Plus className="w-4 h-4" />}
                  title="插入行"
                  onClick={handleInsertRow}
                />
                <FormatButton
                  icon={<Trash2 className="w-4 h-4" />}
                  title="删除行"
                  onClick={handleDeleteRow}
                />
                <FormatButton
                  icon={<Grid3X3 className="w-4 h-4" />}
                  title="边框"
                  onClick={() => applyFormat({
                    backgroundColor: '#f3f4f6',
                    textColor: '#374151'
                  })}
                />
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-600">函数:</span>
                <select className="px-2 py-1 border border-gray-300 rounded text-xs">
                  <option value="">选择函数</option>
                  <option value="SUM">SUM - 求和</option>
                  <option value="AVERAGE">AVERAGE - 平均值</option>
                  <option value="COUNT">COUNT - 计数</option>
                  <option value="MAX">MAX - 最大值</option>
                  <option value="MIN">MIN - 最小值</option>
                  <option value="IF">IF - 条件</option>
                  <option value="VLOOKUP">VLOOKUP - 查找</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* 公式栏 */}
        <div className="flex items-center px-4 py-2 border-t border-gray-100 bg-gray-50">
          <div className="flex items-center space-x-2 mr-4">
            <span className="text-sm font-medium text-gray-700">
              {selectedCell ? `${getColumnLabel(selectedCell.col)}${selectedCell.row + 1}` : ''}
            </span>
          </div>
          <div className="flex-1">
            <input
              type="text"
              value={formulaBarValue}
              onChange={(e) => setFormulaBarValue(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && selectedCell) {
                  handleCellValueChange(selectedCell.row, selectedCell.col, formulaBarValue);
                  setEditingCell(null);
                }
              }}
              className="w-full px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入值或公式 (以 = 开头)"
              readOnly={isReadOnly}
            />
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 表格区域 - 修复超出右侧区域问题 */}
        <div className="flex-1 overflow-auto max-w-full">
          <div
            className="inline-block"
            style={{
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top left',
              minWidth: 'max-content',
              maxWidth: '100%'
            }}
          >
            <table ref={tableRef} className="border-collapse bg-white w-auto">
              {/* 表头 */}
              <thead>
                <tr>
                  <th className="w-12 h-8 bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600"></th>
                  {Array.from({ length: 26 }, (_, i) => (
                    <th
                      key={i}
                      className={`h-8 bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600 px-2 relative cursor-pointer select-none ${
                        selectedCols.includes(i) ? 'bg-blue-200' : 'hover:bg-gray-200'
                      }`}
                      style={{
                        width: columnWidths[i] || 80,
                        minWidth: columnWidths[i] || 80
                      }}
                      onClick={(e) => handleColHeaderClick(i, e)}
                    >
                      {getColumnLabel(i)}
                      {/* 列宽调整手柄 */}
                      <div
                        className="absolute right-0 top-0 w-1 h-full cursor-col-resize hover:bg-blue-500 opacity-0 hover:opacity-100"
                        onMouseDown={(e) => startResize('col', i, e)}
                      />
                    </th>
                  ))}
                </tr>
              </thead>
              
              {/* 表体 */}
              <tbody>
                {Array.from({ length: 50 }, (_, rowIndex) => (
                  <tr key={rowIndex}>
                    {/* 行号 */}
                    <td
                      className={`w-12 bg-gray-100 border border-gray-300 text-xs font-medium text-gray-600 text-center cursor-pointer select-none relative ${
                        selectedRows.includes(rowIndex) ? 'bg-blue-200' : 'hover:bg-gray-200'
                      }`}
                      style={{
                        height: rowHeights[rowIndex] || 32,
                        minHeight: rowHeights[rowIndex] || 32
                      }}
                      onClick={(e) => handleRowHeaderClick(rowIndex, e)}
                    >
                      {rowIndex + 1}
                      {/* 行高调整手柄 */}
                      <div
                        className="absolute bottom-0 left-0 w-full h-1 cursor-row-resize hover:bg-blue-500 opacity-0 hover:opacity-100"
                        onMouseDown={(e) => startResize('row', rowIndex, e)}
                      />
                    </td>
                    
                    {/* 数据单元格 */}
                    {Array.from({ length: 26 }, (_, colIndex) => {
                      const cell = activeWorksheet.data[rowIndex]?.[colIndex] || { value: '' };
                      const isSelected = selectedCell?.row === rowIndex && selectedCell?.col === colIndex;
                      const isRowSelected = selectedRows.includes(rowIndex);
                      const isColSelected = selectedCols.includes(colIndex);
                      const isEditing = editingCell?.row === rowIndex && editingCell?.col === colIndex;

                      let cellBgColor = cell.format?.backgroundColor;
                      if (isSelected) {
                        cellBgColor = '#dbeafe'; // blue-50
                      } else if (isRowSelected || isColSelected) {
                        cellBgColor = '#e0e7ff'; // indigo-50
                      }

                      return (
                        <td
                          key={colIndex}
                          className={`border border-gray-300 p-0 relative ${
                            isSelected
                              ? 'ring-2 ring-blue-500 bg-blue-50'
                              : isRowSelected || isColSelected
                                ? 'bg-indigo-50'
                                : 'hover:bg-gray-50'
                          }`}
                          style={{
                            width: columnWidths[colIndex] || 80,
                            minWidth: columnWidths[colIndex] || 80,
                            height: rowHeights[rowIndex] || 32,
                            minHeight: rowHeights[rowIndex] || 32,
                            backgroundColor: cellBgColor,
                            color: cell.format?.textColor,
                            textAlign: cell.format?.textAlign,
                            fontWeight: cell.format?.bold ? 'bold' : 'normal',
                            fontStyle: cell.format?.italic ? 'italic' : 'normal',
                            fontSize: cell.format?.fontSize ? `${cell.format.fontSize}px` : undefined
                          }}
                          onClick={(e) => handleCellClick(rowIndex, colIndex, e)}
                          onDoubleClick={() => handleCellDoubleClick(rowIndex, colIndex)}
                        >
                          {isEditing ? (
                            <input
                              type="text"
                              value={cell.formula || cell.value}
                              onChange={(e) => handleCellValueChange(rowIndex, colIndex, e.target.value)}
                              onBlur={() => setEditingCell(null)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  setEditingCell(null);
                                } else if (e.key === 'Escape') {
                                  setEditingCell(null);
                                }
                              }}
                              className="w-full h-full px-1 border-0 outline-none bg-transparent"
                              autoFocus
                            />
                          ) : (
                            <div className="px-1 py-1 text-xs truncate">
                              {cell.value}
                            </div>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 底部工作表标签 */}
      <div className="bg-white border-t border-gray-200 flex items-center">
        <div className="flex-1 flex items-center overflow-x-auto">
          {worksheets.map((worksheet, index) => (
            <div
              key={worksheet.id}
              className={`flex items-center px-4 py-2 border-r border-gray-200 cursor-pointer ${
                index === activeWorksheetIndex 
                  ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500' 
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setActiveWorksheetIndex(index)}
            >
              <span className="text-sm font-medium">{worksheet.name}</span>
              {!isReadOnly && worksheets.length > 1 && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteWorksheet(index);
                  }}
                  className="ml-2 text-gray-400 hover:text-red-500"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              )}
            </div>
          ))}
          
          {!isReadOnly && (
            <button
              onClick={addWorksheet}
              className="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-50"
              title="添加工作表"
            >
              <Plus className="w-4 h-4" />
            </button>
          )}
        </div>

        <div className="flex items-center px-4 py-2 space-x-2">
          <span className="text-sm text-gray-600">缩放:</span>
          <button
            onClick={() => setZoom(Math.max(50, zoom - 10))}
            className="text-gray-600 hover:text-gray-900"
          >
            -
          </button>
          <span className="text-sm font-medium min-w-12 text-center">{zoom}%</span>
          <button
            onClick={() => setZoom(Math.min(200, zoom + 10))}
            className="text-gray-600 hover:text-gray-900"
          >
            +
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExcelEditor;
