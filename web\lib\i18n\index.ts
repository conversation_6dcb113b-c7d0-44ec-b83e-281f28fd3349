/**
 * 国际化配置主文件
 * 支持动态加载语言文件
 */

export type Language = 'zh-CN' | 'zh-TW' | 'en' | 'ja';

export interface Translations {
  // 通用
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    back: string;
    next: string;
    submit: string;
    retry: string;
    save: string;
    delete: string;
    edit: string;
    add: string;
    search: string;
    filter: string;
    export: string;
    import: string;
    refresh: string;
  };
  
  // 登录页面
  login: {
    title: string;
    subtitle: string;
    welcomeBack: string;
    username: string;
    password: string;
    rememberMe: string;
    forgotPassword: string;
    loginButton: string;
    noAccount: string;
    signUp: string;
    loginSuccess: string;
    loginError: string;
    invalidCredentials: string;
    networkError: string;
    usernameRequired: string;
    passwordRequired: string;
    usernameMinLength: string;
    passwordMinLength: string;
  };
  
  // AI 相关
  ai: {
    poweredBy: string;
    intelligentSystem: string;
    secureLogin: string;
    aiAssistant: string;
    smartAnalysis: string;
    dataProtection: string;
    knowledgeBase: string;
    intelligentRetrieval: string;
    documentProcessing: string;
  };
  
  // 语言切换
  language: {
    current: string;
    switch: string;
    chinese: string;
    traditionalChinese: string;
    english: string;
    japanese: string;
  };
  
  // 导航
  navigation: {
    home: string;
    dashboard: string;
    documents: string;
    search: string;
    settings: string;
    profile: string;
    logout: string;
    admin: string;
  };
  
  // 仪表板
  dashboard: {
    welcome: string;
    overview: string;
    statistics: string;
    recentActivity: string;
    quickActions: string;
    systemStatus: string;
    userInfo: string;
    totalDocuments: string;
    totalQueries: string;
    totalUsers: string;
    systemSettings: string;
  };
  
  // 文档管理
  documents: {
    title: string;
    upload: string;
    download: string;
    delete: string;
    preview: string;
    details: string;
    fileName: string;
    fileSize: string;
    uploadTime: string;
    fileType: string;
    status: string;
    processing: string;
    completed: string;
    failed: string;
    uploadSuccess: string;
    uploadError: string;
    deleteConfirm: string;
  };
  
  // 搜索
  search: {
    title: string;
    placeholder: string;
    results: string;
    noResults: string;
    searching: string;
    advanced: string;
    filters: string;
    sortBy: string;
    relevance: string;
    date: string;
    size: string;
  };
  
  // 设置
  settings: {
    title: string;
    general: string;
    account: string;
    security: string;
    notifications: string;
    language: string;
    theme: string;
    privacy: string;
    about: string;
  };
  
  // 错误信息
  errors: {
    pageNotFound: string;
    serverError: string;
    networkError: string;
    unauthorized: string;
    forbidden: string;
    validationError: string;
    unknownError: string;
  };
}

export const defaultLanguage: Language = 'zh-CN';

// 语言配置
export const languageConfig = {
  'zh-CN': {
    name: '中文简体',
    flag: '🇨🇳',
    direction: 'ltr' as const,
  },
  'zh-TW': {
    name: '中文繁體',
    flag: '🇹🇼',
    direction: 'ltr' as const,
  },
  'en': {
    name: 'English',
    flag: '🇺🇸',
    direction: 'ltr' as const,
  },
  'ja': {
    name: '日本語',
    flag: '🇯🇵',
    direction: 'ltr' as const,
  },
};

// 动态导入语言文件
const loadTranslations = async (language: Language): Promise<Translations> => {
  try {
    const module = await import(`./locales/${language}.ts`);
    return module.default;
  } catch (error) {
    console.warn(`Failed to load translations for ${language}, falling back to default`);
    const defaultModule = await import(`./locales/${defaultLanguage}.ts`);
    return defaultModule.default;
  }
};

// 缓存已加载的翻译
const translationCache = new Map<Language, Translations>();

export const getTranslation = async (language: Language): Promise<Translations> => {
  if (translationCache.has(language)) {
    return translationCache.get(language)!;
  }
  
  const translations = await loadTranslations(language);
  translationCache.set(language, translations);
  return translations;
};

// 同步获取翻译（用于已缓存的情况）
export const getTranslationSync = (language: Language): Translations | null => {
  return translationCache.get(language) || null;
};

// 预加载所有语言
export const preloadAllLanguages = async (): Promise<void> => {
  const languages: Language[] = ['zh-CN', 'zh-TW', 'en', 'ja'];
  
  await Promise.all(
    languages.map(async (lang) => {
      try {
        await getTranslation(lang);
      } catch (error) {
        console.warn(`Failed to preload language ${lang}:`, error);
      }
    })
  );
};

// 检测浏览器语言
export const detectBrowserLanguage = (): Language => {
  if (typeof window === 'undefined') {
    return defaultLanguage;
  }
  
  const browserLanguage = navigator.language || navigator.languages?.[0];
  
  if (browserLanguage?.startsWith('zh-CN') || browserLanguage === 'zh') {
    return 'zh-CN';
  } else if (browserLanguage?.startsWith('zh-TW') || browserLanguage === 'zh-Hant') {
    return 'zh-TW';
  } else if (browserLanguage?.startsWith('en')) {
    return 'en';
  } else if (browserLanguage?.startsWith('ja')) {
    return 'ja';
  }
  
  return defaultLanguage;
};

// 格式化数字（根据语言环境）
export const formatNumber = (number: number, language: Language): string => {
  const localeMap = {
    'zh-CN': 'zh-CN',
    'zh-TW': 'zh-TW',
    'en': 'en-US',
    'ja': 'ja-JP',
  };
  
  return new Intl.NumberFormat(localeMap[language]).format(number);
};

// 格式化日期（根据语言环境）
export const formatDate = (date: Date, language: Language, options?: Intl.DateTimeFormatOptions): string => {
  const localeMap = {
    'zh-CN': 'zh-CN',
    'zh-TW': 'zh-TW',
    'en': 'en-US',
    'ja': 'ja-JP',
  };
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  return new Intl.DateTimeFormat(localeMap[language], options || defaultOptions).format(date);
};
