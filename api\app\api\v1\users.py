"""
用户管理API端点
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import HTTPBearer
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

router = APIRouter()
security = HTTPBearer()


class UserBase(BaseModel):
    """用户基础模型"""
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool = True


class UserCreate(UserBase):
    """创建用户模型"""
    password: str


class UserUpdate(BaseModel):
    """更新用户模型"""
    username: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """用户列表响应模型"""
    users: List[UserResponse]
    total: int
    page: int
    size: int


@router.get("/", response_model=UserListResponse)
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    token: str = Depends(security)
):
    """
    获取用户列表
    
    Args:
        page: 页码
        size: 每页数量
        search: 搜索关键词
        token: 访问令牌
        
    Returns:
        用户列表
    """
    # TODO: 实现实际的用户查询逻辑
    fake_users = [
        UserResponse(
            id=1,
            username="admin",
            email="<EMAIL>",
            full_name="Administrator",
            is_active=True,
            created_at=datetime.now()
        ),
        UserResponse(
            id=2,
            username="user1",
            email="<EMAIL>",
            full_name="User One",
            is_active=True,
            created_at=datetime.now()
        )
    ]
    
    return UserListResponse(
        users=fake_users,
        total=len(fake_users),
        page=page,
        size=size
    )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    token: str = Depends(security)
):
    """
    获取用户详情
    
    Args:
        user_id: 用户ID
        token: 访问令牌
        
    Returns:
        用户详情
    """
    # TODO: 实现实际的用户查询逻辑
    if user_id == 1:
        return UserResponse(
            id=1,
            username="admin",
            email="<EMAIL>",
            full_name="Administrator",
            is_active=True,
            created_at=datetime.now()
        )
    
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="User not found"
    )


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate,
    token: str = Depends(security)
):
    """
    创建用户
    
    Args:
        user: 用户创建数据
        token: 访问令牌
        
    Returns:
        创建的用户信息
    """
    # TODO: 实现实际的用户创建逻辑
    return UserResponse(
        id=999,
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        is_active=user.is_active,
        created_at=datetime.now()
    )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user: UserUpdate,
    token: str = Depends(security)
):
    """
    更新用户
    
    Args:
        user_id: 用户ID
        user: 用户更新数据
        token: 访问令牌
        
    Returns:
        更新后的用户信息
    """
    # TODO: 实现实际的用户更新逻辑
    if user_id == 1:
        return UserResponse(
            id=1,
            username=user.username or "admin",
            email=user.email or "<EMAIL>",
            full_name=user.full_name or "Administrator",
            is_active=user.is_active if user.is_active is not None else True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="User not found"
    )


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    token: str = Depends(security)
):
    """
    删除用户
    
    Args:
        user_id: 用户ID
        token: 访问令牌
        
    Returns:
        删除确认消息
    """
    # TODO: 实现实际的用户删除逻辑
    if user_id == 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete admin user"
        )
    
    return {"message": f"User {user_id} deleted successfully"}
