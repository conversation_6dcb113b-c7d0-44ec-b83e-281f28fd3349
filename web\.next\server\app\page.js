/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$":
/*!**************************************************************!*\
  !*** ./lib/i18n/locales/ lazy ^\.\/.*\.ts$ namespace object ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.ts": [
		"(ssr)/./lib/i18n/locales/en.ts",
		"_ssr_lib_i18n_locales_en_ts"
	],
	"./ja.ts": [
		"(ssr)/./lib/i18n/locales/ja.ts",
		"_ssr_lib_i18n_locales_ja_ts"
	],
	"./zh-CN.ts": [
		"(ssr)/./lib/i18n/locales/zh-CN.ts",
		"_ssr_lib_i18n_locales_zh-CN_ts"
	],
	"./zh-TW.ts": [
		"(ssr)/./lib/i18n/locales/zh-TW.ts",
		"_ssr_lib_i18n_locales_zh-TW_ts"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1aa5\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBK0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvP2JlOTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFx3b3Jrc3BhY2VcXFxceGhjLXJhZ1xcXFx3ZWJcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/LanguageContext.tsx */ \"(ssr)/./contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Brain,Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _lib_systemInit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/systemInit */ \"(ssr)/./lib/systemInit.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkSystemStatus = async ()=>{\n            try {\n                // 首先检查系统是否需要首次设置\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/api/v1/setup/status\");\n                const status = response.data.data;\n                if (status.needs_setup) {\n                    // 系统需要首次设置，跳转到设置页面\n                    router.push(\"/setup\");\n                    return;\n                }\n                // 系统已设置完成，检查是否已登录\n                const token = localStorage.getItem(\"token\");\n                if (token) {\n                    // 如果已登录，3秒后自动跳转到仪表板\n                    const timer = setTimeout(()=>{\n                        router.push(\"/dashboard\");\n                    }, 3000);\n                    return ()=>clearTimeout(timer);\n                }\n            } catch (error) {\n                console.error(\"检查系统状态失败:\", error);\n                // 如果检查失败，尝试检查旧的初始化状态\n                try {\n                    const { need_init } = await _lib_systemInit__WEBPACK_IMPORTED_MODULE_4__.systemInitApi.checkNeedInit();\n                    if (need_init) {\n                        router.push(\"/system-init\");\n                        return;\n                    }\n                } catch (legacyError) {\n                    console.error(\"检查旧初始化状态失败:\", legacyError);\n                    // 如果都失败，跳转到首次设置页面\n                    router.push(\"/setup\");\n                    return;\n                }\n            } finally{\n                setIsChecking(false);\n            }\n        };\n        checkSystemStatus();\n    }, [\n        router\n    ]);\n    // 如果正在检查系统状态，显示加载界面\n    if (isChecking) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        animate: {\n                            rotate: 360\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"inline-block mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-12 h-12 text-white\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"正在检查系统状态...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl mb-6\",\n                            whileHover: {\n                                scale: 1.1,\n                                rotate: 5\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-10 h-10 text-white\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                    children: \"知识库\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl md:text-5xl text-blue-200\",\n                                    children: \"智能平台\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-blue-200 mb-8 max-w-2xl mx-auto leading-relaxed\",\n                            children: \"基于AI的智能知识管理与检索系统，让知识更智能、检索更精准\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.3\n                    },\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg flex items-center space-x-2 hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-2xl\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                            className: \"border-2 border-white/30 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-all duration-200\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            children: \"Learn More\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto\",\n                    children: [\n                        {\n                            icon: \"\\uD83E\\uDDE0\",\n                            title: \"AI驱动\",\n                            description: \"先进的机器学习算法，实现智能文档处理\"\n                        },\n                        {\n                            icon: \"⚡\",\n                            title: \"极速响应\",\n                            description: \"实时分析和即时结果，最大化生产力\"\n                        },\n                        {\n                            icon: \"\\uD83D\\uDEE1️\",\n                            title: \"安全可靠\",\n                            description: \"企业级安全保障，端到端加密保护\"\n                        }\n                    ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            className: \"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\",\n                            whileHover: {\n                                scale: 1.05,\n                                y: -5\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-2\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-200 text-sm\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 1,\n                        delay: 1\n                    },\n                    className: \"mt-12 flex items-center justify-center space-x-2 text-blue-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"基于 Next.js & FastAPI 构建\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Brain_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\page.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n    const [t, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化语言设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeLanguage = async ()=>{\n            setIsLoading(true);\n            try {\n                // 预加载所有语言\n                await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.preloadAllLanguages)();\n                // 从localStorage加载语言设置\n                const savedLanguage = localStorage.getItem(\"language\");\n                let targetLanguage;\n                if (savedLanguage && [\n                    \"zh-CN\",\n                    \"zh-TW\",\n                    \"en\",\n                    \"ja\"\n                ].includes(savedLanguage)) {\n                    targetLanguage = savedLanguage;\n                } else {\n                    // 检测浏览器语言\n                    targetLanguage = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.detectBrowserLanguage)();\n                }\n                setLanguageState(targetLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(targetLanguage);\n                setTranslations(translations);\n            } catch (error) {\n                console.error(\"Failed to initialize language:\", error);\n                // 回退到默认语言\n                setLanguageState(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                setTranslations(translations);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeLanguage();\n    }, []);\n    const setLanguage = async (newLanguage)=>{\n        setIsLoading(true);\n        try {\n            setLanguageState(newLanguage);\n            const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(newLanguage);\n            setTranslations(translations);\n            localStorage.setItem(\"language\", newLanguage);\n        } catch (error) {\n            console.error(\"Failed to set language:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage,\n            t,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkHealth: () => (/* binding */ checkHealth),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storageApi: () => (/* binding */ storageApi),\n/* harmony export */   testCors: () => (/* binding */ testCors)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API客户端配置\n * 与后端FastAPI接口对接\n */ \n\n// API基础配置\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    },\n    withCredentials: false\n});\n// 添加调试日志\nconsole.log(\"API Client initialized with base URL:\", API_BASE_URL);\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 添加认证token\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    // 添加请求ID用于追踪\n    config.headers[\"X-Request-ID\"] = generateRequestId();\n    // 调试日志\n    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    console.log(`   Base URL: ${config.baseURL}`);\n    console.log(`   Full URL: ${config.baseURL}${config.url}`);\n    console.log(`   Timeout: ${config.timeout}ms`);\n    return config;\n}, (error)=>{\n    console.error(\"❌ Request interceptor error:\", error);\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);\n    return response;\n}, async (error)=>{\n    console.error(`❌ API Error: ${error.code} ${error.config?.method?.toUpperCase()} ${error.config?.url}`);\n    console.error(`   Message: ${error.message}`);\n    console.error(`   Status: ${error.response?.status}`);\n    const originalRequest = error.config;\n    // 处理401未授权错误\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        // 尝试刷新token\n        const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n        if (refreshToken) {\n            try {\n                const response = await refreshAccessToken(refreshToken);\n                const newToken = response.data.access_token;\n                // 更新token\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", newToken, {\n                    expires: 1\n                });\n                // 重试原请求\n                originalRequest.headers.Authorization = `Bearer ${newToken}`;\n                return apiClient(originalRequest);\n            } catch (refreshError) {\n                // 刷新失败，清除token并跳转到登录页\n                clearAuthTokens();\n                window.location.href = \"/login\";\n                return Promise.reject(refreshError);\n            }\n        } else {\n            // 没有刷新token，直接跳转到登录页\n            clearAuthTokens();\n            window.location.href = \"/login\";\n        }\n    }\n    return Promise.reject(error);\n});\n// 生成请求ID\nfunction generateRequestId() {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n// 清除认证token\nfunction clearAuthTokens() {\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n}\n// 刷新访问token\nasync function refreshAccessToken(refreshToken) {\n    return axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/api/v1/auth/refresh`, {\n        refresh_token: refreshToken\n    });\n}\n// 登录\nconst login = async (credentials)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Attempting login with:\", {\n            username: credentials.username\n        });\n        console.log(\"\\uD83C\\uDF10 API Base URL:\", API_BASE_URL);\n        console.log(\"\\uD83D\\uDCE1 Full login URL:\", `${API_BASE_URL}/api/v1/auth/login`);\n        const response = await apiClient.post(\"/api/v1/auth/login\", credentials);\n        console.log(\"✅ Login response:\", response.data);\n        // 保存token到cookie\n        const { access_token, refresh_token, expires_in } = response.data;\n        // 设置cookie过期时间（转换为天数）\n        const expiresInDays = expires_in / (60 * 60 * 24);\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access_token, {\n            expires: expiresInDays\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", refresh_token, {\n            expires: 7\n        }); // 刷新token保存7天\n        // 同时保存到localStorage作为备份\n        localStorage.setItem(\"token\", access_token);\n        localStorage.setItem(\"refresh_token\", refresh_token);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ Login error details:\", error);\n        console.error(\"   Error code:\", error.code);\n        console.error(\"   Error message:\", error.message);\n        console.error(\"   Error response:\", error.response?.data);\n        console.error(\"   Error status:\", error.response?.status);\n        console.error(\"   Request config:\", error.config);\n        // 处理不同类型的错误响应\n        if (error.code === \"ECONNABORTED\") {\n            throw new Error(`请求超时，请检查网络连接或稍后重试。服务器地址：${API_BASE_URL}`);\n        } else if (error.response?.data?.detail) {\n            throw new Error(error.response.data.detail);\n        } else if (error.response?.data?.error?.message) {\n            throw new Error(error.response.data.error.message);\n        } else if (error.response?.status === 401) {\n            throw new Error(\"用户名或密码错误\");\n        } else if (error.response?.status >= 500) {\n            throw new Error(\"服务器错误，请稍后重试\");\n        } else if (error.code === \"ECONNREFUSED\" || error.message.includes(\"Network Error\")) {\n            throw new Error(`无法连接到服务器，请检查网络连接。服务器地址：${API_BASE_URL}`);\n        } else {\n            throw new Error(error.message || \"Login failed\");\n        }\n    }\n};\n// 登出\nconst logout = async ()=>{\n    try {\n        await apiClient.post(\"/api/v1/auth/logout\");\n    } catch (error) {\n        // 即使后端登出失败，也要清除本地token\n        console.error(\"Logout error:\", error);\n    } finally{\n        clearAuthTokens();\n    }\n};\n// 获取当前用户信息\nconst getCurrentUser = async ()=>{\n    try {\n        const response = await apiClient.get(\"/api/v1/auth/me\");\n        return response.data;\n    } catch (error) {\n        const apiError = error.response?.data;\n        throw new Error(apiError?.error?.message || \"Failed to get user info\");\n    }\n};\n// 检查健康状态\nconst checkHealth = async ()=>{\n    try {\n        const response = await apiClient.get(\"/health\");\n        return response.data;\n    } catch (error) {\n        throw new Error(\"API health check failed\");\n    }\n};\n// CORS测试\nconst testCors = async ()=>{\n    try {\n        console.log(\"\\uD83E\\uDDEA Testing CORS configuration...\");\n        const response = await apiClient.get(\"/api/v1/auth/test-cors\");\n        console.log(\"✅ CORS test successful:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ CORS test failed:\", error);\n        throw error;\n    }\n};\n// 存储管理API\nconst storageApi = {\n    // 获取存储列表\n    getStorages: async ()=>{\n        const response = await apiClient.get(\"/api/v1/storage/\");\n        return response.data;\n    },\n    // 创建存储\n    createStorage: async (data)=>{\n        const response = await apiClient.post(\"/api/v1/storage/\", data);\n        return response.data;\n    },\n    // 测试存储连接\n    testStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/test`);\n        return response.data;\n    },\n    // 删除存储\n    deleteStorage: async (storageId)=>{\n        const response = await apiClient.delete(`/api/v1/storage/${storageId}`);\n        return response.data;\n    },\n    // 同步存储\n    syncStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/sync`);\n        return response.data;\n    }\n};\n// 检查token是否有效\nconst isAuthenticated = ()=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    return !!token;\n};\n// 获取token\nconst getAccessToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatDate),\n/* harmony export */   formatNumber: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   getTranslation: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslation),\n/* harmony export */   getTranslationSync: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslationSync),\n/* harmony export */   languageConfig: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.preloadAllLanguages)\n/* harmony export */ });\n/* harmony import */ var _i18n_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./i18n/index */ \"(ssr)/./lib/i18n/index.ts\");\n/**\n * 多语言支持 - 兼容性导出文件\n * 重新导出新架构中的所有功能\n */ // 重新导出所有类型和函数\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFRCxjQUFjO0FBWVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9saWIvaTE4bi50cz80OWFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5aSa6K+t6KiA5pSv5oyBIC0g5YW85a655oCn5a+85Ye65paH5Lu2XG4gKiDph43mlrDlr7zlh7rmlrDmnrbmnoTkuK3nmoTmiYDmnInlip/og71cbiAqL1xuXG4vLyDph43mlrDlr7zlh7rmiYDmnInnsbvlnovlkozlh73mlbBcbmV4cG9ydCB0eXBlIHsgTGFuZ3VhZ2UsIFRyYW5zbGF0aW9ucyB9IGZyb20gJy4vaTE4bi9pbmRleCc7XG5cbmV4cG9ydCB7XG4gIGRlZmF1bHRMYW5ndWFnZSxcbiAgbGFuZ3VhZ2VDb25maWcsXG4gIGdldFRyYW5zbGF0aW9uLFxuICBnZXRUcmFuc2xhdGlvblN5bmMsXG4gIHByZWxvYWRBbGxMYW5ndWFnZXMsXG4gIGRldGVjdEJyb3dzZXJMYW5ndWFnZSxcbiAgZm9ybWF0TnVtYmVyLFxuICBmb3JtYXREYXRlXG59IGZyb20gJy4vaTE4bi9pbmRleCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VDb25maWciLCJnZXRUcmFuc2xhdGlvbiIsImdldFRyYW5zbGF0aW9uU3luYyIsInByZWxvYWRBbGxMYW5ndWFnZXMiLCJkZXRlY3RCcm93c2VyTGFuZ3VhZ2UiLCJmb3JtYXROdW1iZXIiLCJmb3JtYXREYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/index.ts":
/*!***************************!*\
  !*** ./lib/i18n/index.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* binding */ detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   getTranslationSync: () => (/* binding */ getTranslationSync),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* binding */ preloadAllLanguages)\n/* harmony export */ });\n/**\n * 国际化配置主文件\n * 支持动态加载语言文件\n */ const defaultLanguage = \"zh-CN\";\n// 语言配置\nconst languageConfig = {\n    \"zh-CN\": {\n        name: \"中文简体\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        direction: \"ltr\"\n    },\n    \"zh-TW\": {\n        name: \"中文繁體\",\n        flag: \"\\uD83C\\uDDF9\\uD83C\\uDDFC\",\n        direction: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        direction: \"ltr\"\n    },\n    \"ja\": {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        direction: \"ltr\"\n    }\n};\n// 动态导入语言文件\nconst loadTranslations = async (language)=>{\n    try {\n        const module = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${language}.ts`);\n        return module.default;\n    } catch (error) {\n        console.warn(`Failed to load translations for ${language}, falling back to default`);\n        const defaultModule = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${defaultLanguage}.ts`);\n        return defaultModule.default;\n    }\n};\n// 缓存已加载的翻译\nconst translationCache = new Map();\nconst getTranslation = async (language)=>{\n    if (translationCache.has(language)) {\n        return translationCache.get(language);\n    }\n    const translations = await loadTranslations(language);\n    translationCache.set(language, translations);\n    return translations;\n};\n// 同步获取翻译（用于已缓存的情况）\nconst getTranslationSync = (language)=>{\n    return translationCache.get(language) || null;\n};\n// 预加载所有语言\nconst preloadAllLanguages = async ()=>{\n    const languages = [\n        \"zh-CN\",\n        \"zh-TW\",\n        \"en\",\n        \"ja\"\n    ];\n    await Promise.all(languages.map(async (lang)=>{\n        try {\n            await getTranslation(lang);\n        } catch (error) {\n            console.warn(`Failed to preload language ${lang}:`, error);\n        }\n    }));\n};\n// 检测浏览器语言\nconst detectBrowserLanguage = ()=>{\n    if (true) {\n        return defaultLanguage;\n    }\n    const browserLanguage = navigator.language || navigator.languages?.[0];\n    if (browserLanguage?.startsWith(\"zh-CN\") || browserLanguage === \"zh\") {\n        return \"zh-CN\";\n    } else if (browserLanguage?.startsWith(\"zh-TW\") || browserLanguage === \"zh-Hant\") {\n        return \"zh-TW\";\n    } else if (browserLanguage?.startsWith(\"en\")) {\n        return \"en\";\n    } else if (browserLanguage?.startsWith(\"ja\")) {\n        return \"ja\";\n    }\n    return defaultLanguage;\n};\n// 格式化数字（根据语言环境）\nconst formatNumber = (number, language)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    return new Intl.NumberFormat(localeMap[language]).format(number);\n};\n// 格式化日期（根据语言环境）\nconst formatDate = (date, language, options)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(localeMap[language], options || defaultOptions).format(date);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/index.ts\n");

/***/ }),

/***/ "(ssr)/./lib/systemInit.ts":
/*!***************************!*\
  !*** ./lib/systemInit.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   systemInitApi: () => (/* binding */ systemInitApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/axios.js\");\n/**\n * 系统初始化相关API\n */ \nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 响应拦截器\napi.interceptors.response.use((response)=>response, (error)=>{\n    console.error(\"API Error:\", error);\n    return Promise.reject(error);\n});\nconst systemInitApi = {\n    /**\n   * 检查系统是否需要初始化\n   */ async checkNeedInit () {\n        try {\n            const response = await api.get(\"/api/v1/system-init/check\");\n            return response.data.data;\n        } catch (error) {\n            console.error(\"检查系统初始化状态失败:\", error);\n            // 如果API调用失败，假设需要初始化\n            return {\n                need_init: true,\n                is_initialized: false\n            };\n        }\n    },\n    /**\n   * 获取系统初始化状态\n   */ async getStatus () {\n        const response = await api.get(\"/api/v1/system-init/status\");\n        return response.data.data;\n    },\n    /**\n   * 初始化管理员账户\n   */ async initializeAdmin (data) {\n        const response = await api.post(\"/api/v1/system-init/admin\", data);\n        return response.data.data;\n    },\n    /**\n   * 初始化字典数据\n   */ async initializeDictionaries () {\n        await api.post(\"/api/v1/system-init/dictionaries\");\n    },\n    /**\n   * 初始化存储配置\n   */ async initializeStorage (config) {\n        await api.post(\"/api/v1/system-init/storage\", config);\n    },\n    /**\n   * 初始化系统配置\n   */ async initializeSystemConfig () {\n        await api.post(\"/api/v1/system-init/system-config\");\n    },\n    /**\n   * 自动完成所有初始化步骤\n   */ async autoInitialize () {\n        await api.post(\"/api/v1/system-init/auto-init\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/systemInit.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0cc9297c17c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZ2xvYmFscy5jc3M/YWJhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBjYzkyOTdjMTdjNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI知识库 - 智能文档管理平台\",\n    description: \"基于AI技术的智能文档管理和知识库系统\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#4ade80\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#useLanguage`);


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZmF2aWNvbi5pY28/ZjUyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736","vendor-chunks/mime-db@1.52.0","vendor-chunks/axios@1.9.0","vendor-chunks/lucide-react@0.300.0_react@18.3.1","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/debug@4.4.1","vendor-chunks/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d","vendor-chunks/form-data@4.0.3","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/asynckit@0.4.0","vendor-chunks/combined-stream@1.0.8","vendor-chunks/mime-types@2.1.35","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-symbols@1.1.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/function-bind@1.1.2","vendor-chunks/es-set-tostringtag@2.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/es-errors@1.3.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/has-tostringtag@1.0.2","vendor-chunks/es-object-atoms@1.1.1","vendor-chunks/js-cookie@3.0.5"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();