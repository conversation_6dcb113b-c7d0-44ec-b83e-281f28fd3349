#!/usr/bin/env python3
"""
测试Celery API端点
"""
import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

import httpx
from loguru import logger


async def test_celery_api():
    """测试Celery API端点"""
    base_url = "http://127.0.0.1:8000"
    
    # 首先登录获取token
    async with httpx.AsyncClient() as client:
        try:
            # 测试登录
            login_data = {
                "username": "admin",
                "password": "password"
            }
            
            logger.info("尝试登录...")
            login_response = await client.post(
                f"{base_url}/api/v1/auth/login",
                data=login_data
            )
            
            if login_response.status_code != 200:
                logger.error(f"登录失败: {login_response.status_code} - {login_response.text}")
                return False
            
            login_result = login_response.json()
            token = login_result.get("access_token")
            
            if not token:
                logger.error("未获取到访问令牌")
                return False
            
            logger.info("登录成功，获取到访问令牌")
            
            # 设置认证头
            headers = {"Authorization": f"Bearer {token}"}
            
            # 测试Celery状态API
            logger.info("测试Celery状态API...")
            status_response = await client.get(
                f"{base_url}/api/v1/celery/status",
                headers=headers
            )
            
            logger.info(f"状态API响应: {status_response.status_code}")
            if status_response.status_code == 200:
                status_data = status_response.json()
                logger.info(f"状态数据: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
            else:
                logger.error(f"状态API失败: {status_response.text}")
            
            # 测试Celery配置API
            logger.info("测试Celery配置API...")
            config_response = await client.get(
                f"{base_url}/api/v1/celery/config",
                headers=headers
            )
            
            logger.info(f"配置API响应: {config_response.status_code}")
            if config_response.status_code == 200:
                config_data = config_response.json()
                logger.info(f"配置数据: {json.dumps(config_data, indent=2, ensure_ascii=False)}")
            else:
                logger.error(f"配置API失败: {config_response.text}")
            
            # 测试Celery指标API
            logger.info("测试Celery指标API...")
            metrics_response = await client.get(
                f"{base_url}/api/v1/celery/metrics",
                headers=headers
            )
            
            logger.info(f"指标API响应: {metrics_response.status_code}")
            if metrics_response.status_code == 200:
                metrics_data = metrics_response.json()
                logger.info(f"指标数据: {json.dumps(metrics_data, indent=2, ensure_ascii=False)}")
            else:
                logger.error(f"指标API失败: {metrics_response.text}")
            
            # 测试自动启动API
            logger.info("测试自动启动API...")
            auto_start_response = await client.get(
                f"{base_url}/api/v1/celery/auto-start",
                headers=headers
            )
            
            logger.info(f"自动启动API响应: {auto_start_response.status_code}")
            if auto_start_response.status_code == 200:
                auto_start_data = auto_start_response.json()
                logger.info(f"自动启动数据: {json.dumps(auto_start_data, indent=2, ensure_ascii=False)}")
            else:
                logger.error(f"自动启动API失败: {auto_start_response.text}")
            
            return True
            
        except Exception as e:
            logger.error(f"API测试失败: {e}")
            return False


async def main():
    """主函数"""
    logger.info("开始Celery API测试...")
    
    success = await test_celery_api()
    
    if success:
        logger.info("API测试完成")
    else:
        logger.error("API测试失败")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
