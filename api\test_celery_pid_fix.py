#!/usr/bin/env python3
"""
测试Celery PID修复
"""

import asyncio
import json
import time
from app.core.celery_manager import celery_manager
from loguru import logger


def test_celery_status():
    """测试Celery状态获取"""
    print("🔍 测试Celery状态获取...")
    
    try:
        # 获取状态
        status = celery_manager.get_status()
        
        print("\n📊 Celery服务状态:")
        print(f"Redis连接: {'✅ 正常' if status['redis_connected'] else '❌ 失败'}")
        
        print("\n🔧 服务详情:")
        for service_name, service_info in status['services'].items():
            running = service_info['running']
            pid = service_info['pid']
            source = service_info.get('source', 'unknown')
            
            status_icon = "✅" if running else "❌"
            print(f"  {service_name.upper()}: {status_icon} {'运行中' if running else '已停止'}")
            if pid:
                print(f"    PID: {pid} (来源: {source})")
            else:
                print(f"    PID: 无")
        
        return status
        
    except Exception as e:
        print(f"❌ 获取状态失败: {str(e)}")
        return None


def test_real_process_scan():
    """测试真实进程扫描"""
    print("\n🔍 测试真实进程扫描...")
    
    try:
        import psutil
        
        print("📋 查找Celery相关进程:")
        celery_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and len(cmdline) > 1:
                    cmdline_str = ' '.join(cmdline).lower()
                    
                    if 'celery' in cmdline_str:
                        celery_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline_str
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        if celery_processes:
            print(f"✅ 找到 {len(celery_processes)} 个Celery进程:")
            for proc in celery_processes:
                print(f"  PID {proc['pid']}: {proc['name']}")
                print(f"    命令行: {proc['cmdline'][:100]}...")
                
                # 判断进程类型
                if 'worker' in proc['cmdline']:
                    print(f"    类型: Worker")
                elif 'beat' in proc['cmdline']:
                    print(f"    类型: Beat")
                elif 'flower' in proc['cmdline']:
                    print(f"    类型: Flower")
                print()
        else:
            print("❌ 未找到Celery进程")
        
        return celery_processes
        
    except Exception as e:
        print(f"❌ 进程扫描失败: {str(e)}")
        return []


def test_individual_service_detection():
    """测试单个服务检测"""
    print("\n🔍 测试单个服务检测...")
    
    services = ["worker", "beat", "flower"]
    
    for service in services:
        print(f"\n检测 {service.upper()} 服务:")
        try:
            info = celery_manager._get_real_process_info(service)
            
            if info['running']:
                print(f"  ✅ 运行中")
                print(f"  PID: {info['pid']}")
                print(f"  进程名: {info['name']}")
                print(f"  命令行: {info['cmdline'][:80]}...")
            else:
                print(f"  ❌ 未运行")
                
        except Exception as e:
            print(f"  ❌ 检测失败: {str(e)}")


def compare_old_vs_new():
    """比较旧方法和新方法的结果"""
    print("\n🔄 比较检测方法...")
    
    try:
        # 获取完整状态（使用新方法）
        new_status = celery_manager.get_status()
        
        # 模拟旧方法（仅从进程对象获取）
        old_status = {"services": {}}
        for service_type in ["worker", "beat", "flower"]:
            if service_type in celery_manager.processes:
                process = celery_manager.processes[service_type]
                is_running = process.poll() is None
                old_status["services"][service_type] = {
                    "running": is_running,
                    "pid": process.pid if is_running else None
                }
            else:
                old_status["services"][service_type] = {
                    "running": False,
                    "pid": None
                }
        
        print("📊 方法对比结果:")
        print("\n旧方法（仅进程对象）:")
        for service, info in old_status["services"].items():
            print(f"  {service}: 运行={info['running']}, PID={info['pid']}")
        
        print("\n新方法（进程对象+系统扫描）:")
        for service, info in new_status["services"].items():
            source = info.get('source', 'unknown')
            print(f"  {service}: 运行={info['running']}, PID={info['pid']}, 来源={source}")
        
        # 检查差异
        print("\n🔍 差异分析:")
        for service in ["worker", "beat", "flower"]:
            old_pid = old_status["services"][service]["pid"]
            new_pid = new_status["services"][service]["pid"]
            
            if old_pid != new_pid:
                print(f"  {service}: PID差异 - 旧方法: {old_pid}, 新方法: {new_pid}")
            else:
                print(f"  {service}: PID一致 - {old_pid}")
        
        return old_status, new_status
        
    except Exception as e:
        print(f"❌ 比较失败: {str(e)}")
        return None, None


def test_api_response():
    """测试API响应格式"""
    print("\n🔌 测试API响应格式...")
    
    try:
        status = celery_manager.get_status()
        
        # 模拟API响应
        api_response = {
            "redis_connected": status["redis_connected"],
            "services": status["services"],
            "overall_status": "running" if all(s["running"] for s in status["services"].values()) else "partial"
        }
        
        print("📋 API响应格式:")
        print(json.dumps(api_response, indent=2, ensure_ascii=False))
        
        return api_response
        
    except Exception as e:
        print(f"❌ API响应测试失败: {str(e)}")
        return None


def main():
    """主测试函数"""
    print("🚀 开始Celery PID修复测试")
    print("=" * 60)
    
    # 1. 测试基本状态获取
    status = test_celery_status()
    
    # 2. 测试真实进程扫描
    processes = test_real_process_scan()
    
    # 3. 测试单个服务检测
    test_individual_service_detection()
    
    # 4. 比较新旧方法
    old_status, new_status = compare_old_vs_new()
    
    # 5. 测试API响应
    api_response = test_api_response()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    if status:
        running_services = sum(1 for s in status["services"].values() if s["running"])
        print(f"✅ 状态获取成功，{running_services}/3 个服务运行中")
    else:
        print("❌ 状态获取失败")
    
    if processes:
        print(f"✅ 进程扫描成功，找到 {len(processes)} 个Celery进程")
    else:
        print("❌ 进程扫描失败或未找到进程")
    
    if old_status and new_status:
        # 检查是否有改进
        old_pids = [s["pid"] for s in old_status["services"].values() if s["pid"]]
        new_pids = [s["pid"] for s in new_status["services"].values() if s["pid"]]
        
        if len(new_pids) >= len(old_pids):
            print("✅ 新方法检测到的PID数量 >= 旧方法")
        else:
            print("⚠️ 新方法检测到的PID数量 < 旧方法")
    
    if api_response:
        print("✅ API响应格式正确")
    else:
        print("❌ API响应格式错误")
    
    print("\n🎉 测试完成！")
    print("\n📋 下一步:")
    print("1. 重启后端服务")
    print("2. 访问系统设置 -> Celery管理")
    print("3. 检查显示的PID是否与日志中的PID一致")


if __name__ == "__main__":
    main()
