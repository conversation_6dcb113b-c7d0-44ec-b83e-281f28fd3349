#!/usr/bin/env python3
"""
完整测试文档分段功能修复的脚本
验证所有字段和功能是否正常工作
"""

import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_sync_session
from app.models.document_segment import (
    DocumentSegmentTask, DocumentSegment, SegmentStatus, 
    SegmentMethod, VectorizeStatus, SegmentTemplate
)


def test_database_connection():
    """测试数据库连接"""
    print("=== 1. 测试数据库连接 ===")
    try:
        db = next(get_sync_session())
        print("✅ 数据库连接成功")
        db.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_table_structure():
    """测试表结构"""
    print("\n=== 2. 测试表结构 ===")
    try:
        db = next(get_sync_session())
        
        # 检查表是否存在
        tables = ['document_segment_tasks', 'document_segments', 'segment_templates', 'vector_indexes']
        for table in tables:
            result = db.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = '{table}'
                )
            """)
            exists = result.scalar()
            if exists:
                print(f"✅ 表 {table} 存在")
            else:
                print(f"❌ 表 {table} 不存在")
                return False
        
        # 检查关键字段
        result = db.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'document_segment_tasks' 
            AND column_name = 'segment_metadata'
        """)
        if result.scalar():
            print("✅ segment_metadata 字段存在")
        else:
            print("❌ segment_metadata 字段缺失")
            return False
        
        # 检查枚举类型
        result = db.execute("""
            SELECT typname, enumlabel 
            FROM pg_type t 
            JOIN pg_enum e ON t.oid = e.enumtypid 
            WHERE typname = 'segment_method_enum'
            ORDER BY enumsortorder
        """)
        enum_values = [row[1] for row in result]
        expected_values = ['PARAGRAPH', 'SENTENCE', 'FIXED_LENGTH', 'SEMANTIC']
        
        if set(enum_values) == set(expected_values):
            print(f"✅ 枚举值正确: {enum_values}")
        else:
            print(f"❌ 枚举值不正确: 期望 {expected_values}, 实际 {enum_values}")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 表结构测试失败: {e}")
        return False


def test_model_field_access():
    """测试模型字段访问"""
    print("\n=== 3. 测试模型字段访问 ===")
    try:
        # 测试创建任务对象
        task = DocumentSegmentTask(
            task_name="测试任务",
            description="测试描述",
            file_ids=["test_file_1"],
            total_files=1,
            segment_method=SegmentMethod.PARAGRAPH,
            status=SegmentStatus.PENDING
        )
        
        # 测试访问 segment_metadata 字段
        task.segment_metadata = {"test": "data"}
        print(f"✅ segment_metadata 字段访问成功: {task.segment_metadata}")
        
        # 测试其他关键字段
        print(f"✅ task_name: {task.task_name}")
        print(f"✅ segment_method: {task.segment_method}")
        print(f"✅ status: {task.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型字段访问失败: {e}")
        return False


def test_database_operations():
    """测试数据库操作"""
    print("\n=== 4. 测试数据库操作 ===")
    try:
        db = next(get_sync_session())
        
        # 创建测试任务
        task = DocumentSegmentTask(
            task_name="完整测试任务",
            description="测试所有字段的任务",
            file_ids=["test_file_1", "test_file_2"],
            total_files=2,
            segment_method=SegmentMethod.PARAGRAPH,
            max_length=1000,
            overlap=100,
            enable_vectorization=False,
            status=SegmentStatus.PENDING,
            segment_metadata={"celery_task_id": "test-task-123", "test_data": "value"}
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 创建任务成功: ID={task.id}, task_id={task.task_id}")
        print(f"✅ segment_metadata: {task.segment_metadata}")
        
        # 创建测试分段
        segment = DocumentSegment(
            task_id=task.id,
            file_id="test_file_1",
            content="这是一个测试分段内容，用于验证分段功能是否正常工作。",
            segment_index=0,
            word_count=30,
            vectorize_status=VectorizeStatus.PENDING,
            segment_metadata={"source": "test", "page": 1}
        )
        
        db.add(segment)
        db.commit()
        db.refresh(segment)
        
        print(f"✅ 创建分段成功: ID={segment.id}, segment_id={segment.segment_id}")
        print(f"✅ 分段内容: {segment.content[:50]}...")
        
        # 测试查询
        tasks = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_name.like('%测试%')
        ).all()
        print(f"✅ 查询到 {len(tasks)} 个测试任务")
        
        segments = db.query(DocumentSegment).filter(
            DocumentSegment.task_id == task.id
        ).all()
        print(f"✅ 查询到 {len(segments)} 个分段记录")
        
        # 清理测试数据
        db.delete(segment)
        db.delete(task)
        db.commit()
        print("✅ 测试数据已清理")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return False


def test_template_data():
    """测试模板数据"""
    print("\n=== 5. 测试模板数据 ===")
    try:
        db = next(get_sync_session())
        
        templates = db.query(SegmentTemplate).all()
        print(f"✅ 找到 {len(templates)} 个分段模板:")
        
        for template in templates:
            print(f"  - {template.template_name}: {template.segment_method}")
            print(f"    描述: {template.description}")
            print(f"    默认: {template.is_default}, 系统: {template.is_system}")
        
        # 检查是否有默认模板
        default_template = db.query(SegmentTemplate).filter(
            SegmentTemplate.is_default == True
        ).first()
        
        if default_template:
            print(f"✅ 找到默认模板: {default_template.template_name}")
        else:
            print("❌ 没有找到默认模板")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 模板数据测试失败: {e}")
        return False


def test_celery_task_simulation():
    """模拟Celery任务执行"""
    print("\n=== 6. 模拟Celery任务执行 ===")
    try:
        db = next(get_sync_session())
        
        # 创建任务
        task = DocumentSegmentTask(
            task_name="Celery模拟任务",
            description="模拟Celery任务执行流程",
            file_ids=["file_1", "file_2"],
            total_files=2,
            segment_method=SegmentMethod.PARAGRAPH,
            status=SegmentStatus.PENDING,
            segment_metadata={"celery_task_id": "celery-123", "worker": "test-worker"}
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 创建Celery任务: {task.task_id}")
        
        # 模拟任务开始
        task.status = SegmentStatus.PROCESSING
        task.started_at = datetime.now()
        task.segment_metadata = {
            **task.segment_metadata,
            "started_at": task.started_at.isoformat(),
            "progress": 0
        }
        db.commit()
        print("✅ 任务状态更新为PROCESSING")
        
        # 模拟创建分段
        for i in range(3):
            segment = DocumentSegment(
                task_id=task.id,
                file_id=f"file_{i % 2 + 1}",
                content=f"这是第{i+1}个测试分段内容。" * 10,
                segment_index=i,
                word_count=len(f"这是第{i+1}个测试分段内容。" * 10),
                vectorize_status=VectorizeStatus.PENDING,
                segment_metadata={"file_index": i % 2 + 1, "segment_type": "paragraph"}
            )
            db.add(segment)
        
        db.commit()
        print("✅ 创建了3个分段记录")
        
        # 模拟任务完成
        task.status = SegmentStatus.COMPLETED
        task.completed_at = datetime.now()
        task.total_segments = 3
        task.processed_files = 2
        task.progress = 100.0
        task.segment_metadata = {
            **task.segment_metadata,
            "completed_at": task.completed_at.isoformat(),
            "progress": 100,
            "total_segments": 3
        }
        db.commit()
        print("✅ 任务状态更新为COMPLETED")
        
        # 验证最终结果
        final_task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.id == task.id
        ).first()
        
        print(f"✅ 最终任务状态: {final_task.status}")
        print(f"✅ 总分段数: {final_task.total_segments}")
        print(f"✅ 进度: {final_task.progress}%")
        print(f"✅ 元数据: {final_task.segment_metadata}")
        
        # 清理测试数据
        db.query(DocumentSegment).filter(DocumentSegment.task_id == task.id).delete()
        db.delete(task)
        db.commit()
        print("✅ 测试数据已清理")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Celery任务模拟失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 开始完整的文档分段功能修复验证")
    print("=" * 60)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("表结构", test_table_structure),
        ("模型字段访问", test_model_field_access),
        ("数据库操作", test_database_operations),
        ("模板数据", test_template_data),
        ("Celery任务模拟", test_celery_task_simulation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！文档分段功能修复成功！")
        print("\n现在可以正常使用以下功能：")
        print("- 创建分段任务")
        print("- 执行Celery分段处理")
        print("- 存储分段结果到数据库")
        print("- 查询任务进度和状态")
    else:
        print("❌ 部分测试失败，请检查数据库配置和SQL脚本执行情况")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
