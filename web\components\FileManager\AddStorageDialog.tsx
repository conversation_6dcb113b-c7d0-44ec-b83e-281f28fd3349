'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  HardDrive,
  Cloud,
  Server,
  Wifi,
  TestTube,
  Save,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import toast from 'react-hot-toast';

interface AddStorageDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

type StorageType = 'local' | 'minio' | 'ftp' | 'sftp';

interface StorageFormData {
  name: string;
  storage_type: StorageType;
  is_default: boolean;
  is_active: boolean;
  config: Record<string, any>;
}

const AddStorageDialog: React.FC<AddStorageDialogProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState<StorageFormData>({
    name: '',
    storage_type: 'local',
    is_default: false,
    is_active: true,
    config: {}
  });
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [showPassword, setShowPassword] = useState<Record<string, boolean>>({});

  // 存储类型配置
  const storageTypes = [
    {
      type: 'local' as StorageType,
      name: '本地存储',
      icon: HardDrive,
      description: '存储在本地文件系统',
      fields: [
        { key: 'base_path', label: '基础路径', type: 'text', required: true, placeholder: './storage' },
        { key: 'max_file_size', label: '最大文件大小(MB)', type: 'number', placeholder: '100' }
      ]
    },
    {
      type: 'minio' as StorageType,
      name: 'MinIO',
      icon: Cloud,
      description: '兼容S3的对象存储',
      fields: [
        { key: 'endpoint', label: '端点地址', type: 'text', required: true, placeholder: 'localhost:9000' },
        { key: 'access_key', label: '访问密钥', type: 'text', required: true, placeholder: 'minioadmin' },
        { key: 'secret_key', label: '秘密密钥', type: 'password', required: true, placeholder: 'minioadmin' },
        { key: 'bucket_name', label: '存储桶名称', type: 'text', required: true, placeholder: 'ai-knowledge-base' },
        { key: 'secure', label: '使用HTTPS', type: 'boolean', placeholder: 'false' }
      ]
    },
    {
      type: 'ftp' as StorageType,
      name: 'FTP',
      icon: Server,
      description: '文件传输协议',
      fields: [
        { key: 'host', label: '主机地址', type: 'text', required: true, placeholder: 'ftp.example.com' },
        { key: 'port', label: '端口', type: 'number', placeholder: '21' },
        { key: 'username', label: '用户名', type: 'text', required: true, placeholder: 'ftpuser' },
        { key: 'password', label: '密码', type: 'password', required: true },
        { key: 'base_path', label: '基础路径', type: 'text', placeholder: '/' },
        { key: 'passive', label: '被动模式', type: 'boolean', placeholder: 'true' },
        { key: 'encoding', label: '编码', type: 'text', placeholder: 'utf-8' }
      ]
    },
    {
      type: 'sftp' as StorageType,
      name: 'SFTP',
      icon: Wifi,
      description: 'SSH文件传输协议',
      fields: [
        { key: 'host', label: '主机地址', type: 'text', required: true, placeholder: 'sftp.example.com' },
        { key: 'port', label: '端口', type: 'number', placeholder: '22' },
        { key: 'username', label: '用户名', type: 'text', required: true, placeholder: 'sftpuser' },
        { key: 'password', label: '密码', type: 'password', required: true },
        { key: 'base_path', label: '基础路径', type: 'text', placeholder: '/' }
      ]
    }
  ];

  const currentStorageType = storageTypes.find(st => st.type === formData.storage_type);

  // 更新表单数据
  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 更新配置数据
  const updateConfig = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [key]: value
      }
    }));
  };

  // 切换密码显示
  const togglePasswordVisibility = (fieldKey: string) => {
    setShowPassword(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }));
  };

  // 测试连接
  const testConnection = async () => {
    if (!formData.name.trim()) {
      toast.error('请输入存储名称');
      return;
    }

    // 验证必填字段
    const requiredFields = currentStorageType?.fields.filter(f => f.required) || [];
    const missingFields = requiredFields.filter(f => !formData.config[f.key]);
    
    if (missingFields.length > 0) {
      toast.error(`请填写必填字段: ${missingFields.map(f => f.label).join(', ')}`);
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      // 创建临时存储配置进行测试
      const testData = {
        name: formData.name,
        storage_type: formData.storage_type,
        config: formData.config,
        is_default: false,
        is_active: true
      };

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${API_BASE_URL}/api/v1/storage/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
      });

      if (response.ok) {
        const storage = await response.json();

        // 测试连接
        const testResponse = await fetch(`${API_BASE_URL}/api/v1/storage/${storage.id}/test`, {
          method: 'POST'
        });
        const testResult = await testResponse.json();

        // 删除临时存储配置
        await fetch(`${API_BASE_URL}/api/v1/storage/${storage.id}`, {
          method: 'DELETE'
        });

        setTestResult(testResult);
        
        if (testResult.success) {
          toast.success('连接测试成功');
        } else {
          toast.error(`连接测试失败: ${testResult.message}`);
        }
      } else {
        const error = await response.json();
        setTestResult({
          success: false,
          message: error.detail || '创建测试配置失败'
        });
        toast.error('连接测试失败');
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      setTestResult({
        success: false,
        message: '网络错误或服务不可用'
      });
      toast.error('连接测试失败');
    } finally {
      setTesting(false);
    }
  };

  // 保存存储配置
  const saveStorage = async () => {
    if (!formData.name.trim()) {
      toast.error('请输入存储名称');
      return;
    }

    // 验证必填字段
    const requiredFields = currentStorageType?.fields.filter(f => f.required) || [];
    const missingFields = requiredFields.filter(f => !formData.config[f.key]);
    
    if (missingFields.length > 0) {
      toast.error(`请填写必填字段: ${missingFields.map(f => f.label).join(', ')}`);
      return;
    }

    setLoading(true);

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${API_BASE_URL}/api/v1/storage/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        toast.success('存储配置创建成功');
        onSuccess();
        onClose();
      } else {
        const error = await response.json();
        toast.error(error.detail || '创建存储配置失败');
      }
    } catch (error) {
      console.error('创建存储配置失败:', error);
      toast.error('创建存储配置失败');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">添加存储配置</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* 内容区域 */}
          <div className="p-6 max-h-[calc(90vh-180px)] overflow-y-auto">
            {/* 基本信息 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                存储名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => updateFormData('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="输入存储配置名称"
              />
            </div>

            {/* 存储类型选择 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                存储类型 <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 gap-3">
                {storageTypes.map((type) => {
                  const IconComponent = type.icon;
                  return (
                    <button
                      key={type.type}
                      onClick={() => {
                        updateFormData('storage_type', type.type);
                        updateFormData('config', {});
                        setTestResult(null);
                      }}
                      className={`p-4 border-2 rounded-lg text-left transition-colors ${
                        formData.storage_type === type.type
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-2">
                        <IconComponent className={`w-5 h-5 ${
                          formData.storage_type === type.type ? 'text-blue-600' : 'text-gray-400'
                        }`} />
                        <span className="font-medium text-gray-900">{type.name}</span>
                      </div>
                      <p className="text-sm text-gray-500">{type.description}</p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* 存储配置字段 */}
            {currentStorageType && (
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">配置参数</h3>
                <div className="space-y-4">
                  {currentStorageType.fields.map((field) => (
                    <div key={field.key}>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {field.label}
                        {field.required && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      
                      {field.type === 'boolean' ? (
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={formData.config[field.key] || false}
                            onChange={(e) => updateConfig(field.key, e.target.checked)}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-600">启用</span>
                        </div>
                      ) : field.type === 'password' ? (
                        <div className="relative">
                          <input
                            type={showPassword[field.key] ? 'text' : 'password'}
                            value={formData.config[field.key] || ''}
                            onChange={(e) => updateConfig(field.key, e.target.value)}
                            className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder={field.placeholder}
                          />
                          <button
                            type="button"
                            onClick={() => togglePasswordVisibility(field.key)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showPassword[field.key] ? (
                              <EyeOff className="w-4 h-4" />
                            ) : (
                              <Eye className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      ) : (
                        <input
                          type={field.type}
                          value={formData.config[field.key] || ''}
                          onChange={(e) => updateConfig(field.key, field.type === 'number' ? Number(e.target.value) : e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder={field.placeholder}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 其他选项 */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">其他选项</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.is_default}
                    onChange={(e) => updateFormData('is_default', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">设为默认存储</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => updateFormData('is_active', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">启用存储</span>
                </div>
              </div>
            </div>

            {/* 测试结果 */}
            {testResult && (
              <div className={`mb-6 p-4 rounded-lg border ${
                testResult.success 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center space-x-2">
                  {testResult.success ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  )}
                  <span className={`text-sm font-medium ${
                    testResult.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {testResult.success ? '连接测试成功' : '连接测试失败'}
                  </span>
                </div>
                {testResult.message && (
                  <p className={`mt-2 text-sm ${
                    testResult.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {testResult.message}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
            <button
              onClick={testConnection}
              disabled={testing}
              className="flex items-center space-x-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors disabled:opacity-50"
            >
              <TestTube className="w-4 h-4" />
              <span>{testing ? '测试中...' : '测试连接'}</span>
            </button>
            
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
            
            <button
              onClick={saveStorage}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Save className="w-4 h-4" />
              <span>{loading ? '保存中...' : '保存'}</span>
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AddStorageDialog;
