'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  Brain,
  User,
  Settings,
  FileText,
  BarChart3,
  FolderOpen,
  Database,
  Search,
  TrendingUp,
  Activity,
  Clock
} from 'lucide-react';
import { getCurrentUser, User as UserType } from '@/lib/api';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/Layout/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';

const Dashboard: React.FC = () => {
  const [user, setUser] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { t, isLoading: langLoading } = useLanguage();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const userData = await getCurrentUser();
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [router]);

  if (loading || langLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t?.dashboard?.welcome || '欢迎回来'}, {user?.username}!
          </h1>
          <p className="text-gray-600">
            {t?.dashboard?.subtitle || '管理您的AI驱动的文档处理工作流'}
          </p>
        </motion.div>

        {/* 统计卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {[
            { title: t?.dashboard?.stats?.documents || '文档', value: '1,234', icon: FileText, color: 'from-blue-500 to-blue-600' },
            { title: t?.dashboard?.stats?.queries || '查询', value: '5,678', icon: BarChart3, color: 'from-green-500 to-green-600' },
            { title: t?.dashboard?.stats?.users || '用户', value: '89', icon: User, color: 'from-purple-500 to-purple-600' },
            { title: t?.dashboard?.stats?.settings || '设置', value: '12', icon: Settings, color: 'from-orange-500 to-orange-600' },
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
              whileHover={{ scale: 1.02, y: -2 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg bg-gradient-to-r ${stat.color}`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* 快速操作卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
        >
          {/* 文件管理 */}
          <motion.div
            onClick={() => router.push('/file-manager')}
            className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer group"
            whileHover={{ scale: 1.02, y: -2 }}
          >
            <div className="flex items-center space-x-4 mb-4">
              <div className="p-3 bg-blue-500/10 rounded-xl group-hover:bg-blue-500/20 transition-colors">
                <FolderOpen className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                {t?.navigation?.documents || '文件管理'}
              </h3>
            </div>
            <p className="text-gray-600 mb-4">
              {t?.dashboard?.fileManager?.description || '上传、组织和管理您的文件库'}
            </p>
            <div className="text-sm text-blue-600">
              <span>{t?.dashboard?.fileManager?.stats || '125 个文件'}</span>
            </div>
          </motion.div>

          {/* 知识库 */}
          <motion.div
            onClick={() => router.push('/knowledge')}
            className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer group"
            whileHover={{ scale: 1.02, y: -2 }}
          >
            <div className="flex items-center space-x-4 mb-4">
              <div className="p-3 bg-green-500/10 rounded-xl group-hover:bg-green-500/20 transition-colors">
                <Database className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                {t?.navigation?.knowledge || '知识库'}
              </h3>
            </div>
            <p className="text-gray-600 mb-4">
              {t?.dashboard?.knowledge?.description || '构建和查询您的知识库'}
            </p>
            <div className="text-sm text-green-600">
              <span>{t?.dashboard?.knowledge?.stats || '89 个条目'}</span>
            </div>
          </motion.div>

          {/* 智能搜索 */}
          <motion.div
            onClick={() => router.push('/search')}
            className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-all cursor-pointer group"
            whileHover={{ scale: 1.02, y: -2 }}
          >
            <div className="flex items-center space-x-4 mb-4">
              <div className="p-3 bg-purple-500/10 rounded-xl group-hover:bg-purple-500/20 transition-colors">
                <Search className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                {t?.navigation?.search || '智能搜索'}
              </h3>
            </div>
            <p className="text-gray-600 mb-4">
              {t?.dashboard?.search?.description || '使用AI进行智能内容搜索'}
            </p>
            <div className="text-sm text-purple-600">
              <span>{t?.dashboard?.search?.stats || '1,234 次查询'}</span>
            </div>
          </motion.div>
        </motion.div>

        {/* 功能区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8"
        >
          {/* 最近活动 */}
          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {t?.dashboard?.recentActivity || '最近活动'}
            </h2>
            <div className="space-y-4">
              {[
                { action: t?.dashboard?.activities?.documentUploaded || '文档已上传', time: '2 分钟前', type: 'upload', icon: FileText },
                { action: t?.dashboard?.activities?.queryProcessed || '查询已处理', time: '5 分钟前', type: 'query', icon: Search },
                { action: t?.dashboard?.activities?.userLoggedIn || '用户已登录', time: '10 分钟前', type: 'login', icon: User },
                { action: t?.dashboard?.activities?.settingsUpdated || '设置已更新', time: '1 小时前', type: 'settings', icon: Settings },
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <activity.icon className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-900 text-sm font-medium">{activity.action}</p>
                    <p className="text-gray-500 text-xs">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {t?.dashboard?.quickActions || '快速操作'}
            </h2>
            <div className="grid grid-cols-2 gap-4">
              {[
                {
                  title: t?.dashboard?.actions?.uploadDocument || '上传文档',
                  icon: FileText,
                  color: 'from-blue-500 to-blue-600',
                  onClick: () => router.push('/file-manager')
                },
                {
                  title: t?.dashboard?.actions?.runQuery || '运行查询',
                  icon: Search,
                  color: 'from-green-500 to-green-600',
                  onClick: () => router.push('/search')
                },
                {
                  title: t?.dashboard?.actions?.manageUsers || '管理用户',
                  icon: User,
                  color: 'from-purple-500 to-purple-600',
                  onClick: () => router.push('/settings')
                },
                {
                  title: t?.dashboard?.actions?.systemSettings || '系统设置',
                  icon: Settings,
                  color: 'from-orange-500 to-orange-600',
                  onClick: () => router.push('/settings')
                },
              ].map((action, index) => (
                <motion.button
                  key={index}
                  onClick={action.onClick}
                  className={`p-4 rounded-lg bg-gradient-to-r ${action.color} text-white font-medium text-sm flex flex-col items-center space-y-2 hover:shadow-lg transition-all duration-200`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <action.icon className="w-6 h-6" />
                  <span>{action.title}</span>
                </motion.button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* 用户信息 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-8 bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
        >
          <h2 className="text-xl font-semibold text-white mb-4">User Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-blue-200 text-sm">Username</p>
              <p className="text-white font-medium">{user?.username}</p>
            </div>
            <div>
              <p className="text-blue-200 text-sm">Email</p>
              <p className="text-white font-medium">{user?.email}</p>
            </div>
            <div>
              <p className="text-blue-200 text-sm">Status</p>
              <p className="text-white font-medium">
                {user?.is_active ? (
                  <span className="text-green-400">Active</span>
                ) : (
                  <span className="text-red-400">Inactive</span>
                )}
              </p>
            </div>
            <div>
              <p className="text-blue-200 text-sm">Member Since</p>
              <p className="text-white font-medium">
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>
        </motion.div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Dashboard;
