"""
系统初始化API
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_async_session
from app.services.system_init import SystemInitService
from app.core.response import success_response, error_response

router = APIRouter(prefix="/system-init", tags=["系统初始化"])


class AdminInitRequest(BaseModel):
    """管理员初始化请求"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: str = Field(..., description="邮箱")
    password: str = Field(..., min_length=6, description="密码")
    full_name: str = Field(default="系统管理员", description="姓名")


@router.get("/status", summary="获取系统初始化状态")
async def get_init_status(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """获取系统初始化状态"""
    try:
        service = SystemInitService(session)
        status = await service.get_initialization_status()
        return success_response(data=status)
    except Exception as e:
        return error_response(message=f"获取初始化状态失败: {str(e)}")


@router.post("/admin", summary="初始化管理员账户")
async def initialize_admin(
    request: AdminInitRequest,
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """初始化管理员账户"""
    try:
        service = SystemInitService(session)
        
        # 检查系统是否已初始化
        if await service.check_system_initialized():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="系统已初始化，无法重复初始化管理员账户"
            )
        
        # 初始化管理员
        result = await service.initialize_admin_user(request.dict())
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
        
        return success_response(
            data={"admin_id": result.get("admin_id")},
            message=result["message"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        return error_response(message=f"初始化管理员账户失败: {str(e)}")


@router.post("/dictionaries", summary="初始化字典数据")
async def initialize_dictionaries(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """初始化字典数据"""
    try:
        service = SystemInitService(session)
        result = await service.initialize_dictionaries()
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
        
        return success_response(message=result["message"])
        
    except HTTPException:
        raise
    except Exception as e:
        return error_response(message=f"初始化字典数据失败: {str(e)}")


class StorageConfigRequest(BaseModel):
    """存储配置请求"""
    type: str = Field(default="local", description="存储类型")
    basePath: str = Field(default="./storage", description="存储基础路径")


@router.post("/storage", summary="初始化存储配置")
async def initialize_storage(
    request: StorageConfigRequest = None,
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """初始化存储配置"""
    try:
        service = SystemInitService(session)

        # 如果提供了自定义配置，使用自定义配置
        storage_config = None
        if request:
            storage_config = {
                "storage_type": request.type,
                "base_path": request.basePath
            }

        result = await service.initialize_storage_configs(storage_config)

        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )

        return success_response(message=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        return error_response(message="初始化存储配置失败: {}".format(str(e)))


@router.post("/system-config", summary="初始化系统配置")
async def initialize_system_config(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """初始化系统配置"""
    try:
        service = SystemInitService(session)
        result = await service.initialize_system_configs()
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
        
        return success_response(message=result["message"])
        
    except HTTPException:
        raise
    except Exception as e:
        return error_response(message=f"初始化系统配置失败: {str(e)}")


@router.post("/reset", summary="重置系统（清除所有数据）")
async def reset_system(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """重置系统，清除所有数据"""
    try:
        service = SystemInitService(session)
        result = await service.reset_system()

        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )

        return success_response(message=result["message"])

    except HTTPException:
        raise
    except Exception as e:
        return error_response(message="系统重置失败: {}".format(str(e)))


@router.post("/auto-init", summary="自动完成所有初始化步骤")
async def auto_initialize(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """自动完成字典和存储配置初始化（管理员账户需要单独初始化）"""
    try:
        service = SystemInitService(session)
        results = []
        
        # 初始化字典
        dict_result = await service.initialize_dictionaries()
        results.append(f"字典初始化: {dict_result['message']}")
        
        # 初始化存储配置
        storage_result = await service.initialize_storage_configs()
        results.append(f"存储配置初始化: {storage_result['message']}")
        
        # 初始化系统配置
        config_result = await service.initialize_system_configs()
        results.append(f"系统配置初始化: {config_result['message']}")
        
        return success_response(
            data={"results": results},
            message="自动初始化完成"
        )
        
    except Exception as e:
        return error_response(message=f"自动初始化失败: {str(e)}")


@router.get("/check", summary="检查系统是否需要初始化")
async def check_need_init(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """检查系统是否需要初始化"""
    try:
        service = SystemInitService(session)
        is_initialized = await service.check_system_initialized()
        
        return success_response(data={
            "need_init": not is_initialized,
            "is_initialized": is_initialized
        })
        
    except Exception as e:
        return error_response(message=f"检查初始化状态失败: {str(e)}")
