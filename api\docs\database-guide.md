# 多数据库支持指南

本文档介绍AI知识库项目的多数据库支持功能，包括配置、使用和最佳实践。

## 🗄️ 支持的数据库

### PostgreSQL (推荐)
- **优势**: 功能强大、性能优异、支持JSON、全文搜索
- **适用场景**: 生产环境、大型应用
- **驱动**: asyncpg (异步) / psycopg2 (同步)

### MySQL
- **优势**: 广泛使用、社区支持好、性能稳定
- **适用场景**: Web应用、中小型项目
- **驱动**: aiomysql (异步) / pymysql (同步)

### Oracle
- **优势**: 企业级功能、高可用性、强一致性
- **适用场景**: 大型企业应用、关键业务系统
- **驱动**: oracledb (异步) / cx_oracle (同步)

### SQLite
- **优势**: 轻量级、无需服务器、易于部署
- **适用场景**: 开发测试、小型应用、原型开发
- **驱动**: aiosqlite (异步) / sqlite3 (同步)

## ⚙️ 配置指南

### 环境变量配置

在 `.env` 文件中配置数据库连接：

```env
# PostgreSQL 配置
DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/ai_knowledge_base"
DATABASE_TYPE="postgresql"

# MySQL 配置
# DATABASE_URL="mysql+aiomysql://username:password@localhost:3306/ai_knowledge_base"
# DATABASE_TYPE="mysql"

# Oracle 配置
# DATABASE_URL="oracle+oracledb://username:password@localhost:1521/ai_knowledge_base"
# DATABASE_TYPE="oracle"

# SQLite 配置
# DATABASE_URL="sqlite+aiosqlite:///./ai_knowledge_base.db"
# DATABASE_TYPE="sqlite"

# 通用配置
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_TIMEOUT=30
DATABASE_RETRY_ATTEMPTS=3
```

### 数据库特定配置

#### PostgreSQL
```env
DATABASE_URL="postgresql+asyncpg://user:pass@localhost:5432/dbname?sslmode=prefer"
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
```

#### MySQL
```env
DATABASE_URL="mysql+aiomysql://user:pass@localhost:3306/dbname?charset=utf8mb4"
DATABASE_POOL_SIZE=15
DATABASE_MAX_OVERFLOW=25
```

#### Oracle
```env
DATABASE_URL="oracle+oracledb://user:pass@localhost:1521/service_name"
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
```

#### SQLite
```env
DATABASE_URL="sqlite+aiosqlite:///./data/ai_knowledge_base.db"
# SQLite 不需要连接池配置
```

## 🚀 使用指南

### 1. 数据库初始化

```bash
# 初始化数据库结构
uv run ai-knowledge-api init-db

# 或使用 Alembic 直接操作
uv run alembic upgrade head
```

### 2. 创建数据库迁移

```bash
# 自动生成迁移文件
uv run ai-knowledge-api migrate --message "Add user table"

# 或使用 Alembic
uv run alembic revision --autogenerate -m "Add user table"
```

### 3. 升级数据库

```bash
# 升级到最新版本
uv run ai-knowledge-api upgrade-db

# 或使用 Alembic
uv run alembic upgrade head
```

### 4. 重置数据库

```bash
# 重置数据库（谨慎使用）
uv run ai-knowledge-api reset-db --yes
```

## 💻 代码示例

### 1. 定义数据模型

```python
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime
from app.models.base import BaseModel, get_text_type, get_json_type

class User(BaseModel):
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, nullable=False, comment="邮箱")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    full_name = Column(String(100), comment="全名")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 使用兼容性函数
    bio = Column(get_text_type(500), comment="个人简介")
    settings = Column(get_json_type(), comment="用户设置")
```

### 2. 数据库操作

```python
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_async_session
from app.models.user import User

async def create_user(user_data: dict) -> User:
    """创建用户"""
    async with get_async_session() as session:
        user = User(**user_data)
        session.add(user)
        await session.commit()
        await session.refresh(user)
        return user

async def get_user_by_id(user_id: int) -> User:
    """根据ID获取用户"""
    async with get_async_session() as session:
        result = await session.get(User, user_id)
        return result

async def update_user(user_id: int, update_data: dict) -> User:
    """更新用户"""
    async with get_async_session() as session:
        user = await session.get(User, user_id)
        if user:
            user.update_from_dict(update_data)
            await session.commit()
            await session.refresh(user)
        return user
```

### 3. 数据库查询

```python
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

async def search_users(keyword: str, is_active: bool = True) -> list[User]:
    """搜索用户"""
    async with get_async_session() as session:
        stmt = select(User).where(
            and_(
                User.is_active == is_active,
                or_(
                    User.username.ilike(f"%{keyword}%"),
                    User.full_name.ilike(f"%{keyword}%"),
                    User.email.ilike(f"%{keyword}%")
                )
            )
        )
        result = await session.execute(stmt)
        return result.scalars().all()
```

## 🔧 数据库兼容性

### 数据类型映射

| 通用类型 | PostgreSQL | MySQL | Oracle | SQLite |
|---------|------------|-------|--------|--------|
| 整数 | INTEGER | INT | NUMBER | INTEGER |
| 字符串 | VARCHAR | VARCHAR | VARCHAR2 | TEXT |
| 长文本 | TEXT | TEXT | CLOB | TEXT |
| 布尔值 | BOOLEAN | BOOLEAN | NUMBER(1) | INTEGER |
| 日期时间 | TIMESTAMP | DATETIME | TIMESTAMP | TEXT |
| JSON | JSON | JSON | CLOB | TEXT |
| UUID | UUID | CHAR(36) | CHAR(36) | CHAR(36) |

### 兼容性函数

```python
# 在 app/models/base.py 中定义的兼容性函数

def get_text_type(length: int = None):
    """获取文本类型，兼容不同数据库"""
    if length and length <= 255:
        return String(length)
    else:
        return Text

def get_json_type():
    """获取JSON类型，兼容不同数据库"""
    try:
        from sqlalchemy import JSON
        return JSON
    except ImportError:
        return Text

def get_uuid_type():
    """获取UUID类型，兼容不同数据库"""
    try:
        from sqlalchemy.dialects.postgresql import UUID
        return UUID(as_uuid=True)
    except ImportError:
        return String(36)
```

## 📊 性能优化

### 连接池配置

```python
# 不同数据库的推荐连接池配置

# PostgreSQL
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# MySQL
DATABASE_POOL_SIZE=15
DATABASE_MAX_OVERFLOW=25
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Oracle
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# SQLite (不使用连接池)
# 使用 StaticPool
```

### 查询优化

```python
# 使用索引
class User(BaseModel):
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    created_at = Column(DateTime, index=True)

# 批量操作
async def bulk_create_users(users_data: list[dict]):
    """批量创建用户"""
    async with get_async_session() as session:
        users = [User(**data) for data in users_data]
        session.add_all(users)
        await session.commit()
        return users

# 预加载关联数据
async def get_users_with_profiles():
    """获取用户及其资料"""
    async with get_async_session() as session:
        stmt = select(User).options(selectinload(User.profile))
        result = await session.execute(stmt)
        return result.scalars().all()
```

## 🛠️ 故障排除

### 常见问题

1. **连接超时**
   ```python
   # 增加超时时间
   DATABASE_TIMEOUT=60
   DATABASE_POOL_TIMEOUT=60
   ```

2. **连接池耗尽**
   ```python
   # 调整连接池大小
   DATABASE_POOL_SIZE=30
   DATABASE_MAX_OVERFLOW=50
   ```

3. **字符编码问题**
   ```env
   # MySQL 确保使用 UTF-8
   DATABASE_URL="mysql+aiomysql://user:pass@host/db?charset=utf8mb4"
   ```

4. **SSL 连接问题**
   ```env
   # PostgreSQL SSL 配置
   DATABASE_URL="postgresql+asyncpg://user:pass@host/db?sslmode=require"
   ```

### 调试技巧

```python
# 启用 SQL 日志
DATABASE_ECHO=true

# 自定义日志记录
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
```

## 🔒 安全最佳实践

1. **使用环境变量存储敏感信息**
2. **启用 SSL/TLS 连接**
3. **使用最小权限原则**
4. **定期更新数据库驱动**
5. **监控数据库连接和查询**

## 📈 监控和维护

### 健康检查

```python
from app.core.database import check_database_health

async def monitor_database():
    """监控数据库状态"""
    health = await check_database_health()
    if health["status"] != "healthy":
        # 发送告警
        logger.error(f"Database health check failed: {health}")
```

### 性能监控

```python
import time
from functools import wraps

def monitor_query_time(func):
    """监控查询执行时间"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        if execution_time > 1.0:  # 超过1秒的慢查询
            logger.warning(f"Slow query detected: {func.__name__} took {execution_time:.2f}s")
        
        return result
    return wrapper
```

## 🚀 部署建议

### 生产环境

1. **使用 PostgreSQL 或 MySQL**
2. **配置主从复制**
3. **设置连接池监控**
4. **定期备份数据**
5. **使用数据库代理（如 PgBouncer）**

### 开发环境

1. **使用 SQLite 快速开发**
2. **启用 SQL 日志调试**
3. **使用 Docker 容器化数据库**

### 测试环境

1. **使用内存数据库**
2. **自动化数据库重置**
3. **并行测试隔离**
