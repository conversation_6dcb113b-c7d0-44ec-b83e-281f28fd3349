"""
本地存储实现
"""

import os
import shutil
import hashlib
import mimetypes
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, AsyncGenerator

from .base import StorageInterface, FileInfo, StorageException


class LocalStorage(StorageInterface):
    """本地文件系统存储实现"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化本地存储
        
        Args:
            config: 配置参数，包含 base_path 等
        """
        super().__init__(config)
        self.base_path = Path(config.get('base_path', './storage'))
        self.max_file_size = config.get('max_file_size', 100 * 1024 * 1024)  # 默认100MB
        self._connected = False
    
    async def connect(self) -> bool:
        """连接到本地存储（创建基础目录）"""
        try:
            self.base_path.mkdir(parents=True, exist_ok=True)
            self._connected = True
            return True
        except Exception as e:
            raise StorageException(f"Failed to initialize local storage: {e}")
    
    async def disconnect(self) -> None:
        """断开连接（本地存储无需特殊处理）"""
        self._connected = False
    
    def _get_absolute_path(self, path: str) -> Path:
        """获取绝对路径"""
        path = self.normalize_path(path)

        # 处理Windows和Unix路径
        if os.name == 'nt':  # Windows
            # 将Unix风格路径转换为Windows路径
            path = path.replace('/', '\\')
            # 移除开头的反斜杠
            if path.startswith('\\'):
                path = path[1:]
        else:  # Unix/Linux
            # 移除开头的斜杠
            if path.startswith('/'):
                path = path[1:]

        # 处理绝对路径（Windows驱动器路径）
        if os.name == 'nt' and len(path) > 1 and path[1] == ':':
            # 这是一个Windows绝对路径（如 C:\path）
            abs_path = Path(path)
        else:
            # 相对路径，基于base_path
            abs_path = self.base_path / path

        # 确保父目录存在
        try:
            abs_path.parent.mkdir(parents=True, exist_ok=True)
        except Exception:
            pass  # 忽略创建目录的错误

        # 安全检查：确保路径在base_path内（仅对相对路径）
        if not (os.name == 'nt' and len(str(abs_path)) > 1 and str(abs_path)[1] == ':'):
            try:
                abs_path.resolve().relative_to(self.base_path.resolve())
            except ValueError:
                raise StorageException(f"Path outside base directory: {path}")

        return abs_path
    
    def _get_relative_path(self, abs_path: Path) -> str:
        """获取相对路径"""
        try:
            # 对于Windows绝对路径，直接返回
            if os.name == 'nt' and abs_path.is_absolute() and len(str(abs_path)) > 1 and str(abs_path)[1] == ':':
                return "/" + str(abs_path).replace("\\", "/")

            rel_path = abs_path.relative_to(self.base_path)
            return "/" + str(rel_path).replace("\\", "/")
        except ValueError:
            # 如果无法计算相对路径，返回绝对路径
            return "/" + str(abs_path).replace("\\", "/")
    
    async def list_files(self, path: str = "/", recursive: bool = False) -> List[FileInfo]:
        """列出目录下的文件"""
        abs_path = self._get_absolute_path(path)
        
        if not abs_path.exists():
            raise StorageException(f"Path does not exist: {path}")
        
        if not abs_path.is_dir():
            raise StorageException(f"Path is not a directory: {path}")
        
        files = []
        
        try:
            if recursive:
                # 递归遍历
                for item in abs_path.rglob("*"):
                    if item.is_file() or item.is_dir():
                        file_info = await self._create_file_info(item)
                        if file_info:
                            files.append(file_info)
            else:
                # 只遍历当前目录
                for item in abs_path.iterdir():
                    if item.is_file() or item.is_dir():
                        file_info = await self._create_file_info(item)
                        if file_info:
                            files.append(file_info)
        except Exception as e:
            raise StorageException(f"Failed to list files: {e}")
        
        return files
    
    async def _create_file_info(self, path: Path) -> Optional[FileInfo]:
        """创建文件信息对象"""
        try:
            stat = path.stat()
            
            # 获取MIME类型
            mime_type = None
            if path.is_file():
                mime_type, _ = mimetypes.guess_type(str(path))
            
            # 计算文件哈希（仅对小文件）
            hash_value = None
            if path.is_file() and stat.st_size < 10 * 1024 * 1024:  # 小于10MB
                hash_value = await self._calculate_file_hash(path)
            
            return FileInfo(
                path=self._get_relative_path(path),
                name=path.name,
                size=stat.st_size if path.is_file() else 0,
                is_directory=path.is_dir(),
                created_at=datetime.fromtimestamp(stat.st_ctime),
                modified_at=datetime.fromtimestamp(stat.st_mtime),
                mime_type=mime_type,
                extension=path.suffix.lower() if path.is_file() else "",
                hash_value=hash_value
            )
        except Exception as e:
            print(f"Error creating file info for {path}: {e}")
            return None
    
    async def _calculate_file_hash(self, path: Path) -> str:
        """计算文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    async def get_file_info(self, path: str) -> Optional[FileInfo]:
        """获取文件信息"""
        abs_path = self._get_absolute_path(path)
        
        if not abs_path.exists():
            return None
        
        return await self._create_file_info(abs_path)
    
    async def file_exists(self, path: str) -> bool:
        """检查文件是否存在"""
        abs_path = self._get_absolute_path(path)
        return abs_path.exists()
    
    async def create_directory(self, path: str) -> bool:
        """创建目录"""
        abs_path = self._get_absolute_path(path)
        
        try:
            abs_path.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            raise StorageException(f"Failed to create directory: {e}")
    
    async def delete_file(self, path: str) -> bool:
        """删除文件或目录"""
        abs_path = self._get_absolute_path(path)
        
        if not abs_path.exists():
            return False
        
        try:
            if abs_path.is_dir():
                shutil.rmtree(abs_path)
            else:
                abs_path.unlink()
            return True
        except Exception as e:
            raise StorageException(f"Failed to delete file: {e}")
    
    async def move_file(self, source_path: str, target_path: str) -> bool:
        """移动/重命名文件"""
        source_abs = self._get_absolute_path(source_path)
        target_abs = self._get_absolute_path(target_path)
        
        if not source_abs.exists():
            raise StorageException(f"Source file does not exist: {source_path}")
        
        try:
            # 确保目标目录存在
            target_abs.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source_abs), str(target_abs))
            return True
        except Exception as e:
            raise StorageException(f"Failed to move file: {e}")
    
    async def copy_file(self, source_path: str, target_path: str) -> bool:
        """复制文件"""
        source_abs = self._get_absolute_path(source_path)
        target_abs = self._get_absolute_path(target_path)
        
        if not source_abs.exists():
            raise StorageException(f"Source file does not exist: {source_path}")
        
        try:
            # 确保目标目录存在
            target_abs.parent.mkdir(parents=True, exist_ok=True)
            
            if source_abs.is_dir():
                shutil.copytree(str(source_abs), str(target_abs))
            else:
                shutil.copy2(str(source_abs), str(target_abs))
            return True
        except Exception as e:
            raise StorageException(f"Failed to copy file: {e}")
    
    async def upload_file(self, local_path: str, remote_path: str) -> bool:
        """上传文件（本地存储即复制文件）"""
        return await self.copy_file(local_path, remote_path)
    
    async def download_file(self, remote_path: str, local_path: str) -> bool:
        """下载文件（本地存储即复制文件）"""
        return await self.copy_file(remote_path, local_path)
    
    async def read_file(self, path: str) -> bytes:
        """读取文件内容"""
        abs_path = self._get_absolute_path(path)
        
        if not abs_path.exists() or abs_path.is_dir():
            raise StorageException(f"File does not exist or is a directory: {path}")
        
        try:
            with open(abs_path, 'rb') as f:
                return f.read()
        except Exception as e:
            raise StorageException(f"Failed to read file: {e}")
    
    async def write_file(self, path: str, content: bytes) -> bool:
        """写入文件内容"""
        abs_path = self._get_absolute_path(path)
        
        # 检查文件大小限制
        if len(content) > self.max_file_size:
            raise StorageException(f"File size exceeds limit: {len(content)} > {self.max_file_size}")
        
        try:
            # 确保目录存在
            abs_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(abs_path, 'wb') as f:
                f.write(content)
            return True
        except Exception as e:
            raise StorageException(f"Failed to write file: {e}")
    
    async def get_file_stream(self, path: str) -> AsyncGenerator[bytes, None]:
        """获取文件流"""
        abs_path = self._get_absolute_path(path)
        
        if not abs_path.exists() or abs_path.is_dir():
            raise StorageException(f"File does not exist or is a directory: {path}")
        
        try:
            with open(abs_path, 'rb') as f:
                while True:
                    chunk = f.read(8192)  # 8KB chunks
                    if not chunk:
                        break
                    yield chunk
        except Exception as e:
            raise StorageException(f"Failed to stream file: {e}")
    
    async def is_connected(self) -> bool:
        """检查连接状态"""
        return self._connected and self.base_path.exists()
