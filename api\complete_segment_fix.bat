@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo 完整修复文档分段功能
echo 解决 segment_metadata 字段缺失问题
echo ========================================
echo.

REM 设置环境变量
set PYTHONPATH=%cd%

echo 1. 检查Python环境和依赖...
if not exist ".venv\Scripts\python.exe" (
    echo ❌ Python虚拟环境不存在，请先创建虚拟环境
    pause
    exit /b 1
)

echo ✅ Python虚拟环境检查通过

echo.
echo 2. 检查数据库连接...
.venv\Scripts\python.exe -c "
import os
import sys
sys.path.insert(0, '.')
try:
    from app.core.database import get_sync_session
    db = next(get_sync_session())
    db.close()
    print('✅ 数据库连接成功')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    sys.exit(1)
"

if %errorlevel% neq 0 (
    echo 数据库连接失败，请检查数据库配置
    pause
    exit /b 1
)

echo.
echo 3. 显示当前问题诊断...
.venv\Scripts\python.exe -c "
import sys
sys.path.insert(0, '.')
try:
    from app.models.document_segment import DocumentSegmentTask
    task = DocumentSegmentTask()
    # 尝试访问 segment_metadata 字段
    task.segment_metadata = {'test': 'value'}
    print('✅ Python模型 segment_metadata 字段正常')
except Exception as e:
    print(f'❌ Python模型问题: {e}')

try:
    from app.core.database import get_sync_session
    db = next(get_sync_session())
    result = db.execute(\"\"\"
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'document_segment_tasks' 
        AND column_name = 'segment_metadata'
    \"\"\")
    if result.scalar():
        print('✅ 数据库 segment_metadata 字段存在')
    else:
        print('❌ 数据库 segment_metadata 字段缺失')
    db.close()
except Exception as e:
    print(f'❌ 数据库检查失败: {e}')
"

echo.
echo 4. 准备执行完整修复...
echo 注意：此操作将重新创建分段相关表，现有数据将被备份
echo.
set /p confirm="确认执行完整修复？(y/n): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 5. 执行SQL修复脚本...
echo 请手动执行以下SQL脚本：
echo.
echo 方法1 - 使用psql命令行：
echo psql -h ************** -p 5432 -U postgres -d xhc_rag -f sql_scripts/complete_segment_fix.sql
echo.
echo 方法2 - 使用pgAdmin等工具：
echo 打开 sql_scripts/complete_segment_fix.sql 文件并执行
echo.
echo 执行完成后按任意键继续...
pause >nul

echo.
echo 6. 验证修复结果...
.venv\Scripts\python.exe test_complete_segment_fix.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 修复验证失败，请检查：
    echo 1. SQL脚本是否完全执行成功
    echo 2. 数据库连接是否正常
    echo 3. 是否有权限创建表和枚举类型
    pause
    exit /b 1
)

echo.
echo 7. 重启相关服务...

REM 停止现有的Celery进程
echo 停止现有Celery服务...
taskkill /f /im python.exe /fi "WINDOWTITLE eq Celery Worker" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq Celery Beat" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq Celery Flower" 2>nul

timeout /t 3 /nobreak >nul

REM 启动Celery服务
echo 启动Celery Worker...
start "Celery Worker" cmd /k "cd /d %cd% && .venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=4 --queues=default,upload_queue,file_queue,segment_queue --hostname=worker@%%h"

timeout /t 3 /nobreak >nul

echo 启动Celery Beat...
start "Celery Beat" cmd /k "cd /d %cd% && .venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app beat --loglevel=info"

timeout /t 2 /nobreak >nul

echo 启动Celery Flower监控...
start "Celery Flower" cmd /k "cd /d %cd% && .venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app flower --port=5555"

echo.
echo 8. 最终验证...
echo 等待服务启动...
timeout /t 5 /nobreak >nul

.venv\Scripts\python.exe -c "
import sys
sys.path.insert(0, '.')
try:
    from app.models.document_segment import DocumentSegmentTask, SegmentMethod, SegmentStatus
    from app.core.database import get_sync_session
    
    # 创建测试任务验证完整流程
    db = next(get_sync_session())
    task = DocumentSegmentTask(
        task_name='最终验证任务',
        description='验证修复是否成功',
        file_ids=['test_file'],
        total_files=1,
        segment_method=SegmentMethod.PARAGRAPH,
        status=SegmentStatus.PENDING,
        segment_metadata={'verification': True, 'timestamp': '2025-06-16'}
    )
    
    db.add(task)
    db.commit()
    db.refresh(task)
    
    print(f'✅ 最终验证成功！任务ID: {task.task_id}')
    print(f'✅ segment_metadata: {task.segment_metadata}')
    
    # 清理测试数据
    db.delete(task)
    db.commit()
    db.close()
    
    print('✅ 所有功能正常，修复完成！')
    
except Exception as e:
    print(f'❌ 最终验证失败: {e}')
    sys.exit(1)
"

if %errorlevel% neq 0 (
    echo.
    echo ❌ 最终验证失败，请检查日志
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 文档分段功能修复完成！
echo ========================================
echo.
echo ✅ 数据库表结构已修复
echo ✅ segment_metadata 字段已添加
echo ✅ 枚举值已修正为大写
echo ✅ Python模型与数据库完全匹配
echo ✅ Celery服务已重启
echo.
echo 现在可以正常使用以下功能：
echo - 选择多个文件进行批量分段
echo - 点击开始AI分段处理
echo - 查看实时处理进度
echo - 分段数据正常存入document_segments表
echo.
echo 服务状态：
echo - Celery Worker: 处理分段任务
echo - Celery Beat: 定时任务调度
echo - Celery Flower: 监控界面 http://localhost:5555
echo.
echo 如果仍有问题，请检查：
echo 1. 数据库表是否正确创建
echo 2. Celery服务是否正常运行
echo 3. 文件上传和存储是否正常
echo.
pause
