'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Upload,
  X,
  File,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Trash,
  Folder,
  Plus,
  Pause,
  Play,
  ChevronLeft,
  ChevronRight,
  Settings
} from 'lucide-react';
import FileTypeIcon from './FileTypeIcon';
import { uploadTaskManager, UploadTask } from '../../lib/uploadTaskManager';
import apiClient from '@/lib/api';

interface FileUploadItem {
  id: string;
  file: File;
  name: string;
  size: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  uploadedPath?: string;
}

interface FileUploaderProps {
  storageId: number | null;
  currentPath: string;
  onUploadComplete: () => void;
  onClose: () => void;
  isOpen: boolean;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  storageId,
  currentPath,
  onUploadComplete,
  onClose,
  isOpen
}) => {
  const [tasks, setTasks] = useState<UploadTask[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTasks, setTotalTasks] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 更新任务列表
  const updateTasks = useCallback(() => {
    const paginatedData = uploadTaskManager.getTasksPaginated(currentPage, pageSize);
    setTasks(paginatedData.tasks);
    setTotalPages(paginatedData.pages);
    setTotalTasks(paginatedData.total);
  }, [currentPage, pageSize]);

  // 监听任务变化
  useEffect(() => {
    if (isOpen) {
      updateTasks();
      uploadTaskManager.addListener(updateTasks);

      return () => {
        uploadTaskManager.removeListener(updateTasks);
      };
    }
  }, [isOpen, updateTasks]);

  // 添加文件到上传队列
  const addFiles = useCallback((files: FileList | File[]) => {
    if (!storageId) {
      alert('请先选择存储配置');
      return;
    }

    const fileArray = Array.from(files);
    uploadTaskManager.addTasks(fileArray, storageId, currentPath);
    updateTasks();
  }, [storageId, currentPath, updateTasks]);

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      addFiles(files);
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  // 处理拖拽
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      addFiles(files);
    }
  };

  // 移除任务
  const removeTask = (taskId: string) => {
    uploadTaskManager.removeTask(taskId);
    updateTasks();
  };

  // 重试任务
  const retryTask = (taskId: string) => {
    uploadTaskManager.retryTask(taskId);
    updateTasks();
  };

  // 暂停/恢复上传
  const togglePause = () => {
    if (isPaused) {
      uploadTaskManager.resumeAll();
      setIsPaused(false);
    } else {
      uploadTaskManager.pauseAll();
      setIsPaused(true);
    }
    updateTasks();
  };

  // 清空已完成的任务
  const clearCompleted = () => {
    uploadTaskManager.clearCompletedTasks();
    updateTasks();
  };

  // 分页控制
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 重置到第一页
  };

  // 获取状态统计
  const getStats = () => {
    const pending = tasks.filter(task => task.status === 'pending').length;
    const uploading = tasks.filter(task => task.status === 'uploading').length;
    const error = tasks.filter(task => task.status === 'error').length;
    const paused = tasks.filter(task => task.status === 'paused').length;

    return {
      total: totalTasks,
      pending,
      uploading,
      error,
      paused,
      active: pending + uploading
    };
  };

  const stats = getStats();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col"
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">文件上传队列</h2>
            <p className="text-sm text-gray-500 mt-1">
              上传到: {currentPath || '/'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {/* 控制按钮 */}
            {stats.total > 0 && (
              <>
                <button
                  onClick={togglePause}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title={isPaused ? "恢复上传" : "暂停上传"}
                >
                  {isPaused ? (
                    <Play className="w-5 h-5 text-green-600" />
                  ) : (
                    <Pause className="w-5 h-5 text-orange-600" />
                  )}
                </button>
                <button
                  onClick={clearCompleted}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title="清空已完成"
                >
                  <Trash className="w-5 h-5 text-red-600" />
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* 统计信息和分页设置 */}
        {stats.total > 0 && (
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6 text-sm">
                <span className="text-gray-600">
                  总计: <span className="font-medium">{stats.total}</span>
                </span>
                <span className="text-blue-600">
                  待处理: <span className="font-medium">{stats.pending}</span>
                </span>
                <span className="text-green-600">
                  上传中: <span className="font-medium">{stats.uploading}</span>
                </span>
                <span className="text-red-600">
                  失败: <span className="font-medium">{stats.error}</span>
                </span>
                {stats.paused > 0 && (
                  <span className="text-orange-600">
                    暂停: <span className="font-medium">{stats.paused}</span>
                  </span>
                )}
              </div>

              {/* 分页大小设置 */}
              <div className="flex items-center space-x-2 text-sm">
                <span className="text-gray-600">每页显示:</span>
                <select
                  value={pageSize}
                  onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                  className="border border-gray-300 rounded px-2 py-1 text-sm"
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* 文件列表区域 */}
        <div className="flex-1 overflow-hidden">
          {tasks.length === 0 ? (
            // 拖拽上传区域
            <div
              className={`h-full flex items-center justify-center border-2 border-dashed transition-colors ${
                isDragging 
                  ? 'border-blue-400 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="text-center">
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  拖拽文件到这里
                </h3>
                <p className="text-gray-500 mb-4">
                  或者点击下方按钮选择文件
                </p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                >
                  <Plus className="w-4 h-4 inline mr-2" />
                  选择文件
                </button>
              </div>
            </div>
          ) : (
            // 任务列表
            <div className="h-full flex flex-col">
              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-3">
                  <AnimatePresence>
                    {tasks.map((task) => (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="bg-white border border-gray-200 rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3 flex-1">
                            <FileTypeIcon
                              fileName={task.fileName}
                              isDirectory={false}
                              className="w-8 h-8"
                              size={32}
                            />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {task.fileName}
                              </p>
                              <div className="flex items-center space-x-2 text-xs text-gray-500">
                                <span>{formatFileSize(task.fileSize)}</span>
                                {task.retryCount > 0 && (
                                  <span className="text-orange-600">
                                    (重试 {task.retryCount} 次)
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-3">
                            {/* 状态图标 */}
                            {task.status === 'pending' && (
                              <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
                            )}
                            {task.status === 'uploading' && (
                              <div className="w-5 h-5">
                                <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />
                              </div>
                            )}
                            {task.status === 'paused' && (
                              <Pause className="w-5 h-5 text-orange-500" />
                            )}
                            {task.status === 'error' && (
                              <div className="flex items-center space-x-2">
                                <AlertCircle className="w-5 h-5 text-red-500" />
                                <button
                                  onClick={() => retryTask(task.id)}
                                  className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                                >
                                  重试
                                </button>
                              </div>
                            )}

                            {/* 删除按钮 */}
                            <button
                              onClick={() => removeTask(task.id)}
                              className="p-1 hover:bg-gray-100 rounded transition-colors"
                            >
                              <X className="w-4 h-4 text-gray-400" />
                            </button>
                          </div>
                        </div>

                        {/* 进度条 */}
                        {task.status === 'uploading' && (
                          <div className="mt-3">
                            <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                              <span>上传中...</span>
                              <span>{task.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${task.progress}%` }}
                              />
                            </div>
                          </div>
                        )}

                        {/* 错误信息 */}
                        {task.status === 'error' && task.error && (
                          <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
                            {task.error}
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </div>

              {/* 分页控制 */}
              {totalPages > 1 && (
                <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      显示 {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, totalTasks)}
                      ，共 {totalTasks} 个任务
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeft className="w-4 h-4" />
                      </button>

                      <span className="text-sm text-gray-600">
                        {currentPage} / {totalPages}
                      </span>

                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronRight className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Plus className="w-4 h-4 inline mr-2" />
              添加文件
            </button>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              关闭
            </button>
          </div>
        </div>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileSelect}
        />
      </motion.div>
    </div>
  );
};

export default FileUploader;
