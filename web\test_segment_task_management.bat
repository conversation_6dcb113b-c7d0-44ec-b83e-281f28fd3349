@echo off
REM 测试分段任务管理功能 - 前端部分
REM 适用于Windows系统

echo ========================================
echo 分段任务管理功能测试 - 前端
echo ========================================

REM 设置环境变量
cd /d "%~dp0"

echo 当前目录: %cd%
echo.

REM 检查Node.js环境
node --version
if errorlevel 1 (
    echo 错误: Node.js未安装或不在PATH中
    pause
    exit /b 1
)

REM 检查npm环境
npm --version
if errorlevel 1 (
    echo 错误: npm未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 1. 检查依赖安装...
if not exist "node_modules" (
    echo 安装依赖中...
    npm install
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖已安装
)

echo.
echo 2. 检查TypeScript编译...
npx tsc --noEmit --skipLibCheck
if errorlevel 1 (
    echo 警告: TypeScript编译有错误，但继续测试
) else (
    echo TypeScript编译通过
)

echo.
echo 3. 检查关键文件...
if exist "app\file-manager\segment\task\page.tsx" (
    echo ✅ 分段任务管理页面存在
) else (
    echo ❌ 分段任务管理页面不存在
)

if exist "app\file-manager\segment\batch\page.tsx" (
    echo ✅ AI批量分段页面存在
) else (
    echo ❌ AI批量分段页面不存在
)

if exist "lib\api.ts" (
    echo ✅ API客户端文件存在
) else (
    echo ❌ API客户端文件不存在
)

echo.
echo 4. 启动开发服务器...
echo 正在启动Next.js开发服务器...
echo 请在浏览器中访问以下URL进行测试:
echo.
echo - 主页: http://localhost:3000
echo - 文件管理: http://localhost:3000/file-manager
echo - 分段任务管理: http://localhost:3000/file-manager/segment/task
echo - AI批量分段: http://localhost:3000/file-manager/segment/batch
echo.
echo 按Ctrl+C停止服务器
echo.

REM 启动开发服务器
npm run dev

echo.
echo ========================================
echo 测试完成
echo ========================================
pause
