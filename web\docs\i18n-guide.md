# 多语言国际化指南

本文档介绍如何在AI知识库项目中使用和扩展多语言功能。

## 🌍 架构概述

项目采用模块化的多语言架构，支持动态语言切换和按需加载。

### 文件结构
```
web/lib/i18n/
├── index.ts              # 主配置文件，定义类型和工具函数
├── locales/              # 语言文件目录
│   ├── zh-CN.ts         # 中文简体
│   ├── zh-TW.ts         # 中文繁体
│   ├── en.ts            # 英文
│   └── ja.ts            # 日文
└── README.md            # 使用说明
```

## 🚀 基本使用

### 1. 在组件中使用翻译

```tsx
import { useLanguage } from '@/contexts/LanguageContext';

const MyComponent: React.FC = () => {
  const { t, language, setLanguage, isLoading } = useLanguage();

  // 检查加载状态
  if (isLoading || !t) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h1>{t.common.title}</h1>
      <p>{t.common.description}</p>
      <button onClick={() => setLanguage('en')}>
        Switch to English
      </button>
    </div>
  );
};
```

### 2. 访问嵌套翻译

```tsx
// 访问登录相关翻译
<input placeholder={t.login.username} />
<button>{t.login.loginButton}</button>

// 访问错误信息
<div className="error">{t.errors.networkError}</div>

// 访问导航翻译
<nav>
  <a href="/">{t.navigation.home}</a>
  <a href="/dashboard">{t.navigation.dashboard}</a>
</nav>
```

### 3. 使用工具函数

```tsx
import { formatNumber, formatDate, detectBrowserLanguage } from '@/lib/i18n';

const MyComponent: React.FC = () => {
  const { language } = useLanguage();

  // 格式化数字
  const formattedNumber = formatNumber(1234567, language);
  // zh-CN: "1,234,567"
  // en: "1,234,567"
  // ja: "1,234,567"

  // 格式化日期
  const formattedDate = formatDate(new Date(), language);
  // zh-CN: "2024年1月1日"
  // en: "January 1, 2024"
  // ja: "2024年1月1日"

  return (
    <div>
      <p>Number: {formattedNumber}</p>
      <p>Date: {formattedDate}</p>
    </div>
  );
};
```

## 📝 添加新语言

### 1. 创建语言文件

在 `web/lib/i18n/locales/` 目录下创建新的语言文件，例如 `fr.ts`（法语）：

```typescript
// web/lib/i18n/locales/fr.ts
import { Translations } from '../index';

const fr: Translations = {
  common: {
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    // ... 其他翻译
  },
  login: {
    title: 'Base de Connaissances IA',
    subtitle: 'Système de Gestion et de Recherche de Connaissances Intelligentes',
    // ... 其他翻译
  },
  // ... 其他部分
};

export default fr;
```

### 2. 更新类型定义

在 `web/lib/i18n/index.ts` 中添加新语言：

```typescript
// 添加新语言类型
export type Language = 'zh-CN' | 'zh-TW' | 'en' | 'ja' | 'fr';

// 添加语言配置
export const languageConfig = {
  // ... 现有配置
  'fr': {
    name: 'Français',
    flag: '🇫🇷',
    direction: 'ltr' as const,
  },
};
```

### 3. 更新语言切换器

在 `web/components/LanguageSwitcher.tsx` 中添加新语言选项：

```tsx
const languages: { code: Language; name: string; flag: string }[] = [
  // ... 现有语言
  { code: 'fr', name: 'Français', flag: languageConfig['fr'].flag },
];
```

### 4. 更新检测逻辑

在 `detectBrowserLanguage` 函数中添加新语言检测：

```typescript
export const detectBrowserLanguage = (): Language => {
  // ... 现有逻辑
  else if (browserLanguage?.startsWith('fr')) {
    return 'fr';
  }
  // ...
};
```

## 🔧 高级功能

### 1. 条件翻译

```tsx
const MyComponent: React.FC = () => {
  const { t, language } = useLanguage();

  // 根据语言显示不同内容
  const getWelcomeMessage = () => {
    switch (language) {
      case 'zh-CN':
        return '欢迎使用AI知识库！';
      case 'en':
        return 'Welcome to AI Knowledge Base!';
      default:
        return t.common.welcome;
    }
  };

  return <h1>{getWelcomeMessage()}</h1>;
};
```

### 2. 动态翻译键

```tsx
const MyComponent: React.FC = () => {
  const { t } = useLanguage();
  const [status, setStatus] = useState<'processing' | 'completed' | 'failed'>('processing');

  // 动态访问翻译键
  const statusText = t.documents[status]; // t.documents.processing 等

  return <span className={`status-${status}`}>{statusText}</span>;
};
```

### 3. 翻译插值

```tsx
// 在翻译文件中定义带参数的翻译
const translations = {
  messages: {
    welcome: 'Welcome, {name}!',
    itemCount: 'You have {count} items',
  }
};

// 在组件中使用
const MyComponent: React.FC = () => {
  const { t } = useLanguage();
  const userName = 'John';
  const itemCount = 5;

  // 简单的字符串替换
  const welcomeMessage = t.messages.welcome.replace('{name}', userName);
  const countMessage = t.messages.itemCount.replace('{count}', itemCount.toString());

  return (
    <div>
      <p>{welcomeMessage}</p>
      <p>{countMessage}</p>
    </div>
  );
};
```

## 🎯 最佳实践

### 1. 翻译键命名规范

- 使用小驼峰命名法：`loginButton`、`userProfile`
- 按功能模块分组：`login.*`、`dashboard.*`、`settings.*`
- 保持键名简洁且有意义

### 2. 翻译内容规范

- 保持翻译简洁明了
- 考虑不同语言的文本长度差异
- 使用适当的标点符号和格式

### 3. 性能优化

- 利用缓存机制避免重复加载
- 使用 `React.memo` 优化组件渲染
- 预加载常用语言

```tsx
// 优化示例
const OptimizedComponent = React.memo(() => {
  const { t } = useLanguage();
  
  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
});
```

### 4. 错误处理

```tsx
const SafeTranslation: React.FC<{ translationKey: string }> = ({ translationKey }) => {
  const { t } = useLanguage();
  
  try {
    // 安全访问嵌套属性
    const keys = translationKey.split('.');
    let value: any = t;
    
    for (const key of keys) {
      value = value?.[key];
    }
    
    return <span>{value || translationKey}</span>;
  } catch (error) {
    console.warn(`Translation key not found: ${translationKey}`);
    return <span>{translationKey}</span>;
  }
};
```

## 🧪 测试

### 1. 单元测试

```tsx
import { render, screen } from '@testing-library/react';
import { LanguageProvider } from '@/contexts/LanguageContext';
import MyComponent from './MyComponent';

test('renders component with Chinese translation', async () => {
  render(
    <LanguageProvider>
      <MyComponent />
    </LanguageProvider>
  );

  // 等待翻译加载
  await screen.findByText('AI知识库');
  
  expect(screen.getByText('AI知识库')).toBeInTheDocument();
});
```

### 2. 语言切换测试

```tsx
test('switches language correctly', async () => {
  const { user } = render(
    <LanguageProvider>
      <LanguageSwitcher />
      <MyComponent />
    </LanguageProvider>
  );

  // 点击语言切换器
  await user.click(screen.getByText('English'));
  
  // 验证语言已切换
  await screen.findByText('AI Knowledge Base');
  expect(screen.getByText('AI Knowledge Base')).toBeInTheDocument();
});
```

## 📚 参考资源

- [React Internationalization](https://react.i18next.com/)
- [Next.js Internationalization](https://nextjs.org/docs/advanced-features/i18n)
- [MDN Intl API](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)
- [Unicode CLDR](http://cldr.unicode.org/)

## 🤝 贡献指南

1. 确保新翻译准确且符合当地习惯
2. 保持翻译键的一致性
3. 更新相关文档
4. 添加必要的测试用例
5. 提交PR前进行充分测试
