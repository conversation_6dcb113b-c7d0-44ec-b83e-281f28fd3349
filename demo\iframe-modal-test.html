<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe嵌套弹窗测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: flex-start;
            justify-content: center;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            width: 300px;
            display: flex;
            align-items: flex-start;
            gap: 16px;
        }

        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .test-card-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: #6366f1;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
        }

        .test-card-content {
            flex: 1;
            min-width: 0;
        }

        .test-card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .test-card-meta {
            font-size: 0.8rem;
            color: #9ca3af;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .test-card-company {
            font-weight: 500;
            color: #6366f1;
        }

        .test-card-creator {
            color: #8b5cf6;
        }

        .test-card-description {
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 全屏弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
            overflow-y: auto;
        }

        .modal-content {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: transparent;
            padding: 0;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            padding: 12px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .modal-header-left {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
        }

        .modal-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
            color: #6366f1;
            flex-shrink: 0;
            box-shadow: 0 1px 4px rgba(99, 102, 241, 0.1);
        }

        .modal-info {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        .modal-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-meta {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.1;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 2px;
        }

        .modal-company {
            font-weight: 600;
            color: #6366f1;
        }

        .modal-creator {
            color: #8b5cf6;
            font-weight: 500;
        }

        .modal-description-short {
            font-size: 0.7rem;
            color: #9ca3af;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .modal-iframe-container {
            position: absolute;
            top: 68px;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: calc(100vh - 68px);
            overflow: hidden;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            background: white;
            display: block;
        }

        .close {
            width: 32px;
            height: 32px;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #ef4444;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(239, 68, 68, 0.2);
            flex-shrink: 0;
            margin-left: 12px;
        }

        .close:hover {
            background: rgba(239, 68, 68, 0.2);
            color: #dc2626;
            transform: scale(1.05);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .test-card {
                width: 100%;
                padding: 16px;
                gap: 12px;
            }
            
            .test-card-icon {
                width: 48px;
                height: 48px;
                font-size: 24px;
            }
            
            .modal-header {
                padding: 10px 15px;
            }
            
            .modal-icon {
                width: 36px;
                height: 36px;
                font-size: 18px;
                margin-right: 10px;
            }
            
            .modal-title {
                font-size: 1rem;
            }
            
            .modal-meta {
                font-size: 0.7rem;
                gap: 4px;
            }
            
            .modal-description-short {
                font-size: 0.65rem;
            }
            
            .modal-iframe-container {
                top: 58px;
                height: calc(100vh - 58px);
            }
            
            .close {
                width: 28px;
                height: 28px;
                font-size: 14px;
                margin-left: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="test-card" onclick="openModal('baidu')">
        <div class="test-card-icon">🔍</div>
        <div class="test-card-content">
            <div class="test-card-title">百度搜索</div>
            <div class="test-card-meta">
                <span class="test-card-company">百度</span>
                <span> • </span>
                <span class="test-card-creator">李彦宏</span>
            </div>
            <div class="test-card-description">中国最大的搜索引擎，提供网页、图片、视频等多种搜索服务</div>
        </div>
    </div>

    <div class="test-card" onclick="openModal('github')">
        <div class="test-card-icon">💻</div>
        <div class="test-card-content">
            <div class="test-card-title">GitHub</div>
            <div class="test-card-meta">
                <span class="test-card-company">Microsoft</span>
                <span> • </span>
                <span class="test-card-creator">Tom Preston-Werner</span>
            </div>
            <div class="test-card-description">全球最大的代码托管平台，开发者协作和版本控制的首选工具</div>
        </div>
    </div>

    <div class="test-card" onclick="openModal('youtube')">
        <div class="test-card-icon">🎬</div>
        <div class="test-card-content">
            <div class="test-card-title">YouTube</div>
            <div class="test-card-meta">
                <span class="test-card-company">Google</span>
                <span> • </span>
                <span class="test-card-creator">Chad Hurley</span>
            </div>
            <div class="test-card-description">全球最大的视频分享平台，用户可以上传、观看和分享视频内容</div>
        </div>
    </div>

    <!-- 全屏弹窗 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-header-left">
                    <div class="modal-icon" id="modalIcon"></div>
                    <div class="modal-info">
                        <div class="modal-title" id="modalTitle"></div>
                        <div class="modal-meta">
                            <span class="modal-company" id="modalCompany"></span>
                            <span>•</span>
                            <span class="modal-creator" id="modalCreator"></span>
                        </div>
                        <div class="modal-description-short" id="modalDescriptionShort"></div>
                    </div>
                </div>
                <button class="close" onclick="closeModal()" title="关闭">&times;</button>
            </div>
            <div class="modal-iframe-container">
                <iframe id="modalIframe" class="modal-iframe" src="" frameborder="0" border="0" allowfullscreen seamless></iframe>
            </div>
        </div>
    </div>

    <script>
        const testSites = {
            baidu: {
                icon: '🔍',
                title: '百度搜索',
                company: '百度',
                creator: '李彦宏',
                description: '中国最大的搜索引擎，提供网页、图片、视频等多种搜索服务',
                url: 'https://www.baidu.com'
            },
            github: {
                icon: '💻',
                title: 'GitHub',
                company: 'Microsoft',
                creator: 'Tom Preston-Werner',
                description: '全球最大的代码托管平台，开发者协作和版本控制的首选工具',
                url: 'https://github.com'
            },
            youtube: {
                icon: '🎬',
                title: 'YouTube',
                company: 'Google',
                creator: 'Chad Hurley',
                description: '全球最大的视频分享平台，用户可以上传、观看和分享视频内容',
                url: 'https://www.youtube.com'
            }
        };

        function openModal(siteKey) {
            const site = testSites[siteKey];
            const modal = document.getElementById('modal');
            const modalIcon = document.getElementById('modalIcon');
            const modalTitle = document.getElementById('modalTitle');
            const modalCompany = document.getElementById('modalCompany');
            const modalCreator = document.getElementById('modalCreator');
            const modalDescriptionShort = document.getElementById('modalDescriptionShort');
            const modalIframe = document.getElementById('modalIframe');
            
            // 设置头部信息
            modalIcon.textContent = site.icon;
            modalTitle.textContent = site.title;
            modalTitle.title = site.title;
            modalCompany.textContent = site.company;
            modalCreator.textContent = site.creator;
            modalDescriptionShort.textContent = site.description;
            
            // 加载第三方页面
            modalIframe.src = site.url;
            
            // 显示弹窗
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // 添加加载提示
            modalIframe.onload = function() {
                console.log(`已加载: ${site.title}`);
            };
            
            modalIframe.onerror = function() {
                console.error(`加载失败: ${site.title}`);
                modalIframe.src = `data:text/html,<html><body style="font-family: Arial, sans-serif; text-align: center; padding: 50px; color: #666;"><h2>页面加载失败</h2><p>无法加载 ${site.title} 的页面</p><p><a href="${site.url}" target="_blank" style="color: #667eea;">点击这里在新窗口中打开</a></p></body></html>`;
            };
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            const modalIframe = document.getElementById('modalIframe');
            
            // 清空iframe内容以停止加载
            modalIframe.src = 'about:blank';
            
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // 点击弹窗外部关闭（可选）
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
