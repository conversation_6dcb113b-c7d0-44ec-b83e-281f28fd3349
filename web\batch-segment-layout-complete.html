<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量分段功能完整优化方案</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        .status-badge {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 5px;
        }
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            margin-right: 10px;
            font-size: 1.5rem;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            overflow-x: auto;
        }
        .layout-demo {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .layout-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            min-height: 400px;
        }
        .left-panel {
            background: white;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 15px;
        }
        .right-panel {
            background: white;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 15px;
        }
        .file-item {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            padding: 10px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.2s;
        }
        .file-item:hover {
            background: #e2e8f0;
            border-color: #3b82f6;
        }
        .file-item.active {
            background: #dbeafe;
            border-color: #3b82f6;
            border-width: 2px;
        }
        .segment-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        .pagination button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
        }
        .pagination button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .success-box {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #047857;
        }
        .fix-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .fix-item {
            display: flex;
            align-items: flex-start;
            margin: 10px 0;
            padding: 8px 0;
        }
        .fix-item-icon {
            margin-right: 10px;
            margin-top: 2px;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 批量分段功能完整优化方案</h1>
            <p class="subtitle">Celery任务队列 + 左右分栏布局 + 实时进度监控</p>
            <div>
                <span class="status-badge">✅ Celery任务队列</span>
                <span class="status-badge">✅ 左右分栏布局</span>
                <span class="status-badge">✅ 实时进度监控</span>
                <span class="status-badge">✅ 分段详情展示</span>
            </div>
        </div>

        <!-- 优化方案1：Celery任务队列管理 -->
        <div class="feature-card">
            <div class="feature-title">
                <span class="feature-icon">⚙️</span>
                优化方案1：Celery任务队列管理
            </div>
            
            <ul class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">🔄</span>
                    <div>
                        <strong>独立任务队列</strong><br>
                        创建 <code>segment_queue</code> 队列，与文件上传队列分离，避免相互干扰
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">📁</span>
                    <div>
                        <strong>每文件独立任务</strong><br>
                        批量任务创建多个子任务，每个文件一个独立的Celery任务
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">📊</span>
                    <div>
                        <strong>实时状态监控</strong><br>
                        通过Celery任务状态实时获取每个文件的处理进度
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">🔧</span>
                    <div>
                        <strong>任务管理功能</strong><br>
                        支持任务暂停、恢复、停止等操作
                    </div>
                </li>
            </ul>

            <h4>Celery任务架构：</h4>
            <div class="code-block">
# 主任务：批量分段任务
@celery_app.task(bind=True, queue='segment_queue')
def process_batch_segment_task(self, task_id: int):
    # 为每个文件创建独立的子任务
    for file_id in task.file_ids:
        subtask = process_single_file_segment.delay(task_id, file_id)
    
    # 监控所有子任务完成情况
    monitor_subtasks.delay(task_id, subtask_ids)

# 子任务：单文件分段处理
@celery_app.task(bind=True, queue='segment_queue')
def process_single_file_segment(self, task_id: int, file_id: str):
    # 处理单个文件的分段
    # 实时更新进度状态
    current_task.update_state(
        state='PROGRESS',
        meta={'file_id': file_id, 'progress': 50}
    )

# 向量化任务
@celery_app.task(bind=True, queue='segment_queue')
def vectorize_file_segments(self, task_id: int, file_id: str):
    # 对文件分段进行向量化处理
            </div>
        </div>

        <!-- 优化方案2：左右分栏布局 -->
        <div class="feature-card">
            <div class="feature-title">
                <span class="feature-icon">📱</span>
                优化方案2：左右分栏布局
            </div>
            
            <p><strong>布局说明：</strong></p>
            <ul class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">⬅️</span>
                    <span><strong>左侧固定区域</strong>：显示文件列表、进度状态、分段统计</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">➡️</span>
                    <span><strong>右侧内容区域</strong>：显示选中文件的分段详情，支持分页</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">🔄</span>
                    <span><strong>交互逻辑</strong>：点击左侧文件，右侧显示对应分段内容</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">📄</span>
                    <span><strong>分页展示</strong>：每页默认20条分段，支持翻页浏览</span>
                </li>
            </ul>

            <h4>布局演示：</h4>
            <div class="layout-demo">
                <div class="layout-grid">
                    <div class="left-panel">
                        <h4 style="margin-top: 0; color: #3b82f6;">📁 文件列表 (左侧固定)</h4>
                        
                        <div class="file-item active">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: bold; font-size: 0.9rem;">政务云申请表.docx</div>
                                    <div style="font-size: 0.8rem; color: #64748b;">2.3MB • 已完成</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #10b981; font-weight: bold;">✅ 100%</div>
                                    <div style="font-size: 0.8rem; color: #64748b;">23个分段</div>
                                </div>
                            </div>
                        </div>

                        <div class="file-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: bold; font-size: 0.9rem;">技术文档.pdf</div>
                                    <div style="font-size: 0.8rem; color: #64748b;">5.1MB • 处理中</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #3b82f6; font-weight: bold;">🔄 65%</div>
                                    <div style="font-size: 0.8rem; color: #64748b;">13个分段</div>
                                </div>
                            </div>
                        </div>

                        <div class="file-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: bold; font-size: 0.9rem;">AI架构图.pptx</div>
                                    <div style="font-size: 0.8rem; color: #64748b;">1.8MB • 等待中</div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #9ca3af; font-weight: bold;">⏱️ 0%</div>
                                    <div style="font-size: 0.8rem; color: #64748b;">0个分段</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="right-panel">
                        <h4 style="margin-top: 0; color: #10b981;">📄 分段详情 (右侧内容)</h4>
                        <p style="color: #64748b; font-size: 0.9rem; margin-bottom: 15px;">
                            当前文件：政务云申请表.docx (共23个分段)
                        </p>

                        <div class="segment-item">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: bold; color: #374151;">分段 #1</span>
                                <span style="font-size: 0.8rem; color: #64748b;">156字 • 已向量化</span>
                            </div>
                            <div style="color: #4b5563; line-height: 1.5;">
                                深圳市政务云服务申请表是用于申请政务云资源的重要文档。本表格包含了申请单位的基本信息、资源需求说明、技术规格要求等关键内容...
                            </div>
                        </div>

                        <div class="segment-item">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: bold; color: #374151;">分段 #2</span>
                                <span style="font-size: 0.8rem; color: #64748b;">203字 • 已向量化</span>
                            </div>
                            <div style="color: #4b5563; line-height: 1.5;">
                                申请单位需要详细填写组织机构代码、统一社会信用代码、联系人信息等基础资料。同时需要明确说明申请的云服务类型和预期使用规模...
                            </div>
                        </div>

                        <div class="segment-item">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span style="font-weight: bold; color: #374151;">分段 #3</span>
                                <span style="font-size: 0.8rem; color: #64748b;">178字 • 已向量化</span>
                            </div>
                            <div style="color: #4b5563; line-height: 1.5;">
                                技术规格部分要求申请方提供详细的硬件配置需求，包括CPU核数、内存容量、存储空间、网络带宽等具体参数...
                            </div>
                        </div>

                        <div class="pagination">
                            <button disabled>上一页</button>
                            <span style="color: #64748b;">第 1 页，共 2 页</span>
                            <button>下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术实现细节 -->
        <div class="feature-card">
            <div class="feature-title">
                <span class="feature-icon">💻</span>
                技术实现细节
            </div>
            
            <h4>1. 前端状态管理：</h4>
            <div class="code-block">
// 新增状态变量
const [selectedFileId, setSelectedFileId] = useState('');
const [fileSegments, setFileSegments] = useState([]);
const [segmentsPagination, setSegmentsPagination] = useState({
  page: 1, page_size: 20, total_count: 0, total_pages: 0
});

// 获取文件分段详情
const loadFileSegments = async (fileId, page = 1) => {
  const response = await apiClient.get(
    `/api/v1/document-segment/tasks/${taskId}/files/${fileId}/segments?page=${page}&page_size=20`
  );
  setFileSegments(response.data.segments);
  setSegmentsPagination(response.data.pagination);
};

// 处理文件点击
const handleFileClick = (fileId) => {
  setSelectedFileId(fileId);
  loadFileSegments(fileId, 1);
};
            </div>

            <h4>2. 后端API接口：</h4>
            <div class="code-block">
# 获取文件分段详情（分页）
@router.get("/tasks/{task_id}/files/{file_id}/segments")
async def get_file_segments(
    task_id: str, file_id: str, 
    page: int = 1, page_size: int = 20
):
    # 分页查询分段数据
    segments = db.query(DocumentSegment).filter(
        DocumentSegment.task_id == task.id,
        DocumentSegment.file_id == file_id
    ).order_by(DocumentSegment.segment_index)\
     .offset((page-1)*page_size).limit(page_size).all()
    
    return {
        "segments": segments,
        "pagination": {
            "page": page, "page_size": page_size,
            "total_count": total_count, "total_pages": total_pages
        }
    }
            </div>

            <h4>3. 布局结构：</h4>
            <div class="code-block">
{/* 任务开始后：左右分栏布局 */}
{taskStatus === 'running' && (
  &lt;div className="grid grid-cols-3 gap-6 h-screen"&gt;
    {/* 左侧：文件列表 */}
    &lt;div className="col-span-1 bg-white rounded-xl p-6"&gt;
      &lt;h3&gt;文件列表&lt;/h3&gt;
      {files.map(file =&gt; (
        &lt;div 
          key={file.file_id}
          onClick={() =&gt; handleFileClick(file.file_id)}
          className={`file-item ${selectedFileId === file.file_id ? 'active' : ''}`}
        &gt;
          {/* 文件信息和进度 */}
        &lt;/div&gt;
      ))}
    &lt;/div&gt;

    {/* 右侧：分段详情 */}
    &lt;div className="col-span-2 bg-white rounded-xl p-6"&gt;
      &lt;h3&gt;分段详情&lt;/h3&gt;
      {fileSegments.map(segment =&gt; (
        &lt;div key={segment.id} className="segment-item"&gt;
          {/* 分段内容 */}
        &lt;/div&gt;
      ))}
      {/* 分页组件 */}
    &lt;/div&gt;
  &lt;/div&gt;
)}
            </div>
        </div>

        <!-- 优化效果 -->
        <div class="feature-card">
            <div class="feature-title">
                <span class="feature-icon">🎯</span>
                优化效果
            </div>
            
            <div class="success-box">
                <strong>Celery任务队列优势：</strong><br>
                • ⚡ 异步处理，不阻塞主线程<br>
                • 🔄 独立队列，避免与文件上传任务冲突<br>
                • 📊 实时状态监控，精确进度反馈<br>
                • 🛠️ 支持任务控制（暂停/恢复/停止）<br>
                • 📈 可扩展性强，支持分布式处理
            </div>

            <div class="success-box">
                <strong>左右分栏布局优势：</strong><br>
                • 📱 清晰的信息层次，左侧概览右侧详情<br>
                • 🎯 高效的交互体验，点击即可查看详情<br>
                • 📄 分页展示，处理大量分段数据<br>
                • 💾 固定布局，状态保持稳定<br>
                • 🔍 便于查看和管理分段内容
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="action-button" onclick="showCeleryImplementation()">
                ⚙️ Celery实现详情
            </button>
            <button class="action-button" onclick="showLayoutCode()">
                📱 布局代码示例
            </button>
            <button class="action-button" onclick="showAPIDetails()">
                🔌 API接口详情
            </button>
            <button class="action-button" onclick="showTestingGuide()">
                🧪 测试指南
            </button>
        </div>
    </div>

    <script>
        function showCeleryImplementation() {
            alert(`⚙️ Celery实现详情\n\n1. 队列配置\n• 新增 segment_queue 队列\n• 与 upload_queue 和 file_queue 分离\n• 独立的路由和处理逻辑\n\n2. 任务架构\n• 主任务：process_batch_segment_task\n• 子任务：process_single_file_segment\n• 监控任务：monitor_subtasks\n• 向量化任务：vectorize_file_segments\n\n3. 状态管理\n• 使用 current_task.update_state 实时更新\n• 支持 PROGRESS、SUCCESS、FAILURE 状态\n• 元数据包含文件ID、进度百分比等\n\n4. 任务控制\n• 支持任务暂停、恢复、停止\n• 通过 AsyncResult 获取任务状态\n• 错误处理和重试机制\n\n5. 性能优化\n• 每个文件独立处理，并行执行\n• 分批提交数据库，避免长事务\n• 内存优化，及时释放资源`);
        }

        function showLayoutCode() {
            alert(`📱 布局代码示例\n\n主要布局结构：\n\n{taskStatus === 'running' && (\n  <div className="grid grid-cols-3 gap-6 min-h-screen">\n    {/* 左侧文件列表 */}\n    <div className="col-span-1">\n      <FileList \n        files={files}\n        fileProgress={fileProgress}\n        selectedFileId={selectedFileId}\n        onFileClick={handleFileClick}\n      />\n    </div>\n\n    {/* 右侧分段详情 */}\n    <div className="col-span-2">\n      <SegmentDetails\n        fileId={selectedFileId}\n        segments={fileSegments}\n        pagination={segmentsPagination}\n        onPageChange={handlePageChange}\n      />\n    </div>\n  </div>\n)}\n\n关键特性：\n• 响应式网格布局 (grid-cols-3)\n• 左侧1列，右侧2列的比例\n• 固定高度，内容区域可滚动\n• 组件化设计，便于维护`);
        }

        function showAPIDetails() {
            alert(`🔌 API接口详情\n\n1. 创建批量分段任务\nPOST /api/v1/document-segment/tasks\n• 使用 Celery 处理任务\n• 返回任务ID和Celery任务ID\n\n2. 获取任务进度\nGET /api/v1/document-segment/tasks/{task_id}/progress\n• 包含Celery任务状态\n• 每个文件的详细进度\n• 实时状态更新\n\n3. 获取文件分段详情\nGET /api/v1/document-segment/tasks/{task_id}/files/{file_id}/segments\n• 支持分页参数 (page, page_size)\n• 返回分段列表和分页信息\n• 包含分段内容、状态、向量化信息\n\n4. 任务控制接口\nPOST /api/v1/document-segment/tasks/{task_id}/pause\nPOST /api/v1/document-segment/tasks/{task_id}/resume\nPOST /api/v1/document-segment/tasks/{task_id}/stop\n\n5. 数据结构\n• 任务状态：pending/processing/completed/failed\n• 文件进度：百分比、分段数、向量化状态\n• 分段详情：内容、索引、质量评分等`);
        }

        function showTestingGuide() {
            alert(`🧪 测试指南\n\n1. 环境准备\n• 启动 Redis 服务\n• 启动 Celery Worker: celery -A app.core.celery_config worker --loglevel=info -Q segment_queue\n• 启动 FastAPI 服务\n\n2. 功能测试\n• 选择多个文件进入批量分段页面\n• 配置分段参数，启动任务\n• 观察左侧文件列表的进度更新\n• 点击文件查看右侧分段详情\n• 测试分页功能\n\n3. 性能测试\n• 测试大文件分段处理\n• 验证并发任务处理能力\n• 检查内存和CPU使用情况\n\n4. 错误处理测试\n• 模拟网络中断\n• 测试任务失败恢复\n• 验证错误信息显示\n\n5. 用户体验测试\n• 界面响应速度\n• 进度更新及时性\n• 交互操作流畅性\n\n预期结果：\n✅ 任务队列独立运行\n✅ 实时进度准确更新\n✅ 左右分栏布局正常\n✅ 分段详情正确显示\n✅ 分页功能正常工作`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('批量分段功能完整优化方案页面已加载');
        });
    </script>
</body>
</html>
