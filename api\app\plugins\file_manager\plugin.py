"""
文件管理插件实现
"""

import os
from pathlib import Path
from typing import Dict, Any, List
from fastapi import Fast<PERSON><PERSON>
from loguru import logger

from app.core.config import Settings
from app.plugins.base import BasePlugin, PluginMetadata, PluginConfig


class FileManagerPlugin(BasePlugin):
    """文件管理插件"""
    
    def __init__(self, config: PluginConfig):
        super().__init__(config)
        self.upload_dir = None
        self.allowed_extensions = set()
        self.max_file_size = 0
    
    @property
    def metadata(self) -> PluginMetadata:
        return PluginMetadata(
            name="file_manager",
            version="1.0.0",
            description="File upload and management plugin",
            author="XHC Team",
            dependencies=[],
            min_api_version="0.1.0"
        )
    
    async def initialize(self, app_settings: Settings) -> None:
        """初始化文件管理插件"""
        self.app_settings = app_settings
        
        # 设置上传目录
        self.upload_dir = Path(app_settings.UPLOAD_DIR)
        self.upload_dir.mkdir(exist_ok=True)
        
        # 设置允许的文件类型
        self.allowed_extensions = set(app_settings.allowed_file_types_list)
        
        # 设置最大文件大小
        self.max_file_size = app_settings.MAX_FILE_SIZE
        
        logger.info(f"File manager plugin initialized with upload dir: {self.upload_dir}")
    
    async def startup(self) -> None:
        """启动文件管理插件"""
        # 检查上传目录权限
        if not os.access(self.upload_dir, os.W_OK):
            logger.warning(f"Upload directory {self.upload_dir} is not writable")
        
        logger.info("File manager plugin started")
    
    async def shutdown(self) -> None:
        """关闭文件管理插件"""
        logger.info("File manager plugin shutdown")
    
    def is_allowed_file(self, filename: str) -> bool:
        """
        检查文件是否允许上传
        
        Args:
            filename: 文件名
            
        Returns:
            是否允许
        """
        if not filename:
            return False
        
        extension = filename.rsplit('.', 1)[-1].lower()
        return extension in self.allowed_extensions
    
    def get_file_path(self, filename: str) -> Path:
        """
        获取文件完整路径
        
        Args:
            filename: 文件名
            
        Returns:
            文件路径
        """
        return self.upload_dir / filename
    
    def get_upload_stats(self) -> Dict[str, Any]:
        """
        获取上传统计信息
        
        Returns:
            统计信息
        """
        if not self.upload_dir.exists():
            return {
                "total_files": 0,
                "total_size": 0,
                "disk_usage": 0
            }
        
        total_files = 0
        total_size = 0
        
        for file_path in self.upload_dir.rglob('*'):
            if file_path.is_file():
                total_files += 1
                total_size += file_path.stat().st_size
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "disk_usage": total_size,
            "upload_dir": str(self.upload_dir),
            "allowed_extensions": list(self.allowed_extensions),
            "max_file_size": self.max_file_size
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        base_health = await super().health_check()
        
        # 检查上传目录状态
        upload_dir_status = "healthy"
        if not self.upload_dir.exists():
            upload_dir_status = "error"
        elif not os.access(self.upload_dir, os.W_OK):
            upload_dir_status = "warning"
        
        base_health.update({
            "upload_dir_status": upload_dir_status,
            "upload_dir": str(self.upload_dir),
            **self.get_upload_stats()
        })
        
        return base_health
