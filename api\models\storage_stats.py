"""
存储统计模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, BigInteger, Float, String, DateTime, ForeignKey, Text, Boolean
from sqlalchemy.orm import relationship
from .base import Base


class StorageStats(Base):
    """存储统计表"""
    __tablename__ = 'storage_stats'

    id = Column(Integer, primary_key=True, index=True)
    storage_id = Column(Integer, ForeignKey('storages.id'), nullable=False, index=True)
    
    # 统计时间
    stats_date = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    
    # 文件统计
    total_files = Column(Integer, default=0, comment='总文件数')
    total_folders = Column(Integer, default=0, comment='总文件夹数')
    
    # 大小统计
    total_size = Column(BigInteger, default=0, comment='总大小(字节)')
    used_size = Column(BigInteger, default=0, comment='已使用大小(字节)')
    available_size = Column(BigInteger, default=0, comment='可用大小(字节)')
    
    # 文件类型统计
    document_count = Column(Integer, default=0, comment='文档文件数')
    image_count = Column(Integer, default=0, comment='图片文件数')
    video_count = Column(Integer, default=0, comment='视频文件数')
    audio_count = Column(Integer, default=0, comment='音频文件数')
    archive_count = Column(Integer, default=0, comment='压缩文件数')
    other_count = Column(Integer, default=0, comment='其他文件数')
    
    # 大小分布统计
    document_size = Column(BigInteger, default=0, comment='文档文件总大小')
    image_size = Column(BigInteger, default=0, comment='图片文件总大小')
    video_size = Column(BigInteger, default=0, comment='视频文件总大小')
    audio_size = Column(BigInteger, default=0, comment='音频文件总大小')
    archive_size = Column(BigInteger, default=0, comment='压缩文件总大小')
    other_size = Column(BigInteger, default=0, comment='其他文件总大小')
    
    # 性能指标
    avg_file_size = Column(Float, default=0.0, comment='平均文件大小')
    largest_file_size = Column(BigInteger, default=0, comment='最大文件大小')
    smallest_file_size = Column(BigInteger, default=0, comment='最小文件大小')
    
    # 访问统计
    read_operations = Column(Integer, default=0, comment='读操作次数')
    write_operations = Column(Integer, default=0, comment='写操作次数')
    delete_operations = Column(Integer, default=0, comment='删除操作次数')
    
    # 错误统计
    error_count = Column(Integer, default=0, comment='错误次数')
    last_error = Column(Text, comment='最后一次错误信息')
    
    # 健康状态
    health_score = Column(Float, default=100.0, comment='健康评分(0-100)')
    is_healthy = Column(Boolean, default=True, comment='是否健康')
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    storage = relationship("Storage", back_populates="stats")

    def __repr__(self):
        return f"<StorageStats(storage_id={self.storage_id}, stats_date={self.stats_date})>"

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'storage_id': self.storage_id,
            'stats_date': self.stats_date.isoformat() if self.stats_date else None,
            'total_files': self.total_files,
            'total_folders': self.total_folders,
            'total_size': self.total_size,
            'used_size': self.used_size,
            'available_size': self.available_size,
            'document_count': self.document_count,
            'image_count': self.image_count,
            'video_count': self.video_count,
            'audio_count': self.audio_count,
            'archive_count': self.archive_count,
            'other_count': self.other_count,
            'document_size': self.document_size,
            'image_size': self.image_size,
            'video_size': self.video_size,
            'audio_size': self.audio_size,
            'archive_size': self.archive_size,
            'other_size': self.other_size,
            'avg_file_size': self.avg_file_size,
            'largest_file_size': self.largest_file_size,
            'smallest_file_size': self.smallest_file_size,
            'read_operations': self.read_operations,
            'write_operations': self.write_operations,
            'delete_operations': self.delete_operations,
            'error_count': self.error_count,
            'last_error': self.last_error,
            'health_score': self.health_score,
            'is_healthy': self.is_healthy,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class StorageStatsHistory(Base):
    """存储统计历史表"""
    __tablename__ = 'storage_stats_history'

    id = Column(Integer, primary_key=True, index=True)
    storage_id = Column(Integer, ForeignKey('storages.id'), nullable=False, index=True)
    
    # 统计时间
    stats_date = Column(DateTime, nullable=False, index=True)
    
    # 快照数据(JSON格式存储完整统计信息)
    stats_snapshot = Column(Text, comment='统计快照数据')
    
    # 变化量
    files_change = Column(Integer, default=0, comment='文件数变化')
    size_change = Column(BigInteger, default=0, comment='大小变化')
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    storage = relationship("Storage")

    def __repr__(self):
        return f"<StorageStatsHistory(storage_id={self.storage_id}, stats_date={self.stats_date})>"
