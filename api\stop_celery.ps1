# PowerShell脚本停止Celery服务
# 使用方法: powershell -ExecutionPolicy Bypass -File stop_celery.ps1

Write-Host "停止AI知识库Celery服务..." -ForegroundColor Red

# 停止PowerShell Job方式启动的服务
if (Test-Path "pids") {
    # 停止Flower
    if (Test-Path "pids/flower.pid") {
        try {
            $flowerId = Get-Content "pids/flower.pid" -ErrorAction SilentlyContinue
            if ($flowerId) {
                Write-Host "停止Flower监控 (Job ID: $flowerId)..." -ForegroundColor Yellow
                Stop-Job -Id $flowerId -ErrorAction SilentlyContinue
                Remove-Job -Id $flowerId -Force -ErrorAction SilentlyContinue
                Remove-Item "pids/flower.pid" -ErrorAction SilentlyContinue
                Write-Host "Flower已停止" -ForegroundColor Green
            }
        } catch {
            Write-Host "停止Flower时出错: $_" -ForegroundColor Yellow
        }
    }
    
    # 停止Beat
    if (Test-Path "pids/beat.pid") {
        try {
            $beatId = Get-Content "pids/beat.pid" -ErrorAction SilentlyContinue
            if ($beatId) {
                Write-Host "停止Celery Beat (Job ID: $beatId)..." -ForegroundColor Yellow
                Stop-Job -Id $beatId -ErrorAction SilentlyContinue
                Remove-Job -Id $beatId -Force -ErrorAction SilentlyContinue
                Remove-Item "pids/beat.pid" -ErrorAction SilentlyContinue
                Write-Host "Celery Beat已停止" -ForegroundColor Green
            }
        } catch {
            Write-Host "停止Beat时出错: $_" -ForegroundColor Yellow
        }
    }
    
    # 停止Worker
    if (Test-Path "pids/worker.pid") {
        try {
            $workerId = Get-Content "pids/worker.pid" -ErrorAction SilentlyContinue
            if ($workerId) {
                Write-Host "停止Celery Worker (Job ID: $workerId)..." -ForegroundColor Yellow
                Stop-Job -Id $workerId -ErrorAction SilentlyContinue
                Remove-Job -Id $workerId -Force -ErrorAction SilentlyContinue
                Remove-Item "pids/worker.pid" -ErrorAction SilentlyContinue
                Write-Host "Celery Worker已停止" -ForegroundColor Green
            }
        } catch {
            Write-Host "停止Worker时出错: $_" -ForegroundColor Yellow
        }
    }
}

# 停止所有PowerShell Job中的Celery服务
Write-Host "检查并停止所有Celery相关Job..." -ForegroundColor Yellow
$celeryJobs = Get-Job | Where-Object { $_.Name -like "*Celery*" }
if ($celeryJobs) {
    $celeryJobs | Stop-Job -ErrorAction SilentlyContinue
    $celeryJobs | Remove-Job -Force -ErrorAction SilentlyContinue
    Write-Host "已停止 $($celeryJobs.Count) 个Celery Job" -ForegroundColor Green
}

# 强制杀死所有celery相关进程
Write-Host "检查并清理残留的Celery进程..." -ForegroundColor Yellow

# 杀死celery.exe进程
$celeryProcesses = Get-Process -Name "celery" -ErrorAction SilentlyContinue
if ($celeryProcesses) {
    $celeryProcesses | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "已停止 $($celeryProcesses.Count) 个celery.exe进程" -ForegroundColor Green
}

# 杀死包含celery命令行的python进程
$pythonProcesses = Get-WmiObject Win32_Process | Where-Object { 
    $_.Name -eq "python.exe" -and $_.CommandLine -like "*celery*" 
}
if ($pythonProcesses) {
    foreach ($process in $pythonProcesses) {
        try {
            Stop-Process -Id $process.ProcessId -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Host "无法停止进程 $($process.ProcessId): $_" -ForegroundColor Yellow
        }
    }
    Write-Host "已停止 $($pythonProcesses.Count) 个python celery进程" -ForegroundColor Green
}

# 清理PID目录
if (Test-Path "pids") {
    Remove-Item "pids" -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "Celery服务已全部停止!" -ForegroundColor Green
Write-Host ""

# 显示当前运行的相关进程
$remainingProcesses = Get-Process | Where-Object { 
    $_.ProcessName -like "*celery*" -or 
    ($_.ProcessName -eq "python" -and $_.MainWindowTitle -like "*celery*")
}

if ($remainingProcesses) {
    Write-Host "警告: 仍有以下相关进程在运行:" -ForegroundColor Yellow
    $remainingProcesses | Format-Table ProcessName, Id, MainWindowTitle -AutoSize
} else {
    Write-Host "确认: 没有发现残留的Celery进程" -ForegroundColor Green
}

Write-Host ""
Read-Host "按Enter键退出"
