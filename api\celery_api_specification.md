# Celery API 接口规范

## 概述

本文档定义了前端Celery管理界面与后端服务对接所需的API接口规范。

## 基础信息

- **基础路径**: `/api/v1/celery`
- **认证方式**: Bearer <PERSON>
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## API 端点

### 1. 获取Celery状态

**端点**: `GET /api/v1/celery/status`

**描述**: 获取Celery服务的整体状态信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "redis_connected": true,
    "services": {
      "worker": {
        "running": true,
        "pid": 12345
      },
      "beat": {
        "running": true,
        "pid": 12346
      },
      "flower": {
        "running": true,
        "pid": 12347
      }
    },
    "overall_status": "running"
  }
}
```

**字段说明**:
- `redis_connected`: Redis连接状态
- `services`: 各服务状态
- `overall_status`: 整体状态 (`running`/`stopped`/`partial`)

### 2. 获取Celery配置

**端点**: `GET /api/v1/celery/config`

**描述**: 获取Celery的配置信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "redis_host": "**************",
    "redis_port": 6379,
    "redis_db": 10,
    "redis_password": "",
    "worker_concurrency": 4,
    "worker_prefetch_multiplier": 1,
    "task_soft_time_limit": 300,
    "task_time_limit": 600,
    "task_max_retries": 3,
    "flower_port": 5555,
    "flower_basic_auth": "admin:password"
  }
}
```

### 3. 获取性能指标

**端点**: `GET /api/v1/celery/metrics`

**描述**: 获取Celery的实时性能指标

**响应示例**:
```json
{
  "success": true,
  "data": {
    "timestamp": "2024-01-01T00:00:00Z",
    "active_workers": 4,
    "total_workers": 4,
    "active_tasks": 12,
    "processed_tasks": 1234,
    "failed_tasks": 12,
    "queue_lengths": {
      "default": 5,
      "high_priority": 2,
      "low_priority": 8
    },
    "avg_task_runtime": 2.5,
    "task_throughput": 15.2,
    "redis_memory_usage": 45.6,
    "redis_connected_clients": 8
  }
}
```

**字段说明**:
- `active_workers`: 当前活跃的Worker数量
- `total_workers`: 总Worker数量
- `active_tasks`: 当前活跃任务数
- `processed_tasks`: 已处理任务总数
- `failed_tasks`: 失败任务总数
- `queue_lengths`: 各队列中的任务数量
- `avg_task_runtime`: 平均任务运行时间(秒)
- `task_throughput`: 任务吞吐量(任务/秒)
- `redis_memory_usage`: Redis内存使用量(MB)
- `redis_connected_clients`: Redis连接客户端数

### 4. 控制Celery服务

**端点**: `POST /api/v1/celery/control`

**描述**: 启动、停止或重启Celery服务

**请求体**:
```json
{
  "action": "start",
  "service": "all",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**参数说明**:
- `action`: 操作类型 (`start`/`stop`/`restart`)
- `service`: 服务名称 (`all`/`worker`/`beat`/`flower`)

**响应示例**:
```json
{
  "success": true,
  "message": "服务启动成功",
  "data": {
    "action": "start",
    "service": "all",
    "status": {
      "redis_connected": true,
      "services": {
        "worker": {"running": true, "pid": 12345},
        "beat": {"running": true, "pid": 12346},
        "flower": {"running": true, "pid": 12347}
      },
      "overall_status": "running"
    }
  }
}
```

### 5. 获取自动启动设置

**端点**: `GET /api/v1/celery/auto-start`

**描述**: 获取Celery自动启动配置

**响应示例**:
```json
{
  "success": true,
  "data": {
    "enabled": true
  }
}
```

### 6. 设置自动启动

**端点**: `POST /api/v1/celery/auto-start`

**描述**: 设置Celery自动启动配置

**请求体**:
```json
{
  "enabled": true,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "自动启动设置已更新",
  "data": {
    "enabled": true
  }
}
```

### 7. 获取服务日志

**端点**: `GET /api/v1/celery/logs/{service}`

**描述**: 获取指定服务的日志信息

**路径参数**:
- `service`: 服务名称 (`worker`/`beat`/`flower`)

**查询参数**:
- `lines`: 返回的日志行数 (默认100)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "service": "worker",
    "logs": "2024-01-01 00:00:00 [INFO] Worker started\n2024-01-01 00:00:01 [INFO] Task received\n...",
    "lines": 100
  }
}
```

## 错误响应

当API调用失败时，返回以下格式：

```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "CELERY_001",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 实现要点

### 1. 状态监控
- 实时检测Worker、Beat、Flower进程状态
- 监控Redis连接状态
- 提供整体状态汇总

### 2. 性能指标计算
- 定时计算任务吞吐量
- 统计活跃任务数和Worker数
- 监控Redis内存使用情况
- 统计各队列任务数量

### 3. 服务控制
- 支持启动、停止、重启操作
- 支持单个服务或全部服务控制
- 操作后返回最新状态

### 4. 配置管理
- 读取当前Celery配置
- 支持自动启动设置
- 配置变更通知

### 5. 日志管理
- 提供各服务的日志查看
- 支持日志行数限制
- 实时日志更新

## 安全考虑

1. **认证授权**: 所有API都需要有效的Bearer Token
2. **权限控制**: 确保只有管理员可以执行控制操作
3. **输入验证**: 验证所有输入参数的合法性
4. **日志安全**: 避免在日志中暴露敏感信息

## 性能优化

1. **缓存机制**: 对频繁查询的数据进行缓存
2. **异步处理**: 控制操作使用异步处理
3. **批量操作**: 支持批量状态查询
4. **连接池**: 使用Redis连接池提高性能
