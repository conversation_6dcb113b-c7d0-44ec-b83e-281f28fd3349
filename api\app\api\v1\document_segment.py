"""
文档分段API路由
"""

import hashlib
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_sync_session
from app.models.document_segment import (
    DocumentSegmentTask, 
    DocumentSegment, 
    SegmentTemplate,
    SegmentMethod,
    SegmentStatus,
    VectorizeStatus
)
from app.core.response import success_response, error_response

router = APIRouter(prefix="/document-segment", tags=["文档分段"])


# 请求模型
class SegmentConfigRequest(BaseModel):
    """分段配置请求模型"""
    # 基本分段配置
    method: SegmentMethod = Field(default=SegmentMethod.PARAGRAPH, description="分段方法")
    max_length: int = Field(default=500, ge=100, le=2000, description="最大长度(字符)")
    overlap: int = Field(default=50, ge=0, le=200, description="重叠长度(字符)")
    preserve_formatting: bool = Field(default=True, description="是否保留格式")
    
    # 向量化配置
    enable_vectorization: bool = Field(default=True, description="是否启用向量化")
    embedding_model: str = Field(default="text-embedding-ada-002", description="嵌入模型")
    vector_dimension: int = Field(default=1536, ge=512, le=3072, description="向量维度")
    chunk_size: int = Field(default=1000, ge=500, le=2000, description="向量化块大小")
    
    # 高级配置
    language: str = Field(default="zh", description="文档语言")
    remove_stopwords: bool = Field(default=False, description="是否移除停用词")
    normalize_text: bool = Field(default=True, description="是否标准化文本")
    extract_keywords: bool = Field(default=True, description="是否提取关键词")


class CreateTaskRequest(BaseModel):
    """创建分段任务请求模型"""
    task_name: str = Field(..., min_length=1, max_length=200, description="任务名称")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    file_ids: List[str] = Field(..., min_items=1, description="文件ID列表")
    config: SegmentConfigRequest = Field(..., description="分段配置")


class CreateTemplateRequest(BaseModel):
    """创建模板请求模型"""
    template_name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, max_length=500, description="模板描述")
    config: SegmentConfigRequest = Field(..., description="分段配置")
    is_default: bool = Field(default=False, description="是否为默认模板")


# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    id: int
    task_id: str
    task_name: str
    description: Optional[str]
    file_ids: List[str]
    total_files: int
    processed_files: int
    status: SegmentStatus
    progress: float
    error_message: Optional[str]
    total_segments: int
    total_vectors: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class FileProgressResponse(BaseModel):
    """文件处理进度响应模型"""
    file_id: str
    file_name: str
    status: SegmentStatus
    segments_count: int
    vectors_count: int
    error_message: Optional[str]


class SegmentResponse(BaseModel):
    """分段响应模型"""
    id: int
    segment_id: str
    file_id: str
    content: str
    content_preview: str
    segment_index: int
    start_position: int
    end_position: int
    word_count: int
    sentence_count: int
    vectorize_status: VectorizeStatus
    quality_score: float
    readability_score: float
    keywords: Optional[List[str]]
    created_at: datetime


class TemplateResponse(BaseModel):
    """模板响应模型"""
    id: int
    template_name: str
    description: Optional[str]
    is_default: bool
    is_system: bool
    usage_count: int
    config: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


# API路由
@router.post("/tasks", response_model=Dict[str, Any])
async def create_segment_task(
    request: CreateTaskRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_sync_session)
):
    """创建文档分段任务"""
    try:
        # 创建任务记录
        task = DocumentSegmentTask(
            task_name=request.task_name,
            description=request.description,
            file_ids=request.file_ids,
            total_files=len(request.file_ids),
            
            # 分段配置
            segment_method=request.config.method,
            max_length=request.config.max_length,
            overlap=request.config.overlap,
            preserve_formatting=request.config.preserve_formatting,
            
            # 向量化配置
            enable_vectorization=request.config.enable_vectorization,
            embedding_model=request.config.embedding_model,
            vector_dimension=request.config.vector_dimension,
            chunk_size=request.config.chunk_size,
            
            # 高级配置
            language=request.config.language,
            remove_stopwords=request.config.remove_stopwords,
            normalize_text=request.config.normalize_text,
            extract_keywords=request.config.extract_keywords,
            
            status=SegmentStatus.PENDING
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        # 使用Celery处理批量分段任务
        from app.tasks.segment_tasks import process_batch_segment_task
        celery_task = process_batch_segment_task.delay(task.id)

        # 记录Celery任务ID
        task.segment_metadata = {
            "celery_task_id": celery_task.id,
            "created_at": datetime.utcnow().isoformat()
        }
        db.commit()
        
        return success_response(
            data={
                "task_id": task.task_id,
                "celery_task_id": celery_task.id,
                "status": "processing",
                "message": "分段任务创建成功，正在后台处理"
            }
        )
        
    except Exception as e:
        db.rollback()
        return error_response(f"创建分段任务失败: {str(e)}")


@router.get("/tasks/{task_id}", response_model=Dict[str, Any])
async def get_task_info(task_id: str, db: Session = Depends(get_sync_session)):
    """获取任务信息"""
    try:
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_id == task_id
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return success_response(data=task.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        return error_response(f"获取任务信息失败: {str(e)}")


@router.get("/tasks/{task_id}/progress", response_model=Dict[str, Any])
async def get_task_progress(task_id: str, db: Session = Depends(get_sync_session)):
    """获取任务进度"""
    try:
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_id == task_id
        ).first()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 获取Celery任务状态
        celery_task_info = None
        if task.segment_metadata and 'celery_task_id' in task.segment_metadata:
            from app.core.celery_config import celery_app
            celery_task_id = task.segment_metadata['celery_task_id']
            celery_result = celery_app.AsyncResult(celery_task_id)
            celery_task_info = {
                "celery_task_id": celery_task_id,
                "celery_state": celery_result.state,
                "celery_info": celery_result.info if celery_result.info else {}
            }

        # 获取文件处理进度
        files_progress = []
        for file_id in task.file_ids:
            # 查询该文件的分段数量
            segments_count = db.query(DocumentSegment).filter(
                DocumentSegment.task_id == task.id,
                DocumentSegment.file_id == file_id
            ).count()

            # 查询该文件的向量数量
            vectors_count = db.query(DocumentSegment).filter(
                DocumentSegment.task_id == task.id,
                DocumentSegment.file_id == file_id,
                DocumentSegment.vectorize_status == VectorizeStatus.COMPLETED
            ).count()

            # 获取文件的Celery子任务状态
            file_task_status = "pending"
            file_task_progress = 0

            if (task.segment_metadata and 'file_tasks' in task.segment_metadata):
                file_tasks = task.segment_metadata['file_tasks']
                file_task = next((ft for ft in file_tasks if ft['file_id'] == file_id), None)

                if file_task:
                    from app.core.celery_config import celery_app
                    subtask_result = celery_app.AsyncResult(file_task['task_id'])

                    if subtask_result.state == 'PROGRESS' and subtask_result.info:
                        file_task_status = subtask_result.info.get('status', 'processing')
                        file_task_progress = subtask_result.info.get('progress', 0)
                    elif subtask_result.state == 'SUCCESS':
                        file_task_status = 'completed'
                        file_task_progress = 100
                    elif subtask_result.state == 'FAILURE':
                        file_task_status = 'error'
                        file_task_progress = 0
                    else:
                        file_task_status = 'processing'

            # 模拟文件名（实际应该从文件管理系统获取）
            file_name = f"文件_{file_id[:8]}.txt"

            files_progress.append({
                "file_id": file_id,
                "file_name": file_name,
                "status": file_task_status,
                "progress": file_task_progress,
                "segments_count": segments_count,
                "vectors_count": vectors_count,
                "error_message": None
            })

        # 添加Celery任务信息到响应
        task_dict = task.to_dict()
        if celery_task_info:
            task_dict['celery_info'] = celery_task_info

        return success_response(data={
            "task": task_dict,
            "files": files_progress,
            "file_progress": {fp["file_id"]: {
                "status": fp["status"],
                "progress": fp["progress"],
                "segments": fp["segments_count"],
                "error_message": fp["error_message"]
            } for fp in files_progress}
        })

    except HTTPException:
        raise
    except Exception as e:
        return error_response(f"获取任务进度失败: {str(e)}")


@router.get("/tasks/{task_id}/files/{file_id}/segments", response_model=Dict[str, Any])
async def get_file_segments(
    task_id: str,
    file_id: str,
    page: int = 1,
    page_size: int = 20,
    db: Session = Depends(get_sync_session)
):
    """获取指定文件的分段详情（分页）"""
    try:
        # 验证任务存在
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_id == task_id
        ).first()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 计算分页参数
        offset = (page - 1) * page_size

        # 查询分段数据
        segments_query = db.query(DocumentSegment).filter(
            DocumentSegment.task_id == task.id,
            DocumentSegment.file_id == file_id
        ).order_by(DocumentSegment.segment_index)

        # 获取总数
        total_count = segments_query.count()

        # 分页查询
        segments = segments_query.offset(offset).limit(page_size).all()

        # 构建响应数据
        segments_data = []
        for segment in segments:
            segment_dict = segment.to_dict()
            # 添加额外的显示信息
            segment_dict['content_preview'] = segment.content[:100] + '...' if len(segment.content) > 100 else segment.content
            segment_dict['has_vector'] = segment.vectorize_status == VectorizeStatus.COMPLETED
            segments_data.append(segment_dict)

        # 计算分页信息
        total_pages = (total_count + page_size - 1) // page_size

        return success_response(data={
            "file_id": file_id,
            "segments": segments_data,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        })

    except HTTPException:
        raise
    except Exception as e:
        return error_response(f"获取文件分段详情失败: {str(e)}")


@router.post("/tasks/{task_id}/pause", response_model=Dict[str, Any])
async def pause_task(task_id: str, db: Session = Depends(get_sync_session)):
    """暂停任务"""
    try:
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_id == task_id
        ).first()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        if task.status != SegmentStatus.PROCESSING:
            raise HTTPException(status_code=400, detail="只能暂停正在处理的任务")

        task.status = SegmentStatus.PENDING
        db.commit()

        return success_response(message="任务已暂停")

    except HTTPException:
        raise
    except Exception as e:
        return error_response(f"暂停任务失败: {str(e)}")


@router.post("/tasks/{task_id}/resume", response_model=Dict[str, Any])
async def resume_task(
    task_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_sync_session)
):
    """恢复任务"""
    try:
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_id == task_id
        ).first()

        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        if task.status != SegmentStatus.PENDING:
            raise HTTPException(status_code=400, detail="只能恢复暂停的任务")

        task.status = SegmentStatus.PROCESSING
        db.commit()

        # 重新添加后台任务
        background_tasks.add_task(process_segment_task, task.id)

        return success_response(message="任务已恢复")

    except HTTPException:
        raise
    except Exception as e:
        return error_response(f"恢复任务失败: {str(e)}")


@router.post("/tasks/{task_id}/stop", response_model=Dict[str, Any])
async def stop_task(task_id: str, db: Session = Depends(get_sync_session)):
    """停止任务"""
    try:
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_id == task_id
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task.status in [SegmentStatus.COMPLETED, SegmentStatus.FAILED]:
            raise HTTPException(status_code=400, detail="任务已完成或失败，无法停止")
        
        task.status = SegmentStatus.FAILED
        task.error_message = "任务被用户停止"
        task.completed_at = datetime.utcnow()
        db.commit()
        
        return success_response(message="任务已停止")
        
    except HTTPException:
        raise
    except Exception as e:
        return error_response(f"停止任务失败: {str(e)}")


@router.get("/templates", response_model=Dict[str, Any])
async def get_templates(db: Session = Depends(get_sync_session)):
    """获取分段模板列表"""
    try:
        templates = db.query(SegmentTemplate).order_by(
            SegmentTemplate.is_default.desc(),
            SegmentTemplate.created_at.desc()
        ).all()
        
        templates_data = []
        for template in templates:
            template_dict = template.to_dict()
            # 构建配置对象
            template_dict['config'] = {
                'method': template.segment_method,
                'max_length': template.max_length,
                'overlap': template.overlap,
                'preserve_formatting': template.preserve_formatting,
                'enable_vectorization': template.enable_vectorization,
                'embedding_model': template.embedding_model,
                'vector_dimension': template.vector_dimension,
                'chunk_size': template.chunk_size,
                'language': template.language,
                'remove_stopwords': template.remove_stopwords,
                'normalize_text': template.normalize_text,
                'extract_keywords': template.extract_keywords,
            }
            templates_data.append(template_dict)
        
        return success_response(data={"templates": templates_data})
        
    except Exception as e:
        return error_response(f"获取模板列表失败: {str(e)}")


@router.post("/templates", response_model=Dict[str, Any])
async def create_template(request: CreateTemplateRequest, db: Session = Depends(get_sync_session)):
    """创建分段模板"""
    try:
        # 检查模板名称是否已存在
        existing = db.query(SegmentTemplate).filter(
            SegmentTemplate.template_name == request.template_name
        ).first()
        
        if existing:
            raise HTTPException(status_code=400, detail="模板名称已存在")
        
        # 如果设置为默认模板，先取消其他默认模板
        if request.is_default:
            db.query(SegmentTemplate).filter(
                SegmentTemplate.is_default == True
            ).update({"is_default": False})
        
        template = SegmentTemplate(
            template_name=request.template_name,
            description=request.description,
            
            # 分段配置
            segment_method=request.config.method,
            max_length=request.config.max_length,
            overlap=request.config.overlap,
            preserve_formatting=request.config.preserve_formatting,
            
            # 向量化配置
            enable_vectorization=request.config.enable_vectorization,
            embedding_model=request.config.embedding_model,
            vector_dimension=request.config.vector_dimension,
            chunk_size=request.config.chunk_size,
            
            # 高级配置
            language=request.config.language,
            remove_stopwords=request.config.remove_stopwords,
            normalize_text=request.config.normalize_text,
            extract_keywords=request.config.extract_keywords,
            
            is_default=request.is_default,
            is_system=False
        )
        
        db.add(template)
        db.commit()
        db.refresh(template)
        
        return success_response(
            data=template.to_dict(),
            message="模板创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return error_response(f"创建模板失败: {str(e)}")


# 后台任务处理函数
async def process_segment_task(task_id: int):
    """处理分段任务的后台函数"""
    # 创建新的数据库会话
    db = next(get_sync_session())

    try:
        task = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.id == task_id
        ).first()

        if not task:
            return

        # 更新任务状态
        task.status = SegmentStatus.PROCESSING
        task.started_at = datetime.utcnow()
        db.commit()
        
        # 模拟处理每个文件
        for i, file_id in enumerate(task.file_ids):
            # 模拟文件分段处理
            segments_count = 5  # 模拟生成5个分段
            
            for j in range(segments_count):
                content = f"这是文件 {file_id} 的第 {j+1} 个分段内容..."
                content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()

                segment = DocumentSegment(
                    task_id=task.id,
                    file_id=file_id,
                    content=content,
                    content_hash=content_hash,
                    segment_index=j,
                    start_position=j * 100,
                    end_position=(j + 1) * 100,
                    word_count=100,
                    sentence_count=5,
                    vectorize_status=VectorizeStatus.COMPLETED if task.enable_vectorization else VectorizeStatus.PENDING,
                    quality_score=0.8,
                    readability_score=0.7,
                    keywords=["关键词1", "关键词2"] if task.extract_keywords else None
                )
                db.add(segment)
            
            # 更新进度
            task.processed_files = i + 1
            task.progress = (task.processed_files / task.total_files) * 100
            db.commit()
        
        # 更新最终统计
        task.total_segments = db.query(DocumentSegment).filter(
            DocumentSegment.task_id == task.id
        ).count()
        
        task.total_vectors = db.query(DocumentSegment).filter(
            DocumentSegment.task_id == task.id,
            DocumentSegment.vectorize_status == VectorizeStatus.COMPLETED
        ).count()
        
        task.status = SegmentStatus.COMPLETED
        task.completed_at = datetime.utcnow()
        task.progress = 100.0
        
        db.commit()

    except Exception as e:
        # 处理失败
        try:
            if 'task' in locals() and task:
                task.status = SegmentStatus.FAILED
                task.error_message = str(e)
                task.completed_at = datetime.utcnow()
                db.commit()
        except Exception:
            db.rollback()
    finally:
        # 确保关闭数据库会话
        db.close()
