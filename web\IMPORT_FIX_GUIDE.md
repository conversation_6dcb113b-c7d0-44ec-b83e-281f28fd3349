# 导入路径修复指南

## 🚨 问题描述

遇到以下错误：
```
Module not found: Can't resolve '@/lib/api-client'
```

## 🔍 问题原因

1. **错误的导入路径**
   - 代码中使用了 `@/lib/api-client`
   - 实际文件是 `@/lib/api`

2. **文件不存在**
   - 项目中没有 `api-client.ts` 文件
   - 只有 `api.ts` 文件

## ✅ 修复方案

### 1. 导入路径修复

将所有错误的导入路径修复为正确路径：

```typescript
// 修复前 ❌
import { apiClient } from '@/lib/api-client';

// 修复后 ✅
import apiClient from '@/lib/api';
```

### 2. 修复的文件

#### hooks/useStorageInfo.ts
```typescript
// 第6行修复
import apiClient from '@/lib/api';
```

#### components/FileManager/StorageOverview.tsx
```typescript
// 第18行修复
import apiClient from '@/lib/api';
```

#### components/System/SystemStatus.tsx
```typescript
// 第21行修复
import apiClient from '@/lib/api';
```

### 3. 导出方式确认

`lib/api.ts` 文件的导出方式：
```typescript
// 文件末尾
export default apiClient;
```

因此正确的导入方式是：
```typescript
import apiClient from '@/lib/api';
```

## 🚀 修复步骤

### 方法1：使用修复脚本（推荐）
```cmd
cd web
双击运行: fix-import-issue.bat
```

### 方法2：手动修复
```cmd
cd web

# 1. 清理缓存
rmdir /s /q .next

# 2. 文件已自动修复，直接启动
pnpm dev
```

## 🔍 验证修复

### 1. 检查修复状态

确保以下文件的导入已修复：
- ✅ `hooks/useStorageInfo.ts`
- ✅ `components/FileManager/StorageOverview.tsx`
- ✅ `components/System/SystemStatus.tsx`

### 2. 启动测试

运行以下命令：
```cmd
cd web
pnpm dev
```

成功的输出应该不再包含模块解析错误：
```
○ Compiling /file-manager ...
✓ Compiled successfully
- ready started server on 0.0.0.0:3000
```

### 3. 浏览器验证

访问 `http://localhost:3000/file-manager` 应该能正常加载页面。

## 📋 导入规则总结

### 正确的导入方式

| 文件 | 导入方式 | 说明 |
|------|----------|------|
| lib/api.ts | `import apiClient from '@/lib/api'` | 默认导出 |
| 其他模块 | `import { named } from '@/lib/module'` | 命名导出 |

### 常见错误

| 错误导入 | 正确导入 | 问题 |
|----------|----------|------|
| `@/lib/api-client` | `@/lib/api` | 文件名错误 |
| `{ apiClient }` | `apiClient` | 导出方式错误 |

## 🐛 常见问题

### 问题1：仍然报模块解析错误
**解决方案**：
```cmd
# 清理所有缓存
rmdir /s /q .next
rmdir /s /q node_modules\.cache
pnpm dev
```

### 问题2：TypeScript 类型错误
**解决方案**：
检查 `tsconfig.json` 中的路径映射：
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./app/*", "./components/*", "./lib/*", "./hooks/*"]
    }
  }
}
```

### 问题3：API 调用失败
**解决方案**：
确保 `lib/api.ts` 文件中的 `apiClient` 配置正确：
```typescript
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});
```

## 📈 最佳实践

### 1. 导入路径规范

```typescript
// 推荐：使用绝对路径
import apiClient from '@/lib/api';
import { useStorageInfo } from '@/hooks/useStorageInfo';

// 避免：使用相对路径
import apiClient from '../../../lib/api';
```

### 2. 导出规范

```typescript
// 默认导出（推荐用于主要功能）
export default apiClient;

// 命名导出（推荐用于工具函数）
export { login, logout, getCurrentUser };
```

### 3. 类型安全

```typescript
// 使用 TypeScript 接口
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

// 类型化的 API 调用
const response = await apiClient.get<ApiResponse<User>>('/api/v1/auth/me');
```

## ✅ 修复确认

修复成功后，您应该能够：
- ✅ 启动开发服务器无模块错误
- ✅ 访问文件管理页面正常显示
- ✅ API 调用正常工作
- ✅ 组件正常渲染
- ✅ 无 TypeScript 编译错误

## 🔄 回滚方案

如果需要回滚或重新组织导入：

```typescript
// 方案1：重新导出
// 在 lib/index.ts 中
export { default as apiClient } from './api';

// 使用时
import { apiClient } from '@/lib';

// 方案2：命名导出
// 在 lib/api.ts 中
export { apiClient };

// 使用时
import { apiClient } from '@/lib/api';
```

## 📞 进一步支持

如果仍然遇到导入问题：

1. **检查文件路径**：确保文件确实存在
2. **检查导出方式**：确认是默认导出还是命名导出
3. **检查 TypeScript 配置**：确认路径映射正确
4. **清理缓存**：删除 `.next` 和 `node_modules/.cache`

修复完成后，所有组件应该能够正常导入和使用 API 客户端！
