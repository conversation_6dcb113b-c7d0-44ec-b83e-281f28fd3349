/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/file-manager/segment/batch/page";
exports.ids = ["app/file-manager/segment/batch/page"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$":
/*!**************************************************************!*\
  !*** ./lib/i18n/locales/ lazy ^\.\/.*\.ts$ namespace object ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.ts": [
		"(ssr)/./lib/i18n/locales/en.ts",
		"_ssr_lib_i18n_locales_en_ts"
	],
	"./ja.ts": [
		"(ssr)/./lib/i18n/locales/ja.ts",
		"_ssr_lib_i18n_locales_ja_ts"
	],
	"./zh-CN.ts": [
		"(ssr)/./lib/i18n/locales/zh-CN.ts",
		"_ssr_lib_i18n_locales_zh-CN_ts"
	],
	"./zh-TW.ts": [
		"(ssr)/./lib/i18n/locales/zh-TW.ts",
		"_ssr_lib_i18n_locales_zh-TW_ts"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1aa5\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'file-manager',\n        {\n        children: [\n        'segment',\n        {\n        children: [\n        'batch',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/file-manager/segment/batch/page.tsx */ \"(rsc)/./app/file-manager/segment/batch/page.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/file-manager/segment/batch/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/file-manager/segment/batch/page\",\n        pathname: \"/file-manager/segment/batch\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cfile-manager%5C%5Csegment%5C%5Cbatch%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cfile-manager%5C%5Csegment%5C%5Cbatch%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/file-manager/segment/batch/page.tsx */ \"(ssr)/./app/file-manager/segment/batch/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q2FwcCU1QyU1Q2ZpbGUtbWFuYWdlciU1QyU1Q3NlZ21lbnQlNUMlNUNiYXRjaCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBNkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvPzU1MmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFx3b3Jrc3BhY2VcXFxceGhjLXJhZ1xcXFx3ZWJcXFxcYXBwXFxcXGZpbGUtbWFuYWdlclxcXFxzZWdtZW50XFxcXGJhdGNoXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cfile-manager%5C%5Csegment%5C%5Cbatch%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/LanguageContext.tsx */ \"(ssr)/./contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/file-manager/segment/batch/page.tsx":
/*!*************************************************!*\
  !*** ./app/file-manager/segment/batch/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst BatchSegmentPage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // 解析URL参数中的文件ID列表\n    const fileIds = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        const filesParam = searchParams.get(\"files\");\n        if (!filesParam) return [];\n        // 分割文件ID并过滤空值\n        const ids = filesParam.split(\",\").filter(Boolean);\n        console.log(\"批量分段页面 - 接收到的URL参数:\", filesParam);\n        console.log(\"批量分段页面 - 解析的文件IDs:\", ids);\n        return ids;\n    }, [\n        searchParams\n    ]);\n    // 基础状态\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 任务状态\n    const [taskStatus, setTaskStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [taskId, setTaskId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [fileProgress, setFileProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 任务配置\n    const [taskName, setTaskName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        method: \"paragraph\",\n        max_length: 500,\n        overlap: 50,\n        preserve_formatting: true,\n        normalize_text: true,\n        extract_keywords: true,\n        remove_stopwords: false,\n        language: \"zh\"\n    });\n    // 文件选择相关状态\n    const [showFileSelector, setShowFileSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableFiles, setAvailableFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingAvailableFiles, setLoadingAvailableFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileSearchQuery, setFileSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStorage, setSelectedStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storageList, setStorageList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fileIds.length > 0) {\n            loadFiles();\n            // 生成默认任务名称\n            const now = new Date();\n            const timestamp = now.toLocaleString(\"zh-CN\");\n            setTaskName(`批量分段任务 - ${timestamp}`);\n        }\n        loadStorageList();\n    }, [\n        fileIds\n    ]);\n    // 加载存储列表\n    const loadStorageList = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/storage-management\");\n            const data = response.data?.data || response.data || [];\n            setStorageList(data);\n            if (data.length > 0) {\n                setSelectedStorage(data[0].id);\n            }\n        } catch (err) {\n            console.error(\"Failed to load storage list:\", err);\n        }\n    };\n    // 加载文件信息\n    const loadFiles = async ()=>{\n        if (fileIds.length === 0) {\n            console.log(\"批量分段页面 - 没有文件ID，跳过加载\");\n            return;\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            console.log(\"批量分段页面 - 开始加载文件信息，文件IDs:\", fileIds);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/file-management/batch-info\", {\n                file_ids: fileIds\n            });\n            console.log(\"批量分段页面 - API响应:\", response.data);\n            const data = response.data?.data || response.data || [];\n            console.log(\"批量分段页面 - 解析的文件数据:\", data);\n            setFiles(data);\n            if (data.length === 0) {\n                setError(\"未找到指定的文件，请检查文件是否存在\");\n            }\n        } catch (err) {\n            console.error(\"批量分段页面 - 加载文件失败:\", err);\n            console.error(\"批量分段页面 - 错误详情:\", err.response?.data);\n            setError(err.response?.data?.detail || \"加载文件信息失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载可选文件列表\n    const loadAvailableFiles = async (storageId, search = \"\")=>{\n        try {\n            setLoadingAvailableFiles(true);\n            const params = new URLSearchParams({\n                storage_id: storageId,\n                page: \"1\",\n                page_size: \"50\"\n            });\n            if (search) {\n                params.append(\"search\", search);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`/api/v1/file-management?${params.toString()}`);\n            const data = response.data?.data || response.data || {};\n            setAvailableFiles(data.files || []);\n        } catch (err) {\n            console.error(\"Failed to load available files:\", err);\n        } finally{\n            setLoadingAvailableFiles(false);\n        }\n    };\n    // 添加文件到选择列表\n    const addFilesToSelection = (selectedFiles)=>{\n        const newFiles = selectedFiles.filter((newFile)=>!files.some((existingFile)=>existingFile.file_id === newFile.file_id));\n        if (newFiles.length > 0) {\n            setFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n            // 更新URL参数\n            const allFileIds = [\n                ...files,\n                ...newFiles\n            ].map((f)=>f.file_id);\n            const newUrl = `/file-manager/segment/batch?files=${allFileIds.join(\",\")}`;\n            router.replace(newUrl);\n        }\n        setShowFileSelector(false);\n    };\n    // 移除文件\n    const removeFile = (fileId)=>{\n        const newFiles = files.filter((f)=>f.file_id !== fileId);\n        setFiles(newFiles);\n        // 更新URL参数\n        if (newFiles.length > 0) {\n            const newUrl = `/file-manager/segment/batch?files=${newFiles.map((f)=>f.file_id).join(\",\")}`;\n            router.replace(newUrl);\n        } else {\n            router.push(\"/file-manager\");\n        }\n    };\n    // 开始分段任务\n    const startSegmentTask = async ()=>{\n        if (!taskName.trim()) {\n            setError(\"请输入任务名称\");\n            return;\n        }\n        if (files.length === 0) {\n            setError(\"请选择要分段的文件\");\n            return;\n        }\n        try {\n            setTaskStatus(\"running\");\n            setError(null);\n            const taskData = {\n                task_name: taskName,\n                description: \"\",\n                file_ids: files.map((f)=>f.file_id),\n                config: config\n            };\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/document-segment/tasks\", taskData);\n            const data = response.data?.data || response.data;\n            setTaskId(data.task_id);\n            // 初始化文件进度\n            const initialProgress = {};\n            files.forEach((file)=>{\n                initialProgress[file.file_id] = {\n                    status: \"pending\",\n                    progress: 0\n                };\n            });\n            setFileProgress(initialProgress);\n            // 开始轮询任务状态\n            startProgressPolling(data.task_id);\n        } catch (err) {\n            console.error(\"Failed to start segment task:\", err);\n            setError(err.response?.data?.detail || \"启动分段任务失败\");\n            setTaskStatus(\"idle\");\n        }\n    };\n    // 轮询任务进度\n    const startProgressPolling = (taskId)=>{\n        const pollInterval = setInterval(async ()=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`/api/v1/document-segment/tasks/${taskId}/progress`);\n                const data = response.data?.data || response.data;\n                // 更新文件进度\n                if (data.file_progress) {\n                    setFileProgress(data.file_progress);\n                }\n                // 检查任务是否完成\n                if (data.status === \"completed\" || data.status === \"failed\") {\n                    clearInterval(pollInterval);\n                    setTaskStatus(data.status === \"completed\" ? \"completed\" : \"idle\");\n                    if (data.status === \"failed\") {\n                        setError(data.error_message || \"分段任务失败\");\n                    }\n                }\n            } catch (err) {\n                console.error(\"Failed to get task progress:\", err);\n            }\n        }, 2000);\n        // 10分钟后停止轮询\n        setTimeout(()=>{\n            clearInterval(pollInterval);\n        }, 600000);\n    };\n    // 返回文件管理\n    const handleBack = ()=>{\n        router.push(\"/file-manager\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"加载文件信息中...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n            lineNumber: 306,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 计算统计指标\n    const getSegmentStats = ()=>{\n        const totalFiles = files.length;\n        const pendingFiles = Object.values(fileProgress).filter((p)=>p.status === \"pending\").length;\n        const processingFiles = Object.values(fileProgress).filter((p)=>p.status === \"processing\").length;\n        const completedFiles = Object.values(fileProgress).filter((p)=>p.status === \"completed\").length;\n        const failedFiles = Object.values(fileProgress).filter((p)=>p.status === \"error\").length;\n        const totalSegments = Object.values(fileProgress).reduce((sum, p)=>sum + (p.segments || 0), 0);\n        return {\n            totalFiles,\n            pendingFiles,\n            processingFiles,\n            completedFiles,\n            failedFiles,\n            totalSegments\n        };\n    };\n    const stats = getSegmentStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 flex-shrink-0 bg-white/60 backdrop-blur-lg border-r border-white/30 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleBack,\n                                            className: \"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                                            children: \"AI智能批量分段\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-purple-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        files.length,\n                                                                        \" 个文件待处理\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                taskStatus === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: \"处理中...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                taskStatus === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 font-medium\",\n                                                                            children: \"已完成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-900\",\n                                                children: \"任务配置\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"任务名称 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: taskName,\n                                                    onChange: (e)=>setTaskName(e.target.value),\n                                                    disabled: taskStatus !== \"idle\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                    placeholder: \"请输入任务名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-900\",\n                                                children: \"分段配置\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"分段方式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: config.method,\n                                                        onChange: (e)=>setConfig({\n                                                                ...config,\n                                                                method: e.target.value\n                                                            }),\n                                                        disabled: taskStatus !== \"idle\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"paragraph\",\n                                                                children: \"按段落分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"sentence\",\n                                                                children: \"按句子分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"fixed_length\",\n                                                                children: \"固定长度分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"semantic\",\n                                                                children: \"语义分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"最大长度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: config.max_length,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        max_length: parseInt(e.target.value) || 500\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                min: \"100\",\n                                                                max: \"2000\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                                placeholder: \"500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"重叠长度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: config.overlap,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        overlap: parseInt(e.target.value) || 50\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                min: \"0\",\n                                                                max: \"500\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                                placeholder: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"语言\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: config.language,\n                                                        onChange: (e)=>setConfig({\n                                                                ...config,\n                                                                language: e.target.value\n                                                            }),\n                                                        disabled: taskStatus !== \"idle\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"zh\",\n                                                                children: \"中文\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"en\",\n                                                                children: \"英文\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"auto\",\n                                                                children: \"自动检测\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.preserve_formatting,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        preserve_formatting: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"保留格式\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.normalize_text,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        normalize_text: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"文本标准化\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.extract_keywords,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        extract_keywords: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"提取关键词\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.remove_stopwords,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        remove_stopwords: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"移除停用词\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col bg-white/40 backdrop-blur-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: \"文件分段统计\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"实时监控分段进度和状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowFileSelector(true);\n                                                            if (selectedStorage) {\n                                                                loadAvailableFiles(selectedStorage);\n                                                            }\n                                                        },\n                                                        disabled: taskStatus === \"running\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加文件\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    taskStatus === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-blue-100/50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-blue-700\",\n                                                                children: \"AI处理中\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    taskStatus === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-green-100/50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-green-700\",\n                                                                children: \"处理完成\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    taskStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startSegmentTask,\n                                                        disabled: files.length === 0 || !taskName.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"开始AI分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-blue-600 uppercase tracking-wide\",\n                                                                    children: \"文件总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-blue-900\",\n                                                                    children: stats.totalFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-3 border border-purple-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-purple-600 uppercase tracking-wide\",\n                                                                    children: \"分段总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-purple-900\",\n                                                                    children: stats.totalSegments\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-3 border border-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-gray-600 uppercase tracking-wide\",\n                                                                    children: \"待分段\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-gray-900\",\n                                                                    children: stats.pendingFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-gray-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-3 border border-yellow-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-yellow-600 uppercase tracking-wide\",\n                                                                    children: \"分段中\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-yellow-900\",\n                                                                    children: stats.processingFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-green-600 uppercase tracking-wide\",\n                                                                    children: \"已完成\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-green-900\",\n                                                                    children: stats.completedFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-red-50 to-rose-50 rounded-lg p-3 border border-red-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-red-600 uppercase tracking-wide\",\n                                                                    children: \"分段失败\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-red-900\",\n                                                                    children: stats.failedFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-lg font-medium mb-2\",\n                                                children: \"暂无文件\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: '请点击右上角的\"添加文件\"按钮来选择要分段的文件'\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: files.map((file)=>{\n                                        const progress = fileProgress[file.file_id];\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.95\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            className: \"bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-5 h-5 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                            title: file.file_name,\n                                                                            children: file.file_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                file.file_size_formatted,\n                                                                                \" • \",\n                                                                                file.file_extension.toUpperCase()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        taskStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFile(file.file_id),\n                                                            className: \"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors\",\n                                                            title: \"移除文件\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"存储类型: \",\n                                                                    file.storage_type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: new Date(file.created_at).toLocaleDateString(\"zh-CN\")\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-200/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-700\",\n                                                                    children: [\n                                                                        progress.status === \"pending\" && \"等待处理\",\n                                                                        progress.status === \"processing\" && \"处理中...\",\n                                                                        progress.status === \"completed\" && `已完成 (${progress.segments || 0} 段)`,\n                                                                        progress.status === \"error\" && \"处理失败\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        progress.progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `h-2 rounded-full transition-all duration-300 ${progress.status === \"error\" ? \"bg-red-500\" : progress.status === \"completed\" ? \"bg-green-500\" : \"bg-blue-500\"}`,\n                                                                style: {\n                                                                    width: `${progress.progress}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        progress.error_message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-600 mt-1\",\n                                                            children: progress.error_message\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, file.file_id, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined),\n            showFileSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    className: \"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"选择文件\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowFileSelector(false),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fileSearchQuery,\n                                                        onChange: (e)=>setFileSearchQuery(e.target.value),\n                                                        onKeyPress: (e)=>{\n                                                            if (e.key === \"Enter\") {\n                                                                loadAvailableFiles(selectedStorage, fileSearchQuery);\n                                                            }\n                                                        },\n                                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        placeholder: \"搜索文件名...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedStorage,\n                                            onChange: (e)=>{\n                                                setSelectedStorage(e.target.value);\n                                                loadAvailableFiles(e.target.value, fileSearchQuery);\n                                            },\n                                            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: storageList.map((storage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: storage.id,\n                                                    children: storage.name\n                                                }, storage.id, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>loadAvailableFiles(selectedStorage, fileSearchQuery),\n                                            disabled: loadingAvailableFiles,\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                            children: loadingAvailableFiles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 21\n                                            }, undefined) : \"搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 776,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 max-h-96 overflow-y-auto\",\n                            children: loadingAvailableFiles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 animate-spin text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"加载文件中...\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 17\n                            }, undefined) : availableFiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-300 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"没有找到文件\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileSelector, {\n                                files: availableFiles,\n                                selectedFiles: [],\n                                onSelectionChange: (selected)=>addFilesToSelection(selected),\n                                excludeFileIds: files.map((f)=>f.file_id)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 854,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                    lineNumber: 770,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 769,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-5 h-5 text-red-500 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 870,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-700 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 871,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                    lineNumber: 869,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 868,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, undefined);\n};\nconst FileSelector = ({ files, selectedFiles, onSelectionChange, excludeFileIds })=>{\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedFiles);\n    const toggleFile = (file)=>{\n        const isSelected = selected.some((f)=>f.file_id === file.file_id);\n        if (isSelected) {\n            setSelected(selected.filter((f)=>f.file_id !== file.file_id));\n        } else {\n            setSelected([\n                ...selected,\n                file\n            ]);\n        }\n    };\n    const handleConfirm = ()=>{\n        onSelectionChange(selected);\n    };\n    const availableFiles = files.filter((file)=>!excludeFileIds.includes(file.file_id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4\",\n                children: availableFiles.map((file)=>{\n                    const isSelected = selected.some((f)=>f.file_id === file.file_id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>toggleFile(file),\n                        className: `p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${isSelected ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300 hover:bg-gray-50\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-4 h-4 border-2 rounded ${isSelected ? \"bg-blue-600 border-blue-600\" : \"border-gray-300\"}`,\n                                    children: isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: file.file_name\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                file.file_size_formatted,\n                                                \" • \",\n                                                file.file_extension.toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 926,\n                            columnNumber: 15\n                        }, undefined)\n                    }, file.file_id, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 912,\n                columnNumber: 7\n            }, undefined),\n            selected.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"已选择 \",\n                            selected.length,\n                            \" 个文件\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleConfirm,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"确认添加\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 948,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n        lineNumber: 911,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BatchSegmentPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/file-manager/segment/batch/page.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n    const [t, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化语言设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeLanguage = async ()=>{\n            setIsLoading(true);\n            try {\n                // 预加载所有语言\n                await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.preloadAllLanguages)();\n                // 从localStorage加载语言设置\n                const savedLanguage = localStorage.getItem(\"language\");\n                let targetLanguage;\n                if (savedLanguage && [\n                    \"zh-CN\",\n                    \"zh-TW\",\n                    \"en\",\n                    \"ja\"\n                ].includes(savedLanguage)) {\n                    targetLanguage = savedLanguage;\n                } else {\n                    // 检测浏览器语言\n                    targetLanguage = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.detectBrowserLanguage)();\n                }\n                setLanguageState(targetLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(targetLanguage);\n                setTranslations(translations);\n            } catch (error) {\n                console.error(\"Failed to initialize language:\", error);\n                // 回退到默认语言\n                setLanguageState(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                setTranslations(translations);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeLanguage();\n    }, []);\n    const setLanguage = async (newLanguage)=>{\n        setIsLoading(true);\n        try {\n            setLanguageState(newLanguage);\n            const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(newLanguage);\n            setTranslations(translations);\n            localStorage.setItem(\"language\", newLanguage);\n        } catch (error) {\n            console.error(\"Failed to set language:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage,\n            t,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkHealth: () => (/* binding */ checkHealth),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storageApi: () => (/* binding */ storageApi),\n/* harmony export */   testCors: () => (/* binding */ testCors)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API客户端配置\n * 与后端FastAPI接口对接\n */ \n\n// API基础配置\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    },\n    withCredentials: false\n});\n// 添加调试日志\nconsole.log(\"API Client initialized with base URL:\", API_BASE_URL);\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 添加认证token\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    // 添加请求ID用于追踪\n    config.headers[\"X-Request-ID\"] = generateRequestId();\n    // 调试日志\n    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    console.log(`   Base URL: ${config.baseURL}`);\n    console.log(`   Full URL: ${config.baseURL}${config.url}`);\n    console.log(`   Timeout: ${config.timeout}ms`);\n    return config;\n}, (error)=>{\n    console.error(\"❌ Request interceptor error:\", error);\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);\n    return response;\n}, async (error)=>{\n    console.error(`❌ API Error: ${error.code} ${error.config?.method?.toUpperCase()} ${error.config?.url}`);\n    console.error(`   Message: ${error.message}`);\n    console.error(`   Status: ${error.response?.status}`);\n    const originalRequest = error.config;\n    // 处理401未授权错误\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        // 尝试刷新token\n        const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n        if (refreshToken) {\n            try {\n                const response = await refreshAccessToken(refreshToken);\n                const newToken = response.data.access_token;\n                // 更新token\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", newToken, {\n                    expires: 1\n                });\n                // 重试原请求\n                originalRequest.headers.Authorization = `Bearer ${newToken}`;\n                return apiClient(originalRequest);\n            } catch (refreshError) {\n                // 刷新失败，清除token并跳转到登录页\n                clearAuthTokens();\n                window.location.href = \"/login\";\n                return Promise.reject(refreshError);\n            }\n        } else {\n            // 没有刷新token，直接跳转到登录页\n            clearAuthTokens();\n            window.location.href = \"/login\";\n        }\n    }\n    return Promise.reject(error);\n});\n// 生成请求ID\nfunction generateRequestId() {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n// 清除认证token\nfunction clearAuthTokens() {\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n}\n// 刷新访问token\nasync function refreshAccessToken(refreshToken) {\n    return axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/api/v1/auth/refresh`, {\n        refresh_token: refreshToken\n    });\n}\n// 登录\nconst login = async (credentials)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Attempting login with:\", {\n            username: credentials.username\n        });\n        console.log(\"\\uD83C\\uDF10 API Base URL:\", API_BASE_URL);\n        console.log(\"\\uD83D\\uDCE1 Full login URL:\", `${API_BASE_URL}/api/v1/auth/login`);\n        const response = await apiClient.post(\"/api/v1/auth/login\", credentials);\n        console.log(\"✅ Login response:\", response.data);\n        // 保存token到cookie\n        const { access_token, refresh_token, expires_in } = response.data;\n        // 设置cookie过期时间（转换为天数）\n        const expiresInDays = expires_in / (60 * 60 * 24);\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access_token, {\n            expires: expiresInDays\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", refresh_token, {\n            expires: 7\n        }); // 刷新token保存7天\n        // 同时保存到localStorage作为备份\n        localStorage.setItem(\"token\", access_token);\n        localStorage.setItem(\"refresh_token\", refresh_token);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ Login error details:\", error);\n        console.error(\"   Error code:\", error.code);\n        console.error(\"   Error message:\", error.message);\n        console.error(\"   Error response:\", error.response?.data);\n        console.error(\"   Error status:\", error.response?.status);\n        console.error(\"   Request config:\", error.config);\n        // 处理不同类型的错误响应\n        if (error.code === \"ECONNABORTED\") {\n            throw new Error(`请求超时，请检查网络连接或稍后重试。服务器地址：${API_BASE_URL}`);\n        } else if (error.response?.data?.detail) {\n            throw new Error(error.response.data.detail);\n        } else if (error.response?.data?.error?.message) {\n            throw new Error(error.response.data.error.message);\n        } else if (error.response?.status === 401) {\n            throw new Error(\"用户名或密码错误\");\n        } else if (error.response?.status >= 500) {\n            throw new Error(\"服务器错误，请稍后重试\");\n        } else if (error.code === \"ECONNREFUSED\" || error.message.includes(\"Network Error\")) {\n            throw new Error(`无法连接到服务器，请检查网络连接。服务器地址：${API_BASE_URL}`);\n        } else {\n            throw new Error(error.message || \"Login failed\");\n        }\n    }\n};\n// 登出\nconst logout = async ()=>{\n    try {\n        await apiClient.post(\"/api/v1/auth/logout\");\n    } catch (error) {\n        // 即使后端登出失败，也要清除本地token\n        console.error(\"Logout error:\", error);\n    } finally{\n        clearAuthTokens();\n    }\n};\n// 获取当前用户信息\nconst getCurrentUser = async ()=>{\n    try {\n        const response = await apiClient.get(\"/api/v1/auth/me\");\n        return response.data;\n    } catch (error) {\n        const apiError = error.response?.data;\n        throw new Error(apiError?.error?.message || \"Failed to get user info\");\n    }\n};\n// 检查健康状态\nconst checkHealth = async ()=>{\n    try {\n        const response = await apiClient.get(\"/health\");\n        return response.data;\n    } catch (error) {\n        throw new Error(\"API health check failed\");\n    }\n};\n// CORS测试\nconst testCors = async ()=>{\n    try {\n        console.log(\"\\uD83E\\uDDEA Testing CORS configuration...\");\n        const response = await apiClient.get(\"/api/v1/auth/test-cors\");\n        console.log(\"✅ CORS test successful:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ CORS test failed:\", error);\n        throw error;\n    }\n};\n// 存储管理API\nconst storageApi = {\n    // 获取存储列表\n    getStorages: async ()=>{\n        const response = await apiClient.get(\"/api/v1/storage/\");\n        return response.data;\n    },\n    // 创建存储\n    createStorage: async (data)=>{\n        const response = await apiClient.post(\"/api/v1/storage/\", data);\n        return response.data;\n    },\n    // 测试存储连接\n    testStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/test`);\n        return response.data;\n    },\n    // 删除存储\n    deleteStorage: async (storageId)=>{\n        const response = await apiClient.delete(`/api/v1/storage/${storageId}`);\n        return response.data;\n    },\n    // 同步存储\n    syncStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/sync`);\n        return response.data;\n    }\n};\n// 检查token是否有效\nconst isAuthenticated = ()=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    return !!token;\n};\n// 获取token\nconst getAccessToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatDate),\n/* harmony export */   formatNumber: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   getTranslation: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslation),\n/* harmony export */   getTranslationSync: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslationSync),\n/* harmony export */   languageConfig: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.preloadAllLanguages)\n/* harmony export */ });\n/* harmony import */ var _i18n_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./i18n/index */ \"(ssr)/./lib/i18n/index.ts\");\n/**\n * 多语言支持 - 兼容性导出文件\n * 重新导出新架构中的所有功能\n */ // 重新导出所有类型和函数\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFRCxjQUFjO0FBWVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9saWIvaTE4bi50cz80OWFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5aSa6K+t6KiA5pSv5oyBIC0g5YW85a655oCn5a+85Ye65paH5Lu2XG4gKiDph43mlrDlr7zlh7rmlrDmnrbmnoTkuK3nmoTmiYDmnInlip/og71cbiAqL1xuXG4vLyDph43mlrDlr7zlh7rmiYDmnInnsbvlnovlkozlh73mlbBcbmV4cG9ydCB0eXBlIHsgTGFuZ3VhZ2UsIFRyYW5zbGF0aW9ucyB9IGZyb20gJy4vaTE4bi9pbmRleCc7XG5cbmV4cG9ydCB7XG4gIGRlZmF1bHRMYW5ndWFnZSxcbiAgbGFuZ3VhZ2VDb25maWcsXG4gIGdldFRyYW5zbGF0aW9uLFxuICBnZXRUcmFuc2xhdGlvblN5bmMsXG4gIHByZWxvYWRBbGxMYW5ndWFnZXMsXG4gIGRldGVjdEJyb3dzZXJMYW5ndWFnZSxcbiAgZm9ybWF0TnVtYmVyLFxuICBmb3JtYXREYXRlXG59IGZyb20gJy4vaTE4bi9pbmRleCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VDb25maWciLCJnZXRUcmFuc2xhdGlvbiIsImdldFRyYW5zbGF0aW9uU3luYyIsInByZWxvYWRBbGxMYW5ndWFnZXMiLCJkZXRlY3RCcm93c2VyTGFuZ3VhZ2UiLCJmb3JtYXROdW1iZXIiLCJmb3JtYXREYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/index.ts":
/*!***************************!*\
  !*** ./lib/i18n/index.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* binding */ detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   getTranslationSync: () => (/* binding */ getTranslationSync),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* binding */ preloadAllLanguages)\n/* harmony export */ });\n/**\n * 国际化配置主文件\n * 支持动态加载语言文件\n */ const defaultLanguage = \"zh-CN\";\n// 语言配置\nconst languageConfig = {\n    \"zh-CN\": {\n        name: \"中文简体\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        direction: \"ltr\"\n    },\n    \"zh-TW\": {\n        name: \"中文繁體\",\n        flag: \"\\uD83C\\uDDF9\\uD83C\\uDDFC\",\n        direction: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        direction: \"ltr\"\n    },\n    \"ja\": {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        direction: \"ltr\"\n    }\n};\n// 动态导入语言文件\nconst loadTranslations = async (language)=>{\n    try {\n        const module = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${language}.ts`);\n        return module.default;\n    } catch (error) {\n        console.warn(`Failed to load translations for ${language}, falling back to default`);\n        const defaultModule = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${defaultLanguage}.ts`);\n        return defaultModule.default;\n    }\n};\n// 缓存已加载的翻译\nconst translationCache = new Map();\nconst getTranslation = async (language)=>{\n    if (translationCache.has(language)) {\n        return translationCache.get(language);\n    }\n    const translations = await loadTranslations(language);\n    translationCache.set(language, translations);\n    return translations;\n};\n// 同步获取翻译（用于已缓存的情况）\nconst getTranslationSync = (language)=>{\n    return translationCache.get(language) || null;\n};\n// 预加载所有语言\nconst preloadAllLanguages = async ()=>{\n    const languages = [\n        \"zh-CN\",\n        \"zh-TW\",\n        \"en\",\n        \"ja\"\n    ];\n    await Promise.all(languages.map(async (lang)=>{\n        try {\n            await getTranslation(lang);\n        } catch (error) {\n            console.warn(`Failed to preload language ${lang}:`, error);\n        }\n    }));\n};\n// 检测浏览器语言\nconst detectBrowserLanguage = ()=>{\n    if (true) {\n        return defaultLanguage;\n    }\n    const browserLanguage = navigator.language || navigator.languages?.[0];\n    if (browserLanguage?.startsWith(\"zh-CN\") || browserLanguage === \"zh\") {\n        return \"zh-CN\";\n    } else if (browserLanguage?.startsWith(\"zh-TW\") || browserLanguage === \"zh-Hant\") {\n        return \"zh-TW\";\n    } else if (browserLanguage?.startsWith(\"en\")) {\n        return \"en\";\n    } else if (browserLanguage?.startsWith(\"ja\")) {\n        return \"ja\";\n    }\n    return defaultLanguage;\n};\n// 格式化数字（根据语言环境）\nconst formatNumber = (number, language)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    return new Intl.NumberFormat(localeMap[language]).format(number);\n};\n// 格式化日期（根据语言环境）\nconst formatDate = (date, language, options)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(localeMap[language], options || defaultOptions).format(date);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/index.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0cc9297c17c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZ2xvYmFscy5jc3M/YWJhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBjYzkyOTdjMTdjNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/file-manager/segment/batch/page.tsx":
/*!*************************************************!*\
  !*** ./app/file-manager/segment/batch/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\app\file-manager\segment\batch\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI知识库 - 智能文档管理平台\",\n    description: \"基于AI技术的智能文档管理和知识库系统\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#4ade80\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#useLanguage`);


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZmF2aWNvbi5pY28/ZjUyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736","vendor-chunks/mime-db@1.52.0","vendor-chunks/axios@1.9.0","vendor-chunks/lucide-react@0.300.0_react@18.3.1","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/debug@4.4.1","vendor-chunks/form-data@4.0.3","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/asynckit@0.4.0","vendor-chunks/combined-stream@1.0.8","vendor-chunks/mime-types@2.1.35","vendor-chunks/js-cookie@3.0.5","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-symbols@1.1.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/es-set-tostringtag@2.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/es-errors@1.3.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/has-tostringtag@1.0.2","vendor-chunks/es-object-atoms@1.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();