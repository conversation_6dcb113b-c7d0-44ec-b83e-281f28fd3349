'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Save,
  Download,
  Printer,
  Undo,
  Redo,
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,

  Image,
  Table,
  Link,
  FileText,
  Eye,
  Edit3,
  ZoomIn,
  ZoomOut,
  RotateCcw
} from 'lucide-react';

interface DocumentEditorProps {
  fileId: string;
  fileName: string;
  fileType: 'docx' | 'pptx' | 'xlsx' | 'pdf' | 'txt' | 'md';
  content: string;
  isReadOnly?: boolean;
  onSave?: (content: string) => Promise<void>;
  onClose?: () => void;
}

interface FormatState {
  bold: boolean;
  italic: boolean;
  underline: boolean;
  fontSize: number;
  fontFamily: string;
  textAlign: 'left' | 'center' | 'right' | 'justify';
  textColor: string;
  backgroundColor: string;
}

const DocumentEditor: React.FC<DocumentEditorProps> = ({
  fileId,
  fileName,
  fileType,
  content: initialContent,
  isReadOnly = false,
  onSave,
  onClose
}) => {
  const [content, setContent] = useState(initialContent);
  // 保存默认格式状态，防止页面切换后重置
  const defaultFormatState: FormatState = {
    bold: false,
    italic: false,
    underline: false,
    fontSize: 14,
    fontFamily: 'Arial',
    textAlign: 'left',
    textColor: '#000000',
    backgroundColor: '#ffffff'
  };

  const [formatState, setFormatState] = useState<FormatState>(defaultFormatState);
  const [zoom, setZoom] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showFormatPanel, setShowFormatPanel] = useState(false);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setHasChanges(content !== initialContent);
  }, [content, initialContent]);

  // 格式化命令
  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    updateFormatState();
  };

  // 更新格式状态 - 彻底修复字体大小重置问题
  const updateFormatState = useCallback(() => {
    // 不再依赖document.queryCommandValue，因为它会返回不可靠的值
    // 直接从编辑器元素获取计算样式
    if (!editorRef.current) return;

    try {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.startContainer;

        // 如果是文本节点，获取其父元素
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement as Node;
        }

        if (element && element instanceof HTMLElement && editorRef.current.contains(element)) {
          const computedStyle = window.getComputedStyle(element);

          const newState: FormatState = {
            bold: computedStyle.fontWeight === 'bold' || parseInt(computedStyle.fontWeight) >= 600,
            italic: computedStyle.fontStyle === 'italic',
            underline: computedStyle.textDecoration.includes('underline'),
            // 从计算样式获取字体大小，如果无效则保持当前状态
            fontSize: (() => {
              const size = parseInt(computedStyle.fontSize);
              return (size && size > 0) ? size : formatState.fontSize;
            })(),
            fontFamily: computedStyle.fontFamily || formatState.fontFamily,
            textAlign: (() => {
              const align = computedStyle.textAlign;
              if (['left', 'center', 'right', 'justify'].includes(align)) {
                return align as 'left' | 'center' | 'right' | 'justify';
              }
              return formatState.textAlign;
            })(),
            textColor: computedStyle.color || formatState.textColor,
            backgroundColor: computedStyle.backgroundColor || formatState.backgroundColor
          };

          // 只有当状态真正改变时才更新
          if (JSON.stringify(newState) !== JSON.stringify(formatState)) {
            setFormatState(newState);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to update format state:', error);
    }
  }, [formatState]);

  // 页面可见性变化时强制恢复格式状态
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && editorRef.current) {
        // 页面重新可见时，强制恢复编辑器样式
        setTimeout(() => {
          if (editorRef.current) {
            editorRef.current.style.fontFamily = formatState.fontFamily;
            editorRef.current.style.fontSize = `${formatState.fontSize}px`;
            editorRef.current.style.color = formatState.textColor;

            // 强制重新应用样式到所有子元素
            const allElements = editorRef.current.querySelectorAll('*');
            allElements.forEach((el) => {
              if (el instanceof HTMLElement) {
                if (!el.style.fontSize || el.style.fontSize === '8px') {
                  el.style.fontSize = `${formatState.fontSize}px`;
                }
              }
            });
          }
        }, 100);
      }
    };

    const handleFocus = () => {
      // 获得焦点时也恢复样式
      if (editorRef.current) {
        editorRef.current.style.fontFamily = formatState.fontFamily;
        editorRef.current.style.fontSize = `${formatState.fontSize}px`;
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [formatState.fontFamily, formatState.fontSize, formatState.textColor]);

  // 保存文档
  const handleSave = async () => {
    if (!onSave || !hasChanges) return;
    
    try {
      setSaving(true);
      await onSave(content);
      setHasChanges(false);
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 插入表格
  const insertTable = (rows: number, cols: number) => {
    let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%;">';
    for (let i = 0; i < rows; i++) {
      tableHTML += '<tr>';
      for (let j = 0; j < cols; j++) {
        tableHTML += '<td style="padding: 8px; border: 1px solid #ddd;">&nbsp;</td>';
      }
      tableHTML += '</tr>';
    }
    tableHTML += '</table>';
    execCommand('insertHTML', tableHTML);
  };

  // 插入链接
  const insertLink = () => {
    const url = prompt('请输入链接地址:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  // 插入图片
  const insertImage = () => {
    fileInputRef.current?.click();
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = `<img src="${e.target?.result}" style="max-width: 100%; height: auto;" />`;
        execCommand('insertHTML', img);
      };
      reader.readAsDataURL(file);
    }
  };

  // 工具栏按钮组件
  const ToolbarButton: React.FC<{
    icon: React.ReactNode;
    title: string;
    active?: boolean;
    onClick: () => void;
    disabled?: boolean;
  }> = ({ icon, title, active, onClick, disabled }) => (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      disabled={disabled}
      className={`p-2 rounded-lg transition-all duration-200 ${
        active 
          ? 'bg-blue-500 text-white shadow-lg' 
          : disabled
          ? 'text-gray-400 cursor-not-allowed'
          : 'text-gray-700 hover:bg-gray-100'
      }`}
      title={title}
    >
      {icon}
    </motion.button>
  );

  return (
    <div className={`flex flex-col h-screen bg-gray-50 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        {/* 主工具栏 */}
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center space-x-4">
            {/* 文件信息 */}
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-blue-500" />
              <span className="font-medium text-gray-900">{fileName}</span>
              {hasChanges && <span className="text-orange-500 text-sm">• 未保存</span>}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* 缩放控制 */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg px-2 py-1">
              <ToolbarButton
                icon={<ZoomOut className="w-4 h-4" />}
                title="缩小"
                onClick={() => setZoom(Math.max(50, zoom - 10))}
              />
              <span className="text-sm font-medium min-w-12 text-center">{zoom}%</span>
              <ToolbarButton
                icon={<ZoomIn className="w-4 h-4" />}
                title="放大"
                onClick={() => setZoom(Math.min(200, zoom + 10))}
              />
            </div>

            {/* 模式切换 */}
            <div className="flex bg-gray-100 rounded-lg p-1 mr-4">
              <button
                onClick={() => window.location.href = window.location.href.replace('/edit/', '/view/')}
                className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
                  isReadOnly
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Eye className="w-4 h-4" />
                <span>查看</span>
              </button>
              <button
                onClick={() => window.location.href = window.location.href.replace('/view/', '/edit/')}
                className={`flex items-center space-x-1 px-3 py-1 rounded transition-colors ${
                  !isReadOnly
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Edit3 className="w-4 h-4" />
                <span>编辑</span>
              </button>
            </div>

            {/* 操作按钮 */}
            {!isReadOnly && (
              <ToolbarButton
                icon={saving ? <RotateCcw className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
                title="保存"
                onClick={handleSave}
                disabled={!hasChanges || saving}
              />
            )}

            <ToolbarButton
              icon={<Download className="w-4 h-4" />}
              title="下载"
              onClick={() => {/* 实现下载逻辑 */}}
            />

            <ToolbarButton
              icon={<Printer className="w-4 h-4" />}
              title="打印"
              onClick={() => window.print()}
            />

            {onClose && (
              <ToolbarButton
                icon={<Eye className="w-4 h-4" />}
                title="关闭"
                onClick={onClose}
              />
            )}
          </div>
        </div>

        {/* 格式工具栏 */}
        {!isReadOnly && (
          <div className="flex items-center justify-between px-4 py-2 border-t border-gray-100">
            <div className="flex items-center space-x-1">
              {/* 撤销重做 */}
              <ToolbarButton
                icon={<Undo className="w-4 h-4" />}
                title="撤销"
                onClick={() => execCommand('undo')}
              />
              <ToolbarButton
                icon={<Redo className="w-4 h-4" />}
                title="重做"
                onClick={() => execCommand('redo')}
              />

              <div className="w-px h-6 bg-gray-300 mx-2" />

              {/* 字体格式 */}
              <select
                value={formatState.fontFamily}
                onChange={(e) => execCommand('fontName', e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="Arial">Arial</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Helvetica">Helvetica</option>
                <option value="Georgia">Georgia</option>
                <option value="Verdana">Verdana</option>
              </select>

              <select
                value={formatState.fontSize}
                onChange={(e) => execCommand('fontSize', e.target.value)}
                className="px-2 py-1 border border-gray-300 rounded text-sm w-16"
              >
                {[8, 9, 10, 11, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48, 72].map(size => (
                  <option key={size} value={size}>{size}</option>
                ))}
              </select>

              <div className="w-px h-6 bg-gray-300 mx-2" />

              {/* 文本格式 */}
              <ToolbarButton
                icon={<Bold className="w-4 h-4" />}
                title="粗体"
                active={formatState.bold}
                onClick={() => execCommand('bold')}
              />
              <ToolbarButton
                icon={<Italic className="w-4 h-4" />}
                title="斜体"
                active={formatState.italic}
                onClick={() => execCommand('italic')}
              />
              <ToolbarButton
                icon={<Underline className="w-4 h-4" />}
                title="下划线"
                active={formatState.underline}
                onClick={() => execCommand('underline')}
              />

              <div className="w-px h-6 bg-gray-300 mx-2" />

              {/* 对齐方式 */}
              <ToolbarButton
                icon={<AlignLeft className="w-4 h-4" />}
                title="左对齐"
                active={formatState.textAlign === 'left'}
                onClick={() => execCommand('justifyLeft')}
              />
              <ToolbarButton
                icon={<AlignCenter className="w-4 h-4" />}
                title="居中对齐"
                active={formatState.textAlign === 'center'}
                onClick={() => execCommand('justifyCenter')}
              />
              <ToolbarButton
                icon={<AlignRight className="w-4 h-4" />}
                title="右对齐"
                active={formatState.textAlign === 'right'}
                onClick={() => execCommand('justifyRight')}
              />
              <ToolbarButton
                icon={<AlignJustify className="w-4 h-4" />}
                title="两端对齐"
                active={formatState.textAlign === 'justify'}
                onClick={() => execCommand('justifyFull')}
              />

              <div className="w-px h-6 bg-gray-300 mx-2" />

              {/* 列表 */}
              <ToolbarButton
                icon={<List className="w-4 h-4" />}
                title="无序列表"
                onClick={() => execCommand('insertUnorderedList')}
              />
              <ToolbarButton
                icon={<ListOrdered className="w-4 h-4" />}
                title="有序列表"
                onClick={() => execCommand('insertOrderedList')}
              />

              <div className="w-px h-6 bg-gray-300 mx-2" />

              {/* 插入元素 */}
              <ToolbarButton
                icon={<Link className="w-4 h-4" />}
                title="插入链接"
                onClick={insertLink}
              />
              <ToolbarButton
                icon={<Image className="w-4 h-4" />}
                title="插入图片"
                onClick={insertImage}
              />
              <ToolbarButton
                icon={<Table className="w-4 h-4" />}
                title="插入表格"
                onClick={() => insertTable(3, 3)}
              />
            </div>

            <div className="flex items-center space-x-2">
              {/* 颜色选择 */}
              <div className="flex items-center space-x-1">
                <label className="text-sm text-gray-600">字体:</label>
                <input
                  type="color"
                  value={formatState.textColor}
                  onChange={(e) => execCommand('foreColor', e.target.value)}
                  className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                />
              </div>
              <div className="flex items-center space-x-1">
                <label className="text-sm text-gray-600">背景:</label>
                <input
                  type="color"
                  value={formatState.backgroundColor}
                  onChange={(e) => execCommand('backColor', e.target.value)}
                  className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Microsoft Office风格编辑器内容区域 */}
      <div className="flex-1 overflow-auto bg-gradient-to-b from-gray-50 to-gray-100 p-6">
        {/* 页面容器 - 模仿Word的页面效果 */}
        <div className="max-w-5xl mx-auto">
          <div
            className="bg-white shadow-2xl border border-gray-200 mx-auto"
            style={{
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top center',
              width: '8.5in',
              minHeight: '11in',
              padding: '1in',
              boxShadow: '0 4px 20px rgba(0,0,0,0.1), 0 8px 40px rgba(0,0,0,0.05)'
            }}
          >
            {/* 页面标尺线（装饰性） */}
            <div className="absolute left-0 top-0 w-full h-2 bg-gradient-to-r from-blue-50 to-purple-50 opacity-30"></div>
            <div className="absolute left-0 top-0 h-full w-2 bg-gradient-to-b from-blue-50 to-purple-50 opacity-30"></div>

            {/* Word风格编辑区域 */}
            <div
              ref={editorRef}
              contentEditable={!isReadOnly}
              suppressContentEditableWarning={true}
              className="min-h-full focus:outline-none word-editor-content"
              style={{
                fontFamily: formatState.fontFamily,
                fontSize: `${formatState.fontSize}px`,
                lineHeight: '1.6',
                color: '#333333',
                background: 'transparent',
                padding: '0',
                margin: '0'
              }}
              dangerouslySetInnerHTML={{ __html: content }}
              onInput={(e) => {
                const currentContent = e.currentTarget.innerHTML;
                setContent(currentContent);
                setHasChanges(true);

                // 保存当前光标位置
                const selection = window.getSelection();
                if (selection && selection.rangeCount > 0) {
                  const range = selection.getRangeAt(0);
                  const cursorPosition = {
                    startContainer: range.startContainer,
                    startOffset: range.startOffset,
                    endContainer: range.endContainer,
                    endOffset: range.endOffset
                  };

                  // 延迟恢复光标位置和样式，避免被重置
                  setTimeout(() => {
                    try {
                      // 恢复光标位置
                      const newRange = document.createRange();
                      newRange.setStart(cursorPosition.startContainer, cursorPosition.startOffset);
                      newRange.setEnd(cursorPosition.endContainer, cursorPosition.endOffset);
                      selection.removeAllRanges();
                      selection.addRange(newRange);

                      // 强制恢复字体样式
                      if (editorRef.current) {
                        editorRef.current.style.fontFamily = formatState.fontFamily;
                        editorRef.current.style.fontSize = `${formatState.fontSize}px`;

                        // 检查并修复任何被重置为8px的元素
                        const allElements = editorRef.current.querySelectorAll('*');
                        allElements.forEach((el) => {
                          if (el instanceof HTMLElement) {
                            const computedSize = window.getComputedStyle(el).fontSize;
                            if (computedSize === '8px' || !el.style.fontSize) {
                              el.style.fontSize = `${formatState.fontSize}px`;
                            }
                          }
                        });
                      }
                    } catch (error) {
                      // 如果恢复失败，将光标放在内容末尾
                      const range = document.createRange();
                      range.selectNodeContents(e.currentTarget);
                      range.collapse(false);
                      selection.removeAllRanges();
                      selection.addRange(range);
                    }
                  }, 0);
                }
              }}
              onKeyDown={(e) => {
                // 防止某些快捷键导致光标跳转
                if (e.ctrlKey || e.metaKey) {
                  return;
                }
              }}
              onFocus={updateFormatState}
            />
          </div>

          {/* 页面阴影效果 */}
          <div className="h-4 bg-gradient-to-b from-gray-200 to-transparent opacity-20 mx-auto"
               style={{ width: '8.3in' }}></div>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
    </div>
  );
};

export default DocumentEditor;
