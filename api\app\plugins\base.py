"""
插件基类
定义插件接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from fastapi import FastAPI
from pydantic import BaseModel

from app.core.config import Settings


class PluginMetadata(BaseModel):
    """插件元数据"""
    name: str
    version: str
    description: str
    author: str
    dependencies: List[str] = []
    min_api_version: str = "0.1.0"
    max_api_version: Optional[str] = None


class PluginConfig(BaseModel):
    """插件配置"""
    enabled: bool = True
    settings: Dict[str, Any] = {}


class BasePlugin(ABC):
    """插件基类"""
    
    def __init__(self, config: PluginConfig):
        """
        初始化插件
        
        Args:
            config: 插件配置
        """
        self.config = config
        self._initialized = False
        self._started = False
    
    @property
    @abstractmethod
    def metadata(self) -> PluginMetadata:
        """
        插件元数据
        
        Returns:
            插件元数据
        """
        pass
    
    @abstractmethod
    async def initialize(self, app_settings: Settings) -> None:
        """
        初始化插件
        
        Args:
            app_settings: 应用配置
        """
        pass
    
    @abstractmethod
    async def startup(self) -> None:
        """
        插件启动
        """
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """
        插件关闭
        """
        pass
    
    def register_routes(self, app: FastAPI) -> None:
        """
        注册插件路由
        
        Args:
            app: FastAPI应用实例
        """
        pass
    
    def register_middleware(self, app: FastAPI) -> None:
        """
        注册插件中间件
        
        Args:
            app: FastAPI应用实例
        """
        pass
    
    def register_dependencies(self, app: FastAPI) -> None:
        """
        注册插件依赖
        
        Args:
            app: FastAPI应用实例
        """
        pass
    
    async def health_check(self) -> Dict[str, Any]:
        """
        插件健康检查
        
        Returns:
            健康状态信息
        """
        return {
            "name": self.metadata.name,
            "version": self.metadata.version,
            "status": "healthy" if self._started else "stopped",
            "initialized": self._initialized,
            "started": self._started,
        }
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.settings.get(key, default)
    
    def set_config_value(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.config.settings[key] = value
    
    @property
    def is_enabled(self) -> bool:
        """
        插件是否启用
        
        Returns:
            是否启用
        """
        return self.config.enabled
    
    @property
    def is_initialized(self) -> bool:
        """
        插件是否已初始化
        
        Returns:
            是否已初始化
        """
        return self._initialized
    
    @property
    def is_started(self) -> bool:
        """
        插件是否已启动
        
        Returns:
            是否已启动
        """
        return self._started
    
    async def _initialize(self, app_settings: Settings) -> None:
        """
        内部初始化方法
        
        Args:
            app_settings: 应用配置
        """
        if self._initialized:
            return
        
        await self.initialize(app_settings)
        self._initialized = True
    
    async def _startup(self) -> None:
        """
        内部启动方法
        """
        if not self._initialized:
            raise RuntimeError(f"Plugin {self.metadata.name} not initialized")
        
        if self._started:
            return
        
        await self.startup()
        self._started = True
    
    async def _shutdown(self) -> None:
        """
        内部关闭方法
        """
        if not self._started:
            return
        
        await self.shutdown()
        self._started = False
