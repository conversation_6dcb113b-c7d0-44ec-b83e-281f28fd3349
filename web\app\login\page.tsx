'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Toaster, toast } from 'react-hot-toast';
import { Loader2, CheckCircle } from 'lucide-react';
import AIBackground from '@/components/AIBackground';
import LoginForm from '@/components/LoginForm';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import ErrorBoundary from '@/components/ErrorBoundary';
import { systemInitApi } from '@/lib/systemInit';
import apiClient from '@/lib/api';

const LoginPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkSystemStatus = async () => {
      try {
        // 首先检查系统是否需要首次设置
        const response = await apiClient.get('/api/v1/setup/status');
        const status = response.data.data;

        if (status.needs_setup) {
          // 系统需要首次设置，跳转到设置页面
          router.push('/setup');
          return;
        }

        // 检查是否是从设置页面跳转过来的
        const message = searchParams.get('message');
        if (message === 'setup_complete') {
          toast.success('🎉 系统初始化完成！请使用管理员账户登录', {
            duration: 5000,
            icon: <CheckCircle className="w-5 h-5 text-green-500" />,
          });
        }

      } catch (error) {
        console.error('检查系统状态失败:', error);
        // 如果检查失败，尝试检查旧的初始化状态
        try {
          const { need_init } = await systemInitApi.checkNeedInit();
          if (need_init) {
            router.push('/system-init');
            return;
          }
        } catch (legacyError) {
          console.error('检查旧初始化状态失败:', legacyError);
          // 如果都失败，跳转到首次设置页面
          router.push('/setup');
          return;
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkSystemStatus();
  }, [router, searchParams]);

  // 如果正在检查系统状态，显示加载界面
  if (isChecking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="inline-block mb-4"
          >
            <Loader2 className="w-12 h-12 text-white" />
          </motion.div>
          <p className="text-white text-lg">正在检查系统状态...</p>
        </div>
      </div>
    );
  }
  return (
    <ErrorBoundary>
      <div className="min-h-screen relative overflow-hidden">
        {/* AI背景 */}
        <AIBackground />
        
        {/* 主要内容 */}
        <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
          <div className="w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
            
            {/* 左侧 - 品牌信息 */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="hidden lg:block"
            >
              <div className="text-white">
                <motion.h1
                  className="text-5xl font-bold mb-6 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  知识库
                  <br />
                  智能平台
                </motion.h1>
                
                <motion.p
                  className="text-xl text-blue-200 mb-8 leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  体验智能文档处理的未来，
                  基于AI的智能知识管理与检索系统。
                </motion.p>

                <motion.div
                  className="space-y-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  {[
                    '🧠 智能AI处理',
                    '🔍 智能文档检索',
                    '⚡ 实时分析',
                    '🛡️ 企业级安全',
                  ].map((feature, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-3"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                    >
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span className="text-blue-100">{feature}</span>
                    </motion.div>
                  ))}
                </motion.div>

                {/* 统计数据 */}
                <motion.div
                  className="mt-12 grid grid-cols-3 gap-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 1.2 }}
                >
                  {[
                    { number: '99.9%', label: 'Uptime' },
                    { number: '10M+', label: 'Documents' },
                    { number: '500+', label: 'Enterprises' },
                  ].map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-2xl font-bold text-white mb-1">
                        {stat.number}
                      </div>
                      <div className="text-sm text-blue-200">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </motion.div>
              </div>
            </motion.div>

            {/* 右侧 - 登录表单 */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="w-full"
            >
              <LoginForm />
            </motion.div>
          </div>
        </div>

        {/* 语言切换器 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="absolute top-6 right-6 z-20"
        >
          <LanguageSwitcher />
        </motion.div>

        {/* 底部装饰 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.5 }}
          className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-400 to-transparent opacity-50"
        />

        {/* Toast通知 */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'white',
            },
          }}
        />
        </div>
    </ErrorBoundary>
  );
};

export default LoginPage;
