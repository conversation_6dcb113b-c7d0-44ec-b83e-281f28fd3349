"""
FastAPI Application Entry Point
支持多环境、模块化、插件化的FastAPI应用
"""

import uvicorn
from app.core.app_factory import create_app
from app.core.config import get_settings

# 创建FastAPI应用实例
app = create_app()

if __name__ == "__main__":
    settings = get_settings()
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
