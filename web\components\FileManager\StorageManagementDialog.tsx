'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Plus,
  Edit,
  Trash2,
  TestTube,
  RefreshCw,
  HardDrive,
  Cloud,
  Server,
  Wifi,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import toast from 'react-hot-toast';

interface StorageConfig {
  id: number;
  name: string;
  storage_type: 'local' | 'minio' | 'ftp' | 'sftp';
  is_default: boolean;
  is_active: boolean;
  config: Record<string, any>;
  total_files?: number;
  total_size?: number;
  last_sync_at?: string;
  created_at: string;
  updated_at: string;
}

interface StorageManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onStorageChange: () => void;
}

const StorageManagementDialog: React.FC<StorageManagementDialogProps> = ({
  isOpen,
  onClose,
  onStorageChange
}) => {
  const { t } = useLanguage();
  const [storages, setStorages] = useState<StorageConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedStorage, setSelectedStorage] = useState<StorageConfig | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [testingStorage, setTestingStorage] = useState<number | null>(null);

  // 存储类型图标映射
  const storageIcons = {
    local: HardDrive,
    minio: Cloud,
    ftp: Server,
    sftp: Wifi
  };

  // 存储类型名称映射
  const storageTypeNames = {
    local: '本地存储',
    minio: 'MinIO',
    ftp: 'FTP',
    sftp: 'SFTP'
  };

  // 加载存储列表
  const loadStorages = async () => {
    setLoading(true);
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${API_BASE_URL}/api/v1/storage/`);
      if (response.ok) {
        const data = await response.json();
        setStorages(data);
      } else {
        toast.error('加载存储列表失败');
      }
    } catch (error) {
      console.error('加载存储列表失败:', error);
      toast.error('加载存储列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试存储连接
  const testStorage = async (storageId: number) => {
    setTestingStorage(storageId);
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${API_BASE_URL}/api/v1/storage/${storageId}/test`, {
        method: 'POST'
      });
      const result = await response.json();

      if (result.success) {
        toast.success('连接测试成功');
      } else {
        toast.error(`连接测试失败: ${result.message}`);
      }
    } catch (error) {
      console.error('测试存储连接失败:', error);
      toast.error('测试存储连接失败');
    } finally {
      setTestingStorage(null);
    }
  };

  // 删除存储
  const deleteStorage = async (storageId: number) => {
    if (!confirm('确定要删除这个存储配置吗？')) {
      return;
    }

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${API_BASE_URL}/api/v1/storage/${storageId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('存储配置删除成功');
        loadStorages();
        onStorageChange();
      } else {
        const error = await response.json();
        toast.error(error.detail || '删除存储配置失败');
      }
    } catch (error) {
      console.error('删除存储配置失败:', error);
      toast.error('删除存储配置失败');
    }
  };

  // 同步存储
  const syncStorage = async (storageId: number) => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${API_BASE_URL}/api/v1/storage/${storageId}/sync`, {
        method: 'POST'
      });

      if (response.ok) {
        toast.success('存储同步已开始');
        loadStorages();
      } else {
        toast.error('启动存储同步失败');
      }
    } catch (error) {
      console.error('同步存储失败:', error);
      toast.error('同步存储失败');
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '0 B';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  // 格式化时间
  const formatTime = (dateString?: string) => {
    if (!dateString) return '从未';
    return new Date(dateString).toLocaleString();
  };

  useEffect(() => {
    if (isOpen) {
      loadStorages();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <Settings className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">存储管理</h2>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowAddForm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>添加存储</span>
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="w-8 h-8 text-blue-600 animate-spin" />
                <span className="ml-3 text-gray-600">加载中...</span>
              </div>
            ) : storages.length === 0 ? (
              <div className="text-center py-12">
                <HardDrive className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无存储配置</h3>
                <p className="text-gray-500 mb-6">点击"添加存储"按钮创建第一个存储配置</p>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  添加存储
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {storages.map((storage) => {
                  const IconComponent = storageIcons[storage.storage_type];
                  return (
                    <motion.div
                      key={storage.id}
                      layout
                      className="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:border-gray-300 transition-colors"
                    >
                      {/* 存储头部 */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg ${storage.is_active ? 'bg-blue-100 text-blue-600' : 'bg-gray-200 text-gray-400'}`}>
                            <IconComponent className="w-5 h-5" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                              <span>{storage.name}</span>
                              {storage.is_default && (
                                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                  默认
                                </span>
                              )}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {storageTypeNames[storage.storage_type]}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          {storage.is_active ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </div>
                      </div>

                      {/* 存储统计 */}
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-gray-500">文件数量</p>
                          <p className="text-sm font-medium text-gray-900">
                            {storage.total_files || 0}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">总大小</p>
                          <p className="text-sm font-medium text-gray-900">
                            {formatFileSize(storage.total_size)}
                          </p>
                        </div>
                      </div>

                      {/* 最后同步时间 */}
                      <div className="mb-4">
                        <p className="text-xs text-gray-500">最后同步</p>
                        <p className="text-sm text-gray-700">
                          {formatTime(storage.last_sync_at)}
                        </p>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => testStorage(storage.id)}
                          disabled={testingStorage === storage.id}
                          className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors disabled:opacity-50"
                        >
                          <TestTube className="w-4 h-4" />
                          <span>{testingStorage === storage.id ? '测试中...' : '测试'}</span>
                        </button>
                        
                        <button
                          onClick={() => syncStorage(storage.id)}
                          className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                        >
                          <RefreshCw className="w-4 h-4" />
                          <span>同步</span>
                        </button>
                        
                        <button
                          onClick={() => setSelectedStorage(storage)}
                          className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                        >
                          <Edit className="w-4 h-4" />
                          <span>编辑</span>
                        </button>
                        
                        <button
                          onClick={() => deleteStorage(storage.id)}
                          className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                          <span>删除</span>
                        </button>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default StorageManagementDialog;
