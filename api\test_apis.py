"""
测试API服务器
用于测试存储概览和系统状态API
"""
import os
import json
import psutil
import socket
from datetime import datetime, timedelta
from typing import Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="测试API服务器", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def _format_bytes(bytes_value: int) -> str:
    """格式化字节数"""
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = float(bytes_value)
    unit_index = 0
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size)} {units[unit_index]}"
    else:
        return f"{size:.1f} {units[unit_index]}"


def _check_internet_connection() -> bool:
    """检查网络连接状态"""
    try:
        socket.create_connection(("8.8.8.8", 53), timeout=3)
        return True
    except OSError:
        return False


def _get_status_level(percentage: float) -> str:
    """根据使用率百分比获取状态级别"""
    if percentage < 50:
        return "good"
    elif percentage < 80:
        return "warning"
    else:
        return "critical"


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "测试API服务器",
        "version": "1.0.0",
        "endpoints": {
            "storage_overview": "/api/v1/storage-overview/summary",
            "system_status": "/api/v1/system-status/overview"
        }
    }


@app.get("/api/v1/storage-overview/summary")
async def get_storage_summary():
    """获取存储概览摘要"""
    try:
        # 获取真实的磁盘使用情况
        disk_usage = psutil.disk_usage('/')
        
        return {
            "success": True,
            "message": "获取存储概览成功",
            "data": {
                "total_capacity": disk_usage.total,
                "total_capacity_formatted": _format_bytes(disk_usage.total),
                "total_files": 1247,
                "shared_files": 89,
                "recent_access": 23,
                "used_space": disk_usage.used,
                "used_space_formatted": _format_bytes(disk_usage.used),
                "usage_percentage": (disk_usage.used / disk_usage.total * 100),
                "storage_count": 1,
                "storage_details": [],
                "last_updated": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储概览失败: {str(e)}")


@app.get("/api/v1/storage-overview/storage/{storage_id}/details")
async def get_storage_details(storage_id: int):
    """获取指定存储的详细信息"""
    try:
        disk_usage = psutil.disk_usage('/')
        
        storage_info = {
            'id': storage_id,
            'name': f'存储{storage_id}',
            'type': 'local',
            'base_path': 'E:/test2',
            'total_capacity': disk_usage.total,
            'used_space': disk_usage.used,
            'available_space': disk_usage.free,
            'file_count': 1247,
            'folder_count': 156,
            'health_status': 'healthy'
        }
        
        recent_files_data = [
            {
                "id": 1,
                "filename": "document.pdf",
                "file_size": 1024 * 1024 * 2,  # 2MB
                "file_size_formatted": "2.0 MB",
                "upload_time": datetime.utcnow().isoformat(),
                "file_path": "/documents/document.pdf"
            }
        ]
        
        return {
            "success": True,
            "message": "获取存储详情成功",
            "data": {
                **storage_info,
                "recent_files": recent_files_data
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储详情失败: {str(e)}")


@app.get("/api/v1/storage-overview/file-types")
async def get_file_type_distribution():
    """获取文件类型分布"""
    try:
        formatted_data = [
            {'type': 'document', 'count': 450, 'size': 1024*1024*500, 'size_formatted': '500.0 MB', 'percentage': 36.1},
            {'type': 'image', 'count': 320, 'size': 1024*1024*800, 'size_formatted': '800.0 MB', 'percentage': 25.7},
            {'type': 'video', 'count': 89, 'size': 1024*1024*1200, 'size_formatted': '1.2 GB', 'percentage': 7.1},
            {'type': 'audio', 'count': 156, 'size': 1024*1024*300, 'size_formatted': '300.0 MB', 'percentage': 12.5},
            {'type': 'archive', 'count': 67, 'size': 1024*1024*200, 'size_formatted': '200.0 MB', 'percentage': 5.4},
            {'type': 'other', 'count': 165, 'size': 1024*1024*150, 'size_formatted': '150.0 MB', 'percentage': 13.2}
        ]
        
        total_files = sum(item['count'] for item in formatted_data)
        total_size = sum(item['size'] for item in formatted_data)
        
        return {
            "success": True,
            "message": "获取文件类型分布成功",
            "data": {
                "file_types": formatted_data,
                "total_files": total_files,
                "total_size": total_size
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件类型分布失败: {str(e)}")


@app.get("/api/v1/system-status/overview")
async def get_system_overview():
    """获取系统状态概览"""
    try:
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # 内存信息
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # 磁盘信息
        disk_usage = psutil.disk_usage('/')
        
        # 网络信息
        network_stats = psutil.net_io_counters()
        network_connections = len(psutil.net_connections())
        
        # 系统信息
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        
        return {
            "success": True,
            "message": "获取系统状态成功",
            "data": {
                "cpu": {
                    "usage_percent": round(cpu_percent, 1),
                    "core_count": cpu_count,
                    "frequency": {
                        "current": round(cpu_freq.current, 2) if cpu_freq else 0,
                        "min": round(cpu_freq.min, 2) if cpu_freq else 0,
                        "max": round(cpu_freq.max, 2) if cpu_freq else 0
                    },
                    "status": _get_status_level(cpu_percent)
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "usage_percent": round(memory.percent, 1),
                    "total_formatted": _format_bytes(memory.total),
                    "available_formatted": _format_bytes(memory.available),
                    "used_formatted": _format_bytes(memory.used),
                    "swap_total": swap.total,
                    "swap_used": swap.used,
                    "swap_percent": round(swap.percent, 1),
                    "status": _get_status_level(memory.percent)
                },
                "disk": {
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "usage_percent": round((disk_usage.used / disk_usage.total) * 100, 1),
                    "total_formatted": _format_bytes(disk_usage.total),
                    "used_formatted": _format_bytes(disk_usage.used),
                    "free_formatted": _format_bytes(disk_usage.free),
                    "status": _get_status_level((disk_usage.used / disk_usage.total) * 100)
                },
                "network": {
                    "bytes_sent": network_stats.bytes_sent,
                    "bytes_recv": network_stats.bytes_recv,
                    "packets_sent": network_stats.packets_sent,
                    "packets_recv": network_stats.packets_recv,
                    "bytes_sent_formatted": _format_bytes(network_stats.bytes_sent),
                    "bytes_recv_formatted": _format_bytes(network_stats.bytes_recv),
                    "connections": network_connections,
                    "status": "online" if _check_internet_connection() else "offline"
                },
                "system": {
                    "platform": psutil.WINDOWS if hasattr(psutil, 'WINDOWS') else "Windows",
                    "hostname": socket.gethostname(),
                    "uptime_formatted": f"{uptime.days}天 {uptime.seconds//3600}小时 {(uptime.seconds%3600)//60}分钟"
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8001, reload=True)
