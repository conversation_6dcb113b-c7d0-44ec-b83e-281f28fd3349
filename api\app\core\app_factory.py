"""
FastAPI应用工厂
负责创建和配置FastAPI应用实例
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException
from starlette.middleware.sessions import SessionMiddleware

from app.core.config import get_settings
from app.core.logging import setup_logging
from app.core.exceptions import setup_exception_handlers
from app.core.middleware import setup_middleware
from app.core.database import init_database, close_database, check_database_health
from app.api.router import api_router
from app.plugins.manager import PluginManager


def create_app() -> FastAPI:
    """
    创建FastAPI应用实例
    
    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    settings = get_settings()
    
    # 设置日志
    setup_logging(settings)
    
    # 创建FastAPI应用
    app = FastAPI(
        title=settings.APP_NAME,
        description=settings.APP_DESCRIPTION,
        version=settings.APP_VERSION,
        debug=settings.DEBUG,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        openapi_url="/openapi.json" if settings.DEBUG else None,
    )

    # 首先添加CORS中间件，确保最高优先级
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://localhost:3002",
            "http://127.0.0.1:3002"
        ] if settings.DEBUG else ["*"],  # 开发环境明确指定前端域名
        allow_credentials=True,  # 允许凭证
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],  # 明确指定方法
        allow_headers=["*"],  # 允许所有请求头
        expose_headers=["X-Request-ID", "X-Process-Time"],
        max_age=86400,  # 预检请求缓存24小时
    )

    # 设置其他中间件
    setup_middleware(app, settings)
    
    # 设置异常处理器
    setup_exception_handlers(app)
    
    # 注册路由
    app.include_router(api_router, prefix="/api")
    
    # 初始化插件系统
    plugin_manager = PluginManager(settings)
    plugin_manager.load_plugins()
    plugin_manager.register_plugins(app)
    
    # 添加启动和关闭事件
    @app.on_event("startup")
    async def startup_event():
        """应用启动事件"""
        print(f"🚀 {settings.APP_NAME} v{settings.APP_VERSION} starting...")
        print(f"📍 Environment: {settings.ENVIRONMENT}")
        print(f"🌐 Server: http://{settings.HOST}:{settings.PORT}")
        if settings.DEBUG:
            print(f"📚 Docs: http://{settings.HOST}:{settings.PORT}/docs")

        # 初始化数据库连接
        try:
            await init_database(settings)
            print("✅ Database connection initialized")

            # 运行数据库迁移
            print("🔄 Creating/updating database tables...")
            from app.core.simple_migration import create_tables_directly
            migration_result = await create_tables_directly()

            if migration_result["status"] == "success":
                print("✅ Database tables ready")
                if migration_result["summary"]["total_actions"] > 0:
                    print(f"   - {migration_result['summary']['total_actions']} actions completed")
                if migration_result["summary"]["errors"] > 0:
                    print(f"   - {migration_result['summary']['errors']} errors occurred")
            else:
                print(f"⚠️  Database table creation completed with warnings:")
                for error in migration_result.get("errors", []):
                    print(f"     - {error}")

        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            print(f"   Error details: {type(e).__name__}: {str(e)}")
            # 不要抛出异常，让应用继续启动，但数据库状态会显示为错误
            print("⚠️  Application will start without database connection")

        # 初始化插件系统
        await plugin_manager.startup()

        # 启动上传处理器
        try:
            from app.services.upload_processor import upload_processor
            await upload_processor.start()
            print("✅ Upload processor started")
        except Exception as e:
            print(f"⚠️  Upload processor failed to start: {e}")

        # 启动Celery服务
        try:
            from app.core.startup import startup_tasks
            await startup_tasks()
            print("✅ Celery services initialized")
        except Exception as e:
            print(f"⚠️  Celery services failed to start: {e}")

    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭事件"""
        print(f"🛑 {settings.APP_NAME} shutting down...")

        # 停止上传处理器
        try:
            from app.services.upload_processor import upload_processor
            await upload_processor.stop()
            print("✅ Upload processor stopped")
        except Exception as e:
            print(f"⚠️  Upload processor failed to stop: {e}")

        # 停止Celery服务
        try:
            from app.core.startup import shutdown_tasks
            await shutdown_tasks()
            print("✅ Celery services stopped")
        except Exception as e:
            print(f"⚠️  Celery services failed to stop: {e}")

        # 关闭插件系统
        await plugin_manager.shutdown()

        # 关闭数据库连接
        await close_database()
        print("✅ Database connection closed")
    
    # 健康检查端点
    @app.get("/health", tags=["Health"])
    async def health_check():
        """健康检查端点"""
        # 检查数据库健康状态
        db_health = await check_database_health()

        # 检查插件健康状态
        plugins_health = await plugin_manager.get_plugins_health()

        overall_status = "healthy"
        if db_health["status"] != "healthy":
            overall_status = "unhealthy"

        return {
            "status": overall_status,
            "app_name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT,
            "database": db_health,
            "plugins": plugins_health,
            "timestamp": "2024-01-01T00:00:00Z"  # 实际应用中使用当前时间
        }
    
    # 根路径
    @app.get("/", tags=["Root"])
    async def root():
        """根路径"""
        return {
            "message": f"Welcome to {settings.APP_NAME}",
            "version": settings.APP_VERSION,
            "docs_url": "/docs" if settings.DEBUG else "Documentation not available in production",
        }
    
    return app
