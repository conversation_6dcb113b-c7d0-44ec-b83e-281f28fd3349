"""
上传任务模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, BigInteger, Boolean
from sqlalchemy.sql import func
from app.core.database import Base


class UploadTask(Base):
    """上传任务表"""
    __tablename__ = "upload_tasks"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), unique=True, index=True, comment="任务唯一ID")
    file_name = Column(String(500), nullable=False, comment="文件名")
    file_size = Column(BigInteger, nullable=False, comment="文件大小(字节)")
    file_path = Column(Text, nullable=False, comment="本地文件路径")
    storage_id = Column(Integer, nullable=False, comment="存储配置ID")
    upload_path = Column(Text, nullable=False, comment="上传目标路径")
    status = Column(String(20), nullable=False, default="pending", comment="状态: pending, uploading, success, error, paused")
    progress = Column(Integer, default=0, comment="上传进度(0-100)")
    uploaded_bytes = Column(BigInteger, default=0, comment="已上传字节数")
    error_message = Column(Text, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    started_at = Column(DateTime(timezone=True), comment="开始上传时间")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "file_path": self.file_path,
            "storage_id": self.storage_id,
            "upload_path": self.upload_path,
            "status": self.status,
            "progress": self.progress,
            "uploaded_bytes": self.uploaded_bytes,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
        }
