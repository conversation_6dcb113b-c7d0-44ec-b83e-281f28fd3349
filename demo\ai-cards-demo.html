<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能工具集 - 现代化卡片展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 15px;
            padding: 8px 20px;
            background: rgba(255, 255, 255, 0.8);
            background: -webkit-linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8));
            background: -moz-linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8));
            border-radius: 8px;
            -webkit-border-radius: 8px;
            -moz-border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            -moz-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .header h1 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #374151;
            margin: 0 0 2px 0;
            line-height: 1.2;
        }

        .header p {
            font-size: 0.75rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.2;
        }

        .cards-grid {
            display: -ms-grid;
            display: grid;
            -ms-grid-columns: 1fr 16px 1fr 16px 1fr;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 16px;
            grid-gap: 16px;
            padding: 20px 0;
        }

        /* IE11 fallback */
        @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
            .cards-grid {
                display: -ms-flexbox;
                display: flex;
                -ms-flex-wrap: wrap;
                flex-wrap: wrap;
            }

            .card {
                -ms-flex: 0 0 calc(33.333% - 16px);
                flex: 0 0 calc(33.333% - 16px);
                margin: 8px;
            }
        }

        .card {
            background: rgba(255, 255, 255, 0.9);
            background: -webkit-linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9));
            background: -moz-linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9));
            border-radius: 16px;
            -webkit-border-radius: 16px;
            -moz-border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            -webkit-box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            -moz-box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
            -moz-transition: all 0.3s ease;
            -o-transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: start;
            -webkit-align-items: flex-start;
            -moz-box-align: start;
            -ms-flex-align: start;
            align-items: flex-start;
            gap: 16px;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        .card-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: #6366f1;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
        }

        .card-content {
            flex: 1;
            min-width: 0;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .card-meta {
            font-size: 0.8rem;
            color: #9ca3af;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .card-company {
            font-weight: 500;
            color: #6366f1;
        }

        .card-creator {
            color: #8b5cf6;
        }

        .card-description {
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.5;
            display: -webkit-box;
            display: -moz-box;
            display: box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            -moz-box-orient: vertical;
            box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 全屏弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
            overflow-y: auto;
        }

        .modal-content {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: transparent;
            padding: 0;
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            padding: 12px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .modal-header-left {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
        }

        .modal-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
            color: #6366f1;
            flex-shrink: 0;
            box-shadow: 0 1px 4px rgba(99, 102, 241, 0.1);
        }

        .modal-info {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        .modal-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-meta {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.1;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 2px;
        }

        .modal-company {
            font-weight: 600;
            color: #6366f1;
        }

        .modal-creator {
            color: #8b5cf6;
            font-weight: 500;
        }

        .modal-description-short {
            font-size: 0.7rem;
            color: #9ca3af;
            line-height: 1.2;
            display: -webkit-box;
            display: -moz-box;
            display: box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            -moz-box-orient: vertical;
            box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-iframe-container {
            position: absolute;
            top: 68px;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: calc(100vh - 68px);
            overflow: hidden;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            background: white;
            display: block;
        }

        .btn {
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            min-width: 120px;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #6b7280;
            border: 2px solid #e5e7eb;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #d1d5db;
        }

        .close {
            width: 32px;
            height: 32px;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #ef4444;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(239, 68, 68, 0.2);
            flex-shrink: 0;
            margin-left: 12px;
        }

        .close:hover {
            background: rgba(239, 68, 68, 0.2);
            color: #dc2626;
            transform: scale(1.05);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to { 
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                max-width: 100%;
            }

            .header {
                margin-bottom: 10px;
                padding: 6px 15px;
            }

            .header h1 {
                font-size: 1.1rem;
            }

            .header p {
                font-size: 0.7rem;
            }

            .cards-grid {
                grid-template-columns: 1fr;
                gap: 12px;
                padding: 10px 0;
            }

            .card {
                padding: 16px;
                gap: 12px;
                border-radius: 12px;
            }

            .card-icon {
                width: 48px;
                height: 48px;
                font-size: 24px;
                border-radius: 12px;
            }

            .card-title {
                font-size: 1.1rem;
            }

            .card-description {
                font-size: 0.85rem;
                -webkit-line-clamp: 2;
                line-clamp: 2;
            }

            .modal-header {
                padding: 10px 15px;
            }

            .modal-icon {
                width: 36px;
                height: 36px;
                font-size: 18px;
                margin-right: 10px;
            }

            .modal-title {
                font-size: 1rem;
            }

            .modal-meta {
                font-size: 0.7rem;
                gap: 4px;
            }

            .modal-description-short {
                font-size: 0.65rem;
            }

            .modal-iframe-container {
                top: 58px;
                height: calc(100vh - 58px);
            }

            .close {
                width: 28px;
                height: 28px;
                font-size: 14px;
                margin-left: 8px;
            }
        }

        /* 平板端适配 */
        @media (min-width: 769px) and (max-width: 1024px) {
            .cards-grid {
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: 14px;
            }

            .card {
                padding: 18px;
            }

            .card-icon {
                width: 52px;
                height: 52px;
                font-size: 26px;
            }
        }

        /* 大屏幕优化 */
        @media (min-width: 1400px) {
            .cards-grid {
                grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
                gap: 20px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .card:hover {
                transform: none;
            }

            .card:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }

            .btn:hover {
                transform: none;
            }

            .btn:active {
                transform: scale(0.95);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI智能工具集</h1>
            <p>探索最前沿的人工智能工具和服务，提升您的工作效率和创造力</p>
        </div>

        <div class="cards-grid" id="cardsGrid">
            <!-- 卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 全屏弹窗模板 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-header-left">
                    <div class="modal-icon" id="modalIcon"></div>
                    <div class="modal-info">
                        <div class="modal-title" id="modalTitle"></div>
                        <div class="modal-meta">
                            <span class="modal-company" id="modalCompany"></span>
                            <span>•</span>
                            <span class="modal-creator" id="modalCreator"></span>
                        </div>
                        <div class="modal-description-short" id="modalDescriptionShort"></div>
                    </div>
                </div>
                <button class="close" onclick="closeModal()" title="关闭">&times;</button>
            </div>
            <div class="modal-iframe-container">
                <iframe id="modalIframe" class="modal-iframe" src="" frameborder="0" border="0" allowfullscreen seamless></iframe>
            </div>
        </div>
    </div>

    <script>
        // 41个AI工具卡片数据
        const aiTools = [
            {
                icon: "🤖",
                title: "ChatGPT",
                company: "OpenAI",
                creator: "Sam Altman",
                description: "OpenAI开发的强大对话AI，支持文本生成、问答、编程等多种任务，是目前最受欢迎的AI助手之一。它能够理解上下文，进行深度对话，协助完成写作、编程、分析等各种复杂任务。",
                link: "https://www.hao123.com/?tn=02003390_58_hao_pg"
            },
            {
                icon: "🎨",
                title: "Midjourney",
                company: "Midjourney Inc",
                creator: "David Holz",
                description: "AI图像生成工具，通过文本描述创造惊艳的艺术作品和插图。以其独特的艺术风格和高质量的图像输出而闻名，广泛应用于创意设计、概念艺术和商业插画领域。",
                link: "https://midjourney.com"
            },
            {
                icon: "📝",
                title: "Notion AI",
                company: "Notion Labs",
                creator: "Ivan Zhao",
                description: "集成在Notion中的AI写作助手，帮助提升文档编写效率。能够自动生成内容、总结文档、翻译文本，与Notion的协作功能完美结合，为团队工作提供智能支持。",
                link: "https://notion.so"
            },
            {
                icon: "🎵",
                title: "AIVA",
                company: "AIVA Technologies",
                creator: "Pierre Barreau",
                description: "AI音乐创作平台，自动生成各种风格的原创音乐作品。使用深度学习技术分析古典音乐大师的作品，能够创作出专业级别的音乐，适用于电影配乐、游戏音效等场景。",
                link: "https://aiva.ai"
            },
            {
                icon: "🎬",
                title: "Runway ML",
                company: "Runway AI",
                creator: "Cristóbal Valenzuela",
                description: "AI视频编辑工具，提供视频生成、编辑和特效制作功能。集成了多种AI模型，支持实时视频处理、背景替换、风格转换等高级功能，是创作者的强大工具。",
                link: "https://runwayml.com"
            },
            {
                icon: "💻",
                title: "GitHub Copilot",
                company: "GitHub/Microsoft",
                creator: "Nat Friedman",
                description: "AI编程助手，实时提供代码建议和自动补全功能。基于OpenAI Codex模型，能够理解代码上下文，提供智能的代码补全、函数生成和bug修复建议，大幅提升开发效率。",
                link: "https://github.com/features/copilot"
            },
            {
                icon: "🔍",
                title: "Perplexity AI",
                company: "Perplexity AI",
                creator: "Aravind Srinivas",
                description: "AI搜索引擎，提供准确的答案和可靠的信息来源。结合了搜索和AI问答的优势，能够实时获取最新信息并提供详细的引用来源，是研究和学习的理想工具。",
                link: "https://perplexity.ai"
            },
            {
                icon: "📊",
                title: "Tableau AI",
                company: "Salesforce",
                creator: "Christian Chabot",
                description: "智能数据分析平台，自动生成数据洞察和可视化图表。利用AI技术自动发现数据模式，生成智能建议，帮助用户快速创建专业的数据可视化报告。",
                link: "https://tableau.com"
            },
            {
                icon: "🗣️",
                title: "ElevenLabs",
                company: "ElevenLabs",
                creator: "Piotr Dabkowski",
                description: "AI语音合成工具，生成逼真的人声和多语言配音。使用先进的语音克隆技术，能够生成极其自然的语音，支持多种语言和情感表达，广泛应用于有声读物、播客等领域。",
                link: "https://elevenlabs.io"
            },
            {
                icon: "📚",
                title: "Claude",
                company: "Anthropic",
                creator: "Dario Amodei",
                description: "Anthropic开发的AI助手，擅长分析、写作和复杂推理任务。注重AI安全性和可解释性，在处理长文本、复杂分析和创意写作方面表现出色，是ChatGPT的有力竞争者。",
                link: "https://claude.ai"
            },
            {
                icon: "🎯",
                title: "Copy.ai",
                company: "Copy.ai",
                creator: "Paul Yacoubian",
                description: "AI文案生成工具，快速创建营销文案、邮件和社交媒体内容。专为营销人员设计，能够生成各种类型的营销文案，包括广告语、产品描述、邮件主题等，帮助提升营销效果。",
                link: "https://copy.ai"
            },
            {
                icon: "🖼️",
                title: "DALL-E 3",
                company: "OpenAI",
                creator: "Aditya Ramesh",
                description: "OpenAI的图像生成模型，根据文本描述创建高质量图像。相比前代产品，DALL-E 3在图像质量、文本理解和创意表达方面都有显著提升，能够生成更加精确和艺术化的图像。",
                link: "https://openai.com/dall-e-3"
            },
            {
                icon: "📈",
                title: "Jasper AI",
                company: "Jasper AI",
                creator: "Dave Rogenmoser",
                description: "企业级AI写作平台，专注于营销内容和品牌一致性。提供模板化的内容生成，支持多种营销场景，帮助企业保持品牌声音的一致性，提升内容营销效率。",
                link: "https://jasper.ai"
            },
            {
                icon: "🔧",
                title: "Zapier AI",
                company: "Zapier",
                creator: "Wade Foster",
                description: "AI自动化工具，智能连接不同应用和服务。通过AI技术简化工作流程自动化的设置，让用户能够轻松创建复杂的自动化流程，提升工作效率。",
                link: "https://zapier.com"
            },
            {
                icon: "🎪",
                title: "Canva AI",
                company: "Canva",
                creator: "Melanie Perkins",
                description: "AI设计助手，自动生成海报、logo和营销素材。集成了多种AI功能，包括智能设计建议、背景移除、文字生成等，让非设计师也能创作出专业级的视觉内容。",
                link: "https://canva.com"
            },
            {
                icon: "📱",
                title: "Otter.ai",
                description: "AI会议记录工具，实时转录和总结会议内容",
                link: "https://otter.ai"
            },
            {
                icon: "🌐",
                title: "DeepL",
                description: "AI翻译工具，提供高质量的多语言翻译服务",
                link: "https://deepl.com"
            },
            {
                icon: "🎭",
                title: "Character.AI",
                description: "AI角色对话平台，与虚拟角色进行自然对话",
                link: "https://character.ai"
            },
            {
                icon: "📋",
                title: "Grammarly",
                description: "AI写作助手，提供语法检查、风格建议和写作优化",
                link: "https://grammarly.com"
            },
            {
                icon: "🎨",
                title: "Stable Diffusion",
                description: "开源AI图像生成模型，支持本地部署和自定义训练",
                link: "https://stability.ai"
            },
            {
                icon: "🔊",
                title: "Murf AI",
                description: "AI配音工具，生成专业级的语音旁白和播客内容",
                link: "https://murf.ai"
            },
            {
                icon: "📖",
                title: "Summarize.tech",
                description: "AI视频摘要工具，快速提取YouTube视频的关键信息",
                link: "https://summarize.tech"
            },
            {
                icon: "🎯",
                title: "Writesonic",
                description: "AI内容创作平台，生成博客、广告和社交媒体内容",
                link: "https://writesonic.com"
            },
            {
                icon: "🔬",
                title: "Consensus",
                description: "AI学术搜索引擎，快速找到科研论文和学术证据",
                link: "https://consensus.app"
            },
            {
                icon: "🎨",
                title: "Adobe Firefly",
                description: "Adobe的AI创意工具，集成在Creative Suite中的图像生成",
                link: "https://firefly.adobe.com"
            },
            {
                icon: "💡",
                title: "Tome",
                description: "AI演示文稿生成器，自动创建精美的幻灯片和故事",
                link: "https://tome.app"
            },
            {
                icon: "🎵",
                title: "Soundraw",
                description: "AI音乐生成平台，为视频和项目创作背景音乐",
                link: "https://soundraw.io"
            },
            {
                icon: "📊",
                title: "MonkeyLearn",
                description: "AI文本分析工具，提供情感分析和数据挖掘功能",
                link: "https://monkeylearn.com"
            },
            {
                icon: "🎬",
                title: "Synthesia",
                description: "AI视频生成平台，创建虚拟主播和多语言视频内容",
                link: "https://synthesia.io"
            },
            {
                icon: "🔍",
                title: "Scite AI",
                description: "AI学术引用分析工具，评估研究论文的可信度",
                link: "https://scite.ai"
            },
            {
                icon: "📝",
                title: "Rytr",
                description: "AI写作助手，快速生成各种类型的文本内容",
                link: "https://rytr.me"
            },
            {
                icon: "🎨",
                title: "Artbreeder",
                description: "AI图像混合工具，通过基因算法创造独特的艺术作品",
                link: "https://artbreeder.com"
            },
            {
                icon: "📱",
                title: "Luma AI",
                description: "AI 3D扫描应用，将现实物体转换为3D模型",
                link: "https://lumalabs.ai"
            },
            {
                icon: "🎯",
                title: "Phind",
                description: "AI编程搜索引擎，专为开发者提供代码解决方案",
                link: "https://phind.com"
            },
            {
                icon: "📊",
                title: "Beautiful.AI",
                description: "AI演示设计工具，自动优化幻灯片布局和视觉效果",
                link: "https://beautiful.ai"
            },
            {
                icon: "🎨",
                title: "Lexica",
                company: "Lexica",
                creator: "Sharif Shameem",
                description: "AI艺术搜索引擎，探索和发现AI生成的艺术作品。提供海量的AI生成图像库，用户可以搜索、浏览和获取创作灵感。",
                link: "https://lexica.art"
            },
            {
                icon: "📝",
                title: "QuillBot",
                company: "QuillBot",
                creator: "Rohan Gupta",
                description: "AI改写工具，提供文本重写、总结和语法检查功能。帮助用户改善写作质量，避免重复表达，提升文本的可读性和原创性。",
                link: "https://quillbot.com"
            },
            {
                icon: "🎵",
                title: "Boomy",
                company: "Boomy Corporation",
                creator: "Alex Mitchell",
                description: "AI音乐创作平台，让任何人都能创作和发布原创音乐。无需音乐基础，用户可以快速生成各种风格的音乐作品并发布到流媒体平台。",
                link: "https://boomy.com"
            },
            {
                icon: "🔊",
                title: "Descript",
                company: "Descript Inc",
                creator: "Andrew Mason",
                description: "AI音频编辑工具，提供转录、编辑和语音克隆功能。革命性的音频编辑体验，支持文本编辑音频，语音克隆和多人协作。",
                link: "https://descript.com"
            },
            {
                icon: "🎯",
                title: "Gamma",
                company: "Gamma Technologies",
                creator: "Grant Lee",
                description: "AI演示文稿生成器，快速创建精美的幻灯片和网页。通过AI技术自动生成专业的演示文稿，支持多种模板和自定义选项。",
                link: "https://gamma.app"
            },
            {
                icon: "🤖",
                title: "Replika",
                company: "Luka Inc",
                creator: "Eugenia Kuyda",
                description: "AI伴侣聊天机器人，提供个性化的对话和情感支持。通过深度学习技术，能够理解用户情感，提供个性化的陪伴体验。",
                link: "https://replika.ai"
            }
        ];

        // 生成卡片
        function generateCards() {
            const cardsGrid = document.getElementById('cardsGrid');
            
            aiTools.forEach((tool, index) => {
                const card = document.createElement('div');
                card.className = 'card';
                card.onclick = () => openModal(tool);
                
                card.innerHTML = `
                    <div class="card-icon">${tool.icon}</div>
                    <div class="card-content">
                        <div class="card-title">${tool.title}</div>
                        <div class="card-meta">
                            <span class="card-company">${tool.company}</span>
                            <span> • </span>
                            <span class="card-creator">${tool.creator}</span>
                        </div>
                        <div class="card-description">${tool.description}</div>
                    </div>
                `;
                
                cardsGrid.appendChild(card);
            });
        }

        // 打开全屏弹窗并加载第三方页面
        function openModal(tool) {
            const modal = document.getElementById('modal');
            const modalIcon = document.getElementById('modalIcon');
            const modalTitle = document.getElementById('modalTitle');
            const modalCompany = document.getElementById('modalCompany');
            const modalCreator = document.getElementById('modalCreator');
            const modalDescriptionShort = document.getElementById('modalDescriptionShort');
            const modalIframe = document.getElementById('modalIframe');

            // 设置头部信息
            modalIcon.textContent = tool.icon;
            modalTitle.textContent = tool.title;
            modalTitle.title = tool.title; // 添加完整标题的tooltip
            modalCompany.textContent = tool.company;
            modalCreator.textContent = tool.creator;
            modalDescriptionShort.textContent = tool.description;

            // 加载第三方页面
            modalIframe.src = tool.link;

            // 显示弹窗
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';

            // 添加加载提示
            modalIframe.onload = function() {
                console.log(`已加载: ${tool.title}`);
            };

            modalIframe.onerror = function() {
                console.error(`加载失败: ${tool.title}`);
                modalIframe.src = `data:text/html,<html><body style="font-family: Arial, sans-serif; text-align: center; padding: 50px; color: #666;"><h2>页面加载失败</h2><p>无法加载 ${tool.title} 的页面</p><p><a href="${tool.link}" target="_blank" style="color: #667eea;">点击这里在新窗口中打开</a></p></body></html>`;
            };
        }

        // 关闭弹窗
        function closeModal() {
            const modal = document.getElementById('modal');
            const modalIframe = document.getElementById('modalIframe');

            // 清空iframe内容以停止加载
            modalIframe.src = 'about:blank';

            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 事件监听 - 兼容旧版浏览器
        function addEvent(element, event, handler) {
            if (element.addEventListener) {
                element.addEventListener(event, handler, false);
            } else if (element.attachEvent) {
                element.attachEvent('on' + event, handler);
            } else {
                element['on' + event] = handler;
            }
        }

        function ready(fn) {
            if (document.readyState === 'complete' || document.readyState === 'interactive') {
                setTimeout(fn, 1);
            } else if (document.addEventListener) {
                document.addEventListener('DOMContentLoaded', fn);
            } else if (document.attachEvent) {
                document.attachEvent('onreadystatechange', function() {
                    if (document.readyState === 'complete') {
                        fn();
                    }
                });
            }
        }

        ready(function() {
            generateCards();

            // 点击弹窗外部关闭
            var modal = document.getElementById('modal');
            addEvent(modal, 'click', function(e) {
                e = e || window.event;
                var target = e.target || e.srcElement;
                if (target === modal) {
                    closeModal();
                }
            });

            // ESC键关闭弹窗
            addEvent(document, 'keydown', function(e) {
                e = e || window.event;
                var keyCode = e.keyCode || e.which;
                if (keyCode === 27) { // ESC key
                    closeModal();
                }
            });

            // 关闭按钮
            var closeBtn = document.querySelector('.close');
            if (closeBtn) {
                addEvent(closeBtn, 'click', closeModal);
            }
        });
    </script>
</body>
</html>
