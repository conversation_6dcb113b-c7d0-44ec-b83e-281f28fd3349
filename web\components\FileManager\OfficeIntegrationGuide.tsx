'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Presentation,
  FileSpreadsheet,
  File,
  Eye,
  Edit3,
  Scissors,
  Sparkles,
  Zap,
  Brain,
  X,
  CheckCircle,
  ArrowRight,
  Play
} from 'lucide-react';

interface OfficeIntegrationGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

const OfficeIntegrationGuide: React.FC<OfficeIntegrationGuideProps> = ({
  isOpen,
  onClose
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  const features = [
    {
      icon: FileText,
      title: 'Word 文档编辑',
      description: '完整的富文本编辑功能',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      features: [
        '字体格式设置（粗体、斜体、下划线）',
        '段落对齐（左对齐、居中、右对齐、两端对齐）',
        '插入表格、图片、链接',
        '有序和无序列表',
        '实时保存和版本控制'
      ]
    },
    {
      icon: Presentation,
      title: 'PowerPoint 演示',
      description: '专业的幻灯片制作工具',
      color: 'text-orange-500',
      bgColor: 'bg-orange-50',
      features: [
        '多种幻灯片布局模板',
        '幻灯片管理（添加、删除、复制、移动）',
        '全屏演示模式',
        '缩略图导航面板',
        '实时内容编辑'
      ]
    },
    {
      icon: FileSpreadsheet,
      title: 'Excel 表格',
      description: '强大的电子表格功能',
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      features: [
        '多工作表支持',
        '单元格格式设置',
        '公式编辑和计算',
        '数据排序和筛选',
        '图表生成（开发中）'
      ]
    },
    {
      icon: File,
      title: 'PDF 查看器',
      description: '专业的PDF阅读体验',
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      features: [
        '页面导航和缩放',
        '文本搜索和高亮',
        '注释和书签功能',
        '全屏阅读模式',
        '打印和下载支持'
      ]
    }
  ];

  const steps = [
    {
      title: '选择文件',
      description: '在文件列表中找到您要编辑的Office文件',
      icon: Eye
    },
    {
      title: '选择操作',
      description: '点击查看、编辑或分段按钮',
      icon: Edit3
    },
    {
      title: '开始编辑',
      description: '享受类似OnlyOffice的完整编辑体验',
      icon: Sparkles
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* 主要内容 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white rounded-2xl shadow-2xl max-w-4xl max-h-[90vh] overflow-hidden"
          >
            {/* 头部 */}
            <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">Office 在线编辑器</h2>
                  <p className="text-blue-100">类似 OnlyOffice 的完整编辑体验</p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* 内容区域 */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {/* 功能特性 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-6 rounded-xl border-2 border-gray-100 hover:border-gray-200 transition-all ${feature.bgColor}`}
                  >
                    <div className="flex items-center space-x-3 mb-4">
                      <div className={`p-3 rounded-lg bg-white shadow-sm`}>
                        <feature.icon className={`w-6 h-6 ${feature.color}`} />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{feature.title}</h3>
                        <p className="text-sm text-gray-600">{feature.description}</p>
                      </div>
                    </div>
                    
                    <ul className="space-y-2">
                      {feature.features.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-center space-x-2 text-sm text-gray-700">
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                ))}
              </div>

              {/* 使用步骤 */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                  <Zap className="w-5 h-5 text-yellow-500 mr-2" />
                  使用步骤
                </h3>
                
                <div className="flex items-center justify-between">
                  {steps.map((step, index) => (
                    <React.Fragment key={index}>
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.5 + index * 0.2 }}
                        className="flex flex-col items-center text-center max-w-xs"
                      >
                        <div className={`p-4 rounded-full mb-3 ${
                          index <= currentStep ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-400'
                        }`}>
                          <step.icon className="w-6 h-6" />
                        </div>
                        <h4 className="font-medium text-gray-900 mb-1">{step.title}</h4>
                        <p className="text-sm text-gray-600">{step.description}</p>
                      </motion.div>
                      
                      {index < steps.length - 1 && (
                        <ArrowRight className="w-5 h-5 text-gray-400 mx-4" />
                      )}
                    </React.Fragment>
                  ))}
                </div>
              </div>

              {/* AI增强功能 */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
                <div className="flex items-center space-x-3 mb-4">
                  <Brain className="w-6 h-6 text-purple-500" />
                  <h3 className="text-lg font-semibold text-gray-900">AI 增强功能</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="p-3 bg-white rounded-lg shadow-sm mb-2 inline-block">
                      <Scissors className="w-5 h-5 text-purple-500" />
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">智能分段</h4>
                    <p className="text-sm text-gray-600">AI自动分析文档结构</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="p-3 bg-white rounded-lg shadow-sm mb-2 inline-block">
                      <Sparkles className="w-5 h-5 text-purple-500" />
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">格式优化</h4>
                    <p className="text-sm text-gray-600">智能格式建议和优化</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="p-3 bg-white rounded-lg shadow-sm mb-2 inline-block">
                      <Brain className="w-5 h-5 text-purple-500" />
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">内容分析</h4>
                    <p className="text-sm text-gray-600">深度内容理解和提取</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 底部操作 */}
            <div className="bg-gray-50 px-6 py-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                支持 DOC, DOCX, PPT, PPTX, XLS, XLSX, PDF, TXT, MD 等格式
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setCurrentStep((currentStep + 1) % steps.length)}
                  className="flex items-center space-x-1 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                >
                  <Play className="w-4 h-4" />
                  <span>演示步骤</span>
                </button>
                
                <button
                  onClick={onClose}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  开始使用
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default OfficeIntegrationGuide;
