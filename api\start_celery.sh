#!/bin/bash

# 启动Celery相关服务的脚本

echo "启动AI知识库Celery服务..."

# 检查Redis是否运行
echo "检查Redis连接..."
python -c "
import redis
import os
redis_host = os.getenv('REDIS_HOST', '**************')
redis_port = int(os.getenv('REDIS_PORT', '6379'))
redis_db = int(os.getenv('REDIS_DB', '10'))
try:
    r = redis.Redis(host=redis_host, port=redis_port, db=redis_db)
    r.ping()
    print('Redis连接成功')
except Exception as e:
    print(f'Redis连接失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "Redis连接失败，请检查Redis服务"
    exit 1
fi

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 启动Celery Worker (后台运行)
echo "启动Celery Worker..."
celery -A app.core.celery_config:celery_app worker \
    --loglevel=info \
    --concurrency=4 \
    --queues=default,upload_queue,file_queue \
    --hostname=worker@%h \
    --pidfile=/tmp/celery_worker.pid \
    --logfile=/tmp/celery_worker.log \
    --detach

# 等待Worker启动
sleep 3

# 启动Celery Beat (定时任务调度器)
echo "启动Celery Beat..."
celery -A app.core.celery_config:celery_app beat \
    --loglevel=info \
    --pidfile=/tmp/celery_beat.pid \
    --logfile=/tmp/celery_beat.log \
    --detach

# 启动Flower监控 (可选)
echo "启动Flower监控..."
celery -A app.core.celery_config:celery_app flower \
    --port=5555 \
    --basic_auth=admin:password \
    --pidfile=/tmp/celery_flower.pid \
    --logfile=/tmp/celery_flower.log \
    --detach

echo "Celery服务启动完成!"
echo "- Worker PID文件: /tmp/celery_worker.pid"
echo "- Beat PID文件: /tmp/celery_beat.pid"
echo "- Flower PID文件: /tmp/celery_flower.pid"
echo "- Flower监控地址: http://localhost:5555 (admin/password)"
echo ""
echo "查看日志:"
echo "- Worker日志: tail -f /tmp/celery_worker.log"
echo "- Beat日志: tail -f /tmp/celery_beat.log"
echo "- Flower日志: tail -f /tmp/celery_flower.log"
