@echo off
REM 启动文档分段Celery服务
REM 适用于Windows系统

echo ========================================
echo 启动文档分段Celery服务
echo ========================================

REM 设置环境变量
set PYTHONPATH=%cd%
set CELERY_APP=app.core.celery_config:celery_app

REM 检查Redis连接
echo 检查Redis连接...
python -c "import redis; r = redis.Redis(host='**************', port=6379, db=10); r.ping(); print('Redis连接正常')" 2>nul
if errorlevel 1 (
    echo 错误: Redis连接失败，请检查Redis服务是否启动
    echo Redis配置: **************:6379/10
    pause
    exit /b 1
)

REM 检查数据库连接
echo 检查数据库连接...
python -c "from app.core.database import get_sync_session; next(get_sync_session()); print('数据库连接正常')" 2>nul
if errorlevel 1 (
    echo 错误: 数据库连接失败，请检查数据库配置
    pause
    exit /b 1
)

REM 检查分段表是否存在
echo 检查分段表...
python -c "from app.core.database import get_sync_session; from app.models.document_segment import DocumentSegmentTask; db = next(get_sync_session()); db.query(DocumentSegmentTask).first(); print('分段表检查通过')" 2>nul
if errorlevel 1 (
    echo 警告: 分段表可能不存在，请先运行SQL脚本创建表
    echo 请执行: api\sql_scripts\complete_segment_fix_v2.sql
    pause
)

echo.
echo 启动Celery Worker...
echo 队列: segment_queue
echo 并发: 2
echo.

REM 启动Celery Worker
celery -A %CELERY_APP% worker --loglevel=info --queues=segment_queue --concurrency=2 --pool=solo

REM 如果celery命令失败，尝试使用python -m celery
if errorlevel 1 (
    echo.
    echo 尝试使用python -m celery启动...
    python -m celery -A %CELERY_APP% worker --loglevel=info --queues=segment_queue --concurrency=2 --pool=solo
)

pause
