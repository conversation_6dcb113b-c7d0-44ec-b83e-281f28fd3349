[project]
name = "ai-knowledge-base-api"
version = "0.1.0"
description = "AI知识库 API - 基于人工智能的智能知识管理与检索系统"
authors = [
    {name = "AI Knowledge Base Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic[email]>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "bcrypt>=4.0.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    # 数据库驱动支持
    "asyncpg>=0.29.0",  # PostgreSQL async driver
    "psycopg2-binary>=2.9.0",  # PostgreSQL sync driver
    "aiomysql>=0.2.0",  # MySQL async driver
    "pymysql>=1.1.0",   # MySQL sync driver
    "aiosqlite>=0.19.0", # SQLite async driver

    # 文件管理相关
    "python-multipart>=0.0.6",  # 文件上传支持
    "redis>=5.0.0",
    "aioredis>=2.0.0",  # Redis异步客户端
    "minio>=7.2.0",     # MinIO客户端
    "celery>=5.3.0",
    "flower>=2.0.0",  # Celery监控工具
    "loguru>=0.7.0",
    "httpx>=0.25.0",
    "jinja2>=3.1.0",
    "python-dotenv>=1.0.0",
    "typer>=0.9.0",  # CLI support
    "itsdangerous>=2.1.0",  # Session middleware
    "psutil>=5.9.0",  # System monitoring

    # 文件解析相关
    "python-docx>=1.1.0",  # DOCX文档解析
    "openpyxl>=3.1.0",     # XLSX表格解析
    "xlrd>=2.0.0",         # XLS表格解析
    "python-pptx>=0.6.0",  # PPTX演示文稿解析
    "PyPDF2>=3.0.0",       # PDF文档解析
    "pdfplumber>=0.10.0",  # PDF文档解析（更好的文本提取）
    "markdown>=3.5.0",     # Markdown解析
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
]
oracle = [
    "oracledb>=1.4.0",  # 现代 Oracle 驱动，不需要编译
    "cx-oracle>=8.3.0", # 传统 Oracle 驱动（需要编译）
]
mysql = [
    "aiomysql>=0.2.0",
    "pymysql>=1.1.0",
]
postgresql = [
    "asyncpg>=0.29.0",
]
all-databases = [
    "asyncpg>=0.29.0",
    "aiomysql>=0.2.0",
    "pymysql>=1.1.0",
    "oracledb>=1.4.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    "factory-boy>=3.3.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
]

[project.scripts]
ai-knowledge-api = "app.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.9"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]
