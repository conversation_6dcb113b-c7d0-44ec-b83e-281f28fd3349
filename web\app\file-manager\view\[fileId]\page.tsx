'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import '../../../../styles/file-viewer.css';
import {
  ArrowLeft,
  Download,
  Edit3,
  Share2,
  Eye,
  FileText,
  Image,
  Video,
  Music,
  Archive,
  Code,
  Loader2,
  AlertCircle
} from 'lucide-react';
import apiClient from '@/lib/api';
import DocumentEditorWrapper from '@/components/DocumentEditor';

interface FileInfo {
  file_id: string;
  file_name: string;
  file_path: string;
  file_size_formatted: string;
  file_extension: string;
  mime_type: string;
  created_at: string;
  modified_at: string;
  is_image: boolean;
  is_video: boolean;
  is_document: boolean;
}

interface ParsedContent {
  success: boolean;
  content: string;
  raw_content?: string;
  format: 'html' | 'text' | 'markdown';
  metadata: {
    type: string;
    [key: string]: any;
  };
  error?: string;
}

const FileViewPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const encodedFileId = params.fileId as string;

  // 解码file_id
  const fileId = React.useMemo(() => {
    try {
      // 检查是否是有效的字符串
      if (!encodedFileId || encodedFileId.length === 0) {
        return encodedFileId;
      }

      // 先进行URL解码（处理%3D等字符）
      let decoded = decodeURIComponent(encodedFileId);

      // 然后进行base64解码
      try {
        decoded = atob(decoded);
      } catch (base64Error) {
        // 如果base64解码失败，可能是直接的文件ID
        console.warn('Base64 decode failed, using URL decoded value:', decoded);
      }

      return decoded;
    } catch (error) {
      console.error('Failed to decode file ID:', error);
      return encodedFileId; // 如果解码失败，使用原始值
    }
  }, [encodedFileId]);
  
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [parsedContent, setParsedContent] = useState<ParsedContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (fileId) {
      loadFileInfo();
    }
  }, [fileId]);

  const loadFileInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取文件信息
      const encodedFileId = encodeURIComponent(fileId);
      const response = await apiClient.get(`/api/v1/file-management/files/${encodedFileId}`);
      const file = response.data;
      setFileInfo(file);

      // 获取解析后的文件内容（支持多种格式）
      if (!file.is_directory) {
        try {
          const contentResponse = await apiClient.get(`/api/v1/file-management/files/${encodedFileId}/content`);
          setParsedContent(contentResponse.data);
        } catch (contentError) {
          console.warn('Failed to parse file content:', contentError);
          // 如果解析失败，设置一个默认的内容结构
          setParsedContent({
            success: false,
            content: '',
            format: 'text',
            metadata: { type: 'unknown' },
            error: '文件内容解析失败'
          });
        }
      }
    } catch (err) {
      console.error('Failed to load file:', err);
      setError('加载文件失败');
    } finally {
      setLoading(false);
    }
  };

  const isTextFile = (file: FileInfo) => {
    const textExtensions = ['txt', 'md', 'markdown', 'json', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx'];
    return textExtensions.includes(file.file_extension.toLowerCase());
  };

  const isEditableFile = (file: FileInfo) => {
    const editableExtensions = [
      'txt', 'md', 'markdown', 'json', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx',
      'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf'
    ];
    return editableExtensions.includes(file.file_extension.toLowerCase());
  };

  const isOfficeFile = (file: FileInfo) => {
    const officeExtensions = ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'txt', 'md', 'markdown'];
    return officeExtensions.includes(file.file_extension.toLowerCase());
  };

  const handleEdit = () => {
    // 在新窗口中打开编辑器
    const windowFeatures = [
      'width=1200',
      'height=800',
      'left=' + (window.screen.width / 2 - 600),
      'top=' + (window.screen.height / 2 - 400),
      'resizable=yes',
      'scrollbars=yes',
      'status=yes',
      'menubar=no',
      'toolbar=no',
      'location=no'
    ].join(',');

    window.open(`/file-manager/edit/${fileId}`, `edit_${fileId}`, windowFeatures);
  };

  const handleDownload = async () => {
    try {
      const encodedFileId = encodeURIComponent(fileId);
      const response = await apiClient.get(`/api/v1/file-management/files/${encodedFileId}/download`, {
        responseType: 'blob'
      });

      const blob = response.data;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileInfo?.file_name || 'download';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Download failed:', err);
    }
  };

  const renderFileContent = () => {
    if (!fileInfo) return null;

    // 图片文件 - 弹窗查看
    if (fileInfo.is_image) {
      return (
        <div className="text-center">
          <div className="inline-block relative">
            <img
              src={`/api/v1/file-management/files/${encodeURIComponent(fileId)}/download`}
              alt={fileInfo.file_name}
              className="max-w-full max-h-96 object-contain rounded-lg shadow-lg cursor-pointer"
              onClick={() => {
                // 创建弹窗查看图片
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
                modal.innerHTML = `
                  <div class="relative max-w-screen-lg max-h-screen-lg p-4">
                    <img src="/api/v1/file-management/files/${encodeURIComponent(fileId)}/download"
                         alt="${fileInfo.file_name}"
                         class="max-w-full max-h-full object-contain" />
                    <button class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300">&times;</button>
                  </div>
                `;
                modal.onclick = (e) => {
                  if (e.target === modal || e.target.tagName === 'BUTTON') {
                    document.body.removeChild(modal);
                  }
                };
                document.body.appendChild(modal);
              }}
            />
            <div className="mt-4 text-sm text-gray-600">
              点击图片查看大图
            </div>
          </div>
        </div>
      );
    }

    // 视频文件 - 弹窗播放
    if (fileInfo.is_video) {
      return (
        <div className="text-center">
          <div className="inline-block">
            <video
              controls
              className="max-w-full max-h-96 rounded-lg shadow-lg"
              onClick={() => {
                // 创建弹窗播放视频
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
                modal.innerHTML = `
                  <div class="relative max-w-screen-lg max-h-screen-lg p-4">
                    <video controls autoplay class="max-w-full max-h-full">
                      <source src="/api/v1/file-management/files/${fileId}/download" type="${fileInfo.mime_type}" />
                    </video>
                    <button class="absolute top-4 right-4 text-white text-2xl hover:text-gray-300">&times;</button>
                  </div>
                `;
                modal.onclick = (e) => {
                  if (e.target === modal || e.target.tagName === 'BUTTON') {
                    document.body.removeChild(modal);
                  }
                };
                document.body.appendChild(modal);
              }}
            >
              <source src={`/api/v1/file-management/files/${fileId}/download`} type={fileInfo.mime_type} />
              您的浏览器不支持视频播放。
            </video>
            <div className="mt-4 text-sm text-gray-600">
              点击视频全屏播放
            </div>
          </div>
        </div>
      );
    }

    // 解析后的文档内容
    if (parsedContent) {
      if (parsedContent.success) {
        return (
          <div className="prose prose-lg max-w-none">


            {/* 渲染解析后的内容 */}
            {parsedContent.format === 'html' ? (
              <div
                className="parsed-content"
                dangerouslySetInnerHTML={{ __html: parsedContent.content }}
                style={{
                  lineHeight: '1.6',
                  fontSize: '14px'
                }}
              />
            ) : (
              <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono bg-gray-50 p-6 rounded-lg overflow-auto">
                {parsedContent.content}
              </pre>
            )}

            {/* 编辑按钮 */}
            {isEditableFile(fileInfo) && (
              <div className="mt-6 text-center">
                <button
                  onClick={handleEdit}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Edit3 className="w-5 h-5 mr-2" />
                  编辑此文档
                </button>
              </div>
            )}
          </div>
        );
      } else {
        // 解析失败的情况
        return (
          <div className="bg-yellow-50 rounded-lg p-8 text-center border border-yellow-200">
            <AlertCircle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">解析失败</h3>
            <p className="text-gray-600 mb-4">
              {parsedContent.error || '无法解析此文件内容'}
            </p>
            <div className="space-x-4">
              <button
                onClick={handleDownload}
                className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Download className="w-4 h-4 mr-2" />
                下载文件
              </button>
              {isEditableFile(fileInfo) && (
                <button
                  onClick={handleEdit}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  尝试编辑
                </button>
              )}
            </div>
          </div>
        );
      }
    }

    // 默认情况 - 无法预览
    return (
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <Eye className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">无法预览</h3>
        <p className="text-gray-600 mb-4">
          此文件类型暂不支持在线预览，您可以下载文件查看。
        </p>
        <button
          onClick={handleDownload}
          className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          <Download className="w-4 h-4 mr-2" />
          下载文件
        </button>
      </div>
    );
  };

  // 如果是Office文件，使用新的编辑器组件
  if (fileInfo && isOfficeFile(fileInfo)) {
    return (
      <DocumentEditorWrapper
        fileId={fileId}
        fileName={fileInfo.file_name}
        fileType={fileInfo.file_extension}
        mode="view"
        onClose={() => router.back()}
      />
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">加载文件中...</p>
        </div>
      </div>
    );
  }

  if (error || !fileInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">加载失败</div>
          <div className="text-gray-600 mb-4">{error || '文件不存在'}</div>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* AI现代风格头部导航 - 整合文件类型信息 */}
      <div className="bg-white/80 backdrop-blur-lg shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05, x: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.back()}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-xl transition-all duration-200"
              >
                <ArrowLeft className="w-5 h-5" />
              </motion.button>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    {fileInfo.file_name}
                  </h1>
                  <div className="flex items-center space-x-3 mt-1">
                    <span className="text-sm text-gray-500">{fileInfo.file_size_formatted}</span>
                    <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                    <span className="px-2 py-1 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-lg text-xs font-medium">
                      {fileInfo.file_extension.toUpperCase()}
                    </span>
                    <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                    <span className="text-sm text-gray-500">
                      {new Date(fileInfo.modified_at).toLocaleDateString('zh-CN')}
                    </span>
                    {fileInfo.is_document && (
                      <>
                        <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                        <span className="px-2 py-1 bg-green-100 text-green-700 rounded-lg text-xs font-medium flex items-center">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                          可编辑
                        </span>
                      </>
                    )}
                    {isOfficeFile(fileInfo) && (
                      <>
                        <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                        <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded-lg text-xs font-medium flex items-center">
                          <span className="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>
                          Office在线编辑
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {isEditableFile(fileInfo) && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleEdit}
                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  编辑
                </motion.button>
              )}

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleDownload}
                className="inline-flex items-center px-4 py-2 bg-white/70 backdrop-blur-sm border border-gray-200/50 rounded-xl text-gray-700 hover:bg-white/90 hover:border-gray-300/50 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Download className="w-4 h-4 mr-2" />
                下载
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      {/* AI现代风格文件内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white/70 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 p-8 hover:shadow-2xl transition-all duration-300"
        >
          {renderFileContent()}
        </motion.div>
      </div>
    </div>
  );
};

export default FileViewPage;
