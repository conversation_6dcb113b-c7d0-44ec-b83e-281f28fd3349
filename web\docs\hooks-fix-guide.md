# React Hooks 修复指南

本文档记录了修复 React Hooks 调用顺序问题的过程和最佳实践。

## 🐛 问题描述

在 `LoginForm` 组件中出现了以下错误：

```
React has detected a change in the order of Hooks called by LoginForm. 
This will lead to bugs and errors if not fixed.
```

## 🔍 问题原因

违反了 React Hooks 的基本规则：**Hooks 必须在每次渲染时以相同的顺序调用**。

### 错误的代码模式：

```tsx
const LoginForm: React.FC = () => {
  const { t, isLoading: langLoading } = useLanguage();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // ❌ 错误：在条件语句中提前返回，导致后续 Hooks 不被调用
  if (langLoading || !t) {
    return <div>Loading...</div>;
  }

  // 这些 Hooks 在某些渲染中不会被调用
  const { register, handleSubmit, formState: { errors } } = useForm();
  
  // ... 其他代码
};
```

## ✅ 修复方案

### 1. 将所有 Hooks 调用移到条件语句之前

```tsx
const LoginForm: React.FC = () => {
  const { t, isLoading: langLoading } = useLanguage();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // ✅ 正确：所有 Hooks 都在条件语句之前调用
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>();

  // 条件渲染在所有 Hooks 调用之后
  if (langLoading || !t) {
    return (
      <div className="w-full max-w-md mx-auto">
        <LoadingSpinner size="lg" text="Loading language..." className="h-96" />
      </div>
    );
  }

  // ... 其他代码
};
```

### 2. 创建专用的加载组件

```tsx
// LoadingSpinner.tsx
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className = '',
  text 
}) => {
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        className={`${sizeClasses[size]} border-2 border-white border-t-transparent rounded-full`}
      />
      {text && (
        <motion.p className="mt-2 text-sm text-white/80">
          {text}
        </motion.p>
      )}
    </div>
  );
};
```

### 3. 添加错误边界

```tsx
// ErrorBoundary.tsx
class ErrorBoundary extends Component<Props, State> {
  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} onRetry={this.handleRetry} />;
    }
    return this.props.children;
  }
}
```

## 📋 React Hooks 规则

### 规则 1：只在最顶层调用 Hooks
- ❌ 不要在循环、条件或嵌套函数中调用 Hooks
- ✅ 总是在 React 函数的最顶层调用 Hooks

### 规则 2：只在 React 函数中调用 Hooks
- ❌ 不要在普通的 JavaScript 函数中调用 Hooks
- ✅ 在 React 函数组件中调用 Hooks
- ✅ 在自定义 Hooks 中调用 Hooks

### 常见错误模式：

```tsx
// ❌ 错误：在条件语句中调用 Hooks
if (condition) {
  const [state, setState] = useState(false);
}

// ❌ 错误：在循环中调用 Hooks
for (let i = 0; i < count; i++) {
  const [state, setState] = useState(false);
}

// ❌ 错误：在嵌套函数中调用 Hooks
function handleClick() {
  const [state, setState] = useState(false);
}
```

### 正确模式：

```tsx
// ✅ 正确：在顶层调用所有 Hooks
const MyComponent: React.FC = () => {
  const [state1, setState1] = useState(false);
  const [state2, setState2] = useState('');
  const { data, loading } = useCustomHook();

  // 条件逻辑在 Hooks 调用之后
  if (loading) {
    return <LoadingSpinner />;
  }

  return <div>{/* 组件内容 */}</div>;
};
```

## 🛠️ 调试技巧

### 1. 使用 React DevTools
- 安装 React Developer Tools 浏览器扩展
- 查看组件的 Hooks 调用顺序

### 2. 添加调试日志
```tsx
const MyComponent: React.FC = () => {
  console.log('Hook 1: useState');
  const [state1, setState1] = useState(false);
  
  console.log('Hook 2: useEffect');
  useEffect(() => {
    // ...
  }, []);
  
  console.log('Hook 3: useForm');
  const { register } = useForm();
  
  // ...
};
```

### 3. 使用 ESLint 规则
在 `.eslintrc.js` 中添加：

```javascript
{
  "extends": ["react-hooks/exhaustive-deps"],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

## 🎯 最佳实践

### 1. 组件结构模板
```tsx
const MyComponent: React.FC<Props> = (props) => {
  // 1. 所有 Hooks 调用
  const [state, setState] = useState(initialValue);
  const { data, loading, error } = useCustomHook();
  const { register, handleSubmit } = useForm();

  // 2. 事件处理函数
  const handleClick = useCallback(() => {
    // ...
  }, [dependencies]);

  // 3. 条件渲染和早期返回
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  // 4. 主要渲染逻辑
  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
};
```

### 2. 自定义 Hooks
```tsx
const useLanguageWithFallback = () => {
  const { t, isLoading, language, setLanguage } = useLanguage();
  const [fallbackT, setFallbackT] = useState(null);

  useEffect(() => {
    if (!isLoading && !t) {
      // 加载备用翻译
      loadFallbackTranslations().then(setFallbackT);
    }
  }, [isLoading, t]);

  return {
    t: t || fallbackT,
    isLoading: isLoading || (!t && !fallbackT),
    language,
    setLanguage
  };
};
```

### 3. 错误处理
```tsx
const SafeComponent: React.FC = () => {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (error: Error) => {
      console.error('Component error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <ErrorFallback onRetry={() => setHasError(false)} />;
  }

  return <ActualComponent />;
};
```

## 📚 参考资源

- [React Hooks 规则](https://react.dev/link/rules-of-hooks)
- [React DevTools](https://react.dev/learn/react-developer-tools)
- [ESLint Plugin React Hooks](https://www.npmjs.com/package/eslint-plugin-react-hooks)
- [React Error Boundaries](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary)
