"""
存储管理API路由
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from app.core.database import get_async_session
from app.models.file_management import StorageConfig
from app.models.user import User
from app.core.dependencies import get_current_user
from loguru import logger
import os
import json

router = APIRouter(tags=["存储管理"])


async def calculate_storage_stats(storage_config: StorageConfig) -> tuple[int, int]:
    """计算存储配置的统计信息"""
    try:
        if storage_config.storage_type.value == "LOCAL":
            config_dict = json.loads(storage_config.config) if isinstance(storage_config.config, str) else storage_config.config
            base_path = config_dict.get("base_path", "./storage")

            if not os.path.exists(base_path):
                return 0, 0

            total_files = 0
            total_size = 0

            for root, dirs, files in os.walk(base_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        if os.path.isfile(file_path):
                            total_files += 1
                            total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        # 跳过无法访问的文件
                        continue

            return total_files, total_size
        else:
            # 其他存储类型暂时返回0
            return 0, 0
    except Exception as e:
        logger.warning(f"计算存储统计信息失败: {str(e)}")
        return 0, 0


# Pydantic模型
from pydantic import BaseModel, Field, validator
from datetime import datetime


class StorageConfigCreate(BaseModel):
    """创建存储配置请求"""
    name: str = Field(..., min_length=1, max_length=100, description="存储配置名称")
    storage_type: str = Field(..., description="存储类型")
    config: Dict[str, Any] = Field(..., description="存储配置参数")
    is_default: bool = Field(default=False, description="是否为默认存储")
    is_active: bool = Field(default=True, description="是否启用")
    
    @validator('config', pre=True)
    def validate_config(cls, v, values):
        """验证存储配置参数"""
        # 如果是字符串，尝试解析为JSON
        if isinstance(v, str):
            try:
                import json
                v = json.loads(v)
            except json.JSONDecodeError as e:
                raise ValueError(f"配置参数必须是有效的JSON格式: {str(e)}")

        # 确保是字典类型
        if not isinstance(v, dict):
            raise ValueError("配置参数必须是字典类型")

        storage_type = values.get('storage_type')
        if not storage_type:
            return v

        # 转换为大写进行比较
        storage_type_upper = storage_type.upper()

        required_fields = {
            'LOCAL': ['base_path'],
            'MINIO': ['endpoint', 'access_key', 'secret_key', 'bucket_name'],
            'FTP': ['host', 'username', 'password'],
            'SFTP': ['host', 'username', 'password']
        }

        required = required_fields.get(storage_type_upper, [])
        missing = [field for field in required if field not in v]

        if missing:
            raise ValueError("Missing required fields for {}: {}".format(storage_type, missing))

        return v


class StorageConfigUpdate(BaseModel):
    """更新存储配置请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="存储配置名称")
    config: Optional[Dict[str, Any]] = Field(None, description="存储配置参数")
    is_default: Optional[bool] = Field(None, description="是否为默认存储")
    is_active: Optional[bool] = Field(None, description="是否启用")

    @validator('config', pre=True)
    def validate_config(cls, v):
        """验证存储配置参数"""
        if v is None:
            return v

        # 如果是字符串，尝试解析为JSON
        if isinstance(v, str):
            try:
                import json
                v = json.loads(v)
            except json.JSONDecodeError as e:
                raise ValueError(f"配置参数必须是有效的JSON格式: {str(e)}")

        # 确保是字典类型
        if not isinstance(v, dict):
            raise ValueError("配置参数必须是字典类型")

        return v


class StorageConfigResponse(BaseModel):
    """存储配置响应"""
    id: int
    name: str
    storage_type: str
    is_default: bool
    is_active: bool
    config: Dict[str, Any]
    total_files: Optional[int] = 0
    total_size: Optional[int] = 0
    last_sync_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class StorageTestResponse(BaseModel):
    """存储测试响应"""
    success: bool
    message: str
    details: Optional[Dict[str, Any]] = None


@router.get("/", response_model=List[StorageConfigResponse])
async def list_storages(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """获取存储配置列表"""
    try:
        # 构建查询，只选择存在的列
        query = select(
            StorageConfig.id,
            StorageConfig.name,
            StorageConfig.storage_type,
            StorageConfig.is_default,
            StorageConfig.is_active,
            StorageConfig.config,
            StorageConfig.created_at,
            StorageConfig.updated_at
        )

        if active_only:
            query = query.where(StorageConfig.is_active == True)

        query = query.offset(skip).limit(limit).order_by(StorageConfig.created_at.desc())

        result = await db.execute(query)
        rows = result.fetchall()

        # 手动构建响应对象
        storages = []
        for row in rows:
            # 安全地转换存储类型
            storage_type_str = str(row.storage_type)
            if hasattr(row.storage_type, 'value'):
                storage_type_str = row.storage_type.value

            # 创建临时存储配置对象来计算统计信息
            temp_storage = StorageConfig(
                id=row.id,
                name=row.name,
                storage_type=row.storage_type,
                config=row.config,
                is_default=row.is_default,
                is_active=row.is_active
            )

            # 计算实际统计信息
            total_files, total_size = await calculate_storage_stats(temp_storage)

            storage_data = {
                "id": row.id,
                "name": row.name,
                "storage_type": storage_type_str,
                "is_default": row.is_default,
                "is_active": row.is_active,
                "config": row.config,
                "total_files": total_files,
                "total_size": total_size,
                "last_sync_at": None,  # 默认值
                "created_at": row.created_at,
                "updated_at": row.updated_at
            }
            storages.append(StorageConfigResponse(**storage_data))

        return storages
        
    except Exception as e:
        logger.error("获取存储列表失败: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取存储列表失败"
        )


@router.post("/", response_model=StorageConfigResponse)
async def create_storage(
    storage_data: StorageConfigCreate,
    db: AsyncSession = Depends(get_async_session)
):
    """创建存储配置"""
    try:
        # 检查名称是否已存在
        existing = await db.execute(
            select(
                StorageConfig.id,
                StorageConfig.name,
                StorageConfig.storage_type,
                StorageConfig.is_default,
                StorageConfig.is_active,
                StorageConfig.config,
                StorageConfig.created_at,
                StorageConfig.updated_at
            ).where(StorageConfig.name == storage_data.name)
        )
        if existing.fetchone():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="存储配置名称已存在"
            )
        
        # 如果设置为默认存储，取消其他默认存储
        if storage_data.is_default:
            await db.execute(
                update(StorageConfig).where(StorageConfig.is_default == True)
                .values(is_default=False)
            )
        
        # 创建存储配置
        from app.models.file_management import StorageType

        # 安全地转换存储类型
        try:
            storage_type_enum = StorageType(storage_data.storage_type.upper())
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的存储类型: {}".format(storage_data.storage_type)
            )

        storage = StorageConfig(
            name=storage_data.name,
            storage_type=storage_type_enum,
            config=storage_data.config,
            is_default=storage_data.is_default,
            is_active=storage_data.is_active
        )

        db.add(storage)
        await db.commit()
        await db.refresh(storage)

        logger.info("创建存储配置成功: {}".format(storage.name))

        # 手动构建响应对象
        storage_type_str = str(storage.storage_type)
        if hasattr(storage.storage_type, 'value'):
            storage_type_str = storage.storage_type.value

        storage_data = {
            "id": storage.id,
            "name": storage.name,
            "storage_type": storage_type_str,
            "is_default": storage.is_default,
            "is_active": storage.is_active,
            "config": storage.config,
            "total_files": 0,
            "total_size": 0,
            "last_sync_at": None,
            "created_at": storage.created_at,
            "updated_at": storage.updated_at
        }
        return StorageConfigResponse(**storage_data)

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("创建存储配置失败: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建存储配置失败"
        )


@router.get("/{storage_id}", response_model=StorageConfigResponse)
async def get_storage(
    storage_id: int,
    db: AsyncSession = Depends(get_async_session)
):
    """获取存储配置详情"""
    try:
        # 从数据库获取
        result = await db.execute(
            select(
                StorageConfig.id,
                StorageConfig.name,
                StorageConfig.storage_type,
                StorageConfig.is_default,
                StorageConfig.is_active,
                StorageConfig.config,
                StorageConfig.created_at,
                StorageConfig.updated_at
            ).where(StorageConfig.id == storage_id)
        )
        row = result.fetchone()

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="存储配置不存在"
            )

        # 手动构建响应对象
        storage_type_str = str(row.storage_type)
        if hasattr(row.storage_type, 'value'):
            storage_type_str = row.storage_type.value

        storage_data = {
            "id": row.id,
            "name": row.name,
            "storage_type": storage_type_str,
            "is_default": row.is_default,
            "is_active": row.is_active,
            "config": row.config,
            "total_files": 0,
            "total_size": 0,
            "last_sync_at": None,
            "created_at": row.created_at,
            "updated_at": row.updated_at
        }
        return StorageConfigResponse(**storage_data)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取存储配置失败: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取存储配置失败"
        )


@router.put("/{storage_id}", response_model=StorageConfigResponse)
async def update_storage(
    storage_id: int,
    storage_data: StorageConfigUpdate,
    db: AsyncSession = Depends(get_async_session)
):
    """更新存储配置"""
    try:
        # 获取存储配置
        result = await db.execute(
            select(StorageConfig).where(StorageConfig.id == storage_id)
        )
        storage = result.scalar_one_or_none()
        
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="存储配置不存在"
            )
        
        # 检查名称冲突
        if storage_data.name and storage_data.name != storage.name:
            existing = await db.execute(
                select(StorageConfig).where(
                    StorageConfig.name == storage_data.name,
                    StorageConfig.id != storage_id
                )
            )
            if existing.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="存储配置名称已存在"
                )
        
        # 如果设置为默认存储，取消其他默认存储
        if storage_data.is_default:
            await db.execute(
                update(StorageConfig).where(
                    StorageConfig.is_default == True,
                    StorageConfig.id != storage_id
                ).values(is_default=False)
            )
        
        # 更新字段
        update_data = storage_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(storage, field, value)
        
        await db.commit()
        await db.refresh(storage)
        
        logger.info("更新存储配置成功: {}".format(storage.name))

        # 手动构建响应对象
        storage_type_str = str(storage.storage_type)
        if hasattr(storage.storage_type, 'value'):
            storage_type_str = storage.storage_type.value

        storage_data = {
            "id": storage.id,
            "name": storage.name,
            "storage_type": storage_type_str,
            "is_default": storage.is_default,
            "is_active": storage.is_active,
            "config": storage.config,
            "total_files": 0,
            "total_size": 0,
            "last_sync_at": None,
            "created_at": storage.created_at,
            "updated_at": storage.updated_at
        }
        return StorageConfigResponse(**storage_data)

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("更新存储配置失败: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新存储配置失败"
        )


@router.delete("/{storage_id}")
async def delete_storage(
    storage_id: int,
    force: bool = False,
    db: AsyncSession = Depends(get_async_session)
):
    """删除存储配置"""
    try:
        # 获取存储配置
        result = await db.execute(
            select(StorageConfig).where(StorageConfig.id == storage_id)
        )
        storage = result.scalar_one_or_none()
        
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="存储配置不存在"
            )
        
        # 检查是否有关联的文件记录
        if not force:
            from app.models.file_management import FileRecord
            file_count_result = await db.execute(
                select(FileRecord).where(FileRecord.storage_id == storage_id).limit(1)
            )
            if file_count_result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="存储配置下还有文件记录，请先清理文件或使用force=true强制删除"
                )
        
        # 删除存储配置
        await db.execute(delete(StorageConfig).where(StorageConfig.id == storage_id))
        await db.commit()
        
        logger.info("删除存储配置成功: {}".format(storage.name))
        return {"message": "存储配置删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error("删除存储配置失败: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除存储配置失败"
        )


@router.post("/{storage_id}/test", response_model=StorageTestResponse)
async def test_storage_connection(
    storage_id: int,
    db: AsyncSession = Depends(get_async_session)
):
    """测试存储连接"""
    try:
        # 获取存储配置
        result = await db.execute(
            select(StorageConfig).where(StorageConfig.id == storage_id)
        )
        storage = result.scalar_one_or_none()
        
        if not storage:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="存储配置不存在"
            )
        
        # 简单的连接测试
        import json
        config_dict = json.loads(storage.config) if isinstance(storage.config, str) else storage.config

        if storage.storage_type.value == "LOCAL":
            # 测试本地存储
            import os
            base_path = config_dict.get("base_path", "./storage")

            try:
                # 检查目录是否存在，不存在则创建
                if not os.path.exists(base_path):
                    os.makedirs(base_path, exist_ok=True)

                # 测试写入权限
                test_file = os.path.join(base_path, ".test_write")
                with open(test_file, "w") as f:
                    f.write("test")
                os.remove(test_file)

                return StorageTestResponse(
                    success=True,
                    message="本地存储连接正常",
                    details={"path": base_path, "writable": True}
                )
            except Exception as e:
                return StorageTestResponse(
                    success=False,
                    message="本地存储测试失败: {}".format(str(e))
                )
        else:
            return StorageTestResponse(
                success=False,
                message="暂不支持 {} 类型的连接测试".format(storage.storage_type)
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("测试存储连接失败: {}".format(str(e)))
        return StorageTestResponse(
            success=False,
            message="连接测试失败: {}".format(str(e))
        )


@router.post("/{storage_id}/sync")
async def sync_storage(
    storage_id: int,
    full_sync: bool = False,
    db: AsyncSession = Depends(get_async_session)
):
    """同步存储"""
    try:
        # 获取存储配置
        result = await db.execute(
            select(StorageConfig).where(StorageConfig.id == storage_id)
        )
        storage = result.scalar_one_or_none()

        if not storage:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="存储配置不存在"
            )

        # 简单的同步逻辑（暂时返回成功）
        return {
            "message": "同步功能暂未实现",
            "storage_id": storage_id,
            "full_sync": full_sync,
            "status": "pending"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("同步存储失败: {}".format(str(e)))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="同步存储失败: {}".format(str(e))
        )
