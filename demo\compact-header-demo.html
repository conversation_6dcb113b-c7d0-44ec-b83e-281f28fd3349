<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧凑头部演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 紧凑头部样式 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 16px 20px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .header-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #6366f1;
            box-shadow: 0 1px 4px rgba(99, 102, 241, 0.1);
        }

        .header-text {
            text-align: left;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 2px;
            line-height: 1.2;
        }

        .header p {
            font-size: 0.85rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.3;
        }

        /* 卡片网格 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 16px;
            padding: 20px 0;
        }

        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: flex-start;
            gap: 16px;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        .card-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: #6366f1;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
        }

        .card-content {
            flex: 1;
            min-width: 0;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .card-meta {
            font-size: 0.8rem;
            color: #9ca3af;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .card-company {
            font-weight: 500;
            color: #6366f1;
        }

        .card-creator {
            color: #8b5cf6;
        }

        .card-description {
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                margin-bottom: 15px;
                padding: 12px 15px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 8px;
            }
            
            .header-icon {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }
            
            .header-text {
                text-align: center;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .header p {
                font-size: 0.8rem;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
                gap: 12px;
                padding: 10px 0;
            }
            
            .card {
                padding: 16px;
                gap: 12px;
                border-radius: 12px;
            }
            
            .card-icon {
                width: 48px;
                height: 48px;
                font-size: 24px;
                border-radius: 12px;
            }
            
            .card-title {
                font-size: 1.1rem;
            }
            
            .card-description {
                font-size: 0.85rem;
                -webkit-line-clamp: 2;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 紧凑头部 -->
        <div class="header">
            <div class="header-content">
                <div class="header-icon">🤖</div>
                <div class="header-text">
                    <h1>AI智能工具集</h1>
                    <p>探索最前沿的人工智能工具和服务，提升您的工作效率和创造力</p>
                </div>
            </div>
        </div>

        <!-- 卡片网格 -->
        <div class="cards-grid">
            <div class="card">
                <div class="card-icon">🤖</div>
                <div class="card-content">
                    <div class="card-title">ChatGPT</div>
                    <div class="card-meta">
                        <span class="card-company">OpenAI</span>
                        <span> • </span>
                        <span class="card-creator">Sam Altman</span>
                    </div>
                    <div class="card-description">OpenAI开发的强大对话AI，支持文本生成、问答、编程等多种任务</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">🎨</div>
                <div class="card-content">
                    <div class="card-title">Midjourney</div>
                    <div class="card-meta">
                        <span class="card-company">Midjourney Inc</span>
                        <span> • </span>
                        <span class="card-creator">David Holz</span>
                    </div>
                    <div class="card-description">AI图像生成工具，通过文本描述创造惊艳的艺术作品和插图</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">📝</div>
                <div class="card-content">
                    <div class="card-title">Notion AI</div>
                    <div class="card-meta">
                        <span class="card-company">Notion Labs</span>
                        <span> • </span>
                        <span class="card-creator">Ivan Zhao</span>
                    </div>
                    <div class="card-description">集成在Notion中的AI写作助手，帮助提升文档编写效率</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">🎵</div>
                <div class="card-content">
                    <div class="card-title">AIVA</div>
                    <div class="card-meta">
                        <span class="card-company">AIVA Technologies</span>
                        <span> • </span>
                        <span class="card-creator">Pierre Barreau</span>
                    </div>
                    <div class="card-description">AI音乐创作平台，自动生成各种风格的原创音乐作品</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">🎬</div>
                <div class="card-content">
                    <div class="card-title">Runway ML</div>
                    <div class="card-meta">
                        <span class="card-company">Runway AI</span>
                        <span> • </span>
                        <span class="card-creator">Cristóbal Valenzuela</span>
                    </div>
                    <div class="card-description">AI视频编辑工具，提供视频生成、编辑和特效制作功能</div>
                </div>
            </div>

            <div class="card">
                <div class="card-icon">💻</div>
                <div class="card-content">
                    <div class="card-title">GitHub Copilot</div>
                    <div class="card-meta">
                        <span class="card-company">GitHub/Microsoft</span>
                        <span> • </span>
                        <span class="card-creator">Nat Friedman</span>
                    </div>
                    <div class="card-description">AI编程助手，实时提供代码建议和自动补全功能</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
