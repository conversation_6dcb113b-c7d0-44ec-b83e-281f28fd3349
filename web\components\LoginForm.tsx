'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, Lock, User, Loader2, Brain, Shield, Zap } from 'lucide-react';
import toast from 'react-hot-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { login, LoginRequest } from '@/lib/api';
import LoadingSpinner from './LoadingSpinner';

interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
}

const LoginForm: React.FC = () => {
  const { t, isLoading: langLoading } = useLanguage();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 始终调用所有 Hooks，避免条件调用
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginFormData>();

  // 如果语言还在加载中，显示加载状态
  if (langLoading || !t) {
    return (
      <div className="w-full max-w-md mx-auto">
        <LoadingSpinner
          size="lg"
          text="Loading language..."
          className="h-96"
        />
      </div>
    );
  }

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    
    try {
      const loginData: LoginRequest = {
        username: data.username,
        password: data.password,
      };

      await login(loginData);
      
      toast.success(t.login.loginSuccess, {
        icon: '🎉',
        style: {
          background: '#10B981',
          color: 'white',
        },
      });

      // 登录成功后跳转到仪表板
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 1000);

    } catch (error: any) {
      console.error('Login error:', error);
      
      let errorMessage = t.login.loginError;
      
      if (error.message.includes('Invalid credentials') || error.message.includes('401')) {
        errorMessage = t.login.invalidCredentials;
      } else if (error.message.includes('Network Error') || error.message.includes('timeout')) {
        errorMessage = t.login.networkError;
      }

      toast.error(errorMessage, {
        icon: '❌',
        style: {
          background: '#EF4444',
          color: 'white',
        },
      });

      setError('root', { message: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const aiFeatures = [
    {
      icon: Brain,
      title: t.ai.intelligentSystem,
      description: t.ai.smartAnalysis,
    },
    {
      icon: Shield,
      title: t.ai.secureLogin,
      description: t.ai.dataProtection,
    },
    {
      icon: Zap,
      title: t.ai.aiAssistant,
      description: t.ai.poweredBy,
    },
  ];

  return (
    <div className="w-full max-w-md mx-auto">
      {/* 主标题 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-8"
      >
        <motion.div
          className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-4"
          whileHover={{ scale: 1.05, rotate: 5 }}
          transition={{ duration: 0.2 }}
        >
          <Brain className="w-8 h-8 text-white" />
        </motion.div>
        
        <h1 className="text-3xl font-bold text-white mb-2">
          {t.login.title}
        </h1>
        <p className="text-blue-200 text-sm">
          {t.login.subtitle}
        </p>
      </motion.div>

      {/* 登录表单 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl"
      >
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-white mb-2">
            {t.login.welcomeBack}
          </h2>
          <p className="text-blue-200 text-sm">
            {t.ai.poweredBy}
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 用户名输入 */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              {t.login.username}
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-blue-300" />
              </div>
              <input
                {...register('username', {
                  required: t.login.usernameRequired,
                  minLength: {
                    value: 3,
                    message: t.login.usernameMinLength,
                  },
                })}
                type="text"
                className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder={t.login.username}
                disabled={isLoading}
              />
            </div>
            {errors.username && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-1 text-sm text-red-300"
              >
                {errors.username.message}
              </motion.p>
            )}
          </div>

          {/* 密码输入 */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              {t.login.password}
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-blue-300" />
              </div>
              <input
                {...register('password', {
                  required: t.login.passwordRequired,
                  minLength: {
                    value: 6,
                    message: t.login.passwordMinLength,
                  },
                })}
                type={showPassword ? 'text' : 'password'}
                className="w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                placeholder={t.login.password}
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-blue-300 hover:text-white transition-colors duration-200"
                disabled={isLoading}
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
            {errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-1 text-sm text-red-300"
              >
                {errors.password.message}
              </motion.p>
            )}
          </div>

          {/* 记住我和忘记密码 */}
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                {...register('rememberMe')}
                type="checkbox"
                className="w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2"
                disabled={isLoading}
              />
              <span className="ml-2 text-sm text-blue-200">
                {t.login.rememberMe}
              </span>
            </label>
            <button
              type="button"
              className="text-sm text-blue-300 hover:text-white transition-colors duration-200"
              disabled={isLoading}
            >
              {t.login.forgotPassword}
            </button>
          </div>

          {/* 登录按钮 */}
          <motion.button
            type="submit"
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: isLoading ? 1 : 1.02 }}
            whileTap={{ scale: isLoading ? 1 : 0.98 }}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                {t.common.loading}
              </div>
            ) : (
              t.login.loginButton
            )}
          </motion.button>

          {/* 注册链接 */}
          <div className="text-center">
            <span className="text-blue-200 text-sm">
              {t.login.noAccount}{' '}
            </span>
            <button
              type="button"
              className="text-sm text-blue-300 hover:text-white font-medium transition-colors duration-200"
              disabled={isLoading}
            >
              {t.login.signUp}
            </button>
          </div>
        </form>
      </motion.div>

      {/* AI特性展示 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="mt-8 grid grid-cols-3 gap-4"
      >
        {aiFeatures.map((feature, index) => (
          <motion.div
            key={index}
            className="text-center"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <div className="inline-flex items-center justify-center w-12 h-12 bg-white/10 rounded-lg mb-2">
              <feature.icon className="w-6 h-6 text-blue-300" />
            </div>
            <h3 className="text-xs font-medium text-white mb-1">
              {feature.title}
            </h3>
            <p className="text-xs text-blue-200">
              {feature.description}
            </p>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default LoginForm;
