'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Trash2,
  Copy,
  ArrowUp,
  ArrowDown,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Presentation,
  Layout,
  Palette,
  Type,
  Image,
  Shapes,
  BarChart3,
  Table,
  Video,
  Mic,
  Settings,
  Save,
  Download,
  Share2
} from 'lucide-react';

interface Slide {
  id: string;
  title: string;
  content: string;
  layout: 'title' | 'content' | 'two-column' | 'image' | 'blank';
  background: string;
  animations: any[];
}

interface PowerPointEditorProps {
  fileId: string;
  fileName: string;
  slides: Slide[];
  isReadOnly?: boolean;
  onSave?: (slides: Slide[]) => Promise<void>;
  onClose?: () => void;
}

const PowerPointEditor: React.FC<PowerPointEditorProps> = ({
  fileId,
  fileName,
  slides: initialSlides,
  isReadOnly = false,
  onSave,
  onClose
}) => {
  const [slides, setSlides] = useState<Slide[]>(initialSlides);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isPresenting, setIsPresenting] = useState(false);
  const [selectedLayout, setSelectedLayout] = useState<Slide['layout']>('content');
  const [zoom, setZoom] = useState(100);
  const [showSlidePanel, setShowSlidePanel] = useState(true);

  const currentSlide = slides[currentSlideIndex];

  // 添加新幻灯片
  const addSlide = (layout: Slide['layout'] = 'content') => {
    const newSlide: Slide = {
      id: `slide-${Date.now()}`,
      title: '新幻灯片',
      content: '',
      layout,
      background: '#ffffff',
      animations: []
    };
    
    const newSlides = [...slides];
    newSlides.splice(currentSlideIndex + 1, 0, newSlide);
    setSlides(newSlides);
    setCurrentSlideIndex(currentSlideIndex + 1);
  };

  // 删除幻灯片
  const deleteSlide = (index: number) => {
    if (slides.length <= 1) return;
    
    const newSlides = slides.filter((_, i) => i !== index);
    setSlides(newSlides);
    
    if (currentSlideIndex >= newSlides.length) {
      setCurrentSlideIndex(newSlides.length - 1);
    } else if (currentSlideIndex > index) {
      setCurrentSlideIndex(currentSlideIndex - 1);
    }
  };

  // 复制幻灯片
  const duplicateSlide = (index: number) => {
    const slideToClone = slides[index];
    const newSlide: Slide = {
      ...slideToClone,
      id: `slide-${Date.now()}`,
      title: `${slideToClone.title} (副本)`
    };
    
    const newSlides = [...slides];
    newSlides.splice(index + 1, 0, newSlide);
    setSlides(newSlides);
  };

  // 移动幻灯片
  const moveSlide = (fromIndex: number, toIndex: number) => {
    const newSlides = [...slides];
    const [movedSlide] = newSlides.splice(fromIndex, 1);
    newSlides.splice(toIndex, 0, movedSlide);
    setSlides(newSlides);
    setCurrentSlideIndex(toIndex);
  };

  // 更新当前幻灯片
  const updateCurrentSlide = (updates: Partial<Slide>) => {
    const newSlides = [...slides];
    newSlides[currentSlideIndex] = { ...currentSlide, ...updates };
    setSlides(newSlides);
  };

  // 幻灯片布局模板
  const slideLayouts = [
    { type: 'title' as const, name: '标题幻灯片', icon: <Type className="w-4 h-4" /> },
    { type: 'content' as const, name: '内容幻灯片', icon: <Layout className="w-4 h-4" /> },
    { type: 'two-column' as const, name: '两栏布局', icon: <Layout className="w-4 h-4" /> },
    { type: 'image' as const, name: '图片布局', icon: <Image className="w-4 h-4" /> },
    { type: 'blank' as const, name: '空白幻灯片', icon: <Layout className="w-4 h-4" /> }
  ];

  // 渲染幻灯片内容
  const renderSlideContent = (slide: Slide, isEditing: boolean = true) => {
    const baseClasses = "w-full h-full p-6 focus:outline-none";
    
    switch (slide.layout) {
      case 'title':
        return (
          <div className={baseClasses} style={{ backgroundColor: slide.background }}>
            <div className="text-center h-full flex flex-col justify-center">
              <h1 
                contentEditable={isEditing && !isReadOnly}
                suppressContentEditableWarning={true}
                className="text-4xl font-bold mb-4 focus:outline-none"
                onBlur={(e) => updateCurrentSlide({ title: e.currentTarget.textContent || '' })}
              >
                {slide.title}
              </h1>
              <div 
                contentEditable={isEditing && !isReadOnly}
                suppressContentEditableWarning={true}
                className="text-xl text-gray-600 focus:outline-none"
                dangerouslySetInnerHTML={{ __html: slide.content }}
                onBlur={(e) => updateCurrentSlide({ content: e.currentTarget.innerHTML })}
              />
            </div>
          </div>
        );
      
      case 'two-column':
        return (
          <div className={baseClasses} style={{ backgroundColor: slide.background }}>
            <h2 
              contentEditable={isEditing && !isReadOnly}
              suppressContentEditableWarning={true}
              className="text-2xl font-bold mb-6 focus:outline-none"
              onBlur={(e) => updateCurrentSlide({ title: e.currentTarget.textContent || '' })}
            >
              {slide.title}
            </h2>
            <div className="grid grid-cols-2 gap-6 h-4/5">
              <div 
                contentEditable={isEditing && !isReadOnly}
                suppressContentEditableWarning={true}
                className="border-2 border-dashed border-gray-300 p-4 focus:outline-none"
                dangerouslySetInnerHTML={{ __html: slide.content }}
                onBlur={(e) => updateCurrentSlide({ content: e.currentTarget.innerHTML })}
              />
              <div className="border-2 border-dashed border-gray-300 p-4 flex items-center justify-center text-gray-500">
                点击添加内容或图片
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className={baseClasses} style={{ backgroundColor: slide.background }}>
            <h2 
              contentEditable={isEditing && !isReadOnly}
              suppressContentEditableWarning={true}
              className="text-2xl font-bold mb-4 focus:outline-none"
              onBlur={(e) => updateCurrentSlide({ title: e.currentTarget.textContent || '' })}
            >
              {slide.title}
            </h2>
            <div 
              contentEditable={isEditing && !isReadOnly}
              suppressContentEditableWarning={true}
              className="prose max-w-none focus:outline-none"
              dangerouslySetInnerHTML={{ __html: slide.content }}
              onBlur={(e) => updateCurrentSlide({ content: e.currentTarget.innerHTML })}
            />
          </div>
        );
    }
  };

  if (isPresenting) {
    return (
      <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
        <div className="w-full h-full max-w-6xl max-h-4xl bg-white">
          {renderSlideContent(currentSlide, false)}
        </div>
        
        {/* 演示控制栏 */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 rounded-lg px-4 py-2 flex items-center space-x-4">
          <button
            onClick={() => setCurrentSlideIndex(Math.max(0, currentSlideIndex - 1))}
            disabled={currentSlideIndex === 0}
            className="text-white hover:text-blue-400 disabled:text-gray-500"
          >
            <SkipBack className="w-5 h-5" />
          </button>
          
          <span className="text-white text-sm">
            {currentSlideIndex + 1} / {slides.length}
          </span>
          
          <button
            onClick={() => setCurrentSlideIndex(Math.min(slides.length - 1, currentSlideIndex + 1))}
            disabled={currentSlideIndex === slides.length - 1}
            className="text-white hover:text-blue-400 disabled:text-gray-500"
          >
            <SkipForward className="w-5 h-5" />
          </button>
          
          <button
            onClick={() => setIsPresenting(false)}
            className="text-white hover:text-red-400 ml-4"
          >
            退出演示
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* 幻灯片缩略图面板 */}
      {showSlidePanel && (
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-medium text-gray-900">幻灯片</h3>
          </div>
          
          <div className="flex-1 overflow-y-auto p-2">
            {slides.map((slide, index) => (
              <motion.div
                key={slide.id}
                whileHover={{ scale: 1.02 }}
                className={`mb-2 p-2 border-2 rounded-lg cursor-pointer transition-all ${
                  index === currentSlideIndex 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setCurrentSlideIndex(index)}
              >
                <div className="aspect-video bg-white border rounded mb-2 overflow-hidden">
                  <div className="transform scale-25 origin-top-left w-400 h-300">
                    {renderSlideContent(slide, false)}
                  </div>
                </div>
                <div className="text-xs text-gray-600 truncate">{slide.title}</div>
                <div className="text-xs text-gray-400">幻灯片 {index + 1}</div>
                
                {!isReadOnly && (
                  <div className="flex justify-between mt-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        duplicateSlide(index);
                      }}
                      className="text-gray-400 hover:text-blue-500"
                      title="复制"
                    >
                      <Copy className="w-3 h-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteSlide(index);
                      }}
                      className="text-gray-400 hover:text-red-500"
                      title="删除"
                      disabled={slides.length <= 1}
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
          
          {!isReadOnly && (
            <div className="p-4 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-2">
                {slideLayouts.slice(0, 4).map((layout) => (
                  <button
                    key={layout.type}
                    onClick={() => addSlide(layout.type)}
                    className="p-2 border border-gray-300 rounded hover:bg-gray-50 flex flex-col items-center text-xs"
                    title={layout.name}
                  >
                    {layout.icon}
                    <span className="mt-1">{layout.name}</span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 主编辑区域 */}
      <div className="flex-1 flex flex-col">
        {/* 工具栏 */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-lg font-medium">{fileName}</h1>
              
              {!isReadOnly && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => addSlide()}
                    className="flex items-center space-x-1 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    <Plus className="w-4 h-4" />
                    <span>新建幻灯片</span>
                  </button>
                  
                  <select
                    value={selectedLayout}
                    onChange={(e) => {
                      setSelectedLayout(e.target.value as Slide['layout']);
                      updateCurrentSlide({ layout: e.target.value as Slide['layout'] });
                    }}
                    className="px-2 py-1 border border-gray-300 rounded text-sm"
                  >
                    {slideLayouts.map((layout) => (
                      <option key={layout.type} value={layout.type}>
                        {layout.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsPresenting(true)}
                className="flex items-center space-x-1 px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
              >
                <Play className="w-4 h-4" />
                <span>开始演示</span>
              </button>
              
              {!isReadOnly && onSave && (
                <button
                  onClick={() => onSave(slides)}
                  className="flex items-center space-x-1 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  <Save className="w-4 h-4" />
                  <span>保存</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* 幻灯片编辑区域 */}
        <div className="flex-1 p-8 overflow-auto">
          <div 
            className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden"
            style={{ 
              transform: `scale(${zoom / 100})`, 
              transformOrigin: 'top center',
              aspectRatio: '16/9'
            }}
          >
            {currentSlide && renderSlideContent(currentSlide)}
          </div>
        </div>

        {/* 底部状态栏 */}
        <div className="bg-white border-t border-gray-200 px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              幻灯片 {currentSlideIndex + 1} / {slides.length}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">缩放:</span>
            <button
              onClick={() => setZoom(Math.max(25, zoom - 25))}
              className="text-gray-600 hover:text-gray-900"
            >
              -
            </button>
            <span className="text-sm font-medium min-w-12 text-center">{zoom}%</span>
            <button
              onClick={() => setZoom(Math.min(200, zoom + 25))}
              className="text-gray-600 hover:text-gray-900"
            >
              +
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PowerPointEditor;
