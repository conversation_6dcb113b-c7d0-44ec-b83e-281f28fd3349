/**
 * 系统初始化API
 */

import request from '@/utils/request'

export const systemInitApi = {
  /**
   * 获取系统初始化状态
   */
  getStatus() {
    return request({
      url: '/api/v1/system-init/status',
      method: 'get'
    })
  },

  /**
   * 检查系统是否需要初始化
   */
  checkNeedInit() {
    return request({
      url: '/api/v1/system-init/check',
      method: 'get'
    })
  },

  /**
   * 初始化管理员账户
   * @param {Object} data - 管理员信息
   * @param {string} data.username - 用户名
   * @param {string} data.email - 邮箱
   * @param {string} data.password - 密码
   * @param {string} data.full_name - 姓名
   */
  initializeAdmin(data) {
    return request({
      url: '/api/v1/system-init/admin',
      method: 'post',
      data
    })
  },

  /**
   * 初始化字典数据
   */
  initializeDictionaries() {
    return request({
      url: '/api/v1/system-init/dictionaries',
      method: 'post'
    })
  },

  /**
   * 初始化存储配置
   */
  initializeStorage() {
    return request({
      url: '/api/v1/system-init/storage',
      method: 'post'
    })
  },

  /**
   * 初始化系统配置
   */
  initializeSystemConfig() {
    return request({
      url: '/api/v1/system-init/system-config',
      method: 'post'
    })
  },

  /**
   * 自动完成所有初始化步骤
   */
  autoInitialize() {
    return request({
      url: '/api/v1/system-init/auto-init',
      method: 'post'
    })
  }
}
