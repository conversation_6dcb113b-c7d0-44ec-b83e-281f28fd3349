"""
知识库相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, DECIMAL, JSON
from sqlalchemy.sql import func
from app.models.base import BaseModel


class KnowledgeBase(BaseModel):
    """知识库主表"""
    __tablename__ = "knowledge_bases"

    id = Column(Integer, primary_key=True, index=True)
    kb_id = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    avatar_url = Column(String(500))
    status = Column(String(50), default='active', index=True)
    visibility = Column(String(50), default='private')
    owner_id = Column(String(255), index=True)
    
    # 统计信息
    document_count = Column(Integer, default=0)
    segment_count = Column(Integer, default=0)
    vector_count = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    
    # 配置信息
    embedding_model = Column(String(255))
    vector_dimension = Column(Integer)
    chunk_size = Column(Integer, default=1000)
    chunk_overlap = Column(Integer, default=200)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_indexed_at = Column(DateTime(timezone=True))


class KnowledgeBaseDocument(BaseModel):
    """知识库文档关联表"""
    __tablename__ = "knowledge_base_documents"

    id = Column(Integer, primary_key=True, index=True)
    kb_id = Column(String(255), nullable=False, index=True)
    file_id = Column(String(255), nullable=False, index=True)
    document_name = Column(String(255))
    document_path = Column(String(500))
    file_size = Column(Integer)
    file_type = Column(String(100))
    
    # 处理状态
    status = Column(String(50), default='pending', index=True)
    segment_status = Column(String(50), default='pending')
    vector_status = Column(String(50), default='pending')
    
    # 统计信息
    segment_count = Column(Integer, default=0)
    vector_count = Column(Integer, default=0)
    token_count = Column(Integer, default=0)
    
    # 处理信息
    error_message = Column(Text)
    processing_log = Column(Text)
    
    # 时间戳
    added_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True))


class KnowledgeBaseVector(BaseModel):
    """知识库向量表"""
    __tablename__ = "knowledge_base_vectors"

    id = Column(Integer, primary_key=True, index=True)
    kb_id = Column(String(255), nullable=False, index=True)
    document_id = Column(Integer, index=True)
    segment_id = Column(String(255), index=True)
    
    # 向量信息
    vector_id = Column(String(255), unique=True, nullable=False, index=True)
    embedding_model = Column(String(255), index=True)
    vector_dimension = Column(Integer)
    vector_data = Column(Text)  # JSON格式存储
    
    # 内容信息
    content = Column(Text)
    content_hash = Column(String(255), index=True)
    meta_data = Column(JSON)  # 避免与SQLAlchemy的metadata冲突
    
    # 统计信息
    token_count = Column(Integer, default=0)
    quality_score = Column(DECIMAL(5, 4))
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class KnowledgeBaseConfig(BaseModel):
    """知识库配置表"""
    __tablename__ = "knowledge_base_configs"

    id = Column(Integer, primary_key=True, index=True)
    kb_id = Column(String(255), nullable=False, index=True)
    config_type = Column(String(100), nullable=False, index=True)
    config_key = Column(String(255), nullable=False)
    config_value = Column(Text)
    config_description = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class KnowledgeBaseAccessLog(BaseModel):
    """知识库访问日志表"""
    __tablename__ = "knowledge_base_access_logs"

    id = Column(Integer, primary_key=True, index=True)
    kb_id = Column(String(255), nullable=False, index=True)
    user_id = Column(String(255), index=True)
    action = Column(String(100), index=True)
    query_text = Column(Text)
    result_count = Column(Integer)
    response_time = Column(Integer)
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
