@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 停止AI知识库Celery服务...

REM 停止Flower进程
echo 停止Flower监控...
taskkill /f /im "cmd.exe" /fi "WINDOWTITLE eq Celery Flower*" >nul 2>&1
if %errorlevel% equ 0 (
    echo Flower已停止
) else (
    echo Flower进程未找到或已停止
)

REM 停止Beat进程
echo 停止Celery Beat...
taskkill /f /im "cmd.exe" /fi "WINDOWTITLE eq Celery Beat*" >nul 2>&1
if %errorlevel% equ 0 (
    echo Celery Beat已停止
) else (
    echo Beat进程未找到或已停止
)

REM 停止Worker进程
echo 停止Celery Worker...
taskkill /f /im "cmd.exe" /fi "WINDOWTITLE eq Celery Worker*" >nul 2>&1
if %errorlevel% equ 0 (
    echo Celery Worker已停止
) else (
    echo Worker进程未找到或已停止
)

REM 强制杀死所有celery相关进程
echo 检查并清理残留的Celery进程...
taskkill /f /im "celery.exe" >nul 2>&1
taskkill /f /im "python.exe" /fi "COMMANDLINE eq *celery*" >nul 2>&1

echo.
echo Celery服务已全部停止!
echo.
echo 按任意键退出...
pause >nul
