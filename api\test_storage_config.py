#!/usr/bin/env python3
"""
测试存储配置API的脚本
用于验证config字段的JSON处理是否正确
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000/api/v1/storage-management"

# 测试数据
test_configs = [
    {
        "name": "测试本地存储1",
        "storage_type": "LOCAL",
        "config": {"base_path": "E:/test1"},  # 字典格式
        "is_default": False,
        "is_active": True
    },
    {
        "name": "测试本地存储2", 
        "storage_type": "LOCAL",
        "config": '{"base_path":"E:/test2"}',  # 字符串格式
        "is_default": False,
        "is_active": True
    },
    {
        "name": "测试MinIO存储",
        "storage_type": "MINIO",
        "config": {
            "endpoint": "localhost:9000",
            "access_key": "minioadmin",
            "secret_key": "minioadmin",
            "bucket_name": "test-bucket"
        },
        "is_default": False,
        "is_active": True
    }
]

def test_create_storage():
    """测试创建存储配置"""
    print("=== 测试创建存储配置 ===")
    
    # 获取认证token (假设已登录)
    token = "your_token_here"  # 替换为实际token
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    for i, config_data in enumerate(test_configs, 1):
        print(f"\n测试 {i}: {config_data['name']}")
        print(f"Config类型: {type(config_data['config'])}")
        print(f"Config内容: {config_data['config']}")
        
        try:
            response = requests.post(
                BASE_URL,
                headers=headers,
                json=config_data,
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 创建成功: {result}")
            else:
                print(f"❌ 创建失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def test_pydantic_validation():
    """测试Pydantic验证逻辑"""
    print("\n=== 测试Pydantic验证逻辑 ===")
    
    from pydantic import BaseModel, Field, validator
    from typing import Dict, Any
    
    class TestStorageConfig(BaseModel):
        name: str
        storage_type: str
        config: Dict[str, Any]
        
        @validator('config', pre=True)
        def validate_config(cls, v):
            # 如果是字符串，尝试解析为JSON
            if isinstance(v, str):
                try:
                    import json
                    v = json.loads(v)
                except json.JSONDecodeError as e:
                    raise ValueError(f"配置参数必须是有效的JSON格式: {str(e)}")
            
            # 确保是字典类型
            if not isinstance(v, dict):
                raise ValueError("配置参数必须是字典类型")
                
            return v
    
    test_cases = [
        {
            "name": "字典格式",
            "data": {
                "name": "test1",
                "storage_type": "LOCAL", 
                "config": {"base_path": "/test"}
            }
        },
        {
            "name": "JSON字符串格式",
            "data": {
                "name": "test2",
                "storage_type": "LOCAL",
                "config": '{"base_path":"/test"}'
            }
        },
        {
            "name": "无效JSON字符串",
            "data": {
                "name": "test3",
                "storage_type": "LOCAL",
                "config": '{"base_path":"/test"'  # 缺少结束括号
            }
        },
        {
            "name": "非字典类型",
            "data": {
                "name": "test4",
                "storage_type": "LOCAL",
                "config": "not_a_dict"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        try:
            config = TestStorageConfig(**test_case['data'])
            print(f"✅ 验证成功: {config.config}")
        except Exception as e:
            print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    print("存储配置API测试脚本")
    print("=" * 50)
    
    # 测试Pydantic验证逻辑
    test_pydantic_validation()
    
    # 如果需要测试实际API，取消下面的注释
    # test_create_storage()
    
    print("\n测试完成！")
