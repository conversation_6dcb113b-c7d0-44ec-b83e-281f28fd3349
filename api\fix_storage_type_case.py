#!/usr/bin/env python3
"""
修复数据库中存储类型的大小写问题
将小写的存储类型值转换为大写
"""
import asyncio
from sqlalchemy import text
from loguru import logger

from app.core.database import get_async_session


async def fix_storage_type_case():
    """修复存储类型大小写"""
    
    try:
        async for session in get_async_session():
            logger.info("开始修复存储类型大小写...")
            
            # 查询当前的存储配置
            result = await session.execute(text("""
                SELECT id, name, storage_type 
                FROM storage_configs
            """))
            
            configs = result.fetchall()
            logger.info("找到 {} 个存储配置".format(len(configs)))
            
            # 修复每个配置的存储类型
            updates_made = 0
            for config in configs:
                current_type = config.storage_type
                
                # 如果是小写，转换为大写
                if current_type and current_type.islower():
                    new_type = current_type.upper()
                    
                    logger.info("修复存储配置 '{}' (ID: {}): {} -> {}".format(
                        config.name, config.id, current_type, new_type
                    ))
                    
                    # 更新数据库
                    await session.execute(text("""
                        UPDATE storage_configs 
                        SET storage_type = :new_type, updated_at = NOW()
                        WHERE id = :config_id
                    """), {
                        "new_type": new_type,
                        "config_id": config.id
                    })
                    
                    updates_made += 1
                else:
                    logger.info("存储配置 '{}' (ID: {}) 的类型已是正确格式: {}".format(
                        config.name, config.id, current_type
                    ))
            
            # 提交更改
            await session.commit()
            
            logger.info("✅ 存储类型大小写修复完成！共修复 {} 个配置".format(updates_made))
            
            # 验证修复结果
            result = await session.execute(text("""
                SELECT id, name, storage_type 
                FROM storage_configs
            """))
            
            configs = result.fetchall()
            logger.info("修复后的存储配置:")
            for config in configs:
                logger.info("  - ID: {}, 名称: '{}', 类型: '{}'".format(
                    config.id, config.name, config.storage_type
                ))
            
            break  # 退出async for循环
            
    except Exception as e:
        logger.error("修复存储类型大小写失败: {}".format(str(e)))
        raise


if __name__ == "__main__":
    asyncio.run(fix_storage_type_case())
