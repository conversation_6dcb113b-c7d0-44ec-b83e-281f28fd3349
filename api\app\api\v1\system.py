"""
系统管理API端点
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer
from pydantic import BaseModel
from typing import Dict, List, Any
from datetime import datetime
import psutil
import platform

router = APIRouter()
security = HTTPBearer()


class SystemInfo(BaseModel):
    """系统信息模型"""
    platform: str
    platform_version: str
    architecture: str
    hostname: str
    python_version: str
    cpu_count: int
    memory_total: int
    disk_usage: Dict[str, Any]


class SystemHealth(BaseModel):
    """系统健康状态模型"""
    status: str
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    uptime: float


class PluginInfo(BaseModel):
    """插件信息模型"""
    name: str
    version: str
    description: str
    enabled: bool
    author: str


@router.get("/info", response_model=SystemInfo)
async def get_system_info(token: str = Depends(security)):
    """
    获取系统信息
    
    Args:
        token: 访问令牌
        
    Returns:
        系统信息
    """
    # TODO: 添加权限检查，只允许管理员访问
    
    disk_usage = psutil.disk_usage('/')
    
    return SystemInfo(
        platform=platform.system(),
        platform_version=platform.version(),
        architecture=platform.architecture()[0],
        hostname=platform.node(),
        python_version=platform.python_version(),
        cpu_count=psutil.cpu_count(),
        memory_total=psutil.virtual_memory().total,
        disk_usage={
            "total": disk_usage.total,
            "used": disk_usage.used,
            "free": disk_usage.free,
            "percent": (disk_usage.used / disk_usage.total) * 100
        }
    )


@router.get("/health", response_model=SystemHealth)
async def get_system_health(token: str = Depends(security)):
    """
    获取系统健康状态
    
    Args:
        token: 访问令牌
        
    Returns:
        系统健康状态
    """
    cpu_usage = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # 计算系统运行时间
    boot_time = psutil.boot_time()
    uptime = datetime.now().timestamp() - boot_time
    
    # 判断系统状态
    status = "healthy"
    if cpu_usage > 80 or memory.percent > 80 or (disk.used / disk.total) > 0.9:
        status = "warning"
    if cpu_usage > 95 or memory.percent > 95 or (disk.used / disk.total) > 0.95:
        status = "critical"
    
    return SystemHealth(
        status=status,
        timestamp=datetime.now(),
        cpu_usage=cpu_usage,
        memory_usage=memory.percent,
        disk_usage=(disk.used / disk.total) * 100,
        uptime=uptime
    )


@router.get("/plugins", response_model=List[PluginInfo])
async def get_plugins(token: str = Depends(security)):
    """
    获取插件列表
    
    Args:
        token: 访问令牌
        
    Returns:
        插件列表
    """
    # TODO: 实现实际的插件查询逻辑
    fake_plugins = [
        PluginInfo(
            name="auth",
            version="1.0.0",
            description="Authentication plugin",
            enabled=True,
            author="XHC Team"
        ),
        PluginInfo(
            name="file_manager",
            version="1.0.0",
            description="File management plugin",
            enabled=True,
            author="XHC Team"
        ),
        PluginInfo(
            name="rag_engine",
            version="1.0.0",
            description="RAG engine plugin",
            enabled=True,
            author="XHC Team"
        )
    ]
    
    return fake_plugins


@router.post("/plugins/{plugin_name}/enable")
async def enable_plugin(
    plugin_name: str,
    token: str = Depends(security)
):
    """
    启用插件
    
    Args:
        plugin_name: 插件名称
        token: 访问令牌
        
    Returns:
        操作结果
    """
    # TODO: 实现实际的插件启用逻辑
    return {"message": f"Plugin {plugin_name} enabled successfully"}


@router.post("/plugins/{plugin_name}/disable")
async def disable_plugin(
    plugin_name: str,
    token: str = Depends(security)
):
    """
    禁用插件
    
    Args:
        plugin_name: 插件名称
        token: 访问令牌
        
    Returns:
        操作结果
    """
    # TODO: 实现实际的插件禁用逻辑
    return {"message": f"Plugin {plugin_name} disabled successfully"}


@router.get("/logs")
async def get_system_logs(
    lines: int = 100,
    level: str = "INFO",
    token: str = Depends(security)
):
    """
    获取系统日志
    
    Args:
        lines: 日志行数
        level: 日志级别
        token: 访问令牌
        
    Returns:
        系统日志
    """
    # TODO: 实现实际的日志查询逻辑
    fake_logs = [
        {
            "timestamp": datetime.now().isoformat(),
            "level": "INFO",
            "message": "Application started successfully",
            "module": "main"
        },
        {
            "timestamp": datetime.now().isoformat(),
            "level": "DEBUG",
            "message": "Database connection established",
            "module": "database"
        }
    ]
    
    return {
        "logs": fake_logs,
        "total": len(fake_logs),
        "level": level,
        "lines": lines
    }
