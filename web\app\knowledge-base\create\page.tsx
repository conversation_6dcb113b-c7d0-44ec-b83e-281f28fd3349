'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, ArrowRight, Check, FileText, Layers, 
  Search, Filter, Calendar, User, Database,
  Brain, Zap, Settings, ChevronRight
} from 'lucide-react';
import apiClient from '@/lib/api';

// 已分段文件接口
interface SegmentedFile {
  file_id: string;
  file_name: string;
  file_size: number;
  file_size_formatted: string;
  file_extension: string;
  segment_count: number;
  task_name: string;
  task_status: string;
  created_at: string;
  completed_at?: string;
}

const CreateKnowledgeBasePage: React.FC = () => {
  const router = useRouter();
  
  // 状态管理
  const [currentStep, setCurrentStep] = useState(1);
  const [segmentedFiles, setSegmentedFiles] = useState<SegmentedFile[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // 知识库基本信息
  const [kbName, setKbName] = useState('');
  const [kbDescription, setKbDescription] = useState('');

  // 加载已分段文件
  const loadSegmentedFiles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get('/api/v1/document-segment/files', {
        params: {
          status: 'completed',
          search: searchQuery || undefined,
          page: 1,
          page_size: 100
        }
      });
      
      setSegmentedFiles(response.data?.data || []);
    } catch (err) {
      console.error('Failed to load segmented files:', err);
      setError('加载已分段文件失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    loadSegmentedFiles();
  }, [searchQuery]);

  // 处理文件选择
  const handleFileSelect = (fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  // 处理全选
  const handleSelectAll = () => {
    if (selectedFiles.length === segmentedFiles.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(segmentedFiles.map(f => f.file_id));
    }
  };

  // 下一步
  const handleNext = () => {
    if (currentStep === 1) {
      if (selectedFiles.length === 0) {
        setError('请至少选择一个文件');
        return;
      }
      if (!kbName.trim()) {
        setError('请输入知识库名称');
        return;
      }
      setError(null);
      
      // 跳转到向量化配置页面
      const params = new URLSearchParams({
        files: selectedFiles.join(','),
        name: kbName,
        description: kbDescription
      });
      router.push(`/knowledge-base/create/vectorize?${params.toString()}`);
    }
  };

  // 返回上一步
  const handleBack = () => {
    router.back();
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN');
  };

  // 步骤指示器
  const steps = [
    { id: 1, name: '选择文件', icon: FileText },
    { id: 2, name: '向量化配置', icon: Settings },
    { id: 3, name: '开始处理', icon: Zap }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 头部导航 */}
      <div className="bg-white/60 backdrop-blur-lg shadow-sm border-b border-white/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>

              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    创建AI知识库
                  </h1>
                  <p className="text-sm text-gray-500">步骤 {currentStep} / {steps.length}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 步骤指示器 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center mb-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
                currentStep >= step.id 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-400'
              }`}>
                {currentStep > step.id ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <step.icon className="w-5 h-5" />
                )}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                currentStep >= step.id ? 'text-blue-600' : 'text-gray-400'
              }`}>
                {step.name}
              </span>
              {index < steps.length - 1 && (
                <ChevronRight className="w-5 h-5 text-gray-300 mx-4" />
              )}
            </div>
          ))}
        </div>

        {/* 主要内容 */}
        <div className="bg-white/60 backdrop-blur-lg rounded-2xl shadow-xl border border-white/30 p-8">
          {/* 知识库基本信息 */}
          <div className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-6">知识库基本信息</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  知识库名称 *
                </label>
                <input
                  type="text"
                  value={kbName}
                  onChange={(e) => setKbName(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="请输入知识库名称"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  知识库描述
                </label>
                <textarea
                  value={kbDescription}
                  onChange={(e) => setKbDescription(e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="请输入知识库描述（可选）"
                />
              </div>
            </div>
          </div>

          {/* 文件选择 */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900">选择已分段文件</h2>
              <div className="text-sm text-gray-500">
                已选择 {selectedFiles.length} / {segmentedFiles.length} 个文件
              </div>
            </div>

            {/* 搜索和操作栏 */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex-1 relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索文件..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>

              <button
                onClick={handleSelectAll}
                className="px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
              >
                {selectedFiles.length === segmentedFiles.length ? '取消全选' : '全选'}
              </button>
            </div>

            {/* 文件列表 */}
            {loading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">加载文件中...</p>
              </div>
            ) : segmentedFiles.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无已分段文件</h3>
                <p className="text-gray-500 mb-4">请先对文件进行分段处理</p>
                <button
                  onClick={() => router.push('/file-manager')}
                  className="inline-flex items-center px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-all duration-200"
                >
                  前往文件管理
                </button>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {segmentedFiles.map((file) => (
                  <div
                    key={file.file_id}
                    className={`p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                      selectedFiles.includes(file.file_id)
                        ? 'border-blue-500 bg-blue-50/50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleFileSelect(file.file_id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div className={`w-4 h-4 border-2 rounded transition-all duration-200 ${
                          selectedFiles.includes(file.file_id)
                            ? 'bg-blue-600 border-blue-600'
                            : 'border-gray-300'
                        }`}>
                          {selectedFiles.includes(file.file_id) && (
                            <Check className="w-3 h-3 text-white" />
                          )}
                        </div>
                        
                        <FileText className="w-5 h-5 text-gray-400 flex-shrink-0" />
                        
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {file.file_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {file.file_size_formatted} • {file.segment_count} 个分段 • {formatTime(file.created_at)}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full">
                          已完成
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="text-red-500 mr-2">⚠️</div>
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={handleBack}
              className="inline-flex items-center px-6 py-3 rounded-xl border border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-200"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回
            </button>

            <button
              onClick={handleNext}
              disabled={selectedFiles.length === 0 || !kbName.trim()}
              className={`inline-flex items-center px-6 py-3 rounded-xl transition-all duration-200 ${
                selectedFiles.length === 0 || !kbName.trim()
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl'
              }`}
            >
              下一步：配置向量化
              <ArrowRight className="w-4 h-4 ml-2" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateKnowledgeBasePage;
