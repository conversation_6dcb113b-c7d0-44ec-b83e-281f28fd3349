#!/usr/bin/env python3
"""
测试枚举值修复的脚本
验证数据库枚举值与Python模型是否匹配
"""

import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_sync_session
from app.models.document_segment import (
    DocumentSegmentTask, DocumentSegment, SegmentStatus, 
    SegmentMethod, VectorizeStatus
)


def test_enum_values():
    """测试枚举值是否正确"""
    print("=== 测试枚举值 ===")
    
    # 测试SegmentMethod枚举
    print("SegmentMethod枚举值:")
    for method in SegmentMethod:
        print(f"  - {method.name}: {method.value}")
    
    # 测试SegmentStatus枚举
    print("\nSegmentStatus枚举值:")
    for status in SegmentStatus:
        print(f"  - {status.name}: {status.value}")
    
    # 测试VectorizeStatus枚举
    print("\nVectorizeStatus枚举值:")
    for status in VectorizeStatus:
        print(f"  - {status.name}: {status.value}")


def test_database_enum_compatibility():
    """测试数据库枚举兼容性"""
    print("\n=== 测试数据库枚举兼容性 ===")
    
    try:
        db = next(get_sync_session())
        
        # 测试创建分段任务
        task = DocumentSegmentTask(
            task_name="枚举测试任务",
            description="测试枚举值兼容性",
            file_ids=["test_file_1"],
            total_files=1,
            segment_method=SegmentMethod.PARAGRAPH,  # 使用枚举
            max_length=500,
            overlap=50,
            enable_vectorization=False,
            status=SegmentStatus.PENDING  # 使用枚举
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 成功创建分段任务: ID={task.id}")
        print(f"   - segment_method: {task.segment_method}")
        print(f"   - status: {task.status}")
        
        # 测试创建分段记录
        segment = DocumentSegment(
            task_id=task.id,
            file_id="test_file_1",
            content="这是一个测试分段内容",
            segment_index=0,
            vectorize_status=VectorizeStatus.PENDING  # 使用枚举
        )
        
        db.add(segment)
        db.commit()
        db.refresh(segment)
        
        print(f"✅ 成功创建分段记录: ID={segment.id}")
        print(f"   - vectorize_status: {segment.vectorize_status}")
        
        # 清理测试数据
        db.delete(segment)
        db.delete(task)
        db.commit()
        
        print("✅ 测试数据已清理")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库枚举兼容性测试失败: {e}")
        return False


def test_query_enum_values():
    """测试查询枚举值"""
    print("\n=== 测试查询枚举值 ===")
    
    try:
        db = next(get_sync_session())
        
        # 查询数据库中的枚举类型
        result = db.execute("""
            SELECT typname, enumlabel 
            FROM pg_type t 
            JOIN pg_enum e ON t.oid = e.enumtypid 
            WHERE typname IN ('segment_method_enum', 'segment_status_enum', 'vectorize_status_enum')
            ORDER BY typname, enumsortorder
        """)
        
        enum_values = {}
        for row in result:
            type_name = row[0]
            enum_label = row[1]
            if type_name not in enum_values:
                enum_values[type_name] = []
            enum_values[type_name].append(enum_label)
        
        print("数据库中的枚举值:")
        for type_name, values in enum_values.items():
            print(f"  {type_name}: {values}")
        
        # 验证枚举值是否匹配
        expected_values = {
            'segment_method_enum': ['PARAGRAPH', 'SENTENCE', 'FIXED_LENGTH', 'SEMANTIC'],
            'segment_status_enum': ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'],
            'vectorize_status_enum': ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED']
        }
        
        all_match = True
        for type_name, expected in expected_values.items():
            actual = enum_values.get(type_name, [])
            if set(actual) != set(expected):
                print(f"❌ {type_name} 枚举值不匹配:")
                print(f"   期望: {expected}")
                print(f"   实际: {actual}")
                all_match = False
            else:
                print(f"✅ {type_name} 枚举值匹配")
        
        db.close()
        return all_match
        
    except Exception as e:
        print(f"❌ 查询枚举值失败: {e}")
        return False


def test_template_data():
    """测试模板数据"""
    print("\n=== 测试模板数据 ===")
    
    try:
        db = next(get_sync_session())
        
        # 查询模板数据
        from app.models.document_segment import SegmentTemplate
        templates = db.query(SegmentTemplate).all()
        
        print(f"找到 {len(templates)} 个分段模板:")
        for template in templates:
            print(f"  - {template.template_name}: {template.segment_method}")
        
        db.close()
        return len(templates) > 0
        
    except Exception as e:
        print(f"❌ 查询模板数据失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 开始枚举值修复验证测试")
    print("=" * 50)
    
    # 1. 测试枚举值定义
    test_enum_values()
    
    # 2. 测试数据库枚举兼容性
    if not test_database_enum_compatibility():
        print("\n❌ 数据库枚举兼容性测试失败")
        print("请确保已执行 fix_enum_values.sql 脚本")
        return
    
    # 3. 测试查询枚举值
    if not test_query_enum_values():
        print("\n❌ 枚举值查询测试失败")
        return
    
    # 4. 测试模板数据
    if not test_template_data():
        print("\n❌ 模板数据测试失败")
        return
    
    print("\n🎉 所有测试通过！枚举值修复成功！")
    print("\n现在可以正常使用分段功能了。")


if __name__ == "__main__":
    main()
