'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  Circle, 
  Loader2, 
  User, 
  Settings, 
  Database,
  ArrowRight,
  AlertTriangle
} from 'lucide-react';
import { systemInitApi, type AdminInitData } from '@/lib/systemInit';

interface InitStep {
  key: string;
  label: string;
  icon: React.ReactNode;
  completed: boolean;
  enabled: boolean;
}

export default function SystemInitPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<'admin' | 'auto' | 'completed'>('admin');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [autoInitStarted, setAutoInitStarted] = useState(false);

  const [adminForm, setAdminForm] = useState<AdminInitData>({
    username: '',
    email: '',
    password: '',
    full_name: '系统管理员'
  });

  const [confirmPassword, setConfirmPassword] = useState('');

  const [steps, setSteps] = useState<InitStep[]>([
    {
      key: 'admin',
      label: '创建管理员',
      icon: <User className="w-5 h-5" />,
      completed: false,
      enabled: true
    },
    {
      key: 'storage',
      label: '存储配置',
      icon: <Database className="w-5 h-5" />,
      completed: false,
      enabled: false
    },
    {
      key: 'auto',
      label: '系统配置',
      icon: <Settings className="w-5 h-5" />,
      completed: false,
      enabled: false
    },
    {
      key: 'completed',
      label: '初始化完成',
      icon: <CheckCircle className="w-5 h-5" />,
      completed: false,
      enabled: false
    }
  ]);

  const [autoInitTasks, setAutoInitTasks] = useState([
    { key: 'dictionaries', name: '字典数据', description: '初始化系统字典数据', status: 'pending' },
    { key: 'storage', name: '存储配置', description: '创建默认存储配置', status: 'pending' },
    { key: 'system-config', name: '系统配置', description: '初始化系统配置项', status: 'pending' }
  ]);

  const [storageConfig, setStorageConfig] = useState({
    type: 'local',
    basePath: './storage'
  });

  // 检查初始化状态
  useEffect(() => {
    const checkInitStatus = async () => {
      try {
        const { is_initialized } = await systemInitApi.checkNeedInit();
        if (is_initialized) {
          router.push('/login');
        }
      } catch (err) {
        console.error('检查初始化状态失败:', err);
      }
    };

    checkInitStatus();
  }, [router]);

  // 初始化管理员账户
  const handleAdminInit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (adminForm.password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await systemInitApi.initializeAdmin(adminForm);
      
      // 更新步骤状态
      setSteps(prev => prev.map((step, index) => {
        if (step.key === 'admin') {
          return { ...step, completed: true };
        }
        if (step.key === 'storage') {
          return { ...step, enabled: true };
        }
        return step;
      }));

      setCurrentStep('storage');
    } catch (err: any) {
      setError(err.response?.data?.detail || '创建管理员账户失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理存储配置
  const handleStorageConfig = async () => {
    setLoading(true);
    setError('');

    try {
      // 调用存储配置API
      await systemInitApi.initializeStorage(storageConfig);

      // 更新步骤状态
      setSteps(prev => prev.map((step, index) => {
        if (step.key === 'storage') {
          return { ...step, completed: true };
        }
        if (step.key === 'auto') {
          return { ...step, enabled: true };
        }
        return step;
      }));

      setCurrentStep('auto');
    } catch (err: any) {
      setError(err.response?.data?.detail || '存储配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始自动初始化
  const handleAutoInit = async () => {
    setAutoInitStarted(true);
    setLoading(true);

    try {
      // 依次执行初始化任务
      for (const task of autoInitTasks) {
        setAutoInitTasks(prev => 
          prev.map(t => t.key === task.key ? { ...t, status: 'running' } : t)
        );

        let apiCall;
        switch (task.key) {
          case 'dictionaries':
            apiCall = systemInitApi.initializeDictionaries;
            break;
          case 'storage':
            apiCall = systemInitApi.initializeStorage;
            break;
          case 'system-config':
            apiCall = systemInitApi.initializeSystemConfig;
            break;
          default:
            continue;
        }

        await apiCall();
        
        setAutoInitTasks(prev => 
          prev.map(t => t.key === task.key ? { ...t, status: 'completed' } : t)
        );
      }

      // 更新步骤状态
      setSteps(prev => prev.map(step => {
        if (step.key === 'auto') {
          return { ...step, completed: true };
        }
        if (step.key === 'completed') {
          return { ...step, enabled: true, completed: true };
        }
        return step;
      }));

      setCurrentStep('completed');
    } catch (err: any) {
      setError(err.response?.data?.detail || '自动初始化失败');
      // 标记当前任务失败
      setAutoInitTasks(prev => 
        prev.map(t => t.status === 'running' ? { ...t, status: 'failed' } : t)
      );
    } finally {
      setLoading(false);
    }
  };

  const getTaskIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'running':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <Circle className="w-5 h-5 text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-2xl w-full">
        {/* 头部 */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-4"
          >
            <Database className="w-8 h-8 text-white" />
          </motion.div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🚀 AI知识库系统初始化</h1>
          <p className="text-gray-600">欢迎使用AI知识库！请完成系统初始化配置。</p>
        </div>

        {/* 步骤指示器 */}
        <div className="flex justify-between items-center mb-8">
          {steps.map((step, index) => (
            <div key={step.key} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                step.completed 
                  ? 'bg-green-500 border-green-500 text-white' 
                  : currentStep === step.key
                  ? 'bg-blue-500 border-blue-500 text-white'
                  : 'bg-gray-100 border-gray-300 text-gray-400'
              }`}>
                {step.completed ? <CheckCircle className="w-5 h-5" /> : step.icon}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                step.completed ? 'text-green-600' : currentStep === step.key ? 'text-blue-600' : 'text-gray-400'
              }`}>
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <ArrowRight className="w-4 h-4 text-gray-300 mx-4" />
              )}
            </div>
          ))}
        </div>

        {/* 管理员账户创建 */}
        {currentStep === 'admin' && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">👤 创建管理员账户</h2>
              <p className="text-gray-600 mb-6">请创建系统管理员账户，用于管理整个知识库系统。</p>
            </div>

            <form onSubmit={handleAdminInit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                <input
                  type="text"
                  value={adminForm.username}
                  onChange={(e) => setAdminForm(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入管理员用户名"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                <input
                  type="email"
                  value={adminForm.email}
                  onChange={(e) => setAdminForm(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入管理员邮箱"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                <input
                  type="text"
                  value={adminForm.full_name}
                  onChange={(e) => setAdminForm(prev => ({ ...prev, full_name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入管理员姓名"
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">密码</label>
                <input
                  type="password"
                  value={adminForm.password}
                  onChange={(e) => setAdminForm(prev => ({ ...prev, password: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入密码（至少6位）"
                  minLength={6}
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请再次输入密码"
                  required
                  disabled={loading}
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {loading && <Loader2 className="w-4 h-4 animate-spin" />}
                <span>{loading ? '创建中...' : '创建管理员账户'}</span>
              </button>
            </form>
          </motion.div>
        )}

        {/* 存储配置 */}
        {currentStep === 'storage' && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">💾 存储配置</h2>
              <p className="text-gray-600 mb-6">请配置文件存储方式，系统将根据您的选择创建相应的存储配置。</p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">存储类型</label>
                <div className="grid grid-cols-1 gap-3">
                  <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="storageType"
                      value="local"
                      checked={storageConfig.type === 'local'}
                      onChange={(e) => setStorageConfig(prev => ({ ...prev, type: e.target.value }))}
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">本地存储</div>
                      <div className="text-sm text-gray-600">文件存储在服务器本地磁盘</div>
                    </div>
                  </label>

                  <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 opacity-50">
                    <input
                      type="radio"
                      name="storageType"
                      value="minio"
                      disabled
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">MinIO存储</div>
                      <div className="text-sm text-gray-600">对象存储服务（暂未开放）</div>
                    </div>
                  </label>

                  <label className="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 opacity-50">
                    <input
                      type="radio"
                      name="storageType"
                      value="ftp"
                      disabled
                      className="mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">FTP存储</div>
                      <div className="text-sm text-gray-600">FTP文件传输协议（暂未开放）</div>
                    </div>
                  </label>
                </div>
              </div>

              {storageConfig.type === 'local' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">存储目录</label>
                  <input
                    type="text"
                    value={storageConfig.basePath}
                    onChange={(e) => setStorageConfig(prev => ({ ...prev, basePath: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入存储目录路径"
                    disabled={loading}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    文件将存储在此目录下，建议使用相对路径或绝对路径
                  </p>
                </div>
              )}
            </div>

            <button
              onClick={handleStorageConfig}
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {loading && <Loader2 className="w-4 h-4 animate-spin" />}
              <span>{loading ? '配置中...' : '确认存储配置'}</span>
            </button>
          </motion.div>
        )}

        {/* 自动初始化 */}
        {currentStep === 'auto' && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">⚙️ 系统配置初始化</h2>
              <p className="text-gray-600 mb-6">正在自动初始化系统配置、字典数据和存储配置...</p>
            </div>

            <div className="space-y-4">
              {autoInitTasks.map((task) => (
                <div key={task.key} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  {getTaskIcon(task.status)}
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{task.name}</div>
                    <div className="text-sm text-gray-600">{task.description}</div>
                  </div>
                </div>
              ))}
            </div>

            {!autoInitStarted && (
              <button
                onClick={handleAutoInit}
                disabled={loading}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {loading && <Loader2 className="w-4 h-4 animate-spin" />}
                <span>开始自动初始化</span>
              </button>
            )}
          </motion.div>
        )}

        {/* 初始化完成 */}
        {currentStep === 'completed' && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center space-y-6"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
              <CheckCircle className="w-10 h-10 text-green-500" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">🎉 系统初始化完成！</h2>
              <p className="text-gray-600 mb-6">恭喜！AI知识库系统已成功初始化，您现在可以开始使用了。</p>
            </div>
            <button
              onClick={() => router.push('/login')}
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 flex items-center space-x-2 mx-auto"
            >
              <span>前往登录</span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </motion.div>
        )}

        {/* 错误信息 */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2 text-red-700"
          >
            <AlertTriangle className="w-4 h-4" />
            <span>{error}</span>
          </motion.div>
        )}
      </div>
    </div>
  );
}
