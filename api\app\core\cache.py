"""
Redis缓存模块
提供缓存功能的抽象层
"""

import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import timedelta
import redis.asyncio as redis
from loguru import logger

from app.core.config import get_settings

settings = get_settings()

# 全局Redis连接池
redis_pool: Optional[redis.ConnectionPool] = None
redis_client: Optional[redis.Redis] = None


async def init_redis() -> None:
    """初始化Redis连接"""
    global redis_pool, redis_client
    
    try:
        # 创建连接池
        redis_pool = redis.ConnectionPool.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=True,
            max_connections=20,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30
        )
        
        # 创建Redis客户端
        redis_client = redis.Redis(connection_pool=redis_pool)
        
        # 测试连接
        await redis_client.ping()
        logger.info("✅ Redis connection initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Redis: {e}")
        redis_pool = None
        redis_client = None


async def close_redis() -> None:
    """关闭Redis连接"""
    global redis_pool, redis_client
    
    try:
        if redis_client:
            await redis_client.close()
        if redis_pool:
            await redis_pool.disconnect()
        
        redis_client = None
        redis_pool = None
        logger.info("✅ Redis connection closed")
        
    except Exception as e:
        logger.error(f"❌ Error closing Redis connection: {e}")


async def get_redis_client() -> Optional[redis.Redis]:
    """获取Redis客户端"""
    global redis_client
    
    if redis_client is None:
        await init_redis()
    
    return redis_client


class RedisCache:
    """Redis缓存类"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        try:
            value = await self.redis.get(key)
            if value is None:
                return default
            
            # 尝试JSON解析
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # 如果JSON解析失败，返回原始字符串
                return value
                
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return default
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """设置缓存值"""
        try:
            # 序列化值
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, (int, float, bool)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            # 设置过期时间
            if isinstance(expire, timedelta):
                expire_seconds = int(expire.total_seconds())
            else:
                expire_seconds = expire
            
            await self.redis.set(key, serialized_value, ex=expire_seconds)
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            result = await self.redis.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置缓存过期时间"""
        try:
            result = await self.redis.expire(key, seconds)
            return result
        except Exception as e:
            logger.error(f"Error setting expire for cache key {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取缓存剩余时间"""
        try:
            return await self.redis.ttl(key)
        except Exception as e:
            logger.error(f"Error getting TTL for cache key {key}: {e}")
            return -1
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        try:
            return await self.redis.keys(pattern)
        except Exception as e:
            logger.error(f"Error getting keys with pattern {pattern}: {e}")
            return []
    
    async def flushdb(self) -> bool:
        """清空当前数据库"""
        try:
            await self.redis.flushdb()
            return True
        except Exception as e:
            logger.error(f"Error flushing database: {e}")
            return False
    
    async def incr(self, key: str, amount: int = 1) -> Optional[int]:
        """递增计数器"""
        try:
            return await self.redis.incr(key, amount)
        except Exception as e:
            logger.error(f"Error incrementing cache key {key}: {e}")
            return None
    
    async def decr(self, key: str, amount: int = 1) -> Optional[int]:
        """递减计数器"""
        try:
            return await self.redis.decr(key, amount)
        except Exception as e:
            logger.error(f"Error decrementing cache key {key}: {e}")
            return None
    
    async def hget(self, name: str, key: str) -> Any:
        """获取哈希字段值"""
        try:
            value = await self.redis.hget(name, key)
            if value is None:
                return None
            
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logger.error(f"Error getting hash field {name}.{key}: {e}")
            return None
    
    async def hset(self, name: str, key: str, value: Any) -> bool:
        """设置哈希字段值"""
        try:
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, (int, float, bool)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            result = await self.redis.hset(name, key, serialized_value)
            return result >= 0
            
        except Exception as e:
            logger.error(f"Error setting hash field {name}.{key}: {e}")
            return False
    
    async def hdel(self, name: str, *keys: str) -> int:
        """删除哈希字段"""
        try:
            return await self.redis.hdel(name, *keys)
        except Exception as e:
            logger.error(f"Error deleting hash fields {name}.{keys}: {e}")
            return 0
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """获取所有哈希字段"""
        try:
            result = await self.redis.hgetall(name)
            parsed_result = {}
            
            for key, value in result.items():
                try:
                    parsed_result[key] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    parsed_result[key] = value
            
            return parsed_result
            
        except Exception as e:
            logger.error(f"Error getting all hash fields {name}: {e}")
            return {}


# 全局缓存实例
cache_instance: Optional[RedisCache] = None


async def get_redis_cache() -> Optional[RedisCache]:
    """获取Redis缓存实例"""
    global cache_instance
    
    if cache_instance is None:
        redis_client = await get_redis_client()
        if redis_client:
            cache_instance = RedisCache(redis_client)
    
    return cache_instance


# 缓存装饰器
def cache_result(
    key_prefix: str, 
    expire: Optional[Union[int, timedelta]] = None,
    key_func: Optional[callable] = None
):
    """缓存结果装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = f"{key_prefix}:{key_func(*args, **kwargs)}"
            else:
                # 默认使用函数参数生成键
                key_parts = [str(arg) for arg in args]
                key_parts.extend([f"{k}={v}" for k, v in kwargs.items()])
                cache_key = f"{key_prefix}:{'_'.join(key_parts)}"
            
            # 尝试从缓存获取
            cache = await get_redis_cache()
            if cache:
                cached_result = await cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            if cache and result is not None:
                await cache.set(cache_key, result, expire)
            
            return result
        
        return wrapper
    return decorator


# 缓存键生成器
class CacheKeys:
    """缓存键常量"""
    
    # 用户相关
    USER_INFO = "user:info:{user_id}"
    USER_PERMISSIONS = "user:permissions:{user_id}"
    USER_SESSION = "user:session:{session_id}"
    
    # 认证相关
    ACCESS_TOKEN = "auth:token:{token_hash}"
    REFRESH_TOKEN = "auth:refresh:{token_hash}"
    LOGIN_ATTEMPTS = "auth:attempts:{ip}:{username}"
    
    # 存储相关
    STORAGE_CONFIG = "storage:config:{storage_id}"
    STORAGE_STATS = "storage:stats:{storage_id}"
    FILE_METADATA = "file:metadata:{file_id}"
    
    # 系统相关
    SYSTEM_STATS = "system:stats"
    HEALTH_CHECK = "system:health"
    
    @staticmethod
    def format_key(template: str, **kwargs) -> str:
        """格式化缓存键"""
        return template.format(**kwargs)
