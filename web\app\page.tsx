'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from "next/link";
import { motion } from "framer-motion";
import { Brain, ArrowR<PERSON>, Sparkles, Loader2 } from "lucide-react";
import { systemInitApi } from '@/lib/systemInit';
import apiClient from '@/lib/api';

export default function Home() {
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkSystemStatus = async () => {
      try {
        // 首先检查系统是否需要首次设置
        const response = await apiClient.get('/api/v1/setup/status');
        const status = response.data.data;

        if (status.needs_setup) {
          // 系统需要首次设置，跳转到设置页面
          router.push('/setup');
          return;
        }

        // 系统已设置完成，检查是否已登录
        const token = localStorage.getItem('token');
        if (token) {
          // 如果已登录，3秒后自动跳转到仪表板
          const timer = setTimeout(() => {
            router.push('/dashboard');
          }, 3000);
          return () => clearTimeout(timer);
        }
      } catch (error) {
        console.error('检查系统状态失败:', error);
        // 如果检查失败，尝试检查旧的初始化状态
        try {
          const { need_init } = await systemInitApi.checkNeedInit();
          if (need_init) {
            router.push('/system-init');
            return;
          }
        } catch (legacyError) {
          console.error('检查旧初始化状态失败:', legacyError);
          // 如果都失败，跳转到首次设置页面
          router.push('/setup');
          return;
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkSystemStatus();
  }, [router]);

  // 如果正在检查系统状态，显示加载界面
  if (isChecking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="inline-block mb-4"
          >
            <Loader2 className="w-12 h-12 text-white" />
          </motion.div>
          <p className="text-white text-lg">正在检查系统状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="max-w-4xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <motion.div
            className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl mb-6"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.3 }}
          >
            <Brain className="w-10 h-10 text-white" />
          </motion.div>

          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            <span className="bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              知识库
            </span>
            <br />
            <span className="text-3xl md:text-5xl text-blue-200">
              智能平台
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-blue-200 mb-8 max-w-2xl mx-auto leading-relaxed">
            基于AI的智能知识管理与检索系统，让知识更智能、检索更精准
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
        >
          <Link href="/login">
            <motion.button
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg flex items-center space-x-2 hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-2xl"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Get Started</span>
              <ArrowRight className="w-5 h-5" />
            </motion.button>
          </Link>

          <motion.button
            className="border-2 border-white/30 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/10 transition-all duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Learn More
          </motion.button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto"
        >
          {[
            {
              icon: "🧠",
              title: "AI驱动",
              description: "先进的机器学习算法，实现智能文档处理"
            },
            {
              icon: "⚡",
              title: "极速响应",
              description: "实时分析和即时结果，最大化生产力"
            },
            {
              icon: "🛡️",
              title: "安全可靠",
              description: "企业级安全保障，端到端加密保护"
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {feature.title}
              </h3>
              <p className="text-blue-200 text-sm">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="mt-12 flex items-center justify-center space-x-2 text-blue-300"
        >
          <Sparkles className="w-4 h-4" />
          <span className="text-sm">基于 Next.js & FastAPI 构建</span>
          <Sparkles className="w-4 h-4" />
        </motion.div>
      </div>
    </div>
  );
}
