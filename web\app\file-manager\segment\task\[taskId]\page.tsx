'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  FileText,
  Brain,
  Zap,
  Eye,
  Download,
  RefreshCw,
  Play,
  Pause,
  Square
} from 'lucide-react';
import apiClient from '@/lib/api';

interface SegmentTask {
  id: number;
  task_id: string;
  task_name: string;
  description: string;
  file_ids: string[];
  total_files: number;
  processed_files: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  error_message: string;
  total_segments: number;
  total_vectors: number;
  started_at: string;
  completed_at: string;
  duration_formatted: string;
  success_rate: number;
  created_at: string;
  updated_at: string;
}

interface FileProgress {
  file_id: string;
  file_name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  segments_count: number;
  vectors_count: number;
  error_message: string;
}

const TaskMonitorPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const taskId = params.taskId as string;

  const [task, setTask] = useState<SegmentTask | null>(null);
  const [fileProgress, setFileProgress] = useState<FileProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (taskId) {
      loadTaskInfo();
    }
  }, [taskId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (autoRefresh && task && ['pending', 'processing'].includes(task.status)) {
      interval = setInterval(() => {
        loadTaskInfo();
      }, 2000); // 每2秒刷新一次
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh, task?.status]);

  const loadTaskInfo = async () => {
    try {
      setError(null);

      const [taskResponse, progressResponse] = await Promise.all([
        apiClient.get(`/api/v1/document-segment/tasks/${taskId}`),
        apiClient.get(`/api/v1/document-segment/tasks/${taskId}/progress`)
      ]);

      setTask(taskResponse.data);
      setFileProgress(progressResponse.data.files || []);
    } catch (err) {
      console.error('Failed to load task info:', err);
      setError('加载任务信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePauseTask = async () => {
    try {
      await apiClient.post(`/api/v1/document-segment/tasks/${taskId}/pause`);
      await loadTaskInfo();
    } catch (err) {
      console.error('Failed to pause task:', err);
    }
  };

  const handleResumeTask = async () => {
    try {
      await apiClient.post(`/api/v1/document-segment/tasks/${taskId}/resume`);
      await loadTaskInfo();
    } catch (err) {
      console.error('Failed to resume task:', err);
    }
  };

  const handleStopTask = async () => {
    try {
      await apiClient.post(`/api/v1/document-segment/tasks/${taskId}/stop`);
      await loadTaskInfo();
    } catch (err) {
      console.error('Failed to stop task:', err);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'processing':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '等待中';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">加载任务信息中...</p>
        </div>
      </div>
    );
  }

  if (error || !task) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <div className="text-lg font-medium text-gray-900 mb-2">加载失败</div>
          <div className="text-gray-600 mb-4">{error || '任务不存在'}</div>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* AI现代风格头部导航 */}
      <div className="bg-white/60 backdrop-blur-lg shadow-sm border-b border-white/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.back()}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200"
              >
                <ArrowLeft className="w-5 h-5" />
              </motion.button>

              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    {task.task_name}
                  </h1>
                  <p className="text-sm text-gray-500 flex items-center space-x-2">
                    <span>任务ID: {task.task_id}</span>
                    <span>•</span>
                    <span className="flex items-center space-x-1">
                      {getStatusIcon(task.status)}
                      <span>{getStatusText(task.status)}</span>
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`p-2 rounded-xl transition-all duration-200 ${
                  autoRefresh
                    ? 'bg-blue-100/50 text-blue-600 shadow-sm'
                    : 'bg-gray-100/50 text-gray-600 hover:bg-gray-200/50'
                }`}
                title={autoRefresh ? '停止自动刷新' : '开启自动刷新'}
              >
                <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
              </motion.button>

              {task.status === 'processing' && (
                <>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handlePauseTask}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 text-white rounded-xl hover:from-yellow-600 hover:to-orange-700 transition-all duration-200 shadow-lg"
                  >
                    <Pause className="w-4 h-4 mr-2" />
                    暂停任务
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleStopTask}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-xl hover:from-red-600 hover:to-pink-700 transition-all duration-200 shadow-lg"
                  >
                    <Square className="w-4 h-4 mr-2" />
                    停止任务
                  </motion.button>
                </>
              )}

              {task.status === 'pending' && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleResumeTask}
                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg"
                >
                  <Play className="w-4 h-4 mr-2" />
                  开始任务
                </motion.button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* AI现代风格任务概览 */}
          <div className="bg-white/60 backdrop-blur-lg rounded-2xl shadow-xl border border-white/30 p-8">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Brain className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">AI处理概览</h3>
              </div>
              <div className="flex items-center space-x-3">
                {getStatusIcon(task.status)}
                <span className={`px-4 py-2 rounded-xl text-sm font-semibold shadow-sm ${getStatusColor(task.status)}`}>
                  {getStatusText(task.status)}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <FileText className="w-6 h-6 text-blue-600" />
                  <span className="text-xs text-blue-600 font-medium">FILES</span>
                </div>
                <div className="text-3xl font-bold text-blue-900">{task.total_files}</div>
                <div className="text-sm text-blue-700">总文件数</div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 border border-green-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <span className="text-xs text-green-600 font-medium">PROCESSED</span>
                </div>
                <div className="text-3xl font-bold text-green-900">{task.processed_files}</div>
                <div className="text-sm text-green-700">已处理</div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-purple-50 to-violet-100 rounded-xl p-6 border border-purple-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <Brain className="w-6 h-6 text-purple-600" />
                  <span className="text-xs text-purple-600 font-medium">SEGMENTS</span>
                </div>
                <div className="text-3xl font-bold text-purple-900">{task.total_segments}</div>
                <div className="text-sm text-purple-700">总分段数</div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-br from-pink-50 to-rose-100 rounded-xl p-6 border border-pink-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <Zap className="w-6 h-6 text-pink-600" />
                  <span className="text-xs text-pink-600 font-medium">VECTORS</span>
                </div>
                <div className="text-3xl font-bold text-pink-900">{task.total_vectors}</div>
                <div className="text-sm text-pink-700">总向量数</div>
              </motion.div>
            </div>

            {/* AI风格进度条 */}
            <div className="bg-white/50 rounded-xl p-6 border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Loader2 className={`w-5 h-5 ${task.status === 'processing' ? 'animate-spin text-blue-600' : 'text-gray-400'}`} />
                  <span className="text-lg font-semibold text-gray-900">AI处理进度</span>
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {task.progress.toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4 shadow-inner">
                <motion.div
                  className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 h-4 rounded-full shadow-lg"
                  initial={{ width: 0 }}
                  animate={{ width: `${task.progress}%` }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                />
              </div>
              <div className="flex justify-between text-sm text-gray-600 mt-2">
                <span>开始</span>
                <span className="font-medium">
                  {task.processed_files} / {task.total_files} 文件
                </span>
                <span>完成</span>
              </div>
            </div>

            {/* 时间信息 */}
            {(task.started_at || task.completed_at) && (
              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                {task.started_at && (
                  <div>
                    <span className="text-gray-500">开始时间:</span>
                    <div className="font-medium">{new Date(task.started_at).toLocaleString('zh-CN')}</div>
                  </div>
                )}
                
                {task.completed_at && (
                  <div>
                    <span className="text-gray-500">完成时间:</span>
                    <div className="font-medium">{new Date(task.completed_at).toLocaleString('zh-CN')}</div>
                  </div>
                )}
                
                {task.duration_formatted && (
                  <div>
                    <span className="text-gray-500">耗时:</span>
                    <div className="font-medium">{task.duration_formatted}</div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 文件处理进度 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">文件处理进度</h3>
            
            <div className="space-y-4">
              <AnimatePresence>
                {fileProgress.map((file, index) => (
                  <motion.div
                    key={file.file_id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4 flex-1">
                      <FileText className="w-5 h-5 text-blue-500 flex-shrink-0" />
                      
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {file.file_name}
                        </p>
                        {file.error_message && (
                          <p className="text-xs text-red-600 mt-1">
                            {file.error_message}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Brain className="w-4 h-4 mr-1" />
                          {file.segments_count} 分段
                        </div>
                        
                        <div className="flex items-center">
                          <Zap className="w-4 h-4 mr-1" />
                          {file.vectors_count} 向量
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(file.status)}
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(file.status)}`}>
                        {getStatusText(file.status)}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </div>

          {/* 错误信息 */}
          {task.error_message && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-red-800 font-medium mb-1">任务执行错误</h4>
                  <p className="text-red-700 text-sm">{task.error_message}</p>
                </div>
              </div>
            </div>
          )}

          {/* 任务描述 */}
          {task.description && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">任务描述</h3>
              <p className="text-gray-700">{task.description}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskMonitorPage;
