2025-06-17 17:47:23.091 | INFO     | app.core.logging:setup_logging:110 | Logging configured for development environment
2025-06-17 17:47:23.091 | INFO     | app.core.middleware:setup_middleware:136 | Middleware setup completed
2025-06-17 17:47:23.121 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: auth v1.0.0
2025-06-17 17:47:23.122 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: file_manager v1.0.0
2025-06-17 17:47:23.124 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: rag_engine v1.0.0
2025-06-17 17:47:23.125 | INFO     | app.core.database:init_database:222 | Initializing database connection with URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:47:23.130 | INFO     | app.core.database:init_database:254 | Using async URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:47:23.130 | INFO     | app.core.database:init_database:255 | Using sync URL: postgresql+psycopg2://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:47:23.180 | INFO     | app.core.database:init_database:281 | Database connection initialized successfully for postgresql
2025-06-17 17:47:23.181 | INFO     | app.core.simple_migration:create_tables_directly:44 | Creating tables for database type: postgresql
2025-06-17 17:47:23.216 | INFO     | app.plugins.auth.plugin:initialize:36 | Auth plugin initialized
2025-06-17 17:47:23.216 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: auth
2025-06-17 17:47:23.216 | INFO     | app.plugins.file_manager.plugin:initialize:49 | File manager plugin initialized with upload dir: uploads
2025-06-17 17:47:23.217 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: file_manager
2025-06-17 17:47:23.217 | INFO     | app.plugins.rag_engine.plugin:initialize:64 | RAG engine plugin initialized
2025-06-17 17:47:23.217 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: rag_engine
2025-06-17 17:47:23.217 | INFO     | app.plugins.auth.plugin:startup:40 | Auth plugin started
2025-06-17 17:47:23.217 | INFO     | app.plugins.manager:startup:167 | Started plugin: auth
2025-06-17 17:47:23.218 | INFO     | app.plugins.file_manager.plugin:startup:57 | File manager plugin started
2025-06-17 17:47:23.218 | INFO     | app.plugins.manager:startup:167 | Started plugin: file_manager
2025-06-17 17:47:23.218 | INFO     | app.plugins.rag_engine.plugin:startup:69 | RAG engine plugin started
2025-06-17 17:47:23.218 | INFO     | app.plugins.manager:startup:167 | Started plugin: rag_engine
2025-06-17 17:47:23.218 | INFO     | app.services.upload_processor:start:34 | 上传处理器启动
2025-06-17 17:47:23.218 | INFO     | app.core.startup:startup_tasks:66 | 执行应用启动任务...
2025-06-17 17:47:23.220 | INFO     | app.core.startup:initialize_celery:30 | 自动启动Celery服务...
2025-06-17 17:47:23.220 | INFO     | app.core.startup:initialize_celery:55 | Celery指标收集器将在服务完全启动后手动启动
2025-06-17 17:47:23.220 | INFO     | app.core.startup:startup_tasks:71 | 应用启动任务完成
2025-06-17 17:47:23.264 | INFO     | app.core.celery_manager:_check_redis_connection:138 | Redis连接成功
2025-06-17 17:47:23.265 | INFO     | app.core.celery_manager:start_service:169 | 启动 worker 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=4 --queues=default,upload_queue,file_queue --hostname=worker@%h --logfile=logs/celery_worker.log
2025-06-17 17:47:25.274 | INFO     | app.core.celery_manager:start_service:191 | worker 服务启动成功 (PID: 27008)
2025-06-17 17:47:28.385 | INFO     | app.core.celery_manager:start_service:169 | 启动 beat 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app beat --loglevel=info --logfile=logs/celery_beat.log
2025-06-17 17:47:30.397 | INFO     | app.core.celery_manager:start_service:191 | beat 服务启动成功 (PID: 21032)
2025-06-17 17:47:32.465 | INFO     | app.core.celery_manager:_find_available_port:96 | 找到可用端口: 5555
2025-06-17 17:47:32.466 | INFO     | app.core.celery_manager:start_service:169 | 启动 flower 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app flower --port=5555 --basic_auth=admin:password --logfile=logs/celery_flower.log
2025-06-17 17:47:34.477 | INFO     | app.core.celery_manager:start_service:191 | flower 服务启动成功 (PID: 15804)
2025-06-17 17:47:35.481 | INFO     | app.core.startup:start_celery_background:40 | 所有Celery服务启动成功
2025-06-17 17:47:41.362 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:41.363 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:41.363 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:41.364 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:41.364 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:41.371 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:41.372 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:41.376 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:42.780 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:42.785 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:42.786 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:42.791 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/setup/status
2025-06-17 17:47:44.113 | INFO     | app.core.middleware:dispatch:30 | Request started: OPTIONS http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:47:44.114 | INFO     | app.core.middleware:dispatch:61 | Request completed: OPTIONS http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:47:44.115 | INFO     | app.core.middleware:dispatch:30 | Request started: POST http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:47:44.366 | INFO     | app.repositories.user:update_last_login:143 | Updated last login for user 4
2025-06-17 17:47:44.378 | INFO     | app.core.middleware:dispatch:61 | Request completed: POST http://127.0.0.1:8000/api/v1/auth/login
2025-06-17 17:47:44.778 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:44.782 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:44.783 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:44.785 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:44.786 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:44.789 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:44.791 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:44.796 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.814 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.818 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.819 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.823 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.824 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.828 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.830 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:45.832 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:47.303 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:47.332 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:47.333 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:47.337 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/auth/me
2025-06-17 17:47:47.343 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:47:47.380 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:47:47.381 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:47:47.386 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:47:47.406 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:47:47.406 | WARNING  | app.core.exceptions:http_exception_handler:150 | HTTP Exception: 404 - Not Found
2025-06-17 17:47:47.407 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5
2025-06-17 17:47:47.411 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:47:47.414 | INFO     | app.core.middleware:dispatch:30 | Request started: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:47:47.427 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/file-management/storages/5/files?path=%2F&page=1&page_size=50&sort_by=name&sort_order=asc
2025-06-17 17:47:47.474 | INFO     | app.core.middleware:dispatch:61 | Request completed: GET http://127.0.0.1:8000/api/v1/storage/
2025-06-17 17:49:31.781 | INFO     | app.services.upload_processor:stop:42 | 上传处理器停止
2025-06-17 17:49:31.781 | INFO     | app.core.startup:shutdown_tasks:76 | 执行应用关闭任务...
2025-06-17 17:49:31.781 | INFO     | app.core.startup:shutdown_tasks:82 | Celery指标收集器已停止
2025-06-17 17:49:31.782 | INFO     | app.core.startup:shutdown_tasks:88 | 停止Celery服务...
2025-06-17 17:49:31.782 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 flower 服务 (PID: 15804)
2025-06-17 17:49:31.783 | INFO     | app.core.celery_manager:stop_service:236 | flower 服务已停止
2025-06-17 17:49:32.849 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 beat 服务 (PID: 21032)
2025-06-17 17:49:32.850 | INFO     | app.core.celery_manager:stop_service:236 | beat 服务已停止
2025-06-17 17:49:34.980 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 worker 服务 (PID: 27008)
2025-06-17 17:49:34.981 | INFO     | app.core.celery_manager:stop_service:236 | worker 服务已停止
2025-06-17 17:49:38.184 | INFO     | app.core.startup:shutdown_tasks:90 | Celery服务已停止
2025-06-17 17:49:38.185 | INFO     | app.core.startup:shutdown_tasks:94 | 应用关闭任务完成
2025-06-17 17:49:38.185 | INFO     | app.plugins.auth.plugin:shutdown:44 | Auth plugin shutdown
2025-06-17 17:49:38.186 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: auth
2025-06-17 17:49:38.186 | INFO     | app.plugins.file_manager.plugin:shutdown:61 | File manager plugin shutdown
2025-06-17 17:49:38.186 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: file_manager
2025-06-17 17:49:38.187 | INFO     | app.plugins.rag_engine.plugin:shutdown:74 | RAG engine plugin shutdown
2025-06-17 17:49:38.187 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: rag_engine
2025-06-17 17:49:38.194 | INFO     | app.core.database:close_database:348 | Async database connection closed
2025-06-17 17:49:38.194 | INFO     | app.core.database:close_database:352 | Sync database connection closed
