"""
用户相关的Pydantic Schema
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator


class UserBase(BaseModel):
    """用户基础Schema"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否超级用户")
    avatar_url: Optional[str] = Field(None, max_length=255, description="头像URL")
    bio: Optional[str] = Field(None, description="个人简介")


class UserCreate(UserBase):
    """创建用户Schema"""
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    
    @validator('username')
    def validate_username(cls, v):
        if not v.isalnum() and '_' not in v and '-' not in v:
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v.lower()
    
    @validator('email')
    def validate_email(cls, v):
        return v.lower()


class UserUpdate(BaseModel):
    """更新用户Schema"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: Optional[bool] = Field(None, description="是否激活")
    is_superuser: Optional[bool] = Field(None, description="是否超级用户")
    avatar_url: Optional[str] = Field(None, max_length=255, description="头像URL")
    bio: Optional[str] = Field(None, description="个人简介")
    
    @validator('username')
    def validate_username(cls, v):
        if v is not None:
            if not v.isalnum() and '_' not in v and '-' not in v:
                raise ValueError('用户名只能包含字母、数字、下划线和连字符')
            return v.lower()
        return v
    
    @validator('email')
    def validate_email(cls, v):
        if v is not None:
            return v.lower()
        return v


class UserPasswordUpdate(BaseModel):
    """用户密码更新Schema"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('新密码和确认密码不匹配')
        return v


class UserResponse(UserBase):
    """用户响应Schema"""
    id: int = Field(..., description="用户ID")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    login_count: int = Field(0, description="登录次数")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """用户列表响应Schema"""
    users: list[UserResponse] = Field(..., description="用户列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class UserStatsResponse(BaseModel):
    """用户统计响应Schema"""
    total_users: int = Field(..., description="总用户数")
    active_users: int = Field(..., description="活跃用户数")
    inactive_users: int = Field(..., description="非活跃用户数")
    superusers: int = Field(..., description="超级用户数")
    recent_users: int = Field(..., description="最近30天注册用户数")


class UserLoginRequest(BaseModel):
    """用户登录请求Schema"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")


class UserLoginResponse(BaseModel):
    """用户登录响应Schema"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user: UserResponse = Field(..., description="用户信息")


class TokenRefreshRequest(BaseModel):
    """令牌刷新请求Schema"""
    refresh_token: str = Field(..., description="刷新令牌")


class TokenRefreshResponse(BaseModel):
    """令牌刷新响应Schema"""
    access_token: str = Field(..., description="新的访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")


class UserProfileUpdate(BaseModel):
    """用户资料更新Schema"""
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    avatar_url: Optional[str] = Field(None, max_length=255, description="头像URL")
    bio: Optional[str] = Field(None, description="个人简介")


class UserSearchRequest(BaseModel):
    """用户搜索请求Schema"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    skip: int = Field(0, ge=0, description="跳过数量")
    limit: int = Field(20, ge=1, le=100, description="限制数量")


class UserBatchOperation(BaseModel):
    """用户批量操作Schema"""
    user_ids: list[int] = Field(..., description="用户ID列表")
    operation: str = Field(..., description="操作类型: activate, deactivate, delete")
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_operations = ['activate', 'deactivate', 'delete']
        if v not in allowed_operations:
            raise ValueError(f'操作类型必须是: {", ".join(allowed_operations)}')
        return v
