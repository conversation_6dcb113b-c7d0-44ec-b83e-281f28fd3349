'use client';

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import DocumentEditorWrapper from '@/components/DocumentEditor';

const OfficeFileEditPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const encodedFileId = params.fileId as string;
  
  // 解码file_id
  const fileId = React.useMemo(() => {
    try {
      // 检查是否是有效的字符串
      if (!encodedFileId || encodedFileId.length === 0) {
        return encodedFileId;
      }

      // 先进行URL解码（处理%3D等字符）
      let decoded = decodeURIComponent(encodedFileId);

      // 然后进行base64解码
      try {
        decoded = atob(decoded);
      } catch (base64Error) {
        // 如果base64解码失败，可能是直接的文件ID
        console.warn('Base64 decode failed, using URL decoded value:', decoded);
      }

      return decoded;
    } catch (error) {
      console.error('Failed to decode file ID:', error);
      return encodedFileId; // 如果解码失败，使用原始值
    }
  }, [encodedFileId]);

  // 从fileId中提取文件信息
  const fileInfo = React.useMemo(() => {
    try {
      // fileId格式: local_storage_id_encoded_path
      const parts = fileId.split('_', 2);
      if (parts.length >= 2) {
        const encodedPath = fileId.substring(parts[0].length + parts[1].length + 2);
        const filePath = decodeURIComponent(encodedPath);
        const fileName = filePath.split('/').pop() || 'unknown';
        const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
        
        return {
          fileName,
          fileExtension
        };
      }
    } catch (error) {
      console.error('Failed to parse file info:', error);
    }
    
    return {
      fileName: 'unknown',
      fileExtension: 'txt'
    };
  }, [fileId]);

  return (
    <div className="h-screen">
      <DocumentEditorWrapper
        fileId={fileId}
        fileName={fileInfo.fileName}
        fileType={fileInfo.fileExtension}
        mode="edit"
        onClose={() => router.push('/file-manager')}
      />
    </div>
  );
};

export default OfficeFileEditPage;
