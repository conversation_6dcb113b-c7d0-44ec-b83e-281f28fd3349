"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_i18n_locales_zh-CN_ts";
exports.ids = ["_ssr_lib_i18n_locales_zh-CN_ts"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales/zh-CN.ts":
/*!***********************************!*\
  !*** ./lib/i18n/locales/zh-CN.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * 中文简体翻译文件\n */ const zhCN = {\n    common: {\n        loading: \"加载中...\",\n        error: \"错误\",\n        success: \"成功\",\n        cancel: \"取消\",\n        confirm: \"确认\",\n        back: \"返回\",\n        next: \"下一步\",\n        submit: \"提交\",\n        retry: \"重试\",\n        save: \"保存\",\n        delete: \"删除\",\n        edit: \"编辑\",\n        add: \"添加\",\n        search: \"搜索\",\n        filter: \"筛选\",\n        export: \"导出\",\n        import: \"导入\",\n        refresh: \"刷新\"\n    },\n    login: {\n        title: \"AI知识库\",\n        subtitle: \"基于人工智能的智能知识管理与检索系统\",\n        welcomeBack: \"欢迎回来\",\n        username: \"用户名\",\n        password: \"密码\",\n        rememberMe: \"记住我\",\n        forgotPassword: \"忘记密码？\",\n        loginButton: \"登录\",\n        noAccount: \"还没有账户？\",\n        signUp: \"立即注册\",\n        loginSuccess: \"登录成功！\",\n        loginError: \"登录失败\",\n        invalidCredentials: \"用户名或密码错误\",\n        networkError: \"网络连接错误，请稍后重试\",\n        usernameRequired: \"请输入用户名\",\n        passwordRequired: \"请输入密码\",\n        usernameMinLength: \"用户名至少需要3个字符\",\n        passwordMinLength: \"密码至少需要6个字符\"\n    },\n    ai: {\n        poweredBy: \"由AI驱动\",\n        intelligentSystem: \"智能系统\",\n        secureLogin: \"安全登录\",\n        aiAssistant: \"AI助手\",\n        smartAnalysis: \"智能分析\",\n        dataProtection: \"数据保护\",\n        knowledgeBase: \"知识库\",\n        intelligentRetrieval: \"智能检索\",\n        documentProcessing: \"文档处理\"\n    },\n    language: {\n        current: \"中文简体\",\n        switch: \"切换语言\",\n        chinese: \"中文简体\",\n        traditionalChinese: \"中文繁体\",\n        english: \"English\",\n        japanese: \"日本語\"\n    },\n    navigation: {\n        home: \"首页\",\n        dashboard: \"仪表板\",\n        documents: \"文件管理\",\n        knowledge: \"知识库\",\n        search: \"智能搜索\",\n        analytics: \"数据分析\",\n        settings: \"系统设置\",\n        profile: \"个人资料\",\n        logout: \"退出登录\",\n        admin: \"系统管理\"\n    },\n    dashboard: {\n        welcome: \"欢迎回来\",\n        subtitle: \"管理您的AI驱动的文档处理工作流\",\n        overview: \"概览\",\n        statistics: \"统计信息\",\n        recentActivity: \"最近活动\",\n        quickActions: \"快速操作\",\n        systemStatus: \"系统状态\",\n        userInfo: \"用户信息\",\n        totalDocuments: \"文档总数\",\n        totalQueries: \"查询总数\",\n        totalUsers: \"用户总数\",\n        systemSettings: \"系统设置\",\n        stats: {\n            documents: \"文档\",\n            queries: \"查询\",\n            users: \"用户\",\n            settings: \"设置\"\n        },\n        fileManager: {\n            description: \"上传、组织和管理您的文件库\",\n            stats: \"125 个文件\"\n        },\n        knowledge: {\n            description: \"构建和查询您的知识库\",\n            stats: \"89 个条目\"\n        },\n        search: {\n            description: \"使用AI进行智能内容搜索\",\n            stats: \"1,234 次查询\"\n        },\n        activities: {\n            documentUploaded: \"文档已上传\",\n            queryProcessed: \"查询已处理\",\n            userLoggedIn: \"用户已登录\",\n            settingsUpdated: \"设置已更新\"\n        },\n        actions: {\n            uploadDocument: \"上传文档\",\n            runQuery: \"运行查询\",\n            manageUsers: \"管理用户\",\n            systemSettings: \"系统设置\"\n        }\n    },\n    documents: {\n        title: \"文档管理\",\n        upload: \"上传文档\",\n        download: \"下载\",\n        delete: \"删除\",\n        preview: \"预览\",\n        details: \"详情\",\n        fileName: \"文件名\",\n        fileSize: \"文件大小\",\n        uploadTime: \"上传时间\",\n        fileType: \"文件类型\",\n        status: \"状态\",\n        processing: \"处理中\",\n        completed: \"已完成\",\n        failed: \"失败\",\n        uploadSuccess: \"上传成功\",\n        uploadError: \"上传失败\",\n        deleteConfirm: \"确定要删除这个文档吗？\"\n    },\n    search: {\n        title: \"智能搜索\",\n        placeholder: \"请输入搜索关键词...\",\n        results: \"搜索结果\",\n        noResults: \"未找到相关结果\",\n        searching: \"搜索中...\",\n        advanced: \"高级搜索\",\n        filters: \"筛选条件\",\n        sortBy: \"排序方式\",\n        relevance: \"相关性\",\n        date: \"日期\",\n        size: \"大小\"\n    },\n    settings: {\n        title: \"系统设置\",\n        general: \"常规设置\",\n        account: \"账户设置\",\n        security: \"安全设置\",\n        notifications: \"通知设置\",\n        language: \"语言设置\",\n        theme: \"主题设置\",\n        privacy: \"隐私设置\",\n        about: \"关于系统\"\n    },\n    errors: {\n        pageNotFound: \"页面未找到\",\n        serverError: \"服务器内部错误\",\n        networkError: \"网络连接错误\",\n        unauthorized: \"未授权访问\",\n        forbidden: \"访问被禁止\",\n        validationError: \"数据验证错误\",\n        unknownError: \"未知错误\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (zhCN);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/locales/zh-CN.ts\n");

/***/ })

};
;