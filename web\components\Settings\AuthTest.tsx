'use client';

import React, { useState } from 'react';

const AuthTest: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAuth = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // 检查token
      const token = localStorage.getItem('token');
      console.log('Token from localStorage:', token);
      
      if (!token) {
        setResult('❌ 没有找到token，请先登录');
        return;
      }
      
      // 测试认证
      const response = await fetch('/api/v1/celery/status', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ 认证成功！\n状态: ${JSON.stringify(data, null, 2)}`);
      } else {
        const errorText = await response.text();
        setResult(`❌ 认证失败 (${response.status})\n错误: ${errorText}`);
      }
      
    } catch (error) {
      console.error('Auth test error:', error);
      setResult(`❌ 请求失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // 尝试登录
      const loginData = new FormData();
      loginData.append('username', 'admin');
      loginData.append('password', 'password');
      
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        body: loginData
      });
      
      console.log('Login response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Login response:', data);
        
        if (data.access_token) {
          localStorage.setItem('token', data.access_token);
          setResult(`✅ 登录成功！\nToken: ${data.access_token.substring(0, 50)}...`);
        } else {
          setResult(`❌ 登录响应中没有token\n响应: ${JSON.stringify(data, null, 2)}`);
        }
      } else {
        const errorText = await response.text();
        setResult(`❌ 登录失败 (${response.status})\n错误: ${errorText}`);
      }
      
    } catch (error) {
      console.error('Login test error:', error);
      setResult(`❌ 登录请求失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const clearToken = () => {
    localStorage.removeItem('token');
    setResult('🗑️ Token已清除');
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-lg font-semibold mb-4">认证测试</h3>
      
      <div className="space-y-4">
        <div className="flex space-x-2">
          <button
            onClick={testLogin}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试登录'}
          </button>
          
          <button
            onClick={testAuth}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            {loading ? '测试中...' : '测试认证'}
          </button>
          
          <button
            onClick={clearToken}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            清除Token
          </button>
        </div>
        
        {result && (
          <div className="p-4 bg-gray-50 rounded border">
            <h4 className="font-medium mb-2">测试结果:</h4>
            <pre className="text-sm whitespace-pre-wrap">{result}</pre>
          </div>
        )}
        
        <div className="text-sm text-gray-600">
          <p><strong>说明:</strong></p>
          <ul className="list-disc list-inside space-y-1">
            <li>点击"测试登录"使用admin/password登录获取token</li>
            <li>点击"测试认证"验证token是否有效</li>
            <li>点击"清除Token"删除本地存储的token</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AuthTest;
