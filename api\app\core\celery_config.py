"""
Celery配置文件 - 支持动态配置
"""
import os
import asyncio
from celery import Celery
from kombu import Queue
from loguru import logger


def get_redis_url():
    """获取Redis URL"""
    # 从环境变量获取Redis配置
    REDIS_HOST = os.getenv('REDIS_HOST', '**************')
    REDIS_PORT = os.getenv('REDIS_PORT', '6379')
    REDIS_DB = os.getenv('REDIS_DB', '10')
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')

    # 构建Redis URL
    if REDIS_PASSWORD:
        return f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    else:
        return f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"


def load_dynamic_config():
    """加载动态配置"""
    try:
        # 避免循环导入，延迟导入
        import importlib

        # 检查是否可以导入配置服务
        try:
            celery_config_service_module = importlib.import_module('app.services.celery_config_service')
            celery_config_service = celery_config_service_module.celery_config_service
        except ImportError:
            logger.warning("无法导入配置服务，使用默认配置")
            return None

        # 由于这是同步函数，我们需要特殊处理异步调用
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，使用默认配置
                return None
            else:
                config = loop.run_until_complete(celery_config_service.get_active_config())
                return config
        except RuntimeError:
            # 没有事件循环，使用默认配置
            return None
    except Exception as e:
        logger.warning(f"加载动态配置失败，使用默认配置: {e}")
        return None


# 使用默认Redis URL，避免启动时的复杂依赖
REDIS_URL = get_redis_url()
logger.info(f"使用Redis URL: {REDIS_URL}")

# 创建Celery实例
celery_app = Celery(
    'ai_knowledge_base',
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=[
        'app.tasks.upload_tasks',
        'app.tasks.file_tasks',
        'app.tasks.segment_tasks',
    ]
)

# 使用默认配置，避免启动时的数据库依赖
celery_app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,

    # 任务路由
    task_routes={
        'app.tasks.upload_tasks.*': {'queue': 'upload_queue'},
        'app.tasks.file_tasks.*': {'queue': 'file_queue'},
        'app.tasks.segment_tasks.*': {'queue': 'segment_queue'},
    },

    # 队列定义
    task_default_queue='default',
    task_queues=(
        Queue('default', routing_key='default'),
        Queue('upload_queue', routing_key='upload'),
        Queue('file_queue', routing_key='file'),
        Queue('segment_queue', routing_key='segment'),
    ),

    # 任务执行配置
    task_acks_late=True,
    worker_prefetch_multiplier=int(os.getenv('CELERY_WORKER_PREFETCH_MULTIPLIER', '1')),
    task_reject_on_worker_lost=True,

    # 结果过期时间
    result_expires=int(os.getenv('CELERY_RESULT_EXPIRES', '3600')),

    # 任务超时
    task_soft_time_limit=int(os.getenv('CELERY_TASK_SOFT_TIME_LIMIT', '300')),
    task_time_limit=int(os.getenv('CELERY_TASK_TIME_LIMIT', '600')),

    # 重试配置
    task_default_retry_delay=int(os.getenv('CELERY_TASK_DEFAULT_RETRY_DELAY', '60')),
    task_max_retries=int(os.getenv('CELERY_TASK_MAX_RETRIES', '3')),

    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
)

logger.info("Celery配置已加载")


def update_celery_config(new_config_dict: dict):
    """动态更新Celery配置"""
    try:
        celery_app.conf.update(new_config_dict)
        logger.info(f"Celery配置已更新: {list(new_config_dict.keys())}")
        return True
    except Exception as e:
        logger.error(f"更新Celery配置失败: {e}")
        return False

# 自动发现任务
celery_app.autodiscover_tasks([
    'app.tasks',
])


@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
    return 'Debug task completed'


# 导出celery实例
__all__ = ['celery_app']
