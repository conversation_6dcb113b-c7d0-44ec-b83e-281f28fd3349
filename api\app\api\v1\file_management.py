"""
文件管理API路由
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, UploadFile, File, Query, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import io

from app.services.simple_file_manager import SimpleFileManagerService
from app.services.file_parser_service import file_parser_service
from app.models.file_management import StorageConfig
from app.core.database import get_async_session
from app.core.dependencies import get_current_user, get_current_user_optional
from app.models.user import User
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

router = APIRouter(prefix="/file-management", tags=["文件管理"])

# 依赖注入
def get_file_manager() -> SimpleFileManagerService:
    return SimpleFileManagerService()


# Pydantic模型
class FileOperation(BaseModel):
    source_path: str = Field(..., description="源路径")
    target_path: str = Field(..., description="目标路径")


class DirectoryCreate(BaseModel):
    path: str = Field(..., description="目录路径")


# 文件操作
@router.get("/storages/{storage_id}/files", summary="列出文件")
async def list_files(
    storage_id: int,
    path: str = Query("/", description="目录路径"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页大小"),
    sort_by: str = Query("name", description="排序字段"),
    sort_order: str = Query("asc", regex="^(asc|desc)$", description="排序方向"),
    file_type: Optional[str] = Query(None, regex="^(file|directory)$", description="文件类型过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """列出指定路径下的文件"""
    try:
        return await file_manager.list_files(
            storage_id=storage_id,
            path=path,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
            file_type=file_type,
            search=search,
            session=session
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/storages/{storage_id}/files/info", summary="获取文件信息")
async def get_file_info(
    storage_id: int,
    path: str = Query(..., description="文件路径"),
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取文件详细信息"""
    try:
        # 暂时返回基本信息
        return {
            "message": "文件信息功能暂未实现",
            "path": path,
            "storage_id": storage_id
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/storages/{storage_id}/directories", summary="创建目录")
async def create_directory(
    storage_id: int,
    directory_data: DirectoryCreate,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """创建新目录"""
    try:
        # 暂时返回成功消息
        return {"message": "目录创建功能暂未实现"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/storages/{storage_id}/files", summary="删除文件")
async def delete_file(
    storage_id: int,
    path: str = Query(..., description="文件路径"),
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """删除文件或目录"""
    try:
        return {"message": "文件删除功能暂未实现"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/storages/{storage_id}/files/move", summary="移动文件")
async def move_file(
    storage_id: int,
    operation: FileOperation,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """移动或重命名文件"""
    try:
        return {"message": "文件移动功能暂未实现"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/storages/{storage_id}/files/copy", summary="复制文件")
async def copy_file(
    storage_id: int,
    operation: FileOperation,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """复制文件"""
    try:
        return {"message": "文件复制功能暂未实现"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/storages/{storage_id}/files/upload", summary="上传文件")
async def upload_file(
    storage_id: int,
    path: str = Query(..., description="上传路径"),
    file: UploadFile = File(...),
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """上传文件"""
    try:
        content = await file.read()
        result = await file_manager.upload_file(
            storage_id=storage_id,
            file_content=content,
            file_path=path,
            session=session
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/storages/{storage_id}/files/download", summary="下载文件")
async def download_file(
    storage_id: int,
    path: str = Query(..., description="文件路径"),
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> StreamingResponse:
    """下载文件"""
    try:
        # 暂时返回错误，下载功能需要单独实现
        raise HTTPException(status_code=501, detail="文件下载功能暂未实现")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/files/{file_id:path}/content", summary="获取文件解析内容")
async def get_file_content(
    file_id: str,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """获取文件解析后的内容"""
    try:
        # 解析file_id获取存储ID和文件路径
        # file_id格式: local_storage_id_encoded_path
        parts = file_id.split('_', 2)
        if len(parts) < 3:
            raise HTTPException(status_code=400, detail="无效的文件ID格式")

        storage_type = parts[0]  # 应该是 "local"
        storage_id = int(parts[1])
        encoded_path = parts[2]

        # URL解码文件路径
        import urllib.parse
        file_path = urllib.parse.unquote(encoded_path)

        # 确保路径格式正确
        if not file_path.startswith('/'):
            file_path = '/' + file_path

        logger.info(f"解析文件内容: type={storage_type}, storage_id={storage_id}, encoded_path={encoded_path}, decoded_path={file_path}")

        # 获取文件内容
        file_content = await file_manager.get_file_content(
            storage_id=storage_id,
            file_path=file_path,
            session=session
        )

        if not file_content:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 解析文件内容
        result = file_parser_service.parse_file(
            file_path=file_path,
            file_content=file_content
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件内容失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件内容失败: {str(e)}")


@router.get("/files/{file_id:path}", summary="获取文件信息")
async def get_file_info_by_id(
    file_id: str,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """根据文件ID获取文件信息"""
    try:
        # 解析文件ID（支持Base64编码）
        storage_type, storage_id, file_path = parse_file_id(file_id)

        logger.info(f"获取文件信息: type={storage_type}, storage_id={storage_id}, decoded_path={file_path}")

        # 获取文件信息
        file_info = await file_manager.get_file_info(
            storage_id=storage_id,
            file_path=file_path,
            session=session
        )

        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")

        return file_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件信息失败: {str(e)}")


@router.get("/files/{file_id:path}/download", summary="下载文件")
async def download_file_by_id(
    file_id: str,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user_optional)
) -> StreamingResponse:
    """根据文件ID下载文件"""
    try:
        # 解析file_id
        parts = file_id.split('_', 2)
        if len(parts) < 3:
            raise HTTPException(status_code=400, detail="无效的文件ID格式")

        storage_type = parts[0]  # 应该是 "local"
        storage_id = int(parts[1])
        encoded_path = parts[2]

        # URL解码文件路径
        import urllib.parse
        file_path = urllib.parse.unquote(encoded_path)

        # 确保路径格式正确
        if not file_path.startswith('/'):
            file_path = '/' + file_path

        logger.info(f"下载文件: type={storage_type}, storage_id={storage_id}, encoded_path={encoded_path}, decoded_path={file_path}")

        # 获取文件内容
        file_content = await file_manager.get_file_content(
            storage_id=storage_id,
            file_path=file_path,
            session=session
        )

        if not file_content:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 获取文件名
        import os
        filename = os.path.basename(file_path)

        # 创建流响应
        def generate():
            yield file_content

        return StreamingResponse(
            generate(),
            media_type='application/octet-stream',
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")


@router.get("/debug/file-id/{file_id:path}", summary="调试文件ID解析")
async def debug_file_id(
    file_id: str,
    current_user: User = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """调试文件ID解析"""
    try:
        import urllib.parse

        logger.info(f"调试文件ID: {file_id}")

        # 解析file_id
        parts = file_id.split('_', 2)

        result = {
            "original_file_id": file_id,
            "parts": parts,
            "parts_count": len(parts)
        }

        if len(parts) >= 3:
            storage_type = parts[0]
            storage_id = int(parts[1])
            encoded_path = parts[2]

            # URL解码文件路径
            decoded_path = urllib.parse.unquote(encoded_path)

            # 确保路径格式正确
            if not decoded_path.startswith('/'):
                decoded_path = '/' + decoded_path

            # 清理路径用于文件系统
            clean_path = decoded_path.lstrip('/')

            result.update({
                "storage_type": storage_type,
                "storage_id": storage_id,
                "encoded_path": encoded_path,
                "decoded_path": decoded_path,
                "clean_path": clean_path
            })

            # 检查文件是否存在
            import os
            base_path = "./storage"
            full_path = os.path.join(base_path, clean_path)
            full_path = os.path.normpath(full_path)

            result.update({
                "full_path": full_path,
                "file_exists": os.path.exists(full_path),
                "is_file": os.path.isfile(full_path) if os.path.exists(full_path) else False
            })

            # 列出存储目录中的文件
            if os.path.exists(base_path):
                files_in_storage = []
                for item in os.listdir(base_path):
                    item_path = os.path.join(base_path, item)
                    if os.path.isfile(item_path):
                        files_in_storage.append(item)
                result["files_in_storage"] = files_in_storage[:10]  # 只显示前10个

        return result

    except Exception as e:
        logger.error(f"调试文件ID失败: {e}")
        return {
            "error": str(e),
            "original_file_id": file_id
        }


@router.post("/files/create", summary="创建新文件")
async def create_file(
    request: Dict[str, Any],
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """创建新文件"""
    try:
        storage_id = request.get('storage_id')
        file_path = request.get('file_path')
        file_type = request.get('file_type')
        content = request.get('content', '')

        if not all([storage_id, file_path, file_type]):
            raise HTTPException(status_code=400, detail="缺少必要参数")

        logger.info(f"创建文件: storage_id={storage_id}, path={file_path}, type={file_type}")

        # 创建文件
        result = await file_manager.create_file(
            storage_id=storage_id,
            file_path=file_path,
            file_type=file_type,
            content=content,
            session=session
        )

        return {
            "success": True,
            "message": "文件创建成功",
            "file": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建文件失败: {str(e)}")


@router.post("/storages/{storage_id}/sync", summary="同步存储")
async def sync_storage(
    storage_id: int,
    full_sync: bool = Query(False, description="是否完全同步"),
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """同步存储数据"""
    try:
        return {
            "message": "存储同步功能暂未实现",
            "storage_id": storage_id,
            "full_sync": full_sync
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 文档分段相关API
class SegmentConfig(BaseModel):
    """分段配置模型"""
    method: str = Field(default="paragraph", description="分段方法")
    max_length: int = Field(default=500, description="最大长度")
    overlap: int = Field(default=50, description="重叠长度")
    preserve_formatting: bool = Field(default=True, description="保留格式")


@router.get("/files/{file_id:path}/segments", summary="获取文件分段结果")
async def get_file_segments(
    file_id: str,
    current_user: User = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """获取文件的分段结果"""
    try:
        logger.info(f"获取文件分段结果: file_id={file_id}")

        # 解析文件ID（支持Base64编码）
        try:
            storage_type, storage_id, file_path = parse_file_id(file_id)
            logger.info(f"解析文件ID成功: type={storage_type}, storage_id={storage_id}, path={file_path}")
        except Exception as e:
            logger.warning(f"解析文件ID失败，返回空结果: {str(e)}")

        # 暂时返回空结果，表示没有分段
        return {
            "success": True,
            "segments": [],
            "total": 0,
            "message": "暂无分段结果"
        }

    except Exception as e:
        logger.error(f"获取文件分段结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分段结果失败: {str(e)}")


def parse_file_id(file_id: str) -> tuple:
    """解析文件ID，支持Base64编码和直接格式"""
    try:
        # 尝试Base64解码
        import base64
        try:
            decoded_file_id = base64.b64decode(file_id).decode('utf-8')
            logger.info(f"Base64解码成功: {file_id} -> {decoded_file_id}")
            file_id = decoded_file_id
        except Exception:
            # 如果Base64解码失败，使用原始值
            logger.info(f"使用原始文件ID: {file_id}")

        # 解析file_id获取文件信息
        parts = file_id.split('_', 2)
        if len(parts) < 3:
            raise HTTPException(status_code=400, detail="无效的文件ID格式")

        storage_type = parts[0]
        storage_id = int(parts[1])
        encoded_path = parts[2]

        # URL解码文件路径
        import urllib.parse
        file_path = urllib.parse.unquote(encoded_path)

        # 确保路径格式正确
        if not file_path.startswith('/'):
            file_path = '/' + file_path

        return storage_type, storage_id, file_path

    except Exception as e:
        logger.error(f"解析文件ID失败: {file_id}, 错误: {str(e)}")
        raise HTTPException(status_code=400, detail=f"无效的文件ID格式: {str(e)}")


@router.post("/files/{file_id:path}/segment", summary="开始文件分段")
async def start_file_segment(
    file_id: str,
    config: SegmentConfig,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """开始对文件进行分段处理"""
    try:
        logger.info(f"开始文件分段: file_id={file_id}, config={config.dict()}")

        # 解析文件ID
        storage_type, storage_id, file_path = parse_file_id(file_id)

        logger.info(f"分段处理: type={storage_type}, storage_id={storage_id}, path={file_path}")

        # 获取文件内容
        file_content = await file_manager.get_file_content(
            storage_id=storage_id,
            file_path=file_path,
            session=session
        )

        if not file_content:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 解析文件内容
        parsed_content = file_parser_service.parse_file(
            file_path=file_path,
            file_content=file_content
        )

        # 模拟分段处理
        content_text = parsed_content.get('content', '')
        if not content_text:
            raise HTTPException(status_code=400, detail="文件内容为空或无法解析")

        # 简单的分段逻辑
        segments = []
        if config.method == "paragraph":
            # 按段落分段
            paragraphs = content_text.split('\n\n')
            for i, paragraph in enumerate(paragraphs):
                if paragraph.strip():
                    segments.append({
                        "id": f"segment_{i+1}",
                        "content": paragraph.strip(),
                        "start_position": 0,  # 简化处理
                        "end_position": len(paragraph),
                        "word_count": len(paragraph),
                        "segment_index": i
                    })
        else:
            # 固定长度分段
            max_length = config.max_length
            overlap = config.overlap

            for i in range(0, len(content_text), max_length - overlap):
                segment_text = content_text[i:i + max_length]
                if segment_text.strip():
                    segments.append({
                        "id": f"segment_{len(segments)+1}",
                        "content": segment_text.strip(),
                        "start_position": i,
                        "end_position": i + len(segment_text),
                        "word_count": len(segment_text),
                        "segment_index": len(segments)
                    })

        logger.info(f"分段完成: 共生成 {len(segments)} 个分段")

        return {
            "success": True,
            "message": "分段处理完成",
            "segments": segments,
            "total": len(segments),
            "config": config.dict()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件分段失败: {e}")
        raise HTTPException(status_code=500, detail=f"分段处理失败: {str(e)}")


# 批量文件信息请求模型
class BatchFileInfoRequest(BaseModel):
    """批量文件信息请求"""
    file_ids: List[str] = Field(..., description="文件ID列表")


@router.post("/batch-info", summary="批量获取文件信息")
async def get_batch_file_info(
    request: BatchFileInfoRequest,
    file_manager: SimpleFileManagerService = Depends(get_file_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """批量获取文件信息"""
    try:
        logger.info(f"批量获取文件信息: 文件数量={len(request.file_ids)}")
        logger.info(f"文件ID列表: {request.file_ids}")

        files_info = []
        errors = []

        for file_id in request.file_ids:
            try:
                # 解析文件ID
                storage_type, storage_id, file_path = parse_file_id(file_id)
                logger.info(f"解析文件ID: {file_id} -> type={storage_type}, storage_id={storage_id}, path={file_path}")

                # 获取文件信息
                file_info = await file_manager.get_file_info(
                    storage_id=storage_id,
                    file_path=file_path,
                    session=session
                )

                if file_info:
                    # 确保文件信息包含file_id
                    file_info['file_id'] = file_id
                    files_info.append(file_info)
                    logger.info(f"成功获取文件信息: {file_path}")
                else:
                    error_msg = f"文件不存在: {file_path}"
                    logger.warning(error_msg)
                    errors.append({
                        "file_id": file_id,
                        "error": error_msg
                    })

            except Exception as e:
                error_msg = f"处理文件ID失败: {file_id}, 错误: {str(e)}"
                logger.error(error_msg)
                errors.append({
                    "file_id": file_id,
                    "error": error_msg
                })

        logger.info(f"批量获取文件信息完成: 成功={len(files_info)}, 失败={len(errors)}")

        return {
            "success": True,
            "data": files_info,
            "total": len(files_info),
            "errors": errors,
            "message": f"成功获取 {len(files_info)} 个文件信息"
        }

    except Exception as e:
        logger.error(f"批量获取文件信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量获取文件信息失败: {str(e)}")
