/* 文件查看器样式 */

.parsed-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #374151;
}

/* DOCX 内容样式 */
.docx-content h1,
.docx-content h2,
.docx-content h3,
.docx-content h4,
.docx-content h5,
.docx-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #1f2937;
}

.docx-content h1 { font-size: 2em; }
.docx-content h2 { font-size: 1.5em; }
.docx-content h3 { font-size: 1.25em; }
.docx-content h4 { font-size: 1.125em; }
.docx-content h5 { font-size: 1em; }
.docx-content h6 { font-size: 0.875em; }

.docx-content p {
  margin-bottom: 1em;
  text-align: justify;
}

.docx-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.docx-content table th,
.docx-content table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.docx-content table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.docx-content table tr:hover {
  background-color: #f9fafb;
}

/* XLSX 内容样式 */
.xlsx-content h3 {
  margin-top: 2em;
  margin-bottom: 1em;
  padding: 0.5em 1em;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  font-size: 1.125em;
}

.xlsx-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 2em;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.xlsx-content table td {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  font-size: 0.875em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.xlsx-content table tr:nth-child(even) {
  background-color: #f9fafb;
}

.xlsx-content table tr:hover {
  background-color: #eff6ff;
}

/* PPTX 内容样式 */
.pptx-content .slide {
  margin-bottom: 3em;
  padding: 2em;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3b82f6;
}

.pptx-content .slide h2 {
  margin-top: 0;
  margin-bottom: 1em;
  color: #1e40af;
  font-size: 1.5em;
  font-weight: 600;
}

.pptx-content .slide p {
  margin-bottom: 0.75em;
  font-size: 1em;
  line-height: 1.6;
}

/* PDF 内容样式 */
.pdf-content .pdf-page {
  margin-bottom: 2em;
  padding: 2em;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.pdf-content .pdf-page h3 {
  margin-top: 0;
  margin-bottom: 1em;
  color: #dc2626;
  font-size: 1.25em;
  font-weight: 600;
  border-bottom: 2px solid #fecaca;
  padding-bottom: 0.5em;
}

.pdf-content .pdf-page p {
  margin-bottom: 1em;
  text-align: justify;
  line-height: 1.7;
}

/* JSON 内容样式 */
.json-content {
  background: #1f2937;
  color: #f9fafb;
  border-radius: 8px;
  padding: 1.5em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  line-height: 1.5;
  overflow-x: auto;
}

/* XML 内容样式 */
.xml-content {
  background: #f3f4f6;
  border-radius: 8px;
  padding: 1.5em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
  line-height: 1.5;
  overflow-x: auto;
  border: 1px solid #d1d5db;
}

/* CSV 内容样式 */
.csv-content table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.csv-content table th {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 12px;
  font-weight: 600;
  text-align: left;
}

.csv-content table td {
  padding: 10px 12px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875em;
}

.csv-content table tr:nth-child(even) {
  background-color: #f0fdf4;
}

.csv-content table tr:hover {
  background-color: #dcfce7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .parsed-content {
    font-size: 14px;
  }
  
  .docx-content table,
  .xlsx-content table,
  .csv-content table {
    font-size: 0.75em;
  }
  
  .docx-content table th,
  .docx-content table td,
  .xlsx-content table td,
  .csv-content table th,
  .csv-content table td {
    padding: 6px 8px;
  }
  
  .pptx-content .slide {
    padding: 1em;
    margin-bottom: 2em;
  }
  
  .pdf-content .pdf-page {
    padding: 1em;
    margin-bottom: 1.5em;
  }
}

/* 滚动条样式 */
.parsed-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.parsed-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.parsed-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.parsed-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 打印样式 */
@media print {
  .parsed-content {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .pptx-content .slide,
  .pdf-content .pdf-page {
    page-break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .docx-content table,
  .xlsx-content table,
  .csv-content table {
    page-break-inside: avoid;
  }
}
