"""
FTP存储实现
"""

import ftplib
import io
import mimetypes
from datetime import datetime
from typing import List, Optional, Dict, Any, AsyncGenerator
from pathlib import Path

from .base import StorageInterface, FileInfo, StorageException


class FTPStorage(StorageInterface):
    """FTP存储实现"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化FTP存储
        
        Args:
            config: 配置参数
                - host: FTP服务器地址
                - port: FTP端口（默认21）
                - username: 用户名
                - password: 密码
                - base_path: 基础路径（默认/）
                - passive: 是否使用被动模式（默认True）
                - encoding: 编码（默认utf-8）
        """
        super().__init__(config)
        self.host = config.get('host')
        self.port = config.get('port', 21)
        self.username = config.get('username')
        self.password = config.get('password')
        self.base_path = config.get('base_path', '/')
        self.passive = config.get('passive', True)
        self.encoding = config.get('encoding', 'utf-8')
        
        if not all([self.host, self.username, self.password]):
            raise StorageException("Missing required FTP configuration")
        
        self.ftp = None
        self._connected = False
    
    async def connect(self) -> bool:
        """连接到FTP服务器"""
        try:
            self.ftp = ftplib.FTP()
            self.ftp.connect(self.host, self.port)
            self.ftp.login(self.username, self.password)
            self.ftp.set_pasv(self.passive)
            
            # 设置编码
            if hasattr(self.ftp, 'encoding'):
                self.ftp.encoding = self.encoding
            
            # 切换到基础目录
            if self.base_path != '/':
                self.ftp.cwd(self.base_path)
            
            self._connected = True
            return True
        except Exception as e:
            raise StorageException(f"Failed to connect to FTP: {e}")
    
    async def disconnect(self) -> None:
        """断开FTP连接"""
        if self.ftp:
            try:
                self.ftp.quit()
            except Exception:
                try:
                    self.ftp.close()
                except Exception:
                    pass
            finally:
                self.ftp = None
                self._connected = False
    
    def _get_full_path(self, path: str) -> str:
        """获取完整路径"""
        path = self.normalize_path(path)
        if self.base_path == '/':
            return path
        else:
            return self.normalize_path(self.base_path + path)
    
    async def list_files(self, path: str = "/", recursive: bool = False) -> List[FileInfo]:
        """列出目录下的文件"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        full_path = self._get_full_path(path)
        files = []
        
        try:
            # 保存当前目录
            current_dir = self.ftp.pwd()
            
            try:
                self.ftp.cwd(full_path)
                
                if recursive:
                    files = await self._list_recursive(path)
                else:
                    files = await self._list_directory(path)
            finally:
                # 恢复原目录
                self.ftp.cwd(current_dir)
        
        except ftplib.error_perm as e:
            if "550" in str(e):  # 目录不存在
                raise StorageException(f"Directory does not exist: {path}")
            else:
                raise StorageException(f"Permission denied: {e}")
        except Exception as e:
            raise StorageException(f"Failed to list files: {e}")
        
        return files
    
    async def _list_directory(self, path: str) -> List[FileInfo]:
        """列出单个目录"""
        files = []
        
        try:
            # 使用MLSD命令获取详细信息
            try:
                for name, facts in self.ftp.mlsd():
                    if name in ['.', '..']:
                        continue
                    
                    file_info = self._create_file_info_from_mlsd(path, name, facts)
                    if file_info:
                        files.append(file_info)
            except ftplib.error_perm:
                # 如果MLSD不支持，使用LIST命令
                lines = []
                self.ftp.retrlines('LIST', lines.append)
                
                for line in lines:
                    file_info = self._parse_list_line(path, line)
                    if file_info:
                        files.append(file_info)
        
        except Exception as e:
            raise StorageException(f"Failed to list directory: {e}")
        
        return files
    
    async def _list_recursive(self, path: str) -> List[FileInfo]:
        """递归列出所有文件"""
        all_files = []
        
        # 先获取当前目录的文件
        current_files = await self._list_directory(path)
        all_files.extend(current_files)
        
        # 递归处理子目录
        for file_info in current_files:
            if file_info.is_directory:
                sub_files = await self._list_recursive(file_info.path)
                all_files.extend(sub_files)
        
        return all_files
    
    def _create_file_info_from_mlsd(self, parent_path: str, name: str, facts: Dict[str, str]) -> Optional[FileInfo]:
        """从MLSD结果创建文件信息"""
        try:
            file_path = self.normalize_path(parent_path + "/" + name)
            is_directory = facts.get('type', '').lower() == 'dir'
            size = int(facts.get('size', '0')) if not is_directory else 0
            
            # 解析修改时间
            modified_at = None
            if 'modify' in facts:
                try:
                    modified_at = datetime.strptime(facts['modify'], '%Y%m%d%H%M%S')
                except ValueError:
                    pass
            
            return FileInfo(
                path=file_path,
                name=name,
                size=size,
                is_directory=is_directory,
                modified_at=modified_at,
                mime_type=mimetypes.guess_type(name)[0] if not is_directory else None,
                extension=Path(name).suffix.lower() if not is_directory else ""
            )
        except Exception:
            return None
    
    def _parse_list_line(self, parent_path: str, line: str) -> Optional[FileInfo]:
        """解析LIST命令的输出行"""
        try:
            parts = line.split()
            if len(parts) < 9:
                return None
            
            permissions = parts[0]
            is_directory = permissions.startswith('d')
            size = int(parts[4]) if not is_directory else 0
            name = ' '.join(parts[8:])  # 文件名可能包含空格
            
            if name in ['.', '..']:
                return None
            
            file_path = self.normalize_path(parent_path + "/" + name)
            
            # 尝试解析修改时间
            modified_at = None
            try:
                month_str = parts[5]
                day_str = parts[6]
                time_or_year = parts[7]
                
                # 简单的时间解析（可能需要更复杂的逻辑）
                current_year = datetime.now().year
                if ':' in time_or_year:
                    # 当年的文件，包含时间
                    time_str = time_or_year
                    modified_at = datetime.strptime(f"{current_year} {month_str} {day_str} {time_str}", "%Y %b %d %H:%M")
                else:
                    # 往年的文件，只有年份
                    year = int(time_or_year)
                    modified_at = datetime.strptime(f"{year} {month_str} {day_str}", "%Y %b %d")
            except Exception:
                pass
            
            return FileInfo(
                path=file_path,
                name=name,
                size=size,
                is_directory=is_directory,
                modified_at=modified_at,
                mime_type=mimetypes.guess_type(name)[0] if not is_directory else None,
                extension=Path(name).suffix.lower() if not is_directory else ""
            )
        except Exception:
            return None
    
    async def get_file_info(self, path: str) -> Optional[FileInfo]:
        """获取文件信息"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        full_path = self._get_full_path(path)
        
        try:
            # 尝试获取文件大小
            try:
                size = self.ftp.size(full_path)
                is_directory = False
            except ftplib.error_perm:
                # 可能是目录，尝试切换到该目录
                current_dir = self.ftp.pwd()
                try:
                    self.ftp.cwd(full_path)
                    self.ftp.cwd(current_dir)
                    size = 0
                    is_directory = True
                except ftplib.error_perm:
                    return None
            
            # 获取修改时间
            modified_at = None
            try:
                mdtm_response = self.ftp.sendcmd(f'MDTM {full_path}')
                if mdtm_response.startswith('213'):
                    time_str = mdtm_response[4:].strip()
                    modified_at = datetime.strptime(time_str, '%Y%m%d%H%M%S')
            except Exception:
                pass
            
            name = Path(path).name
            
            return FileInfo(
                path=path,
                name=name,
                size=size,
                is_directory=is_directory,
                modified_at=modified_at,
                mime_type=mimetypes.guess_type(name)[0] if not is_directory else None,
                extension=Path(name).suffix.lower() if not is_directory else ""
            )
        
        except Exception:
            return None
    
    async def file_exists(self, path: str) -> bool:
        """检查文件是否存在"""
        return await self.get_file_info(path) is not None
    
    async def create_directory(self, path: str) -> bool:
        """创建目录"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        full_path = self._get_full_path(path)
        
        try:
            self.ftp.mkd(full_path)
            return True
        except ftplib.error_perm as e:
            if "550" in str(e):  # 目录已存在
                return True
            else:
                raise StorageException(f"Failed to create directory: {e}")
    
    async def delete_file(self, path: str) -> bool:
        """删除文件或目录"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        full_path = self._get_full_path(path)
        
        try:
            file_info = await self.get_file_info(path)
            if not file_info:
                return False
            
            if file_info.is_directory:
                # 递归删除目录内容
                files = await self.list_files(path, recursive=False)
                for file in files:
                    await self.delete_file(file.path)
                
                # 删除空目录
                self.ftp.rmd(full_path)
            else:
                # 删除文件
                self.ftp.delete(full_path)
            
            return True
        except Exception as e:
            raise StorageException(f"Failed to delete file: {e}")
    
    async def move_file(self, source_path: str, target_path: str) -> bool:
        """移动/重命名文件"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        source_full = self._get_full_path(source_path)
        target_full = self._get_full_path(target_path)
        
        try:
            self.ftp.rename(source_full, target_full)
            return True
        except Exception as e:
            raise StorageException(f"Failed to move file: {e}")
    
    async def copy_file(self, source_path: str, target_path: str) -> bool:
        """复制文件（FTP不直接支持，需要下载后上传）"""
        try:
            # 读取源文件内容
            content = await self.read_file(source_path)
            # 写入目标文件
            return await self.write_file(target_path, content)
        except Exception as e:
            raise StorageException(f"Failed to copy file: {e}")
    
    async def upload_file(self, local_path: str, remote_path: str) -> bool:
        """上传文件"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        remote_full = self._get_full_path(remote_path)
        
        try:
            with open(local_path, 'rb') as f:
                self.ftp.storbinary(f'STOR {remote_full}', f)
            return True
        except Exception as e:
            raise StorageException(f"Failed to upload file: {e}")
    
    async def download_file(self, remote_path: str, local_path: str) -> bool:
        """下载文件"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        remote_full = self._get_full_path(remote_path)
        
        try:
            with open(local_path, 'wb') as f:
                self.ftp.retrbinary(f'RETR {remote_full}', f.write)
            return True
        except Exception as e:
            raise StorageException(f"Failed to download file: {e}")
    
    async def read_file(self, path: str) -> bytes:
        """读取文件内容"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        full_path = self._get_full_path(path)
        
        try:
            bio = io.BytesIO()
            self.ftp.retrbinary(f'RETR {full_path}', bio.write)
            return bio.getvalue()
        except Exception as e:
            raise StorageException(f"Failed to read file: {e}")
    
    async def write_file(self, path: str, content: bytes) -> bool:
        """写入文件内容"""
        if not self.ftp:
            raise StorageException("Not connected to FTP")
        
        full_path = self._get_full_path(path)
        
        try:
            bio = io.BytesIO(content)
            self.ftp.storbinary(f'STOR {full_path}', bio)
            return True
        except Exception as e:
            raise StorageException(f"Failed to write file: {e}")
    
    async def get_file_stream(self, path: str) -> AsyncGenerator[bytes, None]:
        """获取文件流"""
        # FTP不支持流式读取，一次性读取整个文件
        content = await self.read_file(path)

        # 分块返回
        chunk_size = 8192
        for i in range(0, len(content), chunk_size):
            yield content[i:i + chunk_size]
    
    async def is_connected(self) -> bool:
        """检查连接状态"""
        if not self._connected or not self.ftp:
            return False
        
        try:
            self.ftp.pwd()
            return True
        except Exception:
            return False
