'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft, Scissors, Brain, Zap, Loader2, CheckCircle,
  AlertCircle, FileText, Settings, Plus, Trash2, FolderOpen,
  X, Search
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiClient from '@/lib/api';

// 接口定义
interface FileInfo {
  file_id: string;
  file_name: string;
  file_path: string;
  file_size: number;
  file_size_formatted: string;
  file_extension: string;
  file_type: string;
  storage_type: string;
  created_at: string;
  updated_at: string;
}

interface SegmentConfig {
  method: 'paragraph' | 'sentence' | 'fixed_length' | 'semantic';
  max_length: number;
  overlap: number;
  preserve_formatting: boolean;
  normalize_text: boolean;
  extract_keywords: boolean;
  remove_stopwords: boolean;
  language: string;
}

interface StorageInfo {
  id: string;
  name: string;
  type: string;
  base_path: string;
}

interface FileProgress {
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  segments?: number;
  error_message?: string;
}

const BatchSegmentPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 解析URL参数
  const fileIds = React.useMemo(() => {
    const filesParam = searchParams.get('files');
    if (!filesParam) return [];

    // 分割文件ID并过滤空值
    const ids = filesParam.split(',').filter(Boolean);
    console.log('批量分段页面 - 接收到的URL参数:', filesParam);
    console.log('批量分段页面 - 解析的文件IDs:', ids);
    return ids;
  }, [searchParams]);

  // 检查是否是查看现有任务
  const existingTaskId = searchParams.get('taskId');
  const isViewingExistingTask = !!existingTaskId;

  // 基础状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [files, setFiles] = useState<FileInfo[]>([]);
  
  // 任务状态
  const [taskStatus, setTaskStatus] = useState<'idle' | 'running' | 'completed'>('idle');
  const [taskId, setTaskId] = useState<string>('');
  const [fileProgress, setFileProgress] = useState<Record<string, FileProgress>>({});

  // 任务配置
  const [taskName, setTaskName] = useState('');
  const [config, setConfig] = useState<SegmentConfig>({
    method: 'paragraph',
    max_length: 500,
    overlap: 50,
    preserve_formatting: true,
    normalize_text: true,
    extract_keywords: true,
    remove_stopwords: false,
    language: 'zh'
  });

  // 文件选择相关状态
  const [showFileSelector, setShowFileSelector] = useState(false);
  const [availableFiles, setAvailableFiles] = useState<FileInfo[]>([]);
  const [loadingAvailableFiles, setLoadingAvailableFiles] = useState(false);
  const [fileSearchQuery, setFileSearchQuery] = useState('');
  const [selectedStorage, setSelectedStorage] = useState('');
  const [storageList, setStorageList] = useState<StorageInfo[]>([]);

  // 初始化
  useEffect(() => {
    if (existingTaskId) {
      // 加载现有任务
      loadExistingTask(existingTaskId);
    } else if (fileIds.length > 0) {
      // 新建任务模式
      loadFiles();
      // 生成默认任务名称
      const now = new Date();
      const timestamp = now.toLocaleString('zh-CN');
      setTaskName(`批量分段任务 - ${timestamp}`);
    }
    loadStorageList();
  }, [fileIds, existingTaskId]);

  // 加载现有任务
  const loadExistingTask = async (taskId: string) => {
    try {
      setLoading(true);
      setError(null);

      // 获取任务信息
      const taskResponse = await apiClient.get(`/api/v1/document-segment/tasks/${taskId}`);
      const taskData = taskResponse.data?.data || taskResponse.data;

      if (taskData) {
        // 设置任务信息
        setTaskName(taskData.task_name);
        setTaskId(taskData.task_id);

        // 设置分段配置
        setConfig({
          method: taskData.segment_method || 'paragraph',
          max_length: taskData.max_length || 500,
          overlap: taskData.overlap || 50,
          preserve_formatting: taskData.preserve_formatting !== false,
          normalize_text: taskData.normalize_text !== false,
          extract_keywords: taskData.extract_keywords !== false,
          remove_stopwords: taskData.remove_stopwords === true,
          language: taskData.language || 'zh'
        });

        // 设置任务状态
        if (taskData.status === 'completed') {
          setTaskStatus('completed');
        } else if (taskData.status === 'processing') {
          setTaskStatus('running');
          // 开始轮询进度
          startProgressPolling(taskData.task_id);
        } else {
          setTaskStatus('idle');
        }

        // 加载文件信息
        if (taskData.file_ids && taskData.file_ids.length > 0) {
          const fileResponse = await apiClient.post('/api/v1/file-management/batch-info', {
            file_ids: taskData.file_ids
          });
          const fileData = fileResponse.data?.data || fileResponse.data || [];
          setFiles(fileData);
        }

        // 获取任务进度
        const progressResponse = await apiClient.get(`/api/v1/document-segment/tasks/${taskId}/progress`);
        const progressData = progressResponse.data?.data || progressResponse.data;

        if (progressData.file_progress) {
          setFileProgress(progressData.file_progress);
        }
      }

    } catch (err: any) {
      console.error('Failed to load existing task:', err);
      setError(err.response?.data?.detail || '加载任务信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载存储列表
  const loadStorageList = async () => {
    try {
      const response = await apiClient.get('/api/v1/storage-management');
      const data = response.data?.data || response.data || [];
      setStorageList(data);
      if (data.length > 0) {
        setSelectedStorage(data[0].id);
      }
    } catch (err) {
      console.error('Failed to load storage list:', err);
    }
  };

  // 加载文件信息
  const loadFiles = async () => {
    if (fileIds.length === 0) {
      console.log('批量分段页面 - 没有文件ID，跳过加载');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('批量分段页面 - 开始加载文件信息，文件IDs:', fileIds);

      const response = await apiClient.post('/api/v1/file-management/batch-info', {
        file_ids: fileIds
      });

      console.log('批量分段页面 - API响应:', response.data);

      const data = response.data?.data || response.data || [];
      console.log('批量分段页面 - 解析的文件数据:', data);

      setFiles(data);

      if (data.length === 0) {
        setError('未找到指定的文件，请检查文件是否存在');
      }

    } catch (err: any) {
      console.error('批量分段页面 - 加载文件失败:', err);
      console.error('批量分段页面 - 错误详情:', err.response?.data);
      setError(err.response?.data?.detail || '加载文件信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载可选文件列表
  const loadAvailableFiles = async (storageId: string, search: string = '') => {
    try {
      setLoadingAvailableFiles(true);
      const params = new URLSearchParams({
        storage_id: storageId,
        page: '1',
        page_size: '50'
      });
      
      if (search) {
        params.append('search', search);
      }

      const response = await apiClient.get(`/api/v1/file-management?${params.toString()}`);
      const data = response.data?.data || response.data || {};
      setAvailableFiles(data.files || []);
    } catch (err) {
      console.error('Failed to load available files:', err);
    } finally {
      setLoadingAvailableFiles(false);
    }
  };

  // 添加文件到选择列表
  const addFilesToSelection = (selectedFiles: FileInfo[]) => {
    const newFiles = selectedFiles.filter(
      newFile => !files.some(existingFile => existingFile.file_id === newFile.file_id)
    );
    
    if (newFiles.length > 0) {
      setFiles(prev => [...prev, ...newFiles]);
      
      // 更新URL参数
      const allFileIds = [...files, ...newFiles].map(f => f.file_id);
      const newUrl = `/file-manager/segment/batch?files=${allFileIds.join(',')}`;
      router.replace(newUrl);
    }
    
    setShowFileSelector(false);
  };

  // 移除文件
  const removeFile = (fileId: string) => {
    const newFiles = files.filter(f => f.file_id !== fileId);
    setFiles(newFiles);
    
    // 更新URL参数
    if (newFiles.length > 0) {
      const newUrl = `/file-manager/segment/batch?files=${newFiles.map(f => f.file_id).join(',')}`;
      router.replace(newUrl);
    } else {
      router.push('/file-manager');
    }
  };

  // 开始分段任务
  const startSegmentTask = async () => {
    if (!taskName.trim()) {
      setError('请输入任务名称');
      return;
    }

    if (files.length === 0) {
      setError('请选择要分段的文件');
      return;
    }

    try {
      setTaskStatus('running');
      setError(null);

      const taskData = {
        task_name: taskName,
        description: '',
        file_ids: files.map(f => f.file_id),
        config: config
      };

      const response = await apiClient.post('/api/v1/document-segment/tasks', taskData);
      const data = response.data?.data || response.data;
      
      setTaskId(data.task_id);

      // 初始化文件进度
      const initialProgress: Record<string, FileProgress> = {};
      files.forEach(file => {
        initialProgress[file.file_id] = {
          status: 'pending',
          progress: 0
        };
      });
      setFileProgress(initialProgress);

      // 开始轮询任务状态
      startProgressPolling(data.task_id);

    } catch (err: any) {
      console.error('Failed to start segment task:', err);
      setError(err.response?.data?.detail || '启动分段任务失败');
      setTaskStatus('idle');
    }
  };

  // 轮询任务进度
  const startProgressPolling = (taskId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await apiClient.get(`/api/v1/document-segment/tasks/${taskId}/progress`);
        const data = response.data?.data || response.data;

        console.log('轮询任务进度:', data); // 调试日志

        // 更新文件进度
        if (data.file_progress) {
          setFileProgress(data.file_progress);
        }

        // 检查任务状态
        if (data.task) {
          const taskStatus = data.task.status;
          const taskProgress = data.task.progress || 0;

          console.log('任务状态:', taskStatus, '进度:', taskProgress); // 调试日志

          if (taskStatus === 'completed') {
            clearInterval(pollInterval);
            setTaskStatus('completed');

            toast.success(`AI分段处理完成！共生成 ${data.task.total_segments || 0} 个分段`);

            // 刷新文件列表
            await loadFiles();

          } else if (taskStatus === 'failed') {
            clearInterval(pollInterval);
            setTaskStatus('idle');

            const errorMsg = data.task.error_message || '分段任务失败';
            setError(errorMsg);
            toast.error(`分段处理失败: ${errorMsg}`);

          } else if (taskStatus === 'processing') {
            // 任务正在处理中，保持运行状态
            setTaskStatus('running');
          }
        }

      } catch (err) {
        console.error('Failed to get task progress:', err);
        toast.error('获取任务进度失败，请检查网络连接');
      }
    }, 2000);

    // 10分钟后停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
      if (taskStatus === 'running') {
        setTaskStatus('idle');
        toast('任务监控超时，请手动刷新页面查看结果', { icon: '⚠️' });
      }
    }, 600000);
  };

  // 返回文件管理
  const handleBack = () => {
    router.push('/file-manager');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">加载文件信息中...</p>
        </div>
      </div>
    );
  }

  // 计算统计指标
  const getSegmentStats = () => {
    const totalFiles = files.length;
    const pendingFiles = Object.values(fileProgress).filter(p => p.status === 'pending').length;
    const processingFiles = Object.values(fileProgress).filter(p => p.status === 'processing').length;
    const completedFiles = Object.values(fileProgress).filter(p => p.status === 'completed').length;
    const failedFiles = Object.values(fileProgress).filter(p => p.status === 'error').length;
    const totalSegments = Object.values(fileProgress).reduce((sum, p) => sum + (p.segments || 0), 0);

    return {
      totalFiles,
      pendingFiles,
      processingFiles,
      completedFiles,
      failedFiles,
      totalSegments
    };
  };

  const stats = getSegmentStats();

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex overflow-hidden">
      {/* 主要内容区域 - 全屏布局 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：导航、任务配置和分段配置 */}
        <div className="w-96 flex-shrink-0 bg-white/60 backdrop-blur-lg border-r border-white/30 overflow-y-auto">
          {/* 页面导航区域 */}
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center space-x-4 mb-4">
              <button
                onClick={handleBack}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>

              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Scissors className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    AI智能批量分段
                  </h1>
                  <p className="text-sm text-gray-500 flex items-center space-x-1">
                    <Brain className="w-3 h-3 text-purple-500" />
                    <span>{files.length} 个文件待处理</span>
                    {taskStatus === 'running' && (
                      <>
                        <span>•</span>
                        <span className="text-blue-600 font-medium">处理中...</span>
                      </>
                    )}
                    {taskStatus === 'completed' && (
                      <>
                        <span>•</span>
                        <span className="text-green-600 font-medium">已完成</span>
                      </>
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 任务配置区域 */}
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Settings className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">任务配置</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  任务名称 *
                </label>
                <input
                  type="text"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                  disabled={taskStatus !== 'idle'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm"
                  placeholder="请输入任务名称"
                />
              </div>
            </div>
          </div>

          {/* 分段配置区域 */}
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <Scissors className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">分段配置</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  分段方式
                </label>
                <select
                  value={config.method}
                  onChange={(e) => setConfig({...config, method: e.target.value as any})}
                  disabled={taskStatus !== 'idle'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm"
                >
                  <option value="paragraph">按段落分段</option>
                  <option value="sentence">按句子分段</option>
                  <option value="fixed_length">固定长度分段</option>
                  <option value="semantic">语义分段</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大长度
                  </label>
                  <input
                    type="number"
                    value={config.max_length}
                    onChange={(e) => setConfig({...config, max_length: parseInt(e.target.value) || 500})}
                    disabled={taskStatus !== 'idle'}
                    min="100"
                    max="2000"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm"
                    placeholder="500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    重叠长度
                  </label>
                  <input
                    type="number"
                    value={config.overlap}
                    onChange={(e) => setConfig({...config, overlap: parseInt(e.target.value) || 50})}
                    disabled={taskStatus !== 'idle'}
                    min="0"
                    max="500"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm"
                    placeholder="50"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  语言
                </label>
                <select
                  value={config.language}
                  onChange={(e) => setConfig({...config, language: e.target.value})}
                  disabled={taskStatus !== 'idle'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm"
                >
                  <option value="zh">中文</option>
                  <option value="en">英文</option>
                  <option value="auto">自动检测</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.preserve_formatting}
                    onChange={(e) => setConfig({...config, preserve_formatting: e.target.checked})}
                    disabled={taskStatus !== 'idle'}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">保留格式</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.normalize_text}
                    onChange={(e) => setConfig({...config, normalize_text: e.target.checked})}
                    disabled={taskStatus !== 'idle'}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">文本标准化</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.extract_keywords}
                    onChange={(e) => setConfig({...config, extract_keywords: e.target.checked})}
                    disabled={taskStatus !== 'idle'}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">提取关键词</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.remove_stopwords}
                    onChange={(e) => setConfig({...config, remove_stopwords: e.target.checked})}
                    disabled={taskStatus !== 'idle'}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">移除停用词</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：文件列表 */}
        <div className="flex-1 flex flex-col bg-white/40 backdrop-blur-lg overflow-hidden">
          {/* 文件列表头部 - 带统计指标和操作按钮 */}
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900">文件分段统计</h3>
                  <p className="text-sm text-gray-500">
                    实时监控分段进度和状态
                  </p>
                </div>
              </div>

              {/* 操作按钮区域 */}
              <div className="flex items-center space-x-3">
                {/* 添加文件按钮 */}
                <button
                  onClick={() => {
                    setShowFileSelector(true);
                    if (selectedStorage) {
                      loadAvailableFiles(selectedStorage);
                    }
                  }}
                  disabled={taskStatus === 'running'}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                >
                  <Plus className="w-4 h-4" />
                  <span>添加文件</span>
                </button>

                {taskStatus === 'running' && (
                  <div className="flex items-center space-x-2 px-4 py-2 bg-blue-100/50 rounded-xl">
                    <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                    <span className="text-sm font-medium text-blue-700">AI处理中</span>
                  </div>
                )}

                {taskStatus === 'completed' && (
                  <div className="flex items-center space-x-2 px-4 py-2 bg-green-100/50 rounded-xl">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-700">处理完成</span>
                  </div>
                )}

                {taskStatus === 'idle' && (
                  <button
                    onClick={startSegmentTask}
                    disabled={files.length === 0 || !taskName.trim()}
                    className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                  >
                    <Zap className="w-4 h-4" />
                    <span>开始AI分段</span>
                  </button>
                )}
              </div>
            </div>

            {/* 统计指标卡片 - 优化高度 */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
              {/* 文件总数 */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">文件总数</p>
                    <p className="text-xl font-bold text-blue-900">{stats.totalFiles}</p>
                  </div>
                  <div className="w-7 h-7 bg-blue-500 rounded-lg flex items-center justify-center">
                    <FileText className="w-3.5 h-3.5 text-white" />
                  </div>
                </div>
              </div>

              {/* 分段总数 */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-3 border border-purple-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-purple-600 uppercase tracking-wide">分段总数</p>
                    <p className="text-xl font-bold text-purple-900">{stats.totalSegments}</p>
                  </div>
                  <div className="w-7 h-7 bg-purple-500 rounded-lg flex items-center justify-center">
                    <Scissors className="w-3.5 h-3.5 text-white" />
                  </div>
                </div>
              </div>

              {/* 待分段 */}
              <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-3 border border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">待分段</p>
                    <p className="text-xl font-bold text-gray-900">{stats.pendingFiles}</p>
                  </div>
                  <div className="w-7 h-7 bg-gray-500 rounded-lg flex items-center justify-center">
                    <AlertCircle className="w-3.5 h-3.5 text-white" />
                  </div>
                </div>
              </div>

              {/* 分段中 */}
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-3 border border-yellow-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-yellow-600 uppercase tracking-wide">分段中</p>
                    <p className="text-xl font-bold text-yellow-900">{stats.processingFiles}</p>
                  </div>
                  <div className="w-7 h-7 bg-yellow-500 rounded-lg flex items-center justify-center">
                    <Loader2 className="w-3.5 h-3.5 text-white animate-spin" />
                  </div>
                </div>
              </div>

              {/* 已完成 */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-green-600 uppercase tracking-wide">已完成</p>
                    <p className="text-xl font-bold text-green-900">{stats.completedFiles}</p>
                  </div>
                  <div className="w-7 h-7 bg-green-500 rounded-lg flex items-center justify-center">
                    <CheckCircle className="w-3.5 h-3.5 text-white" />
                  </div>
                </div>
              </div>

              {/* 分段失败 */}
              <div className="bg-gradient-to-r from-red-50 to-rose-50 rounded-lg p-3 border border-red-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-red-600 uppercase tracking-wide">分段失败</p>
                    <p className="text-xl font-bold text-red-900">{stats.failedFiles}</p>
                  </div>
                  <div className="w-7 h-7 bg-red-500 rounded-lg flex items-center justify-center">
                    <X className="w-3.5 h-3.5 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 文件列表内容 */}
          <div className="flex-1 overflow-y-auto p-6">
            {files.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg font-medium mb-2">暂无文件</p>
                  <p className="text-gray-400 text-sm">请点击右上角的"添加文件"按钮来选择要分段的文件</p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {files.map((file) => {
                  const progress = fileProgress[file.file_id];

                  return (
                    <motion.div
                      key={file.file_id}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <FileText className="w-5 h-5 text-white" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-gray-900 truncate" title={file.file_name}>
                              {file.file_name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {file.file_size_formatted} • {file.file_extension.toUpperCase()}
                            </p>
                          </div>
                        </div>

                        {taskStatus === 'idle' && (
                          <button
                            onClick={() => removeFile(file.file_id)}
                            className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors"
                            title="移除文件"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>

                      {/* 文件信息 */}
                      <div className="mb-3">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                          <span>存储类型: {file.storage_type}</span>
                          <span>{new Date(file.created_at).toLocaleDateString('zh-CN')}</span>
                        </div>
                      </div>

                      {/* 进度条和状态 */}
                      {progress && (
                        <div className="mt-3 pt-3 border-t border-gray-200/50">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs font-medium text-gray-700">
                              {progress.status === 'pending' && '等待处理'}
                              {progress.status === 'processing' && '处理中...'}
                              {progress.status === 'completed' && `已完成 (${progress.segments || 0} 段)`}
                              {progress.status === 'error' && '处理失败'}
                            </span>
                            <span className="text-xs text-gray-500">{progress.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${
                                progress.status === 'error' ? 'bg-red-500' :
                                progress.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                              }`}
                              style={{ width: `${progress.progress}%` }}
                            />
                          </div>
                          {progress.error_message && (
                            <p className="text-xs text-red-600 mt-1">{progress.error_message}</p>
                          )}
                        </div>
                      )}
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 文件选择弹窗 */}
      {showFileSelector && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
          >
            {/* 弹窗头部 */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <FolderOpen className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">选择文件</h3>
                </div>
                <button
                  onClick={() => setShowFileSelector(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* 搜索和筛选 */}
              <div className="mt-4 flex items-center space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      value={fileSearchQuery}
                      onChange={(e) => setFileSearchQuery(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          loadAvailableFiles(selectedStorage, fileSearchQuery);
                        }
                      }}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="搜索文件名..."
                    />
                  </div>
                </div>

                <select
                  value={selectedStorage}
                  onChange={(e) => {
                    setSelectedStorage(e.target.value);
                    loadAvailableFiles(e.target.value, fileSearchQuery);
                  }}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {storageList.map((storage) => (
                    <option key={storage.id} value={storage.id}>
                      {storage.name}
                    </option>
                  ))}
                </select>

                <button
                  onClick={() => loadAvailableFiles(selectedStorage, fileSearchQuery)}
                  disabled={loadingAvailableFiles}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  {loadingAvailableFiles ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    '搜索'
                  )}
                </button>
              </div>
            </div>

            {/* 文件列表 */}
            <div className="p-6 max-h-96 overflow-y-auto">
              {loadingAvailableFiles ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-blue-600 mr-2" />
                  <span className="text-gray-600">加载文件中...</span>
                </div>
              ) : availableFiles.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                  <p className="text-gray-500">没有找到文件</p>
                </div>
              ) : (
                <FileSelector
                  files={availableFiles}
                  selectedFiles={[]}
                  onSelectionChange={(selected) => addFilesToSelection(selected)}
                  excludeFileIds={files.map(f => f.file_id)}
                />
              )}
            </div>
          </motion.div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className="absolute bottom-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg max-w-md">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        </div>
      )}
    </div>
  );
};

// 文件选择器组件
interface FileSelectorProps {
  files: FileInfo[];
  selectedFiles: FileInfo[];
  onSelectionChange: (selected: FileInfo[]) => void;
  excludeFileIds: string[];
}

const FileSelector: React.FC<FileSelectorProps> = ({
  files,
  selectedFiles,
  onSelectionChange,
  excludeFileIds
}) => {
  const [selected, setSelected] = useState<FileInfo[]>(selectedFiles);

  const toggleFile = (file: FileInfo) => {
    const isSelected = selected.some(f => f.file_id === file.file_id);
    if (isSelected) {
      setSelected(selected.filter(f => f.file_id !== file.file_id));
    } else {
      setSelected([...selected, file]);
    }
  };

  const handleConfirm = () => {
    onSelectionChange(selected);
  };

  const availableFiles = files.filter(file => !excludeFileIds.includes(file.file_id));

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
        {availableFiles.map((file) => {
          const isSelected = selected.some(f => f.file_id === file.file_id);

          return (
            <div
              key={file.file_id}
              onClick={() => toggleFile(file)}
              className={`p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-4 h-4 border-2 rounded ${
                  isSelected ? 'bg-blue-600 border-blue-600' : 'border-gray-300'
                }`}>
                  {isSelected && (
                    <CheckCircle className="w-4 h-4 text-white" />
                  )}
                </div>
                <FileText className="w-4 h-4 text-gray-400" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{file.file_name}</p>
                  <p className="text-xs text-gray-500">
                    {file.file_size_formatted} • {file.file_extension.toUpperCase()}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {selected.length > 0 && (
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <span className="text-sm text-gray-600">已选择 {selected.length} 个文件</span>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            确认添加
          </button>
        </div>
      )}
    </div>
  );
};

export default BatchSegmentPage;
