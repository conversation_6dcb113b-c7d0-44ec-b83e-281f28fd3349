'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Folder, 
  File, 
  Image, 
  Video, 
  FileText, 
  Archive,
  Music,
  Code,
  Download,
  Eye,
  MoreHorizontal
} from 'lucide-react';

import FileListView from './FileListView';
import FileGridView from './FileGridView';
import ModernFileListView from './ModernFileListView';
import ModernPagination from './ModernPagination';
import LoadingSpinner from '@/components/LoadingSpinner';
import FileTypeIcon, { getFileTypeDescription } from './FileTypeIcon';
import apiClient from '@/lib/api';

interface FileItem {
  file_id: string;
  file_path: string;
  file_name: string;
  file_type: 'file' | 'directory';
  file_size: number;
  file_size_formatted: string;
  mime_type?: string;
  file_extension: string;
  created_at: string;
  modified_at: string;
  is_directory: boolean;
  is_image: boolean;
  is_video: boolean;
  is_document: boolean;
}

interface FileExplorerProps {
  storageId: number | null;
  currentPath: string;
  onPathChange: (path: string) => void;
  viewMode: 'list' | 'grid';
  selectedFiles: string[];
  onSelectionChange: (files: string[]) => void;
  searchQuery: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  isLoading: boolean;
  onLoadingChange: (loading: boolean) => void;
  refreshTrigger?: number;
}

const FileExplorer: React.FC<FileExplorerProps> = ({
  storageId,
  currentPath,
  onPathChange,
  viewMode,
  selectedFiles,
  onSelectionChange,
  searchQuery,
  sortBy,
  sortOrder,
  isLoading,
  onLoadingChange,
  refreshTrigger
}) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    page_size: 50,
    total: 0,
    pages: 0
  });

  // 获取文件图标
  const getFileIcon = (file: FileItem) => {
    if (file.is_directory) {
      return (
        <FileTypeIcon
          fileName={file.file_name}
          fileType={file.file_type}
          isDirectory={file.is_directory}
          className="w-5 h-5"
          size={20}
        />
      );
    }

    // 对于文件，统一使用FileTypeIcon组件
    return (
      <FileTypeIcon
        fileName={file.file_name}
        fileType={file.file_type}
        isDirectory={false}
        className="w-5 h-5"
        size={20}
      />
    );
  };

  // 加载文件列表
  const loadFiles = useCallback(async () => {
    if (!storageId) {
      setFiles([]);
      return;
    }

    try {
      onLoadingChange(true);
      setError(null);

      const params = new URLSearchParams({
        path: currentPath,
        page: pagination.page.toString(),
        page_size: pagination.page_size.toString(),
        sort_by: sortBy,
        sort_order: sortOrder,
      });

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      const response = await apiClient.get(
        `/api/v1/file-management/storages/${storageId}/files?${params}`
      );

      const data = response.data;
      setFiles(data.files || []);
      setPagination(data.pagination || pagination);
    } catch (err) {
      console.error('Failed to load files:', err);
      setError(err instanceof Error ? err.message : '加载文件失败');
      setFiles([]);
    } finally {
      onLoadingChange(false);
    }
  }, [storageId, currentPath, pagination.page, pagination.page_size, sortBy, sortOrder, searchQuery, onLoadingChange]);

  // 监听依赖变化，重新加载文件
  useEffect(() => {
    loadFiles();
  }, [loadFiles]);

  // 监听refreshTrigger变化
  useEffect(() => {
    if (refreshTrigger !== undefined && refreshTrigger > 0) {
      loadFiles();
    }
  }, [refreshTrigger, loadFiles]);

  // 检查是否为Office文件
  const isOfficeFile = (file: FileItem) => {
    const officeExtensions = ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'txt', 'md', 'markdown'];
    return officeExtensions.includes(file.file_extension.toLowerCase());
  };

  // 处理文件双击
  const handleFileDoubleClick = (file: FileItem) => {
    if (file.is_directory) {
      onPathChange(file.file_path);
    } else {
      // 根据文件类型选择操作
      if (isOfficeFile(file)) {
        // Office文件直接打开编辑器查看
        const encodedFileId = btoa(file.file_id);
        window.open(`/file-manager/office/view/${encodedFileId}`, '_blank');
      } else {
        // 其他文件预览
        handleFilePreview(file);
      }
    }
  };

  // 处理文件选择
  const handleFileSelect = (file: FileItem, isSelected: boolean) => {
    if (isSelected) {
      onSelectionChange([...selectedFiles, file.file_path]);
    } else {
      onSelectionChange(selectedFiles.filter(path => path !== file.file_path));
    }
  };

  // 处理全选
  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(files.map(file => file.file_path));
    }
  };

  // 处理文件预览
  const handleFilePreview = (file: FileItem) => {
    if (isOfficeFile(file)) {
      // Office文件使用新的编辑器查看
      const encodedFileId = btoa(file.file_id);
      window.open(`/file-manager/office/view/${encodedFileId}`, '_blank');
    } else {
      // 其他文件的预览逻辑
      console.log('Preview file:', file);
      // 可以在这里添加图片、视频等文件的预览逻辑
      if (file.is_image) {
        // 图片文件可以直接在新窗口打开
        const encodedFileId = btoa(file.file_id);
        window.open(`/file-manager/view/${encodedFileId}`, '_blank');
      } else if (file.is_video) {
        // 视频文件也可以在新窗口打开
        const encodedFileId = btoa(file.file_id);
        window.open(`/file-manager/view/${encodedFileId}`, '_blank');
      } else {
        // 其他文件类型的处理
        const encodedFileId = btoa(file.file_id);
        window.open(`/file-manager/view/${encodedFileId}`, '_blank');
      }
    }
  };

  // 处理文件下载
  const handleFileDownload = async (file: FileItem) => {
    if (!storageId) return;

    try {
      const response = await apiClient.get(
        `/api/v1/file-management/storages/${storageId}/files/download?path=${encodeURIComponent(file.file_path)}`,
        { responseType: 'blob' }
      );

      const blob = response.data;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.file_name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Download failed:', err);
      // 可以显示错误提示
    }
  };

  // 渲染加载状态
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <LoadingSpinner size="lg" text="加载文件中..." />
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">加载失败</div>
          <div className="text-gray-600 mb-4">{error}</div>
          <button
            onClick={loadFiles}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  // 渲染空状态
  if (!storageId) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <Folder className="w-16 h-16 mx-auto mb-4 text-gray-300" />
          <div className="text-lg font-medium mb-2">请选择存储</div>
          <div>选择一个存储来浏览文件</div>
        </div>
      </div>
    );
  }

  if (files.length === 0 && !searchQuery) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <Folder className="w-16 h-16 mx-auto mb-4 text-gray-300" />
          <div className="text-lg font-medium mb-2">文件夹为空</div>
          <div>此文件夹中没有任何文件或文件夹</div>
        </div>
      </div>
    );
  }

  if (files.length === 0 && searchQuery) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <File className="w-16 h-16 mx-auto mb-4 text-gray-300" />
          <div className="text-lg font-medium mb-2">未找到匹配的文件</div>
          <div>尝试使用不同的搜索关键词</div>
        </div>
      </div>
    );
  }

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    setPagination(prev => ({ ...prev, page_size: pageSize, page: 1 }));
  };

  // 渲染文件列表
  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* 文件视图 */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'list' ? (
          <ModernFileListView
            files={files}
            selectedFiles={selectedFiles}
            onFileSelect={handleFileSelect}
            onFileDoubleClick={handleFileDoubleClick}
            onSelectAll={handleSelectAll}
            getFileIcon={getFileIcon}
            onFileDownload={handleFileDownload}
            onFilePreview={handleFilePreview}
            currentPage={pagination.page}
            totalPages={pagination.pages}
            pageSize={pagination.page_size}
            totalItems={pagination.total}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        ) : (
          <FileGridView
            files={files}
            selectedFiles={selectedFiles}
            onFileSelect={handleFileSelect}
            onFileDoubleClick={handleFileDoubleClick}
            getFileIcon={getFileIcon}
            onFileDownload={handleFileDownload}
            onFilePreview={handleFilePreview}
          />
        )}
      </div>

      {/* 现代化分页控件 */}
      {pagination.pages > 1 && (
        <ModernPagination
          currentPage={pagination.page}
          totalPages={pagination.pages}
          pageSize={pagination.page_size}
          totalItems={pagination.total}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}
    </div>
  );
};

export default FileExplorer;
