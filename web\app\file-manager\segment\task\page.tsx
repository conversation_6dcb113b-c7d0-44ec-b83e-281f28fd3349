'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  Activity,
  Server,
  Cpu,
  MemoryStick,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Loader2,
  Play,
  Pause,
  RotateCcw,
  Settings,
  Eye,
  Trash2,
  FileText,
  Scissors,
  Brain,
  Zap,
  TrendingUp,
  Users,
  Database,
  RefreshCw
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import apiClient from '@/lib/api';

interface CeleryStatus {
  is_connected: boolean;
  active_workers: number;
  total_workers: number;
  workers: Array<{
    name: string;
    status: string;
    active_tasks: number;
    processed_tasks: number;
    load_avg: number[];
  }>;
  queues: Array<{
    name: string;
    length: number;
    consumers: number;
  }>;
  broker_info: {
    transport: string;
    hostname: string;
    port: number;
    virtual_host: string;
  };
  performance: {
    tasks_per_minute: number;
    avg_task_duration: number;
    memory_usage: number;
    cpu_usage: number;
  };
}

interface SegmentTask {
  id: number;
  task_id: string;
  task_name: string;
  description: string;
  status: string;
  progress: number;
  total_files: number;
  processed_files: number;
  total_segments: number;
  total_vectors: number;
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  file_ids: string[];
  segment_method: string;
  max_length: number;
  overlap: number;
  language: string;
}

const SegmentTaskManagePage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [celeryStatus, setCeleryStatus] = useState<CeleryStatus | null>(null);
  const [tasks, setTasks] = useState<SegmentTask[]>([]);
  const [selectedTask, setSelectedTask] = useState<SegmentTask | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // 加载Celery状态
  const loadCeleryStatus = async () => {
    try {
      const response = await apiClient.get('/api/v1/system/celery/status');
      setCeleryStatus(response.data.data);
    } catch (error) {
      console.error('获取Celery状态失败:', error);
      toast.error('获取Celery状态失败');
    }
  };

  // 加载分段任务列表
  const loadTasks = async () => {
    try {
      const response = await apiClient.get('/api/v1/document-segment/tasks');
      setTasks(response.data.data || []);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      toast.error('获取任务列表失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      setLoading(true);
      await Promise.all([
        loadCeleryStatus(),
        loadTasks()
      ]);
      setLoading(false);
    };

    initData();
  }, []);

  // 定时刷新
  useEffect(() => {
    const interval = setInterval(() => {
      loadCeleryStatus();
      loadTasks();
    }, 5000); // 每5秒刷新一次

    return () => clearInterval(interval);
  }, []);

  // 手动刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadCeleryStatus(),
      loadTasks()
    ]);
    setRefreshing(false);
    toast.success('数据已刷新');
  };

  // 返回文件管理
  const handleBack = () => {
    router.push('/file-manager');
  };

  // 查看任务详情
  const handleViewTask = (task: SegmentTask) => {
    router.push(`/file-manager/segment/batch?taskId=${task.task_id}`);
  };

  // 删除任务
  const handleDeleteTask = async (taskId: string) => {
    if (!confirm('确定要删除这个任务吗？此操作不可恢复。')) {
      return;
    }

    try {
      await apiClient.delete(`/api/v1/document-segment/tasks/${taskId}`);
      await loadTasks();
      toast.success('任务已删除');
    } catch (error) {
      console.error('删除任务失败:', error);
      toast.error('删除任务失败');
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'processing':
        return 'text-blue-600 bg-blue-50';
      case 'failed':
        return 'text-red-600 bg-red-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'failed':
        return <XCircle className="w-4 h-4" />;
      case 'pending':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'failed':
        return '失败';
      case 'pending':
        return '等待中';
      default:
        return '未知';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">加载任务管理数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex overflow-hidden">
      {/* 左侧：Celery状态面板 */}
      <div className="w-96 flex-shrink-0 bg-white/60 backdrop-blur-lg border-r border-white/30 overflow-y-auto">
        {/* 页面导航 */}
        <div className="p-6 border-b border-gray-200/50">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={handleBack}
              className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>

            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Activity className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  分段任务管理
                </h1>
                <p className="text-sm text-gray-500 flex items-center space-x-1">
                  <Brain className="w-3 h-3 text-purple-500" />
                  <span>Celery状态监控与任务管理</span>
                </p>
              </div>
            </div>
          </div>

          {/* 刷新按钮 */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>{refreshing ? '刷新中...' : '刷新数据'}</span>
          </button>
        </div>

        {/* Celery连接状态 */}
        <div className="p-6 border-b border-gray-200/50">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Server className="w-4 h-4 text-white" />
            </div>
            <h3 className="text-lg font-bold text-gray-900">Celery状态</h3>
          </div>

          {celeryStatus ? (
            <div className="space-y-3">
              {/* 连接状态 */}
              <div className="flex items-center justify-between p-3 bg-white/70 rounded-lg border border-gray-100">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${celeryStatus.is_connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm font-medium text-gray-700">连接状态</span>
                </div>
                <span className={`text-sm font-semibold ${celeryStatus.is_connected ? 'text-green-600' : 'text-red-600'}`}>
                  {celeryStatus.is_connected ? '已连接' : '未连接'}
                </span>
              </div>

              {/* Worker状态 */}
              <div className="flex items-center justify-between p-3 bg-white/70 rounded-lg border border-gray-100">
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium text-gray-700">活跃Worker</span>
                </div>
                <span className="text-sm font-semibold text-gray-900">
                  {celeryStatus.active_workers}/{celeryStatus.total_workers}
                </span>
              </div>

              {/* 性能指标 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-white/50 rounded border border-gray-50">
                  <span className="text-xs text-gray-600">任务/分钟</span>
                  <span className="text-xs font-medium text-gray-900">{celeryStatus.performance?.tasks_per_minute || 0}</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/50 rounded border border-gray-50">
                  <span className="text-xs text-gray-600">平均耗时</span>
                  <span className="text-xs font-medium text-gray-900">{celeryStatus.performance?.avg_task_duration || 0}s</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/50 rounded border border-gray-50">
                  <span className="text-xs text-gray-600">内存使用</span>
                  <span className="text-xs font-medium text-gray-900">{celeryStatus.performance?.memory_usage || 0}%</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/50 rounded border border-gray-50">
                  <span className="text-xs text-gray-600">CPU使用</span>
                  <span className="text-xs font-medium text-gray-900">{celeryStatus.performance?.cpu_usage || 0}%</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <XCircle className="w-12 h-12 text-red-400 mx-auto mb-3" />
              <p className="text-sm text-gray-500">无法获取Celery状态</p>
            </div>
          )}
        </div>

        {/* Worker详情 */}
        {celeryStatus && celeryStatus.workers && celeryStatus.workers.length > 0 && (
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <Cpu className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">Worker详情</h3>
            </div>

            <div className="space-y-3">
              {celeryStatus.workers.map((worker, index) => (
                <div key={index} className="p-3 bg-white/70 rounded-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-900 truncate" title={worker.name}>
                      {worker.name.split('@')[0]}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      worker.status === 'online' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                    }`}>
                      {worker.status}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div>活跃: {worker.active_tasks}</div>
                    <div>已处理: {worker.processed_tasks}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 队列状态 */}
        {celeryStatus && celeryStatus.queues && celeryStatus.queues.length > 0 && (
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                <Database className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-lg font-bold text-gray-900">队列状态</h3>
            </div>

            <div className="space-y-2">
              {celeryStatus.queues.map((queue, index) => (
                <div key={index} className="p-3 bg-white/70 rounded-lg border border-gray-100">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-900">{queue.name}</span>
                    <span className="text-xs text-gray-600">{queue.consumers} 消费者</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-600">队列长度</span>
                    <span className="text-sm font-semibold text-blue-600">{queue.length}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 右侧：任务列表 */}
      <div className="flex-1 flex flex-col bg-white/40 backdrop-blur-lg overflow-hidden">
        {/* 任务列表头部 */}
        <div className="p-6 border-b border-gray-200/50">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Scissors className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">分段任务列表</h3>
                <p className="text-sm text-gray-500">
                  共 {tasks.length} 个任务
                </p>
              </div>
            </div>

            {/* 新建任务按钮 */}
            <button
              onClick={() => router.push('/file-manager/segment/batch')}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <Zap className="w-4 h-4" />
              <span>新建任务</span>
            </button>
          </div>

          {/* 任务统计卡片 */}
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">总任务</p>
                  <p className="text-xl font-bold text-blue-900">{tasks.length}</p>
                </div>
                <div className="w-7 h-7 bg-blue-500 rounded-lg flex items-center justify-center">
                  <FileText className="w-3.5 h-3.5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-green-600 uppercase tracking-wide">已完成</p>
                  <p className="text-xl font-bold text-green-900">
                    {tasks.filter(t => t.status === 'completed').length}
                  </p>
                </div>
                <div className="w-7 h-7 bg-green-500 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-3.5 h-3.5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-3 border border-yellow-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-yellow-600 uppercase tracking-wide">处理中</p>
                  <p className="text-xl font-bold text-yellow-900">
                    {tasks.filter(t => t.status === 'processing').length}
                  </p>
                </div>
                <div className="w-7 h-7 bg-yellow-500 rounded-lg flex items-center justify-center">
                  <Loader2 className="w-3.5 h-3.5 text-white animate-spin" />
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-red-50 to-rose-50 rounded-lg p-3 border border-red-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-red-600 uppercase tracking-wide">失败</p>
                  <p className="text-xl font-bold text-red-900">
                    {tasks.filter(t => t.status === 'failed').length}
                  </p>
                </div>
                <div className="w-7 h-7 bg-red-500 rounded-lg flex items-center justify-center">
                  <XCircle className="w-3.5 h-3.5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 任务列表内容 */}
        <div className="flex-1 overflow-y-auto p-6">
          {tasks.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Scissors className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg font-medium mb-2">暂无分段任务</p>
                <p className="text-gray-400 text-sm mb-4">点击右上角的"新建任务"按钮创建第一个分段任务</p>
                <button
                  onClick={() => router.push('/file-manager/segment/batch')}
                  className="inline-flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200"
                >
                  <Zap className="w-4 h-4" />
                  <span>新建任务</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {tasks.map((task) => (
                <motion.div
                  key={task.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-6 hover:shadow-lg transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-semibold text-gray-900 truncate" title={task.task_name}>
                          {task.task_name}
                        </h4>
                        <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                          {getStatusIcon(task.status)}
                          <span>{getStatusText(task.status)}</span>
                        </span>
                      </div>
                      {task.description && (
                        <p className="text-sm text-gray-600 mb-3">{task.description}</p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => handleViewTask(task)}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="查看详情"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteTask(task.task_id)}
                        className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="删除任务"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* 任务进度 */}
                  {task.status === 'processing' && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">处理进度</span>
                        <span className="text-sm text-gray-600">{task.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* 任务统计 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-gray-900">{task.total_files}</div>
                      <div className="text-xs text-gray-600">总文件数</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-600">{task.processed_files}</div>
                      <div className="text-xs text-gray-600">已处理</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-600">{task.total_segments}</div>
                      <div className="text-xs text-gray-600">分段数</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">{task.total_vectors}</div>
                      <div className="text-xs text-gray-600">向量数</div>
                    </div>
                  </div>

                  {/* 任务配置信息 */}
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                    <span>方法: {task.segment_method}</span>
                    <span>长度: {task.max_length}</span>
                    <span>重叠: {task.overlap}</span>
                    <span>语言: {task.language}</span>
                  </div>

                  {/* 时间信息 */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-4">
                      <span>创建: {new Date(task.created_at).toLocaleString('zh-CN')}</span>
                      {task.started_at && (
                        <span>开始: {new Date(task.started_at).toLocaleString('zh-CN')}</span>
                      )}
                      {task.completed_at && (
                        <span>完成: {new Date(task.completed_at).toLocaleString('zh-CN')}</span>
                      )}
                    </div>
                    <span className="text-gray-400">ID: {task.task_id.slice(0, 8)}...</span>
                  </div>

                  {/* 错误信息 */}
                  {task.error_message && (
                    <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-700">{task.error_message}</p>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SegmentTaskManagePage;
