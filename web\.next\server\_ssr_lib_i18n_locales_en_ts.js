"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_i18n_locales_en_ts";
exports.ids = ["_ssr_lib_i18n_locales_en_ts"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales/en.ts":
/*!********************************!*\
  !*** ./lib/i18n/locales/en.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * English translation file\n */ const en = {\n    common: {\n        loading: \"Loading...\",\n        error: \"Error\",\n        success: \"Success\",\n        cancel: \"Cancel\",\n        confirm: \"Confirm\",\n        back: \"Back\",\n        next: \"Next\",\n        submit: \"Submit\",\n        retry: \"Retry\",\n        save: \"Save\",\n        delete: \"Delete\",\n        edit: \"Edit\",\n        add: \"Add\",\n        search: \"Search\",\n        filter: \"Filter\",\n        export: \"Export\",\n        import: \"Import\",\n        refresh: \"Refresh\"\n    },\n    login: {\n        title: \"AI Knowledge Base\",\n        subtitle: \"AI-Powered Intelligent Knowledge Management & Retrieval System\",\n        welcomeBack: \"Welcome Back\",\n        username: \"Username\",\n        password: \"Password\",\n        rememberMe: \"Remember me\",\n        forgotPassword: \"Forgot password?\",\n        loginButton: \"Sign In\",\n        noAccount: \"Don't have an account?\",\n        signUp: \"Sign Up\",\n        loginSuccess: \"Login successful!\",\n        loginError: \"Login failed\",\n        invalidCredentials: \"Invalid username or password\",\n        networkError: \"Network error, please try again later\",\n        usernameRequired: \"Username is required\",\n        passwordRequired: \"Password is required\",\n        usernameMinLength: \"Username must be at least 3 characters\",\n        passwordMinLength: \"Password must be at least 6 characters\"\n    },\n    ai: {\n        poweredBy: \"Powered by AI\",\n        intelligentSystem: \"Intelligent System\",\n        secureLogin: \"Secure Login\",\n        aiAssistant: \"AI Assistant\",\n        smartAnalysis: \"Smart Analysis\",\n        dataProtection: \"Data Protection\",\n        knowledgeBase: \"Knowledge Base\",\n        intelligentRetrieval: \"Intelligent Retrieval\",\n        documentProcessing: \"Document Processing\"\n    },\n    language: {\n        current: \"English\",\n        switch: \"Switch Language\",\n        chinese: \"中文简体\",\n        traditionalChinese: \"中文繁體\",\n        english: \"English\",\n        japanese: \"日本語\"\n    },\n    navigation: {\n        home: \"Home\",\n        dashboard: \"Dashboard\",\n        documents: \"File Manager\",\n        knowledge: \"Knowledge Base\",\n        search: \"Smart Search\",\n        analytics: \"Analytics\",\n        settings: \"Settings\",\n        profile: \"Profile\",\n        logout: \"Logout\",\n        admin: \"Administration\"\n    },\n    dashboard: {\n        welcome: \"Welcome back\",\n        subtitle: \"Manage your AI-powered document processing workflow\",\n        overview: \"Overview\",\n        statistics: \"Statistics\",\n        recentActivity: \"Recent Activity\",\n        quickActions: \"Quick Actions\",\n        systemStatus: \"System Status\",\n        userInfo: \"User Information\",\n        totalDocuments: \"Total Documents\",\n        totalQueries: \"Total Queries\",\n        totalUsers: \"Total Users\",\n        systemSettings: \"System Settings\",\n        stats: {\n            documents: \"Documents\",\n            queries: \"Queries\",\n            users: \"Users\",\n            settings: \"Settings\"\n        },\n        fileManager: {\n            description: \"Upload, organize and manage your file library\",\n            stats: \"125 files\"\n        },\n        knowledge: {\n            description: \"Build and query your knowledge base\",\n            stats: \"89 entries\"\n        },\n        search: {\n            description: \"Use AI for intelligent content search\",\n            stats: \"1,234 queries\"\n        },\n        activities: {\n            documentUploaded: \"Document uploaded\",\n            queryProcessed: \"Query processed\",\n            userLoggedIn: \"User logged in\",\n            settingsUpdated: \"Settings updated\"\n        },\n        actions: {\n            uploadDocument: \"Upload Document\",\n            runQuery: \"Run Query\",\n            manageUsers: \"Manage Users\",\n            systemSettings: \"System Settings\"\n        }\n    },\n    documents: {\n        title: \"Document Management\",\n        upload: \"Upload Document\",\n        download: \"Download\",\n        delete: \"Delete\",\n        preview: \"Preview\",\n        details: \"Details\",\n        fileName: \"File Name\",\n        fileSize: \"File Size\",\n        uploadTime: \"Upload Time\",\n        fileType: \"File Type\",\n        status: \"Status\",\n        processing: \"Processing\",\n        completed: \"Completed\",\n        failed: \"Failed\",\n        uploadSuccess: \"Upload successful\",\n        uploadError: \"Upload failed\",\n        deleteConfirm: \"Are you sure you want to delete this document?\"\n    },\n    search: {\n        title: \"Smart Search\",\n        placeholder: \"Enter search keywords...\",\n        results: \"Search Results\",\n        noResults: \"No results found\",\n        searching: \"Searching...\",\n        advanced: \"Advanced Search\",\n        filters: \"Filters\",\n        sortBy: \"Sort By\",\n        relevance: \"Relevance\",\n        date: \"Date\",\n        size: \"Size\"\n    },\n    settings: {\n        title: \"Settings\",\n        general: \"General\",\n        account: \"Account\",\n        security: \"Security\",\n        notifications: \"Notifications\",\n        language: \"Language\",\n        theme: \"Theme\",\n        privacy: \"Privacy\",\n        about: \"About\"\n    },\n    errors: {\n        pageNotFound: \"Page Not Found\",\n        serverError: \"Internal Server Error\",\n        networkError: \"Network Connection Error\",\n        unauthorized: \"Unauthorized Access\",\n        forbidden: \"Access Forbidden\",\n        validationError: \"Validation Error\",\n        unknownError: \"Unknown Error\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (en);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/locales/en.ts\n");

/***/ })

};
;