"""
日志配置模块
使用loguru进行结构化日志记录
"""

import sys
from pathlib import Path
from loguru import logger
from app.core.config import Settings


def setup_logging(settings: Settings) -> None:
    """
    设置应用日志配置
    
    Args:
        settings: 应用配置
    """
    # 移除默认处理器
    logger.remove()
    
    # 日志格式
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 控制台日志
    logger.add(
        sys.stdout,
        format=log_format,
        level=settings.LOG_LEVEL,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 文件日志 - 所有日志（添加进程ID避免冲突）
    import os
    process_id = os.getpid()

    logger.add(
        log_dir / f"app_{process_id}.log",
        format=log_format,
        level="DEBUG",
        rotation="10 MB",  # 改为按大小轮转，避免时间轮转冲突
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True,
        enqueue=True,  # 使用队列避免多进程冲突
    )

    # 文件日志 - 错误日志
    logger.add(
        log_dir / f"error_{process_id}.log",
        format=log_format,
        level="ERROR",
        rotation="10 MB",
        retention="90 days",
        compression="zip",
        backtrace=True,
        diagnose=True,
        enqueue=True,
    )
    
    # 生产环境额外配置
    if settings.is_production:
        # 生产环境不输出诊断信息
        logger.remove()
        logger.add(
            sys.stdout,
            format=log_format,
            level=settings.LOG_LEVEL,
            colorize=False,
            backtrace=False,
            diagnose=False,
        )
        
        # 生产环境文件日志
        logger.add(
            log_dir / f"production_{process_id}.log",
            format=log_format,
            level="INFO",
            rotation="50 MB",
            retention="90 days",
            compression="zip",
            backtrace=False,
            diagnose=False,
            enqueue=True,
        )
    
    # 设置第三方库日志级别
    import logging
    
    # 设置uvicorn日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # 设置数据库日志级别
    if not settings.DATABASE_ECHO:
        logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    
    logger.info(f"Logging configured for {settings.ENVIRONMENT} environment")


def get_logger(name: str = None):
    """
    获取logger实例
    
    Args:
        name: logger名称
        
    Returns:
        logger实例
    """
    if name:
        return logger.bind(name=name)
    return logger
