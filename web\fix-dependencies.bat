@echo off
echo 修复前端依赖和配置问题...
echo.

cd /d "%~dp0"

echo 1. 清理旧的依赖和缓存...
if exist node_modules (
    echo 删除 node_modules 目录...
    rmdir /s /q node_modules
)

if exist .next (
    echo 删除 .next 目录...
    rmdir /s /q .next
)

if exist pnpm-lock.yaml (
    echo 删除 pnpm-lock.yaml 文件...
    del pnpm-lock.yaml
)

if exist package-lock.json (
    echo 删除 package-lock.json 文件...
    del package-lock.json
)

echo.
echo 2. 检查配置文件...
if exist next.config.ts (
    echo 发现 next.config.ts，Next.js 14 不支持，已删除...
    del next.config.ts
)

if exist postcss.config.mjs (
    echo 发现错误的 postcss.config.mjs，已删除...
    del postcss.config.mjs
)

echo.
echo 3. 检查字体配置...
echo ✅ 字体问题已修复: <PERSON>eist → Inter + JetBrains Mono
echo ✅ layout.tsx 已更新为兼容字体
echo ✅ globals.css 字体变量已修复
echo ✅ tailwind.config.js 字体配置已添加

echo.
echo 4. 检查导入路径...
echo ✅ 导入路径已修复: @/lib/api-client → @/lib/api
echo ✅ useStorageInfo.ts 导入已修复
echo ✅ StorageOverview.tsx 导入已修复
echo ✅ SystemStatus.tsx 导入已修复

echo 确认配置文件正确...
if exist next.config.js (
    echo ✅ next.config.js 存在
) else (
    echo ❌ next.config.js 缺失
)

if exist postcss.config.js (
    echo ✅ postcss.config.js 存在
) else (
    echo ❌ postcss.config.js 缺失
)

if exist tailwind.config.js (
    echo ✅ tailwind.config.js 存在
) else (
    echo ❌ tailwind.config.js 缺失
)

echo.
echo 5. 重新安装依赖...
echo 使用 pnpm 安装依赖包...
pnpm install

if errorlevel 1 (
    echo.
    echo pnpm 安装失败，尝试使用 npm...
    npm install
)

echo.
echo 6. 验证安装...
if exist node_modules (
    echo ✅ 依赖安装成功！
    echo.
    echo 可以运行以下命令启动开发服务器:
    echo pnpm dev
    echo 或者
    echo npm run dev
    echo.
    echo 如果仍有问题，请检查:
    echo - Node.js 版本 (建议 16.0.0+)
    echo - pnpm 版本 (建议 8.0.0+)
    echo - 网络连接状态
) else (
    echo ❌ 依赖安装失败！
    echo 请检查网络连接和包管理器配置。
)

echo.
pause
