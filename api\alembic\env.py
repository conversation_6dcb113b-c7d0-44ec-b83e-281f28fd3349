"""
Alembic环境配置
支持多数据库类型的迁移
"""

import asyncio
from logging.config import fileConfig
from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config
from alembic import context

from app.core.config import get_settings
from app.core.database import DatabaseConfig
from app.models.base import Base

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def get_database_url():
    """获取数据库URL"""
    settings = get_settings()
    database_url = settings.DATABASE_URL
    
    # 转换为同步URL用于Alembic
    return DatabaseConfig.get_sync_url(database_url)


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """运行迁移"""
    context.configure(
        connection=connection, 
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """Run migrations in 'online' mode with async engine."""
    
    # 获取数据库配置
    settings = get_settings()
    database_url = settings.DATABASE_URL
    sync_url = DatabaseConfig.get_sync_url(database_url)
    
    # 获取引擎配置
    engine_kwargs = DatabaseConfig.get_engine_kwargs(database_url, is_async=False)
    engine_kwargs['echo'] = settings.DATABASE_ECHO
    
    # 创建配置字典
    configuration = {
        'sqlalchemy.url': sync_url,
        'sqlalchemy.echo': settings.DATABASE_ECHO,
    }
    
    # 添加数据库特定配置
    db_type = DatabaseConfig.get_database_type(database_url)
    if db_type == 'sqlite':
        configuration.update({
            'sqlalchemy.poolclass': 'sqlalchemy.pool.StaticPool',
            'sqlalchemy.connect_args': '{"check_same_thread": False}',
        })
    elif db_type in ['postgresql', 'mysql', 'oracle']:
        configuration.update({
            'sqlalchemy.pool_size': str(engine_kwargs.get('pool_size', 10)),
            'sqlalchemy.max_overflow': str(engine_kwargs.get('max_overflow', 20)),
            'sqlalchemy.pool_timeout': str(engine_kwargs.get('pool_timeout', 30)),
        })

    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
