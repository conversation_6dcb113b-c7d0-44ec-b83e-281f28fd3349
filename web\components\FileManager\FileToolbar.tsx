'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  Home,
  Search,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Upload,
  FolderPlus,
  RefreshCw,
  MoreHorizontal,
  Filter,
  Download,
  Trash2,
  Copy,
  Move,
  Settings,
  FolderOpen,
  Plus
} from 'lucide-react';
import StorageSelector from './StorageSelector';

interface FileToolbarProps {
  currentPath: string;
  onPathChange: (path: string) => void;
  viewMode: 'list' | 'grid';
  onViewModeChange: (mode: 'list' | 'grid') => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  sortBy: string;
  onSortByChange: (sortBy: string) => void;
  sortOrder: 'asc' | 'desc';
  onSortOrderChange: (order: 'asc' | 'desc') => void;
  selectedFiles: string[];
  onRefresh: () => void;
  currentStorage: number | null;
  onStorageChange: (storageId: number | null) => void;
  onCreateFile?: () => void;
}

const FileToolbar: React.FC<FileToolbarProps> = ({
  currentPath,
  onPathChange,
  viewMode,
  onViewModeChange,
  searchQuery,
  onSearchChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrderChange,
  selectedFiles,
  onRefresh,
  currentStorage,
  onStorageChange,
  onCreateFile
}) => {
  const router = useRouter();
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);

  // 路径导航
  const pathParts = currentPath.split('/').filter(Boolean);
  
  const navigateToParent = () => {
    if (currentPath !== '/') {
      const parentPath = '/' + pathParts.slice(0, -1).join('/');
      onPathChange(parentPath === '/' ? '/' : parentPath);
    }
  };

  const navigateToPath = (index: number) => {
    if (index === -1) {
      onPathChange('/');
    } else {
      const newPath = '/' + pathParts.slice(0, index + 1).join('/');
      onPathChange(newPath);
    }
  };

  return (
    <div className="bg-white border-b border-gray-200">
      {/* 主工具栏 */}
      <div className="flex items-center justify-between px-4 py-3">
        {/* 左侧：存储选择器和导航控件 */}
        <div className="flex items-center space-x-4">
          {/* 存储选择器 */}
          <StorageSelector
            currentStorage={currentStorage}
            onStorageChange={onStorageChange}
          />

          {/* 导航控件 */}
          <div className="flex items-center space-x-2">
            {/* 返回/前进按钮 */}
            <div className="flex items-center space-x-1">
            <button
              onClick={() => window.history.back()}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
              title="后退"
            >
              <ArrowLeft className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => window.history.forward()}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
              title="前进"
            >
              <ArrowRight className="w-4 h-4" />
            </button>
            
            <button
              onClick={navigateToParent}
              disabled={currentPath === '/'}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="上级目录"
            >
              <ArrowUp className="w-4 h-4" />
            </button>
          </div>

            {/* 路径导航 */}
            <div className="flex items-center space-x-1 text-sm">
            <button
              onClick={() => navigateToPath(-1)}
              className="flex items-center px-2 py-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            >
              <Home className="w-4 h-4 mr-1" />
              根目录
            </button>
            
            {pathParts.map((part, index) => (
              <React.Fragment key={index}>
                <span className="text-gray-400">/</span>
                <button
                  onClick={() => navigateToPath(index)}
                  className="px-2 py-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                >
                  {part}
                </button>
              </React.Fragment>
            ))}
            </div>
          </div>
        </div>

        {/* 右侧：操作按钮 */}
        <div className="flex items-center space-x-2">
          {/* 新建文件按钮 */}
          {onCreateFile && (
            <button
              onClick={onCreateFile}
              disabled={!currentStorage}
              className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-md text-sm font-medium hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              title="新建文件"
            >
              <Plus className="w-4 h-4 mr-2" />
              新建 Office 文件
            </button>
          )}

          {/* AI智能上传按钮 */}
          <button
            onClick={() => {
              if (currentStorage) {
                router.push(`/file-manager/upload?storageId=${currentStorage}&path=${encodeURIComponent(currentPath)}`);
              }
            }}
            disabled={!currentStorage}
            className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md text-sm font-medium hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
            title="AI智能上传"
          >
            <Upload className="w-4 h-4 mr-2" />
            AI 智能上传
          </button>

          {/* 存储管理按钮 */}
          <button
            onClick={() => router.push('/storage')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            title="存储管理"
          >
            <Settings className="w-4 h-4 mr-2" />
            存储管理
          </button>

          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索文件..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 视图模式切换 */}
          <div className="flex items-center border border-gray-300 rounded-md">
            <button
              onClick={() => onViewModeChange('list')}
              className={`p-2 ${
                viewMode === 'list' 
                  ? 'bg-blue-500 text-white' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              } transition-colors`}
              title="列表视图"
            >
              <List className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => onViewModeChange('grid')}
              className={`p-2 ${
                viewMode === 'grid' 
                  ? 'bg-blue-500 text-white' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              } transition-colors`}
              title="网格视图"
            >
              <Grid className="w-4 h-4" />
            </button>
          </div>

          {/* 排序按钮 */}
          <div className="relative">
            <button
              onClick={() => setShowSortMenu(!showSortMenu)}
              className="flex items-center space-x-1 px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
              title="排序"
            >
              {sortOrder === 'asc' ? (
                <SortAsc className="w-4 h-4" />
              ) : (
                <SortDesc className="w-4 h-4" />
              )}
              <span className="text-sm">排序</span>
            </button>

            {/* 排序菜单 */}
            {showSortMenu && (
              <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-2">
                  <div className="text-xs font-medium text-gray-500 px-3 py-2">排序方式</div>
                  {[
                    { key: 'name', label: '名称' },
                    { key: 'size', label: '大小' },
                    { key: 'modified_at', label: '修改时间' },
                    { key: 'created_at', label: '创建时间' }
                  ].map((option) => (
                    <button
                      key={option.key}
                      onClick={() => {
                        onSortByChange(option.key);
                        setShowSortMenu(false);
                      }}
                      className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors ${
                        sortBy === option.key 
                          ? 'bg-blue-50 text-blue-700' 
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                  
                  <div className="border-t border-gray-200 my-2"></div>
                  
                  <div className="text-xs font-medium text-gray-500 px-3 py-2">排序顺序</div>
                  <button
                    onClick={() => {
                      onSortOrderChange(sortOrder === 'asc' ? 'desc' : 'asc');
                      setShowSortMenu(false);
                    }}
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    {sortOrder === 'asc' ? '降序' : '升序'}
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 刷新按钮 */}
          <button
            onClick={onRefresh}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
            title="刷新"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 操作工具栏（当有选中文件时显示） */}
      {selectedFiles.length > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="border-t border-gray-200 bg-blue-50 px-4 py-3"
        >
          <div className="flex items-center justify-between">
            <div className="text-sm text-blue-700">
              已选择 {selectedFiles.length} 个项目
            </div>
            
            <div className="flex items-center space-x-2">
              <button className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md transition-colors">
                <Download className="w-4 h-4" />
                <span>下载</span>
              </button>
              
              <button className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md transition-colors">
                <Copy className="w-4 h-4" />
                <span>复制</span>
              </button>
              
              <button className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md transition-colors">
                <Move className="w-4 h-4" />
                <span>移动</span>
              </button>
              
              <button className="flex items-center space-x-1 px-3 py-1.5 text-sm text-red-600 hover:bg-white hover:shadow-sm rounded-md transition-colors">
                <Trash2 className="w-4 h-4" />
                <span>删除</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* 点击外部关闭菜单 */}
      {(showSortMenu || showMoreMenu) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowSortMenu(false);
            setShowMoreMenu(false);
          }}
        />
      )}
    </div>
  );
};

export default FileToolbar;
