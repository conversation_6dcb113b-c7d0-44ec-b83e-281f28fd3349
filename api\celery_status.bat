@echo off
chcp 65001 >nul
title AI知识库 - Celery服务状态

echo ========================================
echo    AI知识库 Celery服务状态检查
echo ========================================
echo.

REM 检查Redis连接
echo [检查] Redis连接状态...
python -c "
import redis, os
try:
    r = redis.Redis(host=os.getenv('REDIS_HOST', '**************'), port=int(os.getenv('REDIS_PORT', '6379')), db=int(os.getenv('REDIS_DB', '10')))
    info = r.info()
    print(f'  ✓ Redis连接正常 (版本: {info.get(\"redis_version\", \"未知\")})')
    print(f'  ✓ 内存使用: {info.get(\"used_memory_human\", \"未知\")}')
    print(f'  ✓ 连接数: {info.get(\"connected_clients\", \"未知\")}')
except Exception as e:
    print(f'  ✗ Redis连接失败: {e}')
"

echo.
echo [检查] Celery服务状态...

REM 检查Worker进程
set "worker_running="
for /f %%i in ('tasklist /fi "WINDOWTITLE eq Celery Worker" /fo csv 2^>nul ^| find /c /v ""') do (
    if %%i gtr 1 set "worker_running=1"
)
for /f %%i in ('tasklist /fi "WINDOWTITLE eq Celery-Worker" /fo csv 2^>nul ^| find /c /v ""') do (
    if %%i gtr 1 set "worker_running=1"
)

if defined worker_running (
    echo   ✓ Celery Worker - 运行中
) else (
    echo   ✗ Celery Worker - 未运行
)

REM 检查Beat进程
set "beat_running="
for /f %%i in ('tasklist /fi "WINDOWTITLE eq Celery Beat" /fo csv 2^>nul ^| find /c /v ""') do (
    if %%i gtr 1 set "beat_running=1"
)
for /f %%i in ('tasklist /fi "WINDOWTITLE eq Celery-Beat" /fo csv 2^>nul ^| find /c /v ""') do (
    if %%i gtr 1 set "beat_running=1"
)

if defined beat_running (
    echo   ✓ Celery Beat - 运行中
) else (
    echo   ✗ Celery Beat - 未运行
)

REM 检查Flower进程
set "flower_running="
for /f %%i in ('tasklist /fi "WINDOWTITLE eq Celery Flower" /fo csv 2^>nul ^| find /c /v ""') do (
    if %%i gtr 1 set "flower_running=1"
)
for /f %%i in ('tasklist /fi "WINDOWTITLE eq Celery-Flower" /fo csv 2^>nul ^| find /c /v ""') do (
    if %%i gtr 1 set "flower_running=1"
)

if defined flower_running (
    echo   ✓ Celery Flower - 运行中
) else (
    echo   ✗ Celery Flower - 未运行
)

echo.
echo [检查] 网络端口状态...

REM 检查Flower端口
netstat -an | findstr ":5555" >nul 2>&1
if %errorlevel% equ 0 (
    echo   ✓ Flower端口 (5555) - 监听中
    echo     访问地址: http://localhost:5555
) else (
    echo   ✗ Flower端口 (5555) - 未监听
)

echo.
echo [检查] 进程详情...
echo.

REM 显示相关进程
echo Python进程 (包含celery):
tasklist /fi "IMAGENAME eq python.exe" /fo table 2>nul | findstr /i celery
if %errorlevel% neq 0 (
    echo   没有发现Celery相关的Python进程
)

echo.
echo ========================================
echo           状态检查完成
echo ========================================
echo.
echo 管理命令:
echo   启动服务: celery_start.bat
echo   停止服务: celery_stop.bat
echo   查看状态: celery_status.bat
echo.
pause
