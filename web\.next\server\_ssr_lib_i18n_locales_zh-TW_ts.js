"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_i18n_locales_zh-TW_ts";
exports.ids = ["_ssr_lib_i18n_locales_zh-TW_ts"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales/zh-TW.ts":
/*!***********************************!*\
  !*** ./lib/i18n/locales/zh-TW.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * 中文繁體翻譯文件\n */ const zhTW = {\n    common: {\n        loading: \"載入中...\",\n        error: \"錯誤\",\n        success: \"成功\",\n        cancel: \"取消\",\n        confirm: \"確認\",\n        back: \"返回\",\n        next: \"下一步\",\n        submit: \"提交\",\n        retry: \"重試\",\n        save: \"保存\",\n        delete: \"刪除\",\n        edit: \"編輯\",\n        add: \"添加\",\n        search: \"搜索\",\n        filter: \"篩選\",\n        export: \"導出\",\n        import: \"導入\",\n        refresh: \"刷新\"\n    },\n    login: {\n        title: \"AI知識庫\",\n        subtitle: \"基於人工智能的智能知識管理與檢索系統\",\n        welcomeBack: \"歡迎回來\",\n        username: \"用戶名\",\n        password: \"密碼\",\n        rememberMe: \"記住我\",\n        forgotPassword: \"忘記密碼？\",\n        loginButton: \"登入\",\n        noAccount: \"還沒有帳戶？\",\n        signUp: \"立即註冊\",\n        loginSuccess: \"登入成功！\",\n        loginError: \"登入失敗\",\n        invalidCredentials: \"用戶名或密碼錯誤\",\n        networkError: \"網路連接錯誤，請稍後重試\",\n        usernameRequired: \"請輸入用戶名\",\n        passwordRequired: \"請輸入密碼\",\n        usernameMinLength: \"用戶名至少需要3個字符\",\n        passwordMinLength: \"密碼至少需要6個字符\"\n    },\n    ai: {\n        poweredBy: \"由AI驅動\",\n        intelligentSystem: \"智能系統\",\n        secureLogin: \"安全登入\",\n        aiAssistant: \"AI助手\",\n        smartAnalysis: \"智能分析\",\n        dataProtection: \"數據保護\",\n        knowledgeBase: \"知識庫\",\n        intelligentRetrieval: \"智能檢索\",\n        documentProcessing: \"文檔處理\"\n    },\n    language: {\n        current: \"中文繁體\",\n        switch: \"切換語言\",\n        chinese: \"中文簡體\",\n        traditionalChinese: \"中文繁體\",\n        english: \"English\",\n        japanese: \"日本語\"\n    },\n    navigation: {\n        home: \"首頁\",\n        dashboard: \"儀表板\",\n        documents: \"文檔管理\",\n        search: \"智能搜索\",\n        settings: \"系統設置\",\n        profile: \"個人資料\",\n        logout: \"退出登錄\",\n        admin: \"系統管理\"\n    },\n    dashboard: {\n        welcome: \"歡迎回來\",\n        overview: \"概覽\",\n        statistics: \"統計信息\",\n        recentActivity: \"最近活動\",\n        quickActions: \"快速操作\",\n        systemStatus: \"系統狀態\",\n        userInfo: \"用戶信息\",\n        totalDocuments: \"文檔總數\",\n        totalQueries: \"查詢總數\",\n        totalUsers: \"用戶總數\",\n        systemSettings: \"系統設置\"\n    },\n    documents: {\n        title: \"文檔管理\",\n        upload: \"上傳文檔\",\n        download: \"下載\",\n        delete: \"刪除\",\n        preview: \"預覽\",\n        details: \"詳情\",\n        fileName: \"文件名\",\n        fileSize: \"文件大小\",\n        uploadTime: \"上傳時間\",\n        fileType: \"文件類型\",\n        status: \"狀態\",\n        processing: \"處理中\",\n        completed: \"已完成\",\n        failed: \"失敗\",\n        uploadSuccess: \"上傳成功\",\n        uploadError: \"上傳失敗\",\n        deleteConfirm: \"確定要刪除這個文檔嗎？\"\n    },\n    search: {\n        title: \"智能搜索\",\n        placeholder: \"請輸入搜索關鍵詞...\",\n        results: \"搜索結果\",\n        noResults: \"未找到相關結果\",\n        searching: \"搜索中...\",\n        advanced: \"高級搜索\",\n        filters: \"篩選條件\",\n        sortBy: \"排序方式\",\n        relevance: \"相關性\",\n        date: \"日期\",\n        size: \"大小\"\n    },\n    settings: {\n        title: \"系統設置\",\n        general: \"常規設置\",\n        account: \"帳戶設置\",\n        security: \"安全設置\",\n        notifications: \"通知設置\",\n        language: \"語言設置\",\n        theme: \"主題設置\",\n        privacy: \"隱私設置\",\n        about: \"關於系統\"\n    },\n    errors: {\n        pageNotFound: \"頁面未找到\",\n        serverError: \"服務器內部錯誤\",\n        networkError: \"網路連接錯誤\",\n        unauthorized: \"未授權訪問\",\n        forbidden: \"訪問被禁止\",\n        validationError: \"數據驗證錯誤\",\n        unknownError: \"未知錯誤\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (zhTW);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi9sb2NhbGVzL3poLVRXLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUlELE1BQU1BLE9BQXFCO0lBQ3pCQyxRQUFRO1FBQ05DLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLEtBQUs7UUFDTEMsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUkMsUUFBUTtRQUNSQyxTQUFTO0lBQ1g7SUFFQUMsT0FBTztRQUNMQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxnQkFBZ0I7UUFDaEJDLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxRQUFRO1FBQ1JDLGNBQWM7UUFDZEMsWUFBWTtRQUNaQyxvQkFBb0I7UUFDcEJDLGNBQWM7UUFDZEMsa0JBQWtCO1FBQ2xCQyxrQkFBa0I7UUFDbEJDLG1CQUFtQjtRQUNuQkMsbUJBQW1CO0lBQ3JCO0lBRUFDLElBQUk7UUFDRkMsV0FBVztRQUNYQyxtQkFBbUI7UUFDbkJDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsZUFBZTtRQUNmQyxzQkFBc0I7UUFDdEJDLG9CQUFvQjtJQUN0QjtJQUVBQyxVQUFVO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLG9CQUFvQjtRQUNwQkMsU0FBUztRQUNUQyxVQUFVO0lBQ1o7SUFFQUMsWUFBWTtRQUNWQyxNQUFNO1FBQ05DLFdBQVc7UUFDWEMsV0FBVztRQUNYNUMsUUFBUTtRQUNSNkMsVUFBVTtRQUNWQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUFMLFdBQVc7UUFDVE0sU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2RDLGNBQWM7UUFDZEMsVUFBVTtRQUNWQyxnQkFBZ0I7UUFDaEJDLGNBQWM7UUFDZEMsWUFBWTtRQUNaQyxnQkFBZ0I7SUFDbEI7SUFFQWYsV0FBVztRQUNUdEMsT0FBTztRQUNQc0QsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZoRSxRQUFRO1FBQ1JpRSxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxRQUFRO1FBQ1JDLGVBQWU7UUFDZkMsYUFBYTtRQUNiQyxlQUFlO0lBQ2pCO0lBRUExRSxRQUFRO1FBQ05NLE9BQU87UUFDUHFFLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUkMsV0FBVztRQUNYQyxNQUFNO1FBQ05DLE1BQU07SUFDUjtJQUVBdkMsVUFBVTtRQUNSdkMsT0FBTztRQUNQK0UsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsZUFBZTtRQUNmdEQsVUFBVTtRQUNWdUQsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUVBQyxRQUFRO1FBQ05DLGNBQWM7UUFDZEMsYUFBYTtRQUNiM0UsY0FBYztRQUNkNEUsY0FBYztRQUNkQyxXQUFXO1FBQ1hDLGlCQUFpQjtRQUNqQkMsY0FBYztJQUNoQjtBQUNGO0FBRUEsaUVBQWVqSCxJQUFJQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWkta25vd2xlZGdlLWJhc2Utd2ViLy4vbGliL2kxOG4vbG9jYWxlcy96aC1UVy50cz8xNmExIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5Lit5paH57mB6auU57+76K2v5paH5Lu2XG4gKi9cblxuaW1wb3J0IHsgVHJhbnNsYXRpb25zIH0gZnJvbSAnLi4vaW5kZXgnO1xuXG5jb25zdCB6aFRXOiBUcmFuc2xhdGlvbnMgPSB7XG4gIGNvbW1vbjoge1xuICAgIGxvYWRpbmc6ICfovInlhaXkuK0uLi4nLFxuICAgIGVycm9yOiAn6Yyv6KqkJyxcbiAgICBzdWNjZXNzOiAn5oiQ5YqfJyxcbiAgICBjYW5jZWw6ICflj5bmtognLFxuICAgIGNvbmZpcm06ICfnorroqo0nLFxuICAgIGJhY2s6ICfov5Tlm54nLFxuICAgIG5leHQ6ICfkuIvkuIDmraUnLFxuICAgIHN1Ym1pdDogJ+aPkOS6pCcsXG4gICAgcmV0cnk6ICfph43oqaYnLFxuICAgIHNhdmU6ICfkv53lrZgnLFxuICAgIGRlbGV0ZTogJ+WIqumZpCcsXG4gICAgZWRpdDogJ+e3qOi8rycsXG4gICAgYWRkOiAn5re75YqgJyxcbiAgICBzZWFyY2g6ICfmkJzntKInLFxuICAgIGZpbHRlcjogJ+evqemBuCcsXG4gICAgZXhwb3J0OiAn5bCO5Ye6JyxcbiAgICBpbXBvcnQ6ICflsI7lhaUnLFxuICAgIHJlZnJlc2g6ICfliLfmlrAnLFxuICB9LFxuICBcbiAgbG9naW46IHtcbiAgICB0aXRsZTogJ0FJ55+l6K2Y5bqrJyxcbiAgICBzdWJ0aXRsZTogJ+WfuuaWvOS6uuW3peaZuuiDveeahOaZuuiDveefpeitmOeuoeeQhuiIh+aqoue0ouezu+e1sScsXG4gICAgd2VsY29tZUJhY2s6ICfmraHov47lm57kvoYnLFxuICAgIHVzZXJuYW1lOiAn55So5oi25ZCNJyxcbiAgICBwYXNzd29yZDogJ+WvhueivCcsXG4gICAgcmVtZW1iZXJNZTogJ+iomOS9j+aIkScsXG4gICAgZm9yZ290UGFzc3dvcmQ6ICflv5joqJjlr4bnorzvvJ8nLFxuICAgIGxvZ2luQnV0dG9uOiAn55m75YWlJyxcbiAgICBub0FjY291bnQ6ICfpgoTmspLmnInluLPmiLbvvJ8nLFxuICAgIHNpZ25VcDogJ+eri+WNs+iou+WGiicsXG4gICAgbG9naW5TdWNjZXNzOiAn55m75YWl5oiQ5Yqf77yBJyxcbiAgICBsb2dpbkVycm9yOiAn55m75YWl5aSx5pWXJyxcbiAgICBpbnZhbGlkQ3JlZGVudGlhbHM6ICfnlKjmiLblkI3miJblr4bnorzpjK/oqqQnLFxuICAgIG5ldHdvcmtFcnJvcjogJ+e2sui3r+mAo+aOpemMr+iqpO+8jOiri+eojeW+jOmHjeippicsXG4gICAgdXNlcm5hbWVSZXF1aXJlZDogJ+iri+i8uOWFpeeUqOaItuWQjScsXG4gICAgcGFzc3dvcmRSZXF1aXJlZDogJ+iri+i8uOWFpeWvhueivCcsXG4gICAgdXNlcm5hbWVNaW5MZW5ndGg6ICfnlKjmiLblkI3oh7PlsJHpnIDopoEz5YCL5a2X56ymJyxcbiAgICBwYXNzd29yZE1pbkxlbmd0aDogJ+WvhueivOiHs+WwkemcgOimgTblgIvlrZfnrKYnLFxuICB9LFxuICBcbiAgYWk6IHtcbiAgICBwb3dlcmVkQnk6ICfnlLFBSempheWLlScsXG4gICAgaW50ZWxsaWdlbnRTeXN0ZW06ICfmmbrog73ns7vntbEnLFxuICAgIHNlY3VyZUxvZ2luOiAn5a6J5YWo55m75YWlJyxcbiAgICBhaUFzc2lzdGFudDogJ0FJ5Yqp5omLJyxcbiAgICBzbWFydEFuYWx5c2lzOiAn5pm66IO95YiG5p6QJyxcbiAgICBkYXRhUHJvdGVjdGlvbjogJ+aVuOaTmuS/neittycsXG4gICAga25vd2xlZGdlQmFzZTogJ+efpeitmOW6qycsXG4gICAgaW50ZWxsaWdlbnRSZXRyaWV2YWw6ICfmmbrog73mqqLntKInLFxuICAgIGRvY3VtZW50UHJvY2Vzc2luZzogJ+aWh+aqlOiZleeQhicsXG4gIH0sXG4gIFxuICBsYW5ndWFnZToge1xuICAgIGN1cnJlbnQ6ICfkuK3mlofnuYHpq5QnLFxuICAgIHN3aXRjaDogJ+WIh+aPm+iqnuiogCcsXG4gICAgY2hpbmVzZTogJ+S4reaWh+ewoemrlCcsXG4gICAgdHJhZGl0aW9uYWxDaGluZXNlOiAn5Lit5paH57mB6auUJyxcbiAgICBlbmdsaXNoOiAnRW5nbGlzaCcsXG4gICAgamFwYW5lc2U6ICfml6XmnKzoqp4nLFxuICB9LFxuICBcbiAgbmF2aWdhdGlvbjoge1xuICAgIGhvbWU6ICfpppbpoIEnLFxuICAgIGRhc2hib2FyZDogJ+WEgOihqOadvycsXG4gICAgZG9jdW1lbnRzOiAn5paH5qqU566h55CGJyxcbiAgICBzZWFyY2g6ICfmmbrog73mkJzntKInLFxuICAgIHNldHRpbmdzOiAn57O757Wx6Kit572uJyxcbiAgICBwcm9maWxlOiAn5YCL5Lq66LOH5paZJyxcbiAgICBsb2dvdXQ6ICfpgIDlh7rnmbvpjIQnLFxuICAgIGFkbWluOiAn57O757Wx566h55CGJyxcbiAgfSxcbiAgXG4gIGRhc2hib2FyZDoge1xuICAgIHdlbGNvbWU6ICfmraHov47lm57kvoYnLFxuICAgIG92ZXJ2aWV3OiAn5qaC6Ka9JyxcbiAgICBzdGF0aXN0aWNzOiAn57Wx6KiI5L+h5oGvJyxcbiAgICByZWNlbnRBY3Rpdml0eTogJ+acgOi/kea0u+WLlScsXG4gICAgcXVpY2tBY3Rpb25zOiAn5b+r6YCf5pON5L2cJyxcbiAgICBzeXN0ZW1TdGF0dXM6ICfns7vntbHni4DmhYsnLFxuICAgIHVzZXJJbmZvOiAn55So5oi25L+h5oGvJyxcbiAgICB0b3RhbERvY3VtZW50czogJ+aWh+aqlOe4veaVuCcsXG4gICAgdG90YWxRdWVyaWVzOiAn5p+l6Kmi57i95pW4JyxcbiAgICB0b3RhbFVzZXJzOiAn55So5oi257i95pW4JyxcbiAgICBzeXN0ZW1TZXR0aW5nczogJ+ezu+e1seioree9ricsXG4gIH0sXG4gIFxuICBkb2N1bWVudHM6IHtcbiAgICB0aXRsZTogJ+aWh+aqlOeuoeeQhicsXG4gICAgdXBsb2FkOiAn5LiK5YKz5paH5qqUJyxcbiAgICBkb3dubG9hZDogJ+S4i+i8iScsXG4gICAgZGVsZXRlOiAn5Yiq6ZmkJyxcbiAgICBwcmV2aWV3OiAn6aCQ6Ka9JyxcbiAgICBkZXRhaWxzOiAn6Kmz5oOFJyxcbiAgICBmaWxlTmFtZTogJ+aWh+S7tuWQjScsXG4gICAgZmlsZVNpemU6ICfmlofku7blpKflsI8nLFxuICAgIHVwbG9hZFRpbWU6ICfkuIrlgrPmmYLplpMnLFxuICAgIGZpbGVUeXBlOiAn5paH5Lu26aGe5Z6LJyxcbiAgICBzdGF0dXM6ICfni4DmhYsnLFxuICAgIHByb2Nlc3Npbmc6ICfomZXnkIbkuK0nLFxuICAgIGNvbXBsZXRlZDogJ+W3suWujOaIkCcsXG4gICAgZmFpbGVkOiAn5aSx5pWXJyxcbiAgICB1cGxvYWRTdWNjZXNzOiAn5LiK5YKz5oiQ5YqfJyxcbiAgICB1cGxvYWRFcnJvcjogJ+S4iuWCs+WkseaVlycsXG4gICAgZGVsZXRlQ29uZmlybTogJ+eiuuWumuimgeWIqumZpOmAmeWAi+aWh+aqlOWXju+8nycsXG4gIH0sXG4gIFxuICBzZWFyY2g6IHtcbiAgICB0aXRsZTogJ+aZuuiDveaQnOe0oicsXG4gICAgcGxhY2Vob2xkZXI6ICfoq4vovLjlhaXmkJzntKLpl5zpjbXoqZ4uLi4nLFxuICAgIHJlc3VsdHM6ICfmkJzntKLntZDmnpwnLFxuICAgIG5vUmVzdWx0czogJ+acquaJvuWIsOebuOmXnOe1kOaenCcsXG4gICAgc2VhcmNoaW5nOiAn5pCc57Si5LitLi4uJyxcbiAgICBhZHZhbmNlZDogJ+mrmOe0muaQnOe0oicsXG4gICAgZmlsdGVyczogJ+evqemBuOaineS7ticsXG4gICAgc29ydEJ5OiAn5o6S5bqP5pa55byPJyxcbiAgICByZWxldmFuY2U6ICfnm7jpl5zmgKcnLFxuICAgIGRhdGU6ICfml6XmnJ8nLFxuICAgIHNpemU6ICflpKflsI8nLFxuICB9LFxuICBcbiAgc2V0dGluZ3M6IHtcbiAgICB0aXRsZTogJ+ezu+e1seioree9ricsXG4gICAgZ2VuZXJhbDogJ+W4uOimj+ioree9ricsXG4gICAgYWNjb3VudDogJ+W4s+aItuioree9ricsXG4gICAgc2VjdXJpdHk6ICflronlhajoqK3nva4nLFxuICAgIG5vdGlmaWNhdGlvbnM6ICfpgJrnn6XoqK3nva4nLFxuICAgIGxhbmd1YWdlOiAn6Kqe6KiA6Kit572uJyxcbiAgICB0aGVtZTogJ+S4u+mhjOioree9ricsXG4gICAgcHJpdmFjeTogJ+maseengeioree9ricsXG4gICAgYWJvdXQ6ICfpl5zmlrzns7vntbEnLFxuICB9LFxuICBcbiAgZXJyb3JzOiB7XG4gICAgcGFnZU5vdEZvdW5kOiAn6aCB6Z2i5pyq5om+5YiwJyxcbiAgICBzZXJ2ZXJFcnJvcjogJ+acjeWLmeWZqOWFp+mDqOmMr+iqpCcsXG4gICAgbmV0d29ya0Vycm9yOiAn57ay6Lev6YCj5o6l6Yyv6KqkJyxcbiAgICB1bmF1dGhvcml6ZWQ6ICfmnKrmjojmrIroqKrllY8nLFxuICAgIGZvcmJpZGRlbjogJ+ioquWVj+iiq+emgeatoicsXG4gICAgdmFsaWRhdGlvbkVycm9yOiAn5pW45pOa6amX6K2J6Yyv6KqkJyxcbiAgICB1bmtub3duRXJyb3I6ICfmnKrnn6XpjK/oqqQnLFxuICB9LFxufTtcblxuZXhwb3J0IGRlZmF1bHQgemhUVztcbiJdLCJuYW1lcyI6WyJ6aFRXIiwiY29tbW9uIiwibG9hZGluZyIsImVycm9yIiwic3VjY2VzcyIsImNhbmNlbCIsImNvbmZpcm0iLCJiYWNrIiwibmV4dCIsInN1Ym1pdCIsInJldHJ5Iiwic2F2ZSIsImRlbGV0ZSIsImVkaXQiLCJhZGQiLCJzZWFyY2giLCJmaWx0ZXIiLCJleHBvcnQiLCJpbXBvcnQiLCJyZWZyZXNoIiwibG9naW4iLCJ0aXRsZSIsInN1YnRpdGxlIiwid2VsY29tZUJhY2siLCJ1c2VybmFtZSIsInBhc3N3b3JkIiwicmVtZW1iZXJNZSIsImZvcmdvdFBhc3N3b3JkIiwibG9naW5CdXR0b24iLCJub0FjY291bnQiLCJzaWduVXAiLCJsb2dpblN1Y2Nlc3MiLCJsb2dpbkVycm9yIiwiaW52YWxpZENyZWRlbnRpYWxzIiwibmV0d29ya0Vycm9yIiwidXNlcm5hbWVSZXF1aXJlZCIsInBhc3N3b3JkUmVxdWlyZWQiLCJ1c2VybmFtZU1pbkxlbmd0aCIsInBhc3N3b3JkTWluTGVuZ3RoIiwiYWkiLCJwb3dlcmVkQnkiLCJpbnRlbGxpZ2VudFN5c3RlbSIsInNlY3VyZUxvZ2luIiwiYWlBc3Npc3RhbnQiLCJzbWFydEFuYWx5c2lzIiwiZGF0YVByb3RlY3Rpb24iLCJrbm93bGVkZ2VCYXNlIiwiaW50ZWxsaWdlbnRSZXRyaWV2YWwiLCJkb2N1bWVudFByb2Nlc3NpbmciLCJsYW5ndWFnZSIsImN1cnJlbnQiLCJzd2l0Y2giLCJjaGluZXNlIiwidHJhZGl0aW9uYWxDaGluZXNlIiwiZW5nbGlzaCIsImphcGFuZXNlIiwibmF2aWdhdGlvbiIsImhvbWUiLCJkYXNoYm9hcmQiLCJkb2N1bWVudHMiLCJzZXR0aW5ncyIsInByb2ZpbGUiLCJsb2dvdXQiLCJhZG1pbiIsIndlbGNvbWUiLCJvdmVydmlldyIsInN0YXRpc3RpY3MiLCJyZWNlbnRBY3Rpdml0eSIsInF1aWNrQWN0aW9ucyIsInN5c3RlbVN0YXR1cyIsInVzZXJJbmZvIiwidG90YWxEb2N1bWVudHMiLCJ0b3RhbFF1ZXJpZXMiLCJ0b3RhbFVzZXJzIiwic3lzdGVtU2V0dGluZ3MiLCJ1cGxvYWQiLCJkb3dubG9hZCIsInByZXZpZXciLCJkZXRhaWxzIiwiZmlsZU5hbWUiLCJmaWxlU2l6ZSIsInVwbG9hZFRpbWUiLCJmaWxlVHlwZSIsInN0YXR1cyIsInByb2Nlc3NpbmciLCJjb21wbGV0ZWQiLCJmYWlsZWQiLCJ1cGxvYWRTdWNjZXNzIiwidXBsb2FkRXJyb3IiLCJkZWxldGVDb25maXJtIiwicGxhY2Vob2xkZXIiLCJyZXN1bHRzIiwibm9SZXN1bHRzIiwic2VhcmNoaW5nIiwiYWR2YW5jZWQiLCJmaWx0ZXJzIiwic29ydEJ5IiwicmVsZXZhbmNlIiwiZGF0ZSIsInNpemUiLCJnZW5lcmFsIiwiYWNjb3VudCIsInNlY3VyaXR5Iiwibm90aWZpY2F0aW9ucyIsInRoZW1lIiwicHJpdmFjeSIsImFib3V0IiwiZXJyb3JzIiwicGFnZU5vdEZvdW5kIiwic2VydmVyRXJyb3IiLCJ1bmF1dGhvcml6ZWQiLCJmb3JiaWRkZW4iLCJ2YWxpZGF0aW9uRXJyb3IiLCJ1bmtub3duRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/locales/zh-TW.ts\n");

/***/ })

};
;