"""
API路由配置
汇总所有API路由
"""

from fastapi import APIRouter
from app.api.v1 import v1_router

# 创建API路由器
api_router = APIRouter()

# 注册v1版本路由
api_router.include_router(v1_router, prefix="/v1")


@api_router.get("/", tags=["API"])
async def api_info():
    """API信息"""
    return {
        "message": "XHC RAG API v1",
        "version": "1.0.0",
        "endpoints": {
            "auth": "/api/v1/auth",
            "storage": "/api/v1/storage",
            "migration": "/api/v1/migration",
            "system_init": "/api/v1/system-init",
        }
    }
