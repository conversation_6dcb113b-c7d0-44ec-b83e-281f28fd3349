@echo off
echo ========================================
echo AI知识库 - 启动服务脚本
echo ========================================
echo.

REM 检查当前目录
if not exist "web" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "api" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 正在启动服务...
echo.

REM 启动后端服务
echo 步骤 1: 启动后端API服务...
start "后端API服务" cmd /k "cd api; python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload"
echo 后端服务启动中... (端口: 8000)
echo.

REM 等待2秒
timeout /t 2 /nobreak >nul

REM 启动前端服务
echo 步骤 2: 启动前端开发服务...
start "前端开发服务" cmd /k "cd web; pnpm dev"
echo 前端服务启动中... (端口: 3000)
echo.

REM 等待5秒让服务启动
echo 等待服务启动...
timeout /t 5 /nobreak >nul

echo ========================================
echo 服务启动完成!
echo ========================================
echo.
echo 前端地址: http://localhost:3000
echo 后端API: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo.
echo 批量分段功能测试页面:
echo file:///f:/workspace/xhc-rag/test_batch_segment_ui.html
echo.
echo 数据库更新脚本:
echo api/sql_scripts/run_batch_segment_updates.bat
echo.
echo 按任意键打开前端页面...
pause >nul

REM 打开前端页面
start http://localhost:3000

echo.
echo 提示: 
echo - 如需停止服务，请关闭对应的命令窗口
echo - 如遇到端口占用，请检查是否有其他服务在运行
echo - 数据库连接配置在 api/.env 文件中
echo.
pause
