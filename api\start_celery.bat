@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 启动AI知识库Celery服务...

REM 检查Redis连接
echo 检查Redis连接...
python -c "
import redis
import os
redis_host = os.getenv('REDIS_HOST', '**************')
redis_port = int(os.getenv('REDIS_PORT', '6379'))
redis_db = int(os.getenv('REDIS_DB', '10'))
try:
    r = redis.Redis(host=redis_host, port=redis_port, db=redis_db)
    r.ping()
    print('Redis连接成功')
except Exception as e:
    print(f'Redis连接失败: {e}')
    exit(1)
"

if %errorlevel% neq 0 (
    echo Redis连接失败，请检查Redis服务
    pause
    exit /b 1
)

REM 设置环境变量
set PYTHONPATH=%PYTHONPATH%;%cd%

REM 创建日志目录
if not exist "logs" mkdir logs

REM 启动Celery Worker
echo 启动Celery Worker...
start "Celery Worker" cmd /k "celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=4 --queues=default,upload_queue,file_queue,segment_queue --hostname=worker@%%h --logfile=logs/celery_worker.log"

REM 等待Worker启动
timeout /t 3 /nobreak >nul

REM 启动Celery Beat
echo 启动Celery Beat...
start "Celery Beat" cmd /k "celery -A app.core.celery_config:celery_app beat --loglevel=info --logfile=logs/celery_beat.log"

REM 启动Flower监控
echo 启动Flower监控...
start "Celery Flower" cmd /k "celery -A app.core.celery_config:celery_app flower --port=5555 --basic_auth=admin:password --logfile=logs/celery_flower.log"

echo.
echo Celery服务启动完成!
echo - Worker窗口: Celery Worker
echo - Beat窗口: Celery Beat  
echo - Flower窗口: Celery Flower
echo - Flower监控地址: http://localhost:5555 (admin/password)
echo.
echo 查看日志:
echo - Worker日志: type logs\celery_worker.log
echo - Beat日志: type logs\celery_beat.log
echo - Flower日志: type logs\celery_flower.log
echo.
echo 按任意键退出...
pause >nul
