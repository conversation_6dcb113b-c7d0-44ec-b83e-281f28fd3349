"""
数据库迁移管理API
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
from loguru import logger

from app.core.migration import run_migrations, migration_manager

router = APIRouter()


@router.get("/status")
async def get_migration_status() -> Dict[str, Any]:
    """获取数据库迁移状态"""
    try:
        if migration_manager is None:
            return {
                "status": "not_initialized",
                "message": "Migration manager not initialized"
            }
        
        # 检查当前数据库状态
        differences = await migration_manager._compare_schemas()
        
        return {
            "status": "ready",
            "database_type": migration_manager._get_database_type(),
            "total_tables": len(migration_manager.metadata.tables),
            "missing_tables": len(differences["missing_tables"]),
            "schema_changes": len(differences["schema_changes"]),
            "needs_migration": bool(differences["missing_tables"] or differences["schema_changes"]),
            "differences": differences
        }
        
    except Exception as e:
        logger.error(f"Failed to get migration status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/run")
async def run_database_migrations() -> Dict[str, Any]:
    """手动运行数据库迁移"""
    try:
        logger.info("Manual migration triggered via API")
        result = await run_migrations()
        
        if result["status"] == "error":
            raise HTTPException(status_code=500, detail=result.get("errors", ["Migration failed"]))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to run migrations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tables")
async def get_table_info() -> Dict[str, Any]:
    """获取数据库表信息"""
    try:
        if migration_manager is None:
            raise HTTPException(status_code=503, detail="Migration manager not initialized")
        
        # 获取模型定义的表
        model_tables = {}
        for table_name, table in migration_manager.metadata.tables.items():
            model_tables[table_name] = {
                "columns": [
                    {
                        "name": col.name,
                        "type": str(col.type),
                        "nullable": col.nullable,
                        "primary_key": col.primary_key,
                        "foreign_key": bool(col.foreign_keys)
                    }
                    for col in table.columns
                ],
                "indexes": [idx.name for idx in table.indexes] if table.indexes else [],
                "foreign_keys": [
                    {
                        "column": fk.parent.name,
                        "referenced_table": fk.column.table.name,
                        "referenced_column": fk.column.name
                    }
                    for fk in table.foreign_keys
                ] if table.foreign_keys else []
            }
        
        # 获取数据库中实际的表
        async with migration_manager.async_engine.begin() as conn:
            def get_db_tables(sync_conn):
                from sqlalchemy import inspect
                inspector = inspect(sync_conn)
                return inspector.get_table_names()

            db_tables = await conn.run_sync(get_db_tables)
        
        return {
            "model_tables": model_tables,
            "database_tables": db_tables,
            "model_count": len(model_tables),
            "database_count": len(db_tables)
        }
        
    except Exception as e:
        logger.error(f"Failed to get table info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history")
async def get_migration_history() -> Dict[str, Any]:
    """获取迁移历史（占位符，可以后续实现）"""
    return {
        "message": "Migration history tracking not implemented yet",
        "suggestion": "Consider implementing migration history table for production use"
    }
