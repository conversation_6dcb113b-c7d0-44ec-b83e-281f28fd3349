@echo off
REM 运行文档分段功能调试脚本
REM 适用于Windows系统

echo ========================================
echo 文档分段功能调试测试
echo ========================================

REM 设置环境变量
set PYTHONPATH=%cd%
cd /d "%~dp0"

echo 当前目录: %cd%
echo Python路径: %PYTHONPATH%
echo.

REM 检查Python环境
python --version
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 开始运行调试脚本...
echo.

REM 运行调试脚本
python test_segment_debug.py

echo.
echo ========================================
echo 调试测试完成
echo ========================================
pause
