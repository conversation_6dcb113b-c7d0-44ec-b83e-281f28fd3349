"""
用户Service
处理用户相关的业务逻辑
"""

from typing import Optional, List, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.models.user import User
from app.repositories.user import UserRepository
from app.schemas.user import UserCreate, UserUpdate, UserPasswordUpdate
from app.services.base import BaseService, ValidationException, NotFoundException, ConflictException
from app.core.security import get_password_hash, verify_password


class UserService(BaseService[User, UserCreate, UserUpdate]):
    """用户Service"""
    
    def __init__(self, session: AsyncSession):
        repository = UserRepository(session)
        super().__init__(repository)
        self.session = session
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            return await self.repository.get_by_username(username)
        except Exception as e:
            logger.error("Error getting user by username {}: {}".format(username, str(e)))
            raise
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        try:
            return await self.repository.get_by_email(email)
        except Exception as e:
            logger.error("Error getting user by email {}: {}".format(email, str(e)))
            raise
    
    async def get_by_username_or_email(self, identifier: str) -> Optional[User]:
        """根据用户名或邮箱获取用户"""
        try:
            return await self.repository.get_by_username_or_email(identifier)
        except Exception as e:
            logger.error("Error getting user by identifier {}: {}".format(identifier, str(e)))
            raise

    async def authenticate(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        try:
            user = await self.get_by_username_or_email(username)
            if not user:
                return None

            if not verify_password(password, user.hashed_password):
                return None

            if not user.is_active:
                return None

            # 更新最后登录时间
            await self.repository.update_last_login(user.id)

            return user
        except Exception as e:
            logger.error("Error authenticating user {}: {}".format(username, str(e)))
            raise
    
    async def create_user(self, user_create: UserCreate) -> User:
        """创建用户"""
        try:
            # 检查用户名是否已存在
            if await self.repository.check_username_exists(user_create.username):
                raise ConflictException(f"用户名 '{user_create.username}' 已存在")
            
            # 检查邮箱是否已存在
            if await self.repository.check_email_exists(user_create.email):
                raise ConflictException(f"邮箱 '{user_create.email}' 已存在")
            
            # 创建用户数据
            user_data = user_create.dict(exclude={'password'})
            user_data['hashed_password'] = get_password_hash(user_create.password)
            
            return await self.repository.create(user_data)
        except ConflictException:
            raise
        except Exception as e:
            logger.error("Error creating user: {}".format(str(e)))
            raise
    
    async def update_user(self, user_id: int, user_update: UserUpdate) -> Optional[User]:
        """更新用户"""
        try:
            # 检查用户是否存在
            existing_user = await self.repository.get(user_id)
            if not existing_user:
                raise NotFoundException("User", user_id)
            
            # 检查用户名冲突
            if user_update.username and user_update.username != existing_user.username:
                if await self.repository.check_username_exists(user_update.username, user_id):
                    raise ConflictException("用户名 '{}' 已存在".format(user_update.username))

            # 检查邮箱冲突
            if user_update.email and user_update.email != existing_user.email:
                if await self.repository.check_email_exists(user_update.email, user_id):
                    raise ConflictException("邮箱 '{}' 已存在".format(user_update.email))
            
            # 更新用户
            update_data = user_update.dict(exclude_unset=True)
            return await self.repository.update(user_id, update_data)
        except (NotFoundException, ConflictException):
            raise
        except Exception as e:
            logger.error("Error updating user {}: {}".format(user_id, str(e)))
            raise
    
    async def change_password(
        self, 
        user_id: int, 
        password_update: UserPasswordUpdate
    ) -> bool:
        """修改用户密码"""
        try:
            # 检查用户是否存在
            user = await self.repository.get(user_id)
            if not user:
                raise NotFoundException("User", user_id)
            
            # 验证当前密码
            if not verify_password(password_update.current_password, user.hashed_password):
                raise ValidationException("当前密码不正确")
            
            # 更新密码
            new_hashed_password = get_password_hash(password_update.new_password)
            return await self.repository.change_password(user_id, new_hashed_password)
        except (NotFoundException, ValidationException):
            raise
        except Exception as e:
            logger.error("Error changing password for user {}: {}".format(user_id, str(e)))
            raise

    async def activate_user(self, user_id: int) -> bool:
        """激活用户"""
        try:
            if not await self.repository.exists(user_id):
                raise NotFoundException("User", user_id)

            return await self.repository.activate_user(user_id)
        except NotFoundException:
            raise
        except Exception as e:
            logger.error("Error activating user {}: {}".format(user_id, str(e)))
            raise

    async def deactivate_user(self, user_id: int) -> bool:
        """停用用户"""
        try:
            if not await self.repository.exists(user_id):
                raise NotFoundException("User", user_id)

            return await self.repository.deactivate_user(user_id)
        except NotFoundException:
            raise
        except Exception as e:
            logger.error("Error deactivating user {}: {}".format(user_id, str(e)))
            raise

    async def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        try:
            if not await self.repository.exists(user_id):
                raise NotFoundException("User", user_id)

            return await self.repository.delete(user_id)
        except NotFoundException:
            raise
        except Exception as e:
            logger.error("Error deleting user {}: {}".format(user_id, str(e)))
            raise
    
    async def search_users(
        self, 
        query: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[User]:
        """搜索用户"""
        try:
            return await self.repository.search_users(query, skip, limit)
        except Exception as e:
            logger.error("Error searching users with query {}: {}".format(query, str(e)))
            raise

    async def get_active_users(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """获取活跃用户列表"""
        try:
            return await self.repository.get_active_users(skip, limit)
        except Exception as e:
            logger.error("Error getting active users: {}".format(str(e)))
            raise

    async def get_superusers(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """获取超级用户列表"""
        try:
            return await self.repository.get_superusers(skip, limit)
        except Exception as e:
            logger.error("Error getting superusers: {}".format(str(e)))
            raise

    async def get_user_stats(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            return await self.repository.get_user_stats()
        except Exception as e:
            logger.error("Error getting user stats: {}".format(str(e)))
            raise
    
    async def batch_activate_users(self, user_ids: List[int]) -> Dict[str, Any]:
        """批量激活用户"""
        try:
            success_count = 0
            failed_ids = []
            
            for user_id in user_ids:
                try:
                    if await self.activate_user(user_id):
                        success_count += 1
                    else:
                        failed_ids.append(user_id)
                except Exception:
                    failed_ids.append(user_id)
            
            return {
                "success_count": success_count,
                "failed_count": len(failed_ids),
                "failed_ids": failed_ids
            }
        except Exception as e:
            logger.error("Error batch activating users: {}".format(str(e)))
            raise
    
    async def batch_deactivate_users(self, user_ids: List[int]) -> Dict[str, Any]:
        """批量停用用户"""
        try:
            success_count = 0
            failed_ids = []
            
            for user_id in user_ids:
                try:
                    if await self.deactivate_user(user_id):
                        success_count += 1
                    else:
                        failed_ids.append(user_id)
                except Exception:
                    failed_ids.append(user_id)
            
            return {
                "success_count": success_count,
                "failed_count": len(failed_ids),
                "failed_ids": failed_ids
            }
        except Exception as e:
            logger.error("Error batch deactivating users: {}".format(str(e)))
            raise
    
    # 重写基类的验证钩子
    async def _validate_create(self, obj_in: Union[UserCreate, Dict[str, Any]]) -> None:
        """创建前验证"""
        if isinstance(obj_in, dict):
            username = obj_in.get('username')
            email = obj_in.get('email')
        else:
            username = obj_in.username
            email = obj_in.email
        
        if username and await self.repository.check_username_exists(username):
            raise ConflictException("用户名 '{}' 已存在".format(username))

        if email and await self.repository.check_email_exists(email):
            raise ConflictException("邮箱 '{}' 已存在".format(email))
    
    async def _validate_update(
        self, 
        id: int, 
        obj_in: Union[UserUpdate, Dict[str, Any]], 
        existing: User
    ) -> None:
        """更新前验证"""
        if isinstance(obj_in, dict):
            username = obj_in.get('username')
            email = obj_in.get('email')
        else:
            username = obj_in.username
            email = obj_in.email
        
        if username and username != existing.username:
            if await self.repository.check_username_exists(username, id):
                raise ConflictException("用户名 '{}' 已存在".format(username))

        if email and email != existing.email:
            if await self.repository.check_email_exists(email, id):
                raise ConflictException("邮箱 '{}' 已存在".format(email))
