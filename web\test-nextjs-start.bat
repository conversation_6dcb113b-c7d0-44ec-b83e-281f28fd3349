@echo off
echo 测试 Next.js 启动...
echo.

cd /d "%~dp0"

echo 检查配置文件...
if exist next.config.js (
    echo ✅ next.config.js 存在
) else (
    echo ❌ next.config.js 缺失
    goto :error
)

if exist package.json (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 缺失
    goto :error
)

echo.
echo 检查 Node.js 版本...
node --version
echo.

echo 检查 pnpm 版本...
pnpm --version
echo.

echo 尝试启动 Next.js...
echo 如果成功，应该看到类似以下输出:
echo "- ready started server on 0.0.0.0:3000"
echo.
echo 按 Ctrl+C 停止服务器
echo.

pnpm dev

goto :end

:error
echo.
echo ❌ 配置检查失败，请先运行 fix-dependencies.bat
pause
exit /b 1

:end
echo.
echo 测试完成
pause
