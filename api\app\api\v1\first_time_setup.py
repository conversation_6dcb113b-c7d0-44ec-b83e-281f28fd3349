"""
首次设置API路由
"""
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from loguru import logger

from app.services.simple_first_time_setup import simple_first_time_setup_service


router = APIRouter(prefix="/setup", tags=["首次设置"])


class AdminUserCreate(BaseModel):
    username: str = Field(..., description="管理员用户名", min_length=3, max_length=50)
    password: str = Field(..., description="管理员密码", min_length=6, max_length=100)
    email: str = Field(..., description="管理员邮箱")
    full_name: Optional[str] = Field(None, description="管理员姓名")


class SystemInitRequest(BaseModel):
    admin_user: AdminUserCreate = Field(..., description="管理员用户信息")
    storage_path: Optional[str] = Field("./storage", description="默认存储路径")


@router.get("/status", summary="检查系统状态")
async def check_system_status() -> Dict[str, Any]:
    """
    检查系统是否需要首次设置
    
    Returns:
        Dict[str, Any]: 系统状态信息
    """
    try:
        status = await simple_first_time_setup_service.check_system_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"检查系统状态失败: {str(e)}"
        )


@router.post("/initialize", summary="初始化系统")
async def initialize_system(
    request: SystemInitRequest
) -> Dict[str, Any]:
    """
    执行系统初始化
    
    Args:
        request: 初始化请求数据
        
    Returns:
        Dict[str, Any]: 初始化结果
    """
    try:
        # 检查系统是否需要初始化
        status = await simple_first_time_setup_service.check_system_status()
        
        if not status.get("needs_setup", True):
            return {
                "success": True,
                "message": "系统已完成初始化",
                "data": status
            }
        
        # 执行初始化
        logger.info("API接收到的存储路径: {}".format(request.storage_path))

        admin_data = {
            "username": request.admin_user.username,
            "password": request.admin_user.password,
            "email": request.admin_user.email,
            "full_name": request.admin_user.full_name
        }

        logger.info("调用initialize_system，存储路径: {}".format(request.storage_path))
        result = await simple_first_time_setup_service.initialize_system(
            admin_data=admin_data,
            storage_path=request.storage_path
        )
        
        if result["success"]:
            return {
                "success": True,
                "message": "系统初始化成功",
                "data": {
                    "steps_completed": result["steps_completed"],
                    "admin_username": request.admin_user.username
                }
            }
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "系统初始化失败",
                    "errors": result["errors"]
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"系统初始化失败: {str(e)}"
        )


@router.get("/health", summary="健康检查")
async def health_check() -> Dict[str, Any]:
    """
    系统健康检查
    
    Returns:
        Dict[str, Any]: 健康状态
    """
    try:
        status = await simple_first_time_setup_service.check_system_status()
        
        return {
            "success": True,
            "status": "healthy" if not status.get("needs_setup", True) else "needs_setup",
            "data": {
                "database_connected": True,
                "tables_exist": status.get("tables", {}).get("all_exist", False),
                "has_admin": status.get("has_admin_user", False),
                "has_storage": status.get("has_storage_config", False)
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e)
        }


@router.post("/reset", summary="重置系统")
async def reset_system() -> Dict[str, Any]:
    """
    重置系统（仅开发环境）
    
    Returns:
        Dict[str, Any]: 重置结果
    """
    try:
        # 这里可以实现系统重置逻辑
        # 注意：这个功能应该只在开发环境中启用
        
        return {
            "success": True,
            "message": "系统重置功能暂未实现",
            "note": "此功能仅在开发环境中可用"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"系统重置失败: {str(e)}"
        )
