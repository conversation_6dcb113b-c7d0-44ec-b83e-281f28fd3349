"""
Celery配置管理服务
"""
import json
import os
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from loguru import logger

from app.models.celery_config import CeleryConfiguration, CeleryMetrics
from app.core.database import get_async_session


class CeleryConfigService:
    """Celery配置服务"""
    
    async def get_active_config(self, session: AsyncSession = None) -> Optional[CeleryConfiguration]:
        """获取当前激活的配置"""
        if session is None:
            async for session in get_async_session():
                return await self._get_active_config(session)
        else:
            return await self._get_active_config(session)
    
    async def _get_active_config(self, session: AsyncSession) -> Optional[CeleryConfiguration]:
        """内部获取激活配置方法"""
        query = select(CeleryConfiguration).where(CeleryConfiguration.is_active == True)
        result = await session.execute(query)
        config = result.scalar_one_or_none()
        
        # 如果没有配置，创建默认配置
        if not config:
            config = await self.create_default_config(session)
        
        return config
    
    async def create_default_config(self, session: AsyncSession = None) -> CeleryConfiguration:
        """创建默认配置"""
        if session is None:
            async for session in get_async_session():
                return await self._create_default_config(session)
        else:
            return await self._create_default_config(session)
    
    async def _create_default_config(self, session: AsyncSession) -> CeleryConfiguration:
        """内部创建默认配置方法"""
        # 从环境变量获取默认值
        config = CeleryConfiguration(
            redis_host=os.getenv('REDIS_HOST', '**************'),
            redis_port=int(os.getenv('REDIS_PORT', '6379')),
            redis_db=int(os.getenv('REDIS_DB', '10')),
            redis_password=os.getenv('REDIS_PASSWORD'),
            worker_concurrency=int(os.getenv('CELERY_WORKER_CONCURRENCY', '4')),
            worker_prefetch_multiplier=int(os.getenv('CELERY_WORKER_PREFETCH_MULTIPLIER', '1')),
            task_soft_time_limit=int(os.getenv('CELERY_TASK_SOFT_TIME_LIMIT', '300')),
            task_time_limit=int(os.getenv('CELERY_TASK_TIME_LIMIT', '600')),
            task_max_retries=int(os.getenv('CELERY_TASK_MAX_RETRIES', '3')),
            flower_port=int(os.getenv('FLOWER_PORT', '5555')),
            is_active=True
        )
        
        session.add(config)
        await session.commit()
        await session.refresh(config)
        
        logger.info("创建默认Celery配置")
        return config
    
    async def update_config(self, config_data: Dict[str, Any], session: AsyncSession = None) -> CeleryConfiguration:
        """更新配置"""
        if session is None:
            async for session in get_async_session():
                return await self._update_config(config_data, session)
        else:
            return await self._update_config(config_data, session)
    
    async def _update_config(self, config_data: Dict[str, Any], session: AsyncSession) -> CeleryConfiguration:
        """内部更新配置方法"""
        # 获取当前配置
        config = await self._get_active_config(session)
        
        # 更新字段
        for key, value in config_data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        await session.commit()
        await session.refresh(config)
        
        logger.info(f"更新Celery配置: {list(config_data.keys())}")
        return config
    
    def generate_celery_config(self, config: CeleryConfiguration) -> Dict[str, Any]:
        """生成Celery配置字典"""
        # 构建Redis URL
        if config.redis_password:
            redis_url = f"redis://:{config.redis_password}@{config.redis_host}:{config.redis_port}/{config.redis_db}"
        else:
            redis_url = f"redis://{config.redis_host}:{config.redis_port}/{config.redis_db}"
        
        # 解析任务路由
        task_routes = {}
        if config.task_routes:
            try:
                task_routes = json.loads(config.task_routes)
            except json.JSONDecodeError:
                logger.warning("任务路由配置解析失败，使用默认配置")
        
        # 默认任务路由
        if not task_routes:
            task_routes = {
                'app.tasks.upload_tasks.*': {'queue': 'upload_queue'},
                'app.tasks.file_tasks.*': {'queue': 'file_queue'},
            }
        
        # 解析结果后端传输选项
        result_backend_transport_options = {}
        if config.result_backend_transport_options:
            try:
                result_backend_transport_options = json.loads(config.result_backend_transport_options)
            except json.JSONDecodeError:
                logger.warning("结果后端传输选项解析失败")
        
        celery_config = {
            # 基础配置
            'broker_url': redis_url,
            'result_backend': redis_url,
            
            # 任务序列化
            'task_serializer': 'json',
            'accept_content': ['json'],
            'result_serializer': 'json',
            'timezone': config.timezone,
            'enable_utc': config.enable_utc,
            
            # 任务路由
            'task_routes': task_routes,
            'task_default_queue': config.task_default_queue,
            
            # Worker配置
            'worker_prefetch_multiplier': config.worker_prefetch_multiplier,
            'worker_max_tasks_per_child': config.worker_max_tasks_per_child,
            'worker_max_memory_per_child': config.worker_max_memory_per_child,
            
            # 任务执行配置
            'task_acks_late': True,
            'task_reject_on_worker_lost': True,
            'task_soft_time_limit': config.task_soft_time_limit,
            'task_time_limit': config.task_time_limit,
            'task_max_retries': config.task_max_retries,
            'task_default_retry_delay': config.task_default_retry_delay,
            
            # 结果配置
            'result_expires': config.result_expires,
            'result_backend_transport_options': result_backend_transport_options,
            
            # 监控配置
            'worker_send_task_events': config.worker_send_task_events,
            'task_send_sent_event': config.task_send_sent_event,
        }
        
        return celery_config
    
    async def save_metrics(self, metrics_data: Dict[str, Any], session: AsyncSession = None) -> CeleryMetrics:
        """保存指标数据"""
        if session is None:
            async for session in get_async_session():
                return await self._save_metrics(metrics_data, session)
        else:
            return await self._save_metrics(metrics_data, session)
    
    async def _save_metrics(self, metrics_data: Dict[str, Any], session: AsyncSession) -> CeleryMetrics:
        """内部保存指标方法"""
        # 序列化JSON字段
        queue_lengths = json.dumps(metrics_data.get('queue_lengths', {}))
        worker_memory_usage = json.dumps(metrics_data.get('worker_memory_usage', {}))
        
        metrics = CeleryMetrics(
            active_workers=metrics_data.get('active_workers', 0),
            total_workers=metrics_data.get('total_workers', 0),
            active_tasks=metrics_data.get('active_tasks', 0),
            processed_tasks=metrics_data.get('processed_tasks', 0),
            failed_tasks=metrics_data.get('failed_tasks', 0),
            retried_tasks=metrics_data.get('retried_tasks', 0),
            queue_lengths=queue_lengths,
            avg_task_runtime=metrics_data.get('avg_task_runtime', 0.0),
            task_throughput=metrics_data.get('task_throughput', 0.0),
            worker_memory_usage=worker_memory_usage,
            redis_memory_usage=metrics_data.get('redis_memory_usage', 0),
            redis_connected_clients=metrics_data.get('redis_connected_clients', 0)
        )
        
        session.add(metrics)
        await session.commit()
        await session.refresh(metrics)
        
        return metrics
    
    async def get_recent_metrics(self, limit: int = 100, session: AsyncSession = None) -> list:
        """获取最近的指标数据"""
        if session is None:
            async for session in get_async_session():
                return await self._get_recent_metrics(limit, session)
        else:
            return await self._get_recent_metrics(limit, session)
    
    async def _get_recent_metrics(self, limit: int, session: AsyncSession) -> list:
        """内部获取最近指标方法"""
        query = select(CeleryMetrics).order_by(CeleryMetrics.timestamp.desc()).limit(limit)
        result = await session.execute(query)
        return result.scalars().all()
    
    async def cleanup_old_metrics(self, days: int = 7, session: AsyncSession = None) -> int:
        """清理旧的指标数据"""
        if session is None:
            async for session in get_async_session():
                return await self._cleanup_old_metrics(days, session)
        else:
            return await self._cleanup_old_metrics(days, session)
    
    async def _cleanup_old_metrics(self, days: int, session: AsyncSession) -> int:
        """内部清理旧指标方法"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        query = select(CeleryMetrics).where(CeleryMetrics.timestamp < cutoff_date)
        result = await session.execute(query)
        old_metrics = result.scalars().all()
        
        count = len(old_metrics)
        
        for metrics in old_metrics:
            await session.delete(metrics)
        
        await session.commit()
        
        logger.info(f"清理了 {count} 条旧的Celery指标数据")
        return count


# 全局服务实例
celery_config_service = CeleryConfigService()
