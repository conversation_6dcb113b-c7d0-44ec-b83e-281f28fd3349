'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Scissors, 
  Play,
  Pause,
  RotateCcw,
  FileText,
  Loader2,
  AlertCircle,
  CheckCircle,
  Brain,
  Zap,
  Settings,
  Eye
} from 'lucide-react';
import apiClient from '@/lib/api';
import { isSegmentableFile } from '@/utils/fileTypeUtils';

interface FileInfo {
  file_id: string;
  file_name: string;
  file_path: string;
  file_size_formatted: string;
  file_extension: string;
  mime_type: string;
  created_at: string;
  modified_at: string;
  is_document: boolean;
}

interface SegmentConfig {
  method: 'paragraph' | 'sentence' | 'fixed_length' | 'semantic';
  max_length: number;
  overlap: number;
  preserve_formatting: boolean;
}

interface Segment {
  id: string;
  content: string;
  start_position: number;
  end_position: number;
  word_count: number;
}

const FileSegmentPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const encodedFileId = params.fileId as string;

  // 解码file_id
  const fileId = React.useMemo(() => {
    try {
      // 检查是否是有效的字符串
      if (!encodedFileId || encodedFileId.length === 0) {
        return encodedFileId;
      }

      // 先进行URL解码（处理%3D等字符）
      let decoded = decodeURIComponent(encodedFileId);

      // 然后进行base64解码
      try {
        decoded = atob(decoded);
      } catch (base64Error) {
        // 如果base64解码失败，可能是直接的文件ID
        console.warn('Base64 decode failed, using URL decoded value:', decoded);
      }

      return decoded;
    } catch (error) {
      console.error('Failed to decode file ID:', error);
      return encodedFileId; // 如果解码失败，使用原始值
    }
  }, [encodedFileId]);
  
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [segments, setSegments] = useState<Segment[]>([]);
  const [loading, setLoading] = useState(true);
  const [segmenting, setSegmenting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<SegmentConfig>({
    method: 'paragraph',
    max_length: 500,
    overlap: 50,
    preserve_formatting: true
  });

  useEffect(() => {
    if (fileId) {
      loadFileInfo();
    }
  }, [fileId]);

  const loadFileInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // 直接使用原始的encodedFileId，不要重复编码
      const response = await apiClient.get(`/api/v1/file-management/files/${encodedFileId}`);
      const file = response.data;
      setFileInfo(file);

      // 检查是否已有分段结果
      try {
        const segmentsResponse = await apiClient.get(`/api/v1/file-management/files/${encodedFileId}/segments`);
        setSegments(segmentsResponse.data.segments || []);
      } catch (segmentError) {
        // 没有分段结果，这是正常的
        setSegments([]);
      }
    } catch (err) {
      console.error('Failed to load file:', err);
      setError('加载文件失败');
    } finally {
      setLoading(false);
    }
  };



  const handleStartSegmentation = async () => {
    if (!fileInfo) return;

    try {
      setSegmenting(true);
      setError(null);

      const response = await apiClient.post(`/api/v1/file-management/files/${encodedFileId}/segment`, config);
      setSegments(response.data.segments || []);
    } catch (err) {
      console.error('Segmentation failed:', err);
      setError('文件分段失败');
    } finally {
      setSegmenting(false);
    }
  };

  const handleResetSegments = async () => {
    try {
      setSegments([]);
      setError(null);

      // 重新执行分段
      await handleStartSegment();
    } catch (err) {
      console.error('重新分段失败:', err);
      setError('重新分段失败');
    }
  };

  const renderSegmentConfig = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
          <Settings className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">分段配置</h3>
          <p className="text-sm text-gray-500">配置文档分段参数</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            分段方法
          </label>
          <select
            value={config.method}
            onChange={(e) => setConfig(prev => ({ ...prev, method: e.target.value as any }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="paragraph">按段落分段</option>
            <option value="sentence">按句子分段</option>
            <option value="fixed_length">固定长度分段</option>
            <option value="semantic">语义分段 (AI)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            最大长度 (字符)
          </label>
          <input
            type="number"
            value={config.max_length}
            onChange={(e) => setConfig(prev => ({ ...prev, max_length: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="100"
            max="2000"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            重叠长度 (字符)
          </label>
          <input
            type="number"
            value={config.overlap}
            onChange={(e) => setConfig(prev => ({ ...prev, overlap: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="0"
            max="200"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="preserve_formatting"
            checked={config.preserve_formatting}
            onChange={(e) => setConfig(prev => ({ ...prev, preserve_formatting: e.target.checked }))}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          <label htmlFor="preserve_formatting" className="ml-2 text-sm text-gray-700">
            保留格式
          </label>
        </div>
      </div>
    </div>
  );

  const renderSegmentResults = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg">
            <Brain className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">分段结果</h3>
            <p className="text-sm text-gray-500">共 {segments.length} 个分段</p>
          </div>
        </div>

        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleResetSegments}
          className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          重新分段
        </motion.button>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        <AnimatePresence>
          {segments.map((segment, index) => (
            <motion.div
              key={segment.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">
                  分段 {index + 1}
                </span>
                <span className="text-xs text-gray-500">
                  {segment.word_count} 字符
                </span>
              </div>
              <p className="text-sm text-gray-700 leading-relaxed line-clamp-3">
                {segment.content}
              </p>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">加载文件中...</p>
        </div>
      </div>
    );
  }

  if (error || !fileInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">加载失败</div>
          <div className="text-gray-600 mb-4">{error || '文件不存在'}</div>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  if (!isSegmentableFile(fileInfo)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <div className="text-lg font-medium text-gray-900 mb-2">不支持分段</div>
          <div className="text-gray-600 mb-4">此文件类型暂不支持智能分段</div>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.back()}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </motion.button>
              
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  智能分段 - {fileInfo.file_name}
                </h1>
                <p className="text-sm text-gray-500">
                  {fileInfo.file_size_formatted} • {fileInfo.file_extension.toUpperCase()}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.push(`/file-manager/view/${fileId}`)}
                className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Eye className="w-4 h-4 mr-2" />
                查看原文
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleStartSegmentation}
                disabled={segmenting}
                className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
                  segmenting
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700'
                }`}
              >
                {segmenting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Scissors className="w-4 h-4 mr-2" />
                )}
                {segmenting ? '分段中...' : '开始分段'}
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {renderSegmentConfig()}
          
          {segments.length > 0 && renderSegmentResults()}
          
          {segments.length === 0 && !segmenting && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
              <Scissors className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">开始智能分段</h3>
              <p className="text-gray-600 mb-4">
                配置分段参数后，点击"开始分段"按钮来智能分析文档结构
              </p>
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <Brain className="w-4 h-4 mr-1" />
                  AI智能分析
                </div>
                <div className="flex items-center">
                  <Zap className="w-4 h-4 mr-1" />
                  快速处理
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default FileSegmentPage;
