"""
Repository基类和接口
实现数据访问层的抽象
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Optional, Dict, Any, Union
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from loguru import logger

from app.models.base import BaseModel

# 泛型类型变量
ModelType = TypeVar("ModelType", bound=BaseModel)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class IRepository(ABC, Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Repository接口"""
    
    @abstractmethod
    async def get(self, id: int) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        pass
    
    @abstractmethod
    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        filters: Dict[str, Any] = None
    ) -> List[ModelType]:
        """获取多个记录"""
        pass
    
    @abstractmethod
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """创建记录"""
        pass
    
    @abstractmethod
    async def update(self, id: int, obj_in: UpdateSchemaType) -> Optional[ModelType]:
        """更新记录"""
        pass
    
    @abstractmethod
    async def delete(self, id: int) -> bool:
        """删除记录"""
        pass


class BaseRepository(IRepository[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Repository基类"""
    
    def __init__(self, model: type[ModelType], session: AsyncSession):
        self.model = model
        self.session = session
    
    async def get(self, id: int) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        try:
            result = await self.session.execute(
                select(self.model).where(self.model.id == id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting {self.model.__name__} with id {id}: {e}")
            return None
    
    async def get_by_field(self, field: str, value: Any) -> Optional[ModelType]:
        """根据字段获取单个记录"""
        try:
            result = await self.session.execute(
                select(self.model).where(getattr(self.model, field) == value)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting {self.model.__name__} by {field}={value}: {e}")
            return None
    
    async def get_all(self) -> List[ModelType]:
        """获取所有记录"""
        try:
            result = await self.session.execute(select(self.model))
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all {self.model.__name__}: {e}")
            return []

    async def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Dict[str, Any] = None,
        order_by: str = None,
        desc: bool = False
    ) -> List[ModelType]:
        """获取多个记录"""
        try:
            query = select(self.model)
            
            # 应用过滤条件
            if filters:
                conditions = []
                for field, value in filters.items():
                    if hasattr(self.model, field):
                        if isinstance(value, list):
                            conditions.append(getattr(self.model, field).in_(value))
                        elif isinstance(value, dict):
                            # 支持范围查询 {"gte": 10, "lte": 20}
                            if "gte" in value:
                                conditions.append(getattr(self.model, field) >= value["gte"])
                            if "lte" in value:
                                conditions.append(getattr(self.model, field) <= value["lte"])
                            if "gt" in value:
                                conditions.append(getattr(self.model, field) > value["gt"])
                            if "lt" in value:
                                conditions.append(getattr(self.model, field) < value["lt"])
                        else:
                            conditions.append(getattr(self.model, field) == value)
                
                if conditions:
                    query = query.where(and_(*conditions))
            
            # 排序
            if order_by and hasattr(self.model, order_by):
                order_field = getattr(self.model, order_by)
                if desc:
                    query = query.order_by(order_field.desc())
                else:
                    query = query.order_by(order_field)
            else:
                # 默认按ID排序
                query = query.order_by(self.model.id)
            
            # 分页
            query = query.offset(skip).limit(limit)
            
            result = await self.session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting multiple {self.model.__name__}: {e}")
            return []
    
    async def count(self, filters: Dict[str, Any] = None) -> int:
        """统计记录数量"""
        try:
            query = select(func.count(self.model.id))
            
            if filters:
                conditions = []
                for field, value in filters.items():
                    if hasattr(self.model, field):
                        conditions.append(getattr(self.model, field) == value)
                
                if conditions:
                    query = query.where(and_(*conditions))
            
            result = await self.session.execute(query)
            return result.scalar() or 0
            
        except Exception as e:
            logger.error(f"Error counting {self.model.__name__}: {e}")
            return 0
    
    async def create(self, obj_in: Union[CreateSchemaType, Dict[str, Any]]) -> ModelType:
        """创建记录"""
        try:
            if isinstance(obj_in, dict):
                db_obj = self.model(**obj_in)
            else:
                # 假设是Pydantic模型
                db_obj = self.model(**obj_in.dict())
            
            self.session.add(db_obj)
            await self.session.commit()
            await self.session.refresh(db_obj)
            
            logger.info(f"Created {self.model.__name__} with id {db_obj.id}")
            return db_obj
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error creating {self.model.__name__}: {e}")
            raise
    
    async def update(
        self, 
        id: int, 
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> Optional[ModelType]:
        """更新记录"""
        try:
            db_obj = await self.get(id)
            if not db_obj:
                return None
            
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                # 假设是Pydantic模型
                update_data = obj_in.dict(exclude_unset=True)
            
            # 更新字段
            for field, value in update_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)
            
            await self.session.commit()
            await self.session.refresh(db_obj)
            
            logger.info(f"Updated {self.model.__name__} with id {id}")
            return db_obj
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error updating {self.model.__name__} with id {id}: {e}")
            raise
    
    async def delete(self, id: int) -> bool:
        """删除记录"""
        try:
            result = await self.session.execute(
                delete(self.model).where(self.model.id == id)
            )
            await self.session.commit()
            
            deleted = result.rowcount > 0
            if deleted:
                logger.info(f"Deleted {self.model.__name__} with id {id}")
            
            return deleted
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error deleting {self.model.__name__} with id {id}: {e}")
            return False
    
    async def bulk_create(self, objs_in: List[Union[CreateSchemaType, Dict[str, Any]]]) -> List[ModelType]:
        """批量创建记录"""
        try:
            db_objs = []
            for obj_in in objs_in:
                if isinstance(obj_in, dict):
                    db_obj = self.model(**obj_in)
                else:
                    db_obj = self.model(**obj_in.dict())
                db_objs.append(db_obj)
            
            self.session.add_all(db_objs)
            await self.session.commit()
            
            # 刷新所有对象
            for db_obj in db_objs:
                await self.session.refresh(db_obj)
            
            logger.info(f"Bulk created {len(db_objs)} {self.model.__name__} records")
            return db_objs
            
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error bulk creating {self.model.__name__}: {e}")
            raise
    
    async def exists(self, id: int) -> bool:
        """检查记录是否存在"""
        try:
            result = await self.session.execute(
                select(func.count(self.model.id)).where(self.model.id == id)
            )
            return result.scalar() > 0
        except Exception as e:
            logger.error(f"Error checking existence of {self.model.__name__} with id {id}: {e}")
            return False
    
    async def get_or_create(
        self, 
        defaults: Dict[str, Any] = None, 
        **kwargs
    ) -> tuple[ModelType, bool]:
        """获取或创建记录"""
        try:
            # 尝试获取现有记录
            conditions = []
            for field, value in kwargs.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)
            
            if conditions:
                result = await self.session.execute(
                    select(self.model).where(and_(*conditions))
                )
                existing = result.scalar_one_or_none()
                
                if existing:
                    return existing, False
            
            # 创建新记录
            create_data = {**kwargs}
            if defaults:
                create_data.update(defaults)
            
            new_obj = await self.create(create_data)
            return new_obj, True
            
        except Exception as e:
            logger.error(f"Error in get_or_create for {self.model.__name__}: {e}")
            raise
