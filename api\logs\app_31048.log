2025-06-17 17:35:59.997 | INFO     | app.core.logging:setup_logging:110 | Logging configured for development environment
2025-06-17 17:35:59.997 | INFO     | app.core.middleware:setup_middleware:136 | Middleware setup completed
2025-06-17 17:36:00.027 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: auth v1.0.0
2025-06-17 17:36:00.028 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: file_manager v1.0.0
2025-06-17 17:36:00.030 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: rag_engine v1.0.0
2025-06-17 17:36:00.031 | INFO     | app.core.database:init_database:222 | Initializing database connection with URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:36:00.036 | INFO     | app.core.database:init_database:254 | Using async URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:36:00.036 | INFO     | app.core.database:init_database:255 | Using sync URL: postgresql+psycopg2://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:36:00.082 | INFO     | app.core.database:init_database:281 | Database connection initialized successfully for postgresql
2025-06-17 17:36:00.083 | INFO     | app.core.simple_migration:create_tables_directly:44 | Creating tables for database type: postgresql
2025-06-17 17:36:00.148 | INFO     | app.plugins.auth.plugin:initialize:36 | Auth plugin initialized
2025-06-17 17:36:00.148 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: auth
2025-06-17 17:36:00.148 | INFO     | app.plugins.file_manager.plugin:initialize:49 | File manager plugin initialized with upload dir: uploads
2025-06-17 17:36:00.149 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: file_manager
2025-06-17 17:36:00.149 | INFO     | app.plugins.rag_engine.plugin:initialize:64 | RAG engine plugin initialized
2025-06-17 17:36:00.149 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: rag_engine
2025-06-17 17:36:00.149 | INFO     | app.plugins.auth.plugin:startup:40 | Auth plugin started
2025-06-17 17:36:00.149 | INFO     | app.plugins.manager:startup:167 | Started plugin: auth
2025-06-17 17:36:00.149 | INFO     | app.plugins.file_manager.plugin:startup:57 | File manager plugin started
2025-06-17 17:36:00.150 | INFO     | app.plugins.manager:startup:167 | Started plugin: file_manager
2025-06-17 17:36:00.150 | INFO     | app.plugins.rag_engine.plugin:startup:69 | RAG engine plugin started
2025-06-17 17:36:00.150 | INFO     | app.plugins.manager:startup:167 | Started plugin: rag_engine
2025-06-17 17:36:00.151 | INFO     | app.services.upload_processor:start:34 | 上传处理器启动
2025-06-17 17:36:00.151 | INFO     | app.core.startup:startup_tasks:66 | 执行应用启动任务...
2025-06-17 17:36:00.152 | INFO     | app.core.startup:initialize_celery:30 | 自动启动Celery服务...
2025-06-17 17:36:00.152 | INFO     | app.core.startup:initialize_celery:55 | Celery指标收集器将在服务完全启动后手动启动
2025-06-17 17:36:00.152 | INFO     | app.core.startup:startup_tasks:71 | 应用启动任务完成
2025-06-17 17:36:00.196 | INFO     | app.core.celery_manager:_check_redis_connection:138 | Redis连接成功
2025-06-17 17:36:00.196 | INFO     | app.core.celery_manager:start_service:169 | 启动 worker 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=4 --queues=default,upload_queue,file_queue --hostname=worker@%h --logfile=logs/celery_worker.log
2025-06-17 17:36:02.207 | INFO     | app.core.celery_manager:start_service:191 | worker 服务启动成功 (PID: 31824)
2025-06-17 17:36:05.316 | INFO     | app.core.celery_manager:start_service:169 | 启动 beat 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app beat --loglevel=info --logfile=logs/celery_beat.log
2025-06-17 17:36:07.328 | INFO     | app.core.celery_manager:start_service:191 | beat 服务启动成功 (PID: 1324)
2025-06-17 17:36:09.384 | INFO     | app.core.celery_manager:_find_available_port:96 | 找到可用端口: 5555
2025-06-17 17:36:09.384 | INFO     | app.core.celery_manager:start_service:169 | 启动 flower 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app flower --port=5555 --basic_auth=admin:password --logfile=logs/celery_flower.log
2025-06-17 17:36:11.391 | INFO     | app.core.celery_manager:start_service:191 | flower 服务启动成功 (PID: 17652)
2025-06-17 17:36:12.393 | INFO     | app.core.startup:start_celery_background:40 | 所有Celery服务启动成功
2025-06-17 17:36:13.059 | INFO     | app.services.upload_processor:stop:42 | 上传处理器停止
2025-06-17 17:36:13.059 | INFO     | app.core.startup:shutdown_tasks:76 | 执行应用关闭任务...
2025-06-17 17:36:13.060 | INFO     | app.core.startup:shutdown_tasks:82 | Celery指标收集器已停止
2025-06-17 17:36:13.060 | INFO     | app.core.startup:shutdown_tasks:88 | 停止Celery服务...
2025-06-17 17:36:13.060 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 flower 服务 (PID: 17652)
2025-06-17 17:36:13.061 | INFO     | app.core.celery_manager:stop_service:236 | flower 服务已停止
2025-06-17 17:36:14.110 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 beat 服务 (PID: 1324)
2025-06-17 17:36:14.111 | INFO     | app.core.celery_manager:stop_service:236 | beat 服务已停止
2025-06-17 17:36:16.212 | INFO     | app.core.celery_manager:stop_service:223 | 正在停止 worker 服务 (PID: 31824)
2025-06-17 17:36:16.213 | INFO     | app.core.celery_manager:stop_service:236 | worker 服务已停止
2025-06-17 17:36:19.424 | INFO     | app.core.startup:shutdown_tasks:90 | Celery服务已停止
2025-06-17 17:36:19.425 | INFO     | app.core.startup:shutdown_tasks:94 | 应用关闭任务完成
2025-06-17 17:36:19.425 | INFO     | app.plugins.auth.plugin:shutdown:44 | Auth plugin shutdown
2025-06-17 17:36:19.425 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: auth
2025-06-17 17:36:19.425 | INFO     | app.plugins.file_manager.plugin:shutdown:61 | File manager plugin shutdown
2025-06-17 17:36:19.425 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: file_manager
2025-06-17 17:36:19.425 | INFO     | app.plugins.rag_engine.plugin:shutdown:74 | RAG engine plugin shutdown
2025-06-17 17:36:19.425 | INFO     | app.plugins.manager:shutdown:179 | Shutdown plugin: rag_engine
2025-06-17 17:36:19.427 | INFO     | app.core.database:close_database:348 | Async database connection closed
2025-06-17 17:36:19.427 | INFO     | app.core.database:close_database:352 | Sync database connection closed
