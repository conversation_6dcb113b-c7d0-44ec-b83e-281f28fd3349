"""
依赖注入系统
提供Repository和Service的依赖注入
"""

from typing import AsyncGenerator
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_async_session
from app.repositories.user import UserRepository
from app.repositories.storage import StorageConfigRepository, FileRecordRepository, SyncLogRepository
from app.services.user import UserService


# Database Session Dependency
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话依赖"""
    async for session in get_async_session():
        yield session


# Repository Dependencies
def get_user_repository(
    session: AsyncSession = Depends(get_db_session)
) -> UserRepository:
    """获取用户Repository依赖"""
    return UserRepository(session)


def get_storage_config_repository(
    session: AsyncSession = Depends(get_db_session)
) -> StorageConfigRepository:
    """获取存储配置Repository依赖"""
    return StorageConfigRepository(session)


def get_file_record_repository(
    session: AsyncSession = Depends(get_db_session)
) -> FileRecordRepository:
    """获取文件记录Repository依赖"""
    return FileRecordRepository(session)


def get_sync_log_repository(
    session: AsyncSession = Depends(get_db_session)
) -> SyncLogRepository:
    """获取同步日志Repository依赖"""
    return SyncLogRepository(session)


# Service Dependencies
def get_user_service(
    session: AsyncSession = Depends(get_db_session)
) -> UserService:
    """获取用户Service依赖"""
    return UserService(session)


# 认证相关依赖
from fastapi import HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.core.security import decode_access_token
from app.models.user import User

security = HTTPBearer(auto_error=False)  # 不自动抛出错误


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_service: UserService = Depends(get_user_service)
) -> User:
    """获取当前用户依赖"""
    # 检查是否提供了认证凭据
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        # 解码token
        payload = decode_access_token(credentials.credentials)
        user_id = payload.get("user_id") or payload.get("sub")

        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 获取用户
        user = await user_service.get(int(user_id))
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Inactive user",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录详细错误信息
        from loguru import logger
        logger.error(f"认证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户依赖"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前超级用户依赖"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


# 可选的认证依赖（不强制要求认证）
async def get_current_user_optional(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_service: UserService = Depends(get_user_service)
) -> User | None:
    """获取当前用户依赖（可选）"""
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, user_service)
    except HTTPException:
        return None


# 分页依赖
from fastapi import Query


class PaginationParams:
    """分页参数"""
    
    def __init__(
        self,
        skip: int = Query(0, ge=0, description="跳过的记录数"),
        limit: int = Query(20, ge=1, le=100, description="返回的记录数")
    ):
        self.skip = skip
        self.limit = limit


def get_pagination_params(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数")
) -> PaginationParams:
    """获取分页参数依赖"""
    return PaginationParams(skip=skip, limit=limit)


# 搜索依赖
class SearchParams:
    """搜索参数"""
    
    def __init__(
        self,
        q: str = Query(None, min_length=1, description="搜索关键词"),
        skip: int = Query(0, ge=0, description="跳过的记录数"),
        limit: int = Query(20, ge=1, le=100, description="返回的记录数")
    ):
        self.query = q
        self.skip = skip
        self.limit = limit


def get_search_params(
    q: str = Query(None, min_length=1, description="搜索关键词"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数")
) -> SearchParams:
    """获取搜索参数依赖"""
    return SearchParams(q=q, skip=skip, limit=limit)


# 排序依赖
class SortParams:
    """排序参数"""
    
    def __init__(
        self,
        order_by: str = Query("id", description="排序字段"),
        desc: bool = Query(False, description="是否降序")
    ):
        self.order_by = order_by
        self.desc = desc


def get_sort_params(
    order_by: str = Query("id", description="排序字段"),
    desc: bool = Query(False, description="是否降序")
) -> SortParams:
    """获取排序参数依赖"""
    return SortParams(order_by=order_by, desc=desc)


# 过滤依赖
from typing import Dict, Any, Optional


class FilterParams:
    """过滤参数"""
    
    def __init__(self, filters: Dict[str, Any] = None):
        self.filters = filters or {}


def get_filter_params(
    is_active: Optional[bool] = Query(None, description="是否激活"),
    is_superuser: Optional[bool] = Query(None, description="是否超级用户")
) -> FilterParams:
    """获取过滤参数依赖"""
    filters = {}
    
    if is_active is not None:
        filters["is_active"] = is_active
    
    if is_superuser is not None:
        filters["is_superuser"] = is_superuser
    
    return FilterParams(filters)


# 缓存依赖
async def get_cache():
    """获取缓存依赖"""
    try:
        from app.core.cache import get_redis_cache
        return await get_redis_cache()
    except Exception:
        # 如果Redis不可用，返回None
        return None


# 配置依赖
from app.core.config import get_settings


def get_app_settings():
    """获取应用配置依赖"""
    return get_settings()
