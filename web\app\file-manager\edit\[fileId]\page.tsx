'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Save, 
  Eye,
  FileText,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import apiClient from '@/lib/api';
import DocumentEditorWrapper from '@/components/DocumentEditor';

interface FileInfo {
  file_id: string;
  file_name: string;
  file_path: string;
  file_size_formatted: string;
  file_extension: string;
  mime_type: string;
  created_at: string;
  modified_at: string;
  is_image: boolean;
  is_video: boolean;
  is_document: boolean;
}

const FileEditPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const encodedFileId = params.fileId as string;

  // 解码file_id
  const fileId = React.useMemo(() => {
    try {
      // 检查是否是有效的字符串
      if (!encodedFileId || encodedFileId.length === 0) {
        return encodedFileId;
      }

      // 先进行URL解码（处理%3D等字符）
      let decoded = decodeURIComponent(encodedFileId);

      // 然后进行base64解码
      try {
        decoded = atob(decoded);
      } catch (base64Error) {
        // 如果base64解码失败，可能是直接的文件ID
        console.warn('Base64 decode failed, using URL decoded value:', decoded);
      }

      return decoded;
    } catch (error) {
      console.error('Failed to decode file ID:', error);
      return encodedFileId; // 如果解码失败，使用原始值
    }
  }, [encodedFileId]);

  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (fileId) {
      loadFileInfo();
    }
  }, [fileId]);

  const loadFileInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取文件信息
      const response = await apiClient.get(`/api/v1/file-management/files/${fileId}`);
      const file = response.data;
      setFileInfo(file);
    } catch (err) {
      console.error('Failed to load file:', err);
      setError('加载文件失败');
    } finally {
      setLoading(false);
    }
  };

  const isEditableFile = (file: FileInfo) => {
    const editableExtensions = [
      'txt', 'md', 'markdown', 'json', 'xml', 'html', 'css', 'js', 'ts', 'jsx', 'tsx',
      'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf'
    ];
    return editableExtensions.includes(file.file_extension.toLowerCase());
  };

  const isOfficeFile = (file: FileInfo) => {
    const officeExtensions = ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'txt', 'md', 'markdown'];
    return officeExtensions.includes(file.file_extension.toLowerCase());
  };

  // 如果是Office文件，使用新的编辑器组件
  if (fileInfo && isOfficeFile(fileInfo)) {
    return (
      <DocumentEditorWrapper
        fileId={fileId}
        fileName={fileInfo.file_name}
        fileType={fileInfo.file_extension}
        mode="edit"
        onClose={() => router.back()}
      />
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">加载文件中...</p>
        </div>
      </div>
    );
  }

  if (error || !fileInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">加载失败</div>
          <div className="text-gray-600 mb-4">{error || '文件不存在'}</div>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  if (!isEditableFile(fileInfo)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <div className="text-lg font-medium text-gray-900 mb-2">不支持编辑</div>
          <div className="text-gray-600 mb-4">此文件类型暂不支持在线编辑</div>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 头部导航 */}
      <div className="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => router.back()}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </motion.button>
              
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  编辑 {fileInfo.file_name}
                </h1>
                <p className="text-sm text-gray-500">
                  {fileInfo.file_size_formatted} • {fileInfo.file_extension.toUpperCase()}
                  {hasChanges && <span className="text-orange-500 ml-2">• 未保存</span>}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* 保存状态指示器 */}
              {saveStatus === 'success' && (
                <div className="flex items-center text-green-600 text-sm">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  已保存
                </div>
              )}
              
              {saveStatus === 'error' && (
                <div className="flex items-center text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  保存失败
                </div>
              )}

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handlePreview}
                className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Eye className="w-4 h-4 mr-2" />
                预览
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleSave}
                disabled={!hasChanges || saving}
                className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
                  hasChanges && !saving
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {saving ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {saving ? '保存中...' : '保存'}
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      {/* 编辑器区域 */}
      <div className="flex-1 max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 h-full overflow-hidden"
        >
          {renderEditor()}
        </motion.div>
      </div>
    </div>
  );
};

export default FileEditPage;
