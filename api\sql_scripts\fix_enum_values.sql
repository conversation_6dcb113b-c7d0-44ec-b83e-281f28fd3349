-- 修复枚举值不匹配问题的SQL脚本
-- 将数据库中的小写枚举值改为大写，以匹配Python模型
-- 适用于PostgreSQL数据库

-- 1. 首先备份现有数据（如果有的话）
CREATE TABLE IF NOT EXISTS document_segment_tasks_backup AS 
SELECT * FROM document_segment_tasks WHERE 1=0;

CREATE TABLE IF NOT EXISTS document_segments_backup AS 
SELECT * FROM document_segments WHERE 1=0;

-- 2. 如果表中有数据，先备份
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM document_segment_tasks LIMIT 1) THEN
        INSERT INTO document_segment_tasks_backup SELECT * FROM document_segment_tasks;
        RAISE NOTICE '已备份 document_segment_tasks 表数据';
    END IF;
    
    IF EXISTS (SELECT 1 FROM document_segments LIMIT 1) THEN
        INSERT INTO document_segments_backup SELECT * FROM document_segments;
        RAISE NOTICE '已备份 document_segments 表数据';
    END IF;
END $$;

-- 3. 删除现有表（如果存在）
DROP TABLE IF EXISTS document_segments CASCADE;
DROP TABLE IF EXISTS document_segment_tasks CASCADE;
DROP TABLE IF EXISTS segment_templates CASCADE;
DROP TABLE IF EXISTS vector_indexes CASCADE;

-- 4. 删除现有枚举类型
DROP TYPE IF EXISTS segment_method_enum CASCADE;
DROP TYPE IF EXISTS segment_status_enum CASCADE;
DROP TYPE IF EXISTS vectorize_status_enum CASCADE;

-- 5. 重新创建枚举类型（使用大写值）
CREATE TYPE segment_method_enum AS ENUM ('PARAGRAPH', 'SENTENCE', 'FIXED_LENGTH', 'SEMANTIC');
CREATE TYPE segment_status_enum AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');
CREATE TYPE vectorize_status_enum AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');

-- 6. 重新创建文档分段任务表
CREATE TABLE document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL,           -- 任务唯一ID
    task_name VARCHAR(200) NOT NULL,               -- 任务名称
    description TEXT,                              -- 任务描述
    
    -- 文件信息
    file_ids JSONB NOT NULL,                       -- 文件ID列表
    total_files INTEGER DEFAULT 0,                 -- 总文件数
    processed_files INTEGER DEFAULT 0,             -- 已处理文件数
    
    -- 分段配置
    segment_method segment_method_enum NOT NULL,    -- 分段方法
    max_length INTEGER DEFAULT 500,                -- 最大长度(字符)
    overlap INTEGER DEFAULT 50,                    -- 重叠长度(字符)
    preserve_formatting BOOLEAN DEFAULT true,      -- 是否保留格式
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,     -- 是否启用向量化
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002', -- 嵌入模型
    vector_dimension INTEGER DEFAULT 1536,         -- 向量维度
    chunk_size INTEGER DEFAULT 1000,               -- 向量化块大小
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh',             -- 文档语言
    remove_stopwords BOOLEAN DEFAULT false,        -- 是否移除停用词
    normalize_text BOOLEAN DEFAULT true,           -- 是否标准化文本
    extract_keywords BOOLEAN DEFAULT true,         -- 是否提取关键词
    
    -- 任务状态
    status segment_status_enum DEFAULT 'PENDING',  -- 任务状态
    progress FLOAT DEFAULT 0.0,                    -- 进度百分比
    error_message TEXT,                            -- 错误信息
    
    -- 统计信息
    total_segments INTEGER DEFAULT 0,              -- 总分段数
    total_vectors INTEGER DEFAULT 0,               -- 总向量数
    
    -- 元数据
    segment_metadata JSONB,                        -- 分段元数据
    
    -- 时间信息
    started_at TIMESTAMP WITH TIME ZONE,           -- 开始时间
    completed_at TIMESTAMP WITH TIME ZONE,         -- 完成时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 知识库相关字段
    kb_id VARCHAR(255),                            -- 关联的知识库ID
    enable_vectorization_kb BOOLEAN DEFAULT false  -- 知识库向量化开关
);

-- 7. 重新创建文档分段表
CREATE TABLE document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,        -- 分段唯一ID
    task_id INTEGER NOT NULL,                      -- 任务ID（不创建外键约束）
    file_id VARCHAR(36) NOT NULL,                  -- 文件ID
    
    -- 分段内容
    content TEXT NOT NULL,                         -- 分段内容
    content_hash VARCHAR(64),                      -- 内容哈希值
    
    -- 位置信息
    segment_index INTEGER NOT NULL,                -- 分段索引
    start_position INTEGER DEFAULT 0,              -- 开始位置
    end_position INTEGER DEFAULT 0,                -- 结束位置
    
    -- 统计信息
    word_count INTEGER DEFAULT 0,                  -- 字符数
    sentence_count INTEGER DEFAULT 0,              -- 句子数
    
    -- 元数据
    segment_metadata JSONB,                        -- 分段元数据
    keywords JSONB,                                -- 关键词列表
    
    -- 向量化信息
    vectorize_status vectorize_status_enum DEFAULT 'PENDING', -- 向量化状态
    vector_id VARCHAR(100),                        -- 向量ID
    embedding_vector JSONB,                        -- 嵌入向量
    
    -- 质量评分
    quality_score FLOAT DEFAULT 0.0,               -- 质量评分
    readability_score FLOAT DEFAULT 0.0,           -- 可读性评分
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 知识库相关字段
    kb_id VARCHAR(255),                            -- 关联的知识库ID
    vector_status VARCHAR(50) DEFAULT 'PENDING',   -- 向量化状态
    embedding_model VARCHAR(255),                  -- 嵌入模型
    vector_dimension INTEGER                       -- 向量维度
);

-- 8. 重新创建分段模板表
CREATE TABLE segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) UNIQUE NOT NULL,    -- 模板名称
    description TEXT,                              -- 模板描述
    
    -- 分段配置
    segment_method segment_method_enum NOT NULL,    -- 分段方法
    max_length INTEGER DEFAULT 500,                -- 最大长度(字符)
    overlap INTEGER DEFAULT 50,                    -- 重叠长度(字符)
    preserve_formatting BOOLEAN DEFAULT true,      -- 是否保留格式
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,     -- 是否启用向量化
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002', -- 嵌入模型
    vector_dimension INTEGER DEFAULT 1536,         -- 向量维度
    chunk_size INTEGER DEFAULT 1000,               -- 向量化块大小
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh',             -- 文档语言
    remove_stopwords BOOLEAN DEFAULT false,        -- 是否移除停用词
    normalize_text BOOLEAN DEFAULT true,           -- 是否标准化文本
    extract_keywords BOOLEAN DEFAULT true,         -- 是否提取关键词
    
    -- 模板属性
    is_default BOOLEAN DEFAULT false,              -- 是否为默认模板
    is_system BOOLEAN DEFAULT false,               -- 是否为系统模板
    usage_count INTEGER DEFAULT 0,                 -- 使用次数
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 9. 重新创建向量索引表
CREATE TABLE vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(100) UNIQUE NOT NULL,       -- 索引名称
    description TEXT,                              -- 索引描述
    
    -- 索引配置
    embedding_model VARCHAR(100) NOT NULL,         -- 嵌入模型
    vector_dimension INTEGER NOT NULL,             -- 向量维度
    similarity_metric VARCHAR(20) DEFAULT 'cosine', -- 相似度度量
    
    -- 统计信息
    total_vectors INTEGER DEFAULT 0,               -- 总向量数
    total_documents INTEGER DEFAULT 0,             -- 总文档数
    index_size_mb FLOAT DEFAULT 0.0,               -- 索引大小(MB)
    
    -- 状态信息
    is_active BOOLEAN DEFAULT true,                -- 是否激活
    last_updated_at TIMESTAMP WITH TIME ZONE,      -- 最后更新时间
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 10. 创建所有索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_created_at ON document_segment_tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_kb_id ON document_segment_tasks(kb_id);

CREATE INDEX IF NOT EXISTS idx_document_segments_segment_id ON document_segments(segment_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_vectorize_status ON document_segments(vectorize_status);
CREATE INDEX IF NOT EXISTS idx_document_segments_content_hash ON document_segments(content_hash);
CREATE INDEX IF NOT EXISTS idx_document_segments_kb_id ON document_segments(kb_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_vector_status ON document_segments(vector_status);
CREATE INDEX IF NOT EXISTS idx_document_segments_vector_id ON document_segments(vector_id);

CREATE INDEX IF NOT EXISTS idx_segment_templates_template_name ON segment_templates(template_name);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_system ON segment_templates(is_system);

CREATE INDEX IF NOT EXISTS idx_vector_indexes_index_name ON vector_indexes(index_name);
CREATE INDEX IF NOT EXISTS idx_vector_indexes_embedding_model ON vector_indexes(embedding_model);
CREATE INDEX IF NOT EXISTS idx_vector_indexes_is_active ON vector_indexes(is_active);

-- 11. 插入默认模板数据
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
(
    '默认分段模板', '系统默认的文档分段模板，适用于大多数文档类型',
    'PARAGRAPH', 1000, 100, true, false, 'text-embedding-ada-002', 1536,
    'zh', false, true, true, true, true
),
(
    '短文本分段', '适用于短文档或需要精细分段的场景',
    'SENTENCE', 500, 50, true, false, 'text-embedding-ada-002', 1536,
    'zh', false, true, true, false, true
),
(
    '长文档分段', '适用于长文档的快速分段处理',
    'FIXED_LENGTH', 2000, 200, false, false, 'text-embedding-ada-002', 1536,
    'zh', true, true, false, false, true
);

-- 12. 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 13. 添加更新时间触发器
CREATE TRIGGER update_document_segment_tasks_updated_at
    BEFORE UPDATE ON document_segment_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_segments_updated_at
    BEFORE UPDATE ON document_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_segment_templates_updated_at
    BEFORE UPDATE ON segment_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vector_indexes_updated_at
    BEFORE UPDATE ON vector_indexes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 14. 添加表和列注释
COMMENT ON TABLE document_segment_tasks IS '文档分段任务表';
COMMENT ON TABLE document_segments IS '文档分段表';
COMMENT ON TABLE segment_templates IS '分段模板表';
COMMENT ON TABLE vector_indexes IS '向量索引表';

COMMENT ON COLUMN document_segment_tasks.task_id IS '任务唯一ID';
COMMENT ON COLUMN document_segment_tasks.file_ids IS '文件ID列表(JSON格式)';
COMMENT ON COLUMN document_segment_tasks.segment_method IS '分段方法：PARAGRAPH, SENTENCE, FIXED_LENGTH, SEMANTIC';
COMMENT ON COLUMN document_segment_tasks.status IS '任务状态：PENDING, PROCESSING, COMPLETED, FAILED';

COMMENT ON COLUMN document_segments.segment_id IS '分段唯一ID';
COMMENT ON COLUMN document_segments.task_id IS '关联的任务ID（无外键约束）';
COMMENT ON COLUMN document_segments.vectorize_status IS '向量化状态：PENDING, PROCESSING, COMPLETED, FAILED';
COMMENT ON COLUMN document_segments.embedding_vector IS '嵌入向量(JSON格式)';

-- 15. 显示修复完成信息
DO $$
BEGIN
    RAISE NOTICE '=== 枚举值修复完成 ===';
    RAISE NOTICE '1. 已删除旧的枚举类型和表';
    RAISE NOTICE '2. 已创建新的大写枚举类型';
    RAISE NOTICE '3. 已重新创建所有相关表';
    RAISE NOTICE '4. 已添加所有索引和触发器';
    RAISE NOTICE '5. 已插入默认模板数据';
    RAISE NOTICE '现在可以正常使用分段功能了！';
END $$;
