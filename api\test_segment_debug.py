#!/usr/bin/env python3
"""
文档分段功能调试脚本
用于测试和调试分段任务的完整流程
"""

import os
import sys
import json
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_sync_session
from app.models.document_segment import DocumentSegmentTask, DocumentSegment, SegmentStatus
from app.models.file_management import FileRecord
from app.core.celery_config import celery_app


def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    try:
        db = next(get_sync_session())
        
        # 测试分段任务表
        task_count = db.query(DocumentSegmentTask).count()
        print(f"✅ 分段任务表连接正常，当前记录数: {task_count}")
        
        # 测试分段表
        segment_count = db.query(DocumentSegment).count()
        print(f"✅ 分段表连接正常，当前记录数: {segment_count}")
        
        # 测试文件表
        file_count = db.query(FileRecord).count()
        print(f"✅ 文件表连接正常，当前记录数: {file_count}")
        
        db.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_celery_connection():
    """测试Celery连接"""
    print("\n🔍 测试Celery连接...")
    try:
        # 测试Celery连接
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print(f"✅ Celery连接正常，活跃Worker数: {len(stats)}")
            for worker, stat in stats.items():
                print(f"   Worker: {worker}")
            return True
        else:
            print("⚠️ 没有活跃的Celery Worker")
            return False
    except Exception as e:
        print(f"❌ Celery连接失败: {e}")
        return False


def get_test_file():
    """获取测试文件"""
    print("\n🔍 查找测试文件...")
    try:
        db = next(get_sync_session())
        
        # 查找第一个可用的文件
        file_record = db.query(FileRecord).first()
        if file_record:
            print(f"✅ 找到测试文件: {file_record.file_name} (ID: {file_record.file_id})")
            
            # 检查文件是否存在
            if os.path.exists(file_record.file_path):
                print(f"✅ 文件路径有效: {file_record.file_path}")
                db.close()
                return file_record.file_id
            else:
                print(f"⚠️ 文件路径不存在: {file_record.file_path}")
        
        db.close()
        return None
    except Exception as e:
        print(f"❌ 查找测试文件失败: {e}")
        return None


def create_test_task(file_id):
    """创建测试分段任务"""
    print(f"\n🔍 创建测试分段任务...")
    try:
        db = next(get_sync_session())
        
        task = DocumentSegmentTask(
            task_name=f"调试测试任务 - {datetime.now().strftime('%Y%m%d_%H%M%S')}",
            description="用于调试的测试分段任务",
            file_ids=[file_id],
            total_files=1,
            segment_method="paragraph",
            max_length=500,
            overlap=50,
            preserve_formatting=True,
            enable_vectorization=False,  # 暂时禁用向量化
            language="zh",
            normalize_text=True,
            extract_keywords=True,
            remove_stopwords=False,
            status=SegmentStatus.PENDING
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 创建测试任务成功: {task.task_name} (ID: {task.id}, task_id: {task.task_id})")
        
        db.close()
        return task.id, task.task_id
    except Exception as e:
        print(f"❌ 创建测试任务失败: {e}")
        return None, None


def start_celery_task(task_id):
    """启动Celery分段任务"""
    print(f"\n🔍 启动Celery分段任务...")
    try:
        from app.tasks.segment_tasks import process_batch_segment_task
        
        # 启动Celery任务
        celery_task = process_batch_segment_task.delay(task_id)
        print(f"✅ Celery任务已启动: {celery_task.id}")
        
        return celery_task.id
    except Exception as e:
        print(f"❌ 启动Celery任务失败: {e}")
        return None


def monitor_task_progress(task_id, celery_task_id, max_wait_time=300):
    """监控任务进度"""
    print(f"\n🔍 监控任务进度 (最大等待时间: {max_wait_time}秒)...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            db = next(get_sync_session())
            
            # 查询任务状态
            task = db.query(DocumentSegmentTask).filter(DocumentSegmentTask.id == task_id).first()
            if task:
                print(f"📊 任务状态: {task.status}, 进度: {task.progress}%, 已处理文件: {task.processed_files}/{task.total_files}")
                
                # 查询分段数量
                segments_count = db.query(DocumentSegment).filter(DocumentSegment.task_id == task_id).count()
                print(f"📊 已创建分段数: {segments_count}")
                
                if task.status in [SegmentStatus.COMPLETED, SegmentStatus.FAILED]:
                    print(f"✅ 任务完成: {task.status}")
                    if task.error_message:
                        print(f"❌ 错误信息: {task.error_message}")
                    db.close()
                    return task.status == SegmentStatus.COMPLETED
            
            db.close()
            
            # 查询Celery任务状态
            if celery_task_id:
                celery_result = celery_app.AsyncResult(celery_task_id)
                print(f"📊 Celery状态: {celery_result.state}")
                if celery_result.info:
                    print(f"📊 Celery信息: {celery_result.info}")
            
            time.sleep(5)  # 等待5秒后再次检查
            
        except Exception as e:
            print(f"❌ 监控过程中出错: {e}")
            time.sleep(5)
    
    print("⏰ 监控超时")
    return False


def check_results(task_id):
    """检查分段结果"""
    print(f"\n🔍 检查分段结果...")
    try:
        db = next(get_sync_session())
        
        # 查询任务信息
        task = db.query(DocumentSegmentTask).filter(DocumentSegmentTask.id == task_id).first()
        if task:
            print(f"📊 任务最终状态: {task.status}")
            print(f"📊 总分段数: {task.total_segments}")
            print(f"📊 总向量数: {task.total_vectors}")
            print(f"📊 进度: {task.progress}%")
        
        # 查询分段详情
        segments = db.query(DocumentSegment).filter(DocumentSegment.task_id == task_id).all()
        print(f"📊 实际分段记录数: {len(segments)}")
        
        if segments:
            print("📋 分段详情 (前3个):")
            for i, segment in enumerate(segments[:3]):
                print(f"   分段 {i+1}: {segment.content[:50]}...")
                print(f"   质量评分: {segment.quality_score}, 可读性: {segment.readability_score}")
                print(f"   向量化状态: {segment.vectorize_status}")
        
        db.close()
        return len(segments) > 0
    except Exception as e:
        print(f"❌ 检查结果失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始文档分段功能调试")
    print("=" * 60)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("❌ 数据库连接失败，退出测试")
        return
    
    # 2. 测试Celery连接
    celery_ok = test_celery_connection()
    if not celery_ok:
        print("⚠️ Celery连接失败，但继续测试")
    
    # 3. 获取测试文件
    file_id = get_test_file()
    if not file_id:
        print("❌ 没有可用的测试文件，退出测试")
        return
    
    # 4. 创建测试任务
    task_id, task_uuid = create_test_task(file_id)
    if not task_id:
        print("❌ 创建测试任务失败，退出测试")
        return
    
    # 5. 启动Celery任务
    celery_task_id = None
    if celery_ok:
        celery_task_id = start_celery_task(task_id)
        if not celery_task_id:
            print("❌ 启动Celery任务失败")
    
    # 6. 监控任务进度
    if celery_task_id:
        success = monitor_task_progress(task_id, celery_task_id)
    else:
        print("⚠️ 跳过任务监控（Celery不可用）")
        success = False
    
    # 7. 检查结果
    has_results = check_results(task_id)
    
    # 8. 总结
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"   数据库连接: ✅")
    print(f"   Celery连接: {'✅' if celery_ok else '❌'}")
    print(f"   任务创建: ✅")
    print(f"   任务执行: {'✅' if success else '❌'}")
    print(f"   分段结果: {'✅' if has_results else '❌'}")
    
    if success and has_results:
        print("\n🎉 所有测试通过！分段功能正常工作")
    else:
        print("\n⚠️ 部分测试失败，请检查以下问题:")
        if not celery_ok:
            print("   - Celery服务未启动或配置错误")
        if not success:
            print("   - 分段任务执行失败")
        if not has_results:
            print("   - 分段结果未正确保存到数据库")
    
    print(f"\n📝 测试任务ID: {task_id} (UUID: {task_uuid})")
    print("可以在数据库中查看详细信息")


if __name__ == "__main__":
    main()
