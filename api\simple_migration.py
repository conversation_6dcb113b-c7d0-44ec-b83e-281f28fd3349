#!/usr/bin/env python3
"""
简单的数据库迁移脚本
"""
import asyncio
import asyncpg
import os
from loguru import logger


async def create_upload_tasks_table():
    """创建upload_tasks表"""
    
    # 从环境变量获取数据库连接信息
    db_host = os.getenv('DB_HOST', '**************')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'xhc_rag')
    db_user = os.getenv('DB_USERNAME', 'postgres')
    db_password = os.getenv('DB_PASSWORD', 'XHC12345')
    
    # 构建连接字符串
    dsn = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(dsn)
        logger.info("数据库连接成功")
        
        # 检查表是否存在
        exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'upload_tasks'
            );
        """)
        
        if exists:
            logger.info("✅ upload_tasks表已存在")
            await conn.close()
            return
        
        # 创建表
        create_table_sql = """
        CREATE TABLE upload_tasks (
            id SERIAL PRIMARY KEY,
            task_id VARCHAR(100) UNIQUE NOT NULL,
            file_name VARCHAR(500) NOT NULL,
            file_size BIGINT NOT NULL,
            file_path TEXT NOT NULL,
            storage_id INTEGER NOT NULL,
            upload_path TEXT NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            progress INTEGER DEFAULT 0,
            uploaded_bytes BIGINT DEFAULT 0,
            error_message TEXT,
            retry_count INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            started_at TIMESTAMP WITH TIME ZONE,
            completed_at TIMESTAMP WITH TIME ZONE
        );
        """
        
        await conn.execute(create_table_sql)
        logger.info("✅ upload_tasks表创建成功")
        
        # 创建索引
        indexes = [
            "CREATE INDEX idx_upload_tasks_task_id ON upload_tasks(task_id);",
            "CREATE INDEX idx_upload_tasks_status ON upload_tasks(status);",
            "CREATE INDEX idx_upload_tasks_created_at ON upload_tasks(created_at);",
            "CREATE INDEX idx_upload_tasks_storage_id ON upload_tasks(storage_id);"
        ]
        
        for index_sql in indexes:
            await conn.execute(index_sql)
        
        logger.info("✅ 索引创建成功")
        
        # 关闭连接
        await conn.close()
        logger.info("✅ 数据库迁移完成！")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(create_upload_tasks_table())
