"""
存储概览API路由
提供存储统计、文件总数、共享文件、最近访问等数据
"""
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from app.core.database import get_sync_session
# from app.core.dependencies import get_current_user
# from app.models.user import User
from app.models.file_management import StorageConfig
from app.models.upload_task import UploadTask

router = APIRouter(prefix="/api/v1/storage-overview", tags=["storage-overview"])


@router.get("/summary")
async def get_storage_summary():
    """获取存储概览摘要"""
    try:
        # 模拟数据，实际应从数据库获取
        total_capacity = 1024 * 1024 * 1024 * 100  # 100GB
        total_used_space = 1024 * 1024 * 1024 * 45  # 45GB
        total_files = 1247
        shared_files = 89
        recent_access = 23

        return {
            "success": True,
            "message": "获取存储概览成功",
            "data": {
                "total_capacity": total_capacity,
                "total_capacity_formatted": _format_bytes(total_capacity),
                "total_files": total_files,
                "shared_files": shared_files,
                "recent_access": recent_access,
                "used_space": total_used_space,
                "used_space_formatted": _format_bytes(total_used_space),
                "usage_percentage": (total_used_space / total_capacity * 100) if total_capacity > 0 else 0,
                "storage_count": 1,
                "storage_details": [],
                "last_updated": datetime.utcnow().isoformat()
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储概览失败: {str(e)}")


@router.get("/storage/{storage_id}/details")
async def get_storage_details(
    storage_id: int
):
    """获取指定存储的详细信息"""
    try:
        # 模拟存储详情数据
        storage_info = {
            'id': storage_id,
            'name': f'存储{storage_id}',
            'type': 'local',
            'base_path': 'E:/test2',
            'total_capacity': 1024 * 1024 * 1024 * 100,  # 100GB
            'used_space': 1024 * 1024 * 1024 * 45,  # 45GB
            'available_space': 1024 * 1024 * 1024 * 55,  # 55GB
            'file_count': 1247,
            'folder_count': 156,
            'health_status': 'healthy'
        }

        recent_files_data = [
            {
                "id": 1,
                "filename": "document.pdf",
                "file_size": 1024 * 1024 * 2,  # 2MB
                "file_size_formatted": "2.0 MB",
                "upload_time": datetime.utcnow().isoformat(),
                "file_path": "/documents/document.pdf"
            }
        ]

        return {
            "success": True,
            "message": "获取存储详情成功",
            "data": {
                **storage_info,
                "recent_files": recent_files_data
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取存储详情失败: {str(e)}")


@router.get("/file-types")
async def get_file_type_distribution():
    """获取文件类型分布"""
    try:
        # 模拟文件类型分布数据
        formatted_data = [
            {'type': 'document', 'count': 450, 'size': 1024*1024*500, 'size_formatted': '500.0 MB', 'percentage': 36.1},
            {'type': 'image', 'count': 320, 'size': 1024*1024*800, 'size_formatted': '800.0 MB', 'percentage': 25.7},
            {'type': 'video', 'count': 89, 'size': 1024*1024*1200, 'size_formatted': '1.2 GB', 'percentage': 7.1},
            {'type': 'audio', 'count': 156, 'size': 1024*1024*300, 'size_formatted': '300.0 MB', 'percentage': 12.5},
            {'type': 'archive', 'count': 67, 'size': 1024*1024*200, 'size_formatted': '200.0 MB', 'percentage': 5.4},
            {'type': 'other', 'count': 165, 'size': 1024*1024*150, 'size_formatted': '150.0 MB', 'percentage': 13.2}
        ]

        total_files = sum(item['count'] for item in formatted_data)
        total_size = sum(item['size'] for item in formatted_data)

        return {
            "success": True,
            "message": "获取文件类型分布成功",
            "data": {
                "file_types": formatted_data,
                "total_files": total_files,
                "total_size": total_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件类型分布失败: {str(e)}")


async def _calculate_storage_info(storage: StorageConfig) -> Dict:
    """计算存储信息"""
    try:
        config = storage.config or {}
        base_path = config.get('base_path', '')
        
        storage_info = {
            'id': storage.id,
            'name': storage.name,
            'type': storage.storage_type,
            'base_path': base_path,
            'total_capacity': 0,
            'used_space': 0,
            'available_space': 0,
            'file_count': 0,
            'folder_count': 0,
            'health_status': 'healthy'
        }
        
        if storage.storage_type == 'local' and base_path and os.path.exists(base_path):
            # 计算本地存储信息
            try:
                # 获取磁盘空间信息
                statvfs = os.statvfs(base_path) if hasattr(os, 'statvfs') else None
                if statvfs:
                    storage_info['total_capacity'] = statvfs.f_blocks * statvfs.f_frsize
                    storage_info['available_space'] = statvfs.f_bavail * statvfs.f_frsize
                    storage_info['used_space'] = storage_info['total_capacity'] - storage_info['available_space']
                else:
                    # Windows系统使用shutil
                    import shutil
                    total, used, free = shutil.disk_usage(base_path)
                    storage_info['total_capacity'] = total
                    storage_info['used_space'] = used
                    storage_info['available_space'] = free
                
                # 计算文件和文件夹数量
                file_count = 0
                folder_count = 0
                
                for root, dirs, files in os.walk(base_path):
                    file_count += len(files)
                    folder_count += len(dirs)
                
                storage_info['file_count'] = file_count
                storage_info['folder_count'] = folder_count
                
            except Exception as e:
                print(f"计算存储信息失败: {e}")
                storage_info['health_status'] = 'error'
        
        elif storage.storage_type in ['minio', 'ftp', 'sftp']:
            # 对于其他存储类型，使用数据库中的统计信息
            storage_info['total_capacity'] = 1024 * 1024 * 1024 * 100  # 默认100GB
            storage_info['used_space'] = storage.total_size or 0
            storage_info['available_space'] = storage_info['total_capacity'] - storage_info['used_space']
            storage_info['file_count'] = storage.total_files or 0
        
        return storage_info
        
    except Exception as e:
        print(f"计算存储信息失败: {e}")
        return {
            'id': storage.id,
            'name': storage.name,
            'type': storage.storage_type,
            'base_path': config.get('base_path', ''),
            'total_capacity': 0,
            'used_space': 0,
            'available_space': 0,
            'file_count': 0,
            'folder_count': 0,
            'health_status': 'error'
        }


def _format_bytes(bytes_value: int) -> str:
    """格式化字节数"""
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = float(bytes_value)
    unit_index = 0
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size)} {units[unit_index]}"
    else:
        return f"{size:.1f} {units[unit_index]}"
