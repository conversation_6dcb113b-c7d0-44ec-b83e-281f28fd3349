#!/usr/bin/env python3
"""
测试文件ID生成和解析
"""
import urllib.parse

def test_file_id():
    # 测试文件路径
    file_path = '/1-深圳市政务云服务申请表-民生警务平台汇总20250605(1)(1).docx'
    storage_id = 1

    # 生成file_id
    encoded_path = urllib.parse.quote(file_path, safe='/')
    file_id = f'local_{storage_id}_{encoded_path}'
    print(f'Generated file_id: {file_id}')

    # 解析file_id
    parts = file_id.split('_', 2)
    print(f'Parts: {parts}')

    if len(parts) >= 3:
        storage_type = parts[0]
        storage_id_parsed = int(parts[1])
        encoded_path_parsed = parts[2]
        decoded_path = urllib.parse.unquote(encoded_path_parsed)
        
        print(f'Storage type: {storage_type}')
        print(f'Storage ID: {storage_id_parsed}')
        print(f'Encoded path: {encoded_path_parsed}')
        print(f'Decoded path: {decoded_path}')
        print(f'Original path matches: {file_path == decoded_path}')
        
        # 测试URL中的file_id
        url_file_id = 'local_1_/1-%E6%B7%B1%E5%9C%B3%E5%B8%82%E6%94%BF%E5%8A%A1%E4%BA%91%E6%9C%8D%E5%8A%A1%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%B0%91%E7%94%9F%E8%AD%A6%E5%8A%A1%E5%B9%B3%E5%8F%B0%E6%B1%87%E6%80%BB20250605(1)(1).docx'
        print(f'\nTesting URL file_id: {url_file_id}')
        
        url_parts = url_file_id.split('_', 2)
        if len(url_parts) >= 3:
            url_storage_type = url_parts[0]
            url_storage_id = int(url_parts[1])
            url_encoded_path = url_parts[2]
            url_decoded_path = urllib.parse.unquote(url_encoded_path)
            
            print(f'URL Storage type: {url_storage_type}')
            print(f'URL Storage ID: {url_storage_id}')
            print(f'URL Encoded path: {url_encoded_path}')
            print(f'URL Decoded path: {url_decoded_path}')

if __name__ == '__main__':
    test_file_id()
