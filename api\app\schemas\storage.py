"""
存储相关的Pydantic Schema
"""

from datetime import datetime
from typing import Optional, Dict, Any, Literal
from pydantic import BaseModel, Field, validator


class StorageConfigBase(BaseModel):
    """存储配置基础Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="存储名称")
    storage_type: Literal["local", "minio", "ftp", "sftp"] = Field(..., description="存储类型")
    config: Dict[str, Any] = Field(..., description="存储配置")
    is_default: bool = Field(False, description="是否默认存储")
    is_active: bool = Field(True, description="是否激活")


class StorageConfigCreate(StorageConfigBase):
    """创建存储配置Schema"""
    
    @validator('config')
    def validate_config(cls, v, values):
        storage_type = values.get('storage_type')
        if storage_type == 'local':
            required_fields = ['base_path']
        elif storage_type == 'minio':
            required_fields = ['endpoint', 'access_key', 'secret_key', 'bucket_name']
        elif storage_type in ['ftp', 'sftp']:
            required_fields = ['host', 'port', 'username', 'password']
        else:
            required_fields = []
        
        for field in required_fields:
            if field not in v:
                raise ValueError(f'{storage_type} 存储类型需要配置字段: {field}')
        
        return v


class StorageConfigUpdate(BaseModel):
    """更新存储配置Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="存储名称")
    config: Optional[Dict[str, Any]] = Field(None, description="存储配置")
    is_default: Optional[bool] = Field(None, description="是否默认存储")
    is_active: Optional[bool] = Field(None, description="是否激活")


class StorageConfigResponse(StorageConfigBase):
    """存储配置响应Schema"""
    id: int = Field(..., description="存储ID")
    total_files: int = Field(0, description="总文件数")
    total_size: int = Field(0, description="总大小(字节)")
    last_sync_at: Optional[datetime] = Field(None, description="最后同步时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class StorageConfigListResponse(BaseModel):
    """存储配置列表响应Schema"""
    storages: list[StorageConfigResponse] = Field(..., description="存储配置列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class StorageStatsResponse(BaseModel):
    """存储统计响应Schema"""
    total_storages: int = Field(..., description="总存储数")
    active_storages: int = Field(..., description="活跃存储数")
    inactive_storages: int = Field(..., description="非活跃存储数")
    type_stats: Dict[str, int] = Field(..., description="按类型统计")
    total_files: int = Field(..., description="总文件数")
    total_size: int = Field(..., description="总大小(字节)")


class StorageTestRequest(BaseModel):
    """存储测试请求Schema"""
    storage_type: Literal["local", "minio", "ftp", "sftp"] = Field(..., description="存储类型")
    config: Dict[str, Any] = Field(..., description="存储配置")


class StorageTestResponse(BaseModel):
    """存储测试响应Schema"""
    success: bool = Field(..., description="测试是否成功")
    message: str = Field(..., description="测试结果消息")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


class FileRecordBase(BaseModel):
    """文件记录基础Schema"""
    file_id: str = Field(..., description="文件ID")
    storage_id: int = Field(..., description="存储ID")
    file_path: str = Field(..., description="文件路径")
    file_name: str = Field(..., description="文件名")
    parent_path: Optional[str] = Field(None, description="父路径")
    file_type: Optional[str] = Field(None, description="文件类型")
    file_size: Optional[int] = Field(None, description="文件大小")
    mime_type: Optional[str] = Field(None, description="MIME类型")
    file_extension: Optional[str] = Field(None, description="文件扩展名")
    file_hash: Optional[str] = Field(None, description="文件哈希")
    file_created_at: Optional[datetime] = Field(None, description="文件创建时间")
    file_modified_at: Optional[datetime] = Field(None, description="文件修改时间")
    status: str = Field("active", description="状态")
    file_metadata: Optional[Dict[str, Any]] = Field(None, description="文件元数据")
    thumbnail_path: Optional[str] = Field(None, description="缩略图路径")
    has_thumbnail: bool = Field(False, description="是否有缩略图")


class FileRecordResponse(FileRecordBase):
    """文件记录响应Schema"""
    id: int = Field(..., description="记录ID")
    last_sync_at: Optional[datetime] = Field(None, description="最后同步时间")
    sync_version: int = Field(1, description="同步版本")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class FileRecordListResponse(BaseModel):
    """文件记录列表响应Schema"""
    files: list[FileRecordResponse] = Field(..., description="文件记录列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class SyncLogBase(BaseModel):
    """同步日志基础Schema"""
    storage_id: int = Field(..., description="存储ID")
    sync_type: str = Field(..., description="同步类型")
    files_scanned: int = Field(0, description="扫描文件数")
    files_added: int = Field(0, description="新增文件数")
    files_updated: int = Field(0, description="更新文件数")
    files_deleted: int = Field(0, description="删除文件数")
    status: str = Field(..., description="同步状态")
    error_message: Optional[str] = Field(None, description="错误消息")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration_seconds: Optional[int] = Field(None, description="持续时间(秒)")


class SyncLogResponse(SyncLogBase):
    """同步日志响应Schema"""
    id: int = Field(..., description="日志ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class SyncLogListResponse(BaseModel):
    """同步日志列表响应Schema"""
    logs: list[SyncLogResponse] = Field(..., description="同步日志列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class FileSearchRequest(BaseModel):
    """文件搜索请求Schema"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    storage_id: Optional[int] = Field(None, description="存储ID")
    file_type: Optional[str] = Field(None, description="文件类型")
    skip: int = Field(0, ge=0, description="跳过数量")
    limit: int = Field(20, ge=1, le=100, description="限制数量")


class StorageSyncRequest(BaseModel):
    """存储同步请求Schema"""
    storage_id: int = Field(..., description="存储ID")
    sync_type: Literal["full", "incremental"] = Field("incremental", description="同步类型")
    force: bool = Field(False, description="是否强制同步")


class StorageSyncResponse(BaseModel):
    """存储同步响应Schema"""
    success: bool = Field(..., description="同步是否成功")
    message: str = Field(..., description="同步结果消息")
    sync_log_id: Optional[int] = Field(None, description="同步日志ID")
