-- 完整的文档分段功能数据库修复脚本
-- 适用于Windows系统，无外键约束
-- 2025-06-17

-- 1. 检查并创建文档分段任务表
DROP TABLE IF EXISTS document_segment_tasks CASCADE;
CREATE TABLE document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- 文件信息
    file_ids JSONB NOT NULL DEFAULT '[]'::jsonb,
    total_files INTEGER DEFAULT 0,
    processed_files INTEGER DEFAULT 0,
    
    -- 分段配置
    segment_method VARCHAR(50) DEFAULT 'paragraph' CHECK (segment_method IN ('paragraph', 'sentence', 'fixed_length', 'semantic')),
    max_length INTEGER DEFAULT 500 CHECK (max_length >= 100 AND max_length <= 5000),
    overlap INTEGER DEFAULT 50 CHECK (overlap >= 0 AND overlap <= 500),
    preserve_formatting BOOLEAN DEFAULT true,
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536 CHECK (vector_dimension >= 512 AND vector_dimension <= 4096),
    chunk_size INTEGER DEFAULT 1000 CHECK (chunk_size >= 500 AND chunk_size <= 3000),
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh' CHECK (language IN ('zh', 'en', 'auto')),
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    
    -- 任务状态
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    progress FLOAT DEFAULT 0.0 CHECK (progress >= 0.0 AND progress <= 100.0),
    error_message TEXT,
    
    -- 统计信息
    total_segments INTEGER DEFAULT 0,
    total_vectors INTEGER DEFAULT 0,
    
    -- 元数据
    segment_metadata JSONB DEFAULT '{}'::jsonb,
    
    -- 时间信息
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. 检查并创建文档分段表
DROP TABLE IF EXISTS document_segments CASCADE;
CREATE TABLE document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL DEFAULT gen_random_uuid()::text,
    task_id INTEGER NOT NULL, -- 关联到document_segment_tasks.id，但不创建外键
    file_id VARCHAR(36) NOT NULL,
    
    -- 分段内容
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    
    -- 位置信息
    segment_index INTEGER NOT NULL CHECK (segment_index >= 0),
    start_position INTEGER DEFAULT 0 CHECK (start_position >= 0),
    end_position INTEGER DEFAULT 0 CHECK (end_position >= 0),
    
    -- 统计信息
    word_count INTEGER DEFAULT 0 CHECK (word_count >= 0),
    sentence_count INTEGER DEFAULT 0 CHECK (sentence_count >= 0),
    
    -- 元数据
    segment_metadata JSONB DEFAULT '{}'::jsonb,
    keywords JSONB DEFAULT '[]'::jsonb,
    
    -- 向量化信息
    vectorize_status VARCHAR(20) DEFAULT 'pending' CHECK (vectorize_status IN ('pending', 'processing', 'completed', 'failed')),
    vector_id VARCHAR(100),
    embedding_vector JSONB,
    
    -- 质量评分
    quality_score FLOAT DEFAULT 0.0 CHECK (quality_score >= 0.0 AND quality_score <= 1.0),
    readability_score FLOAT DEFAULT 0.0 CHECK (readability_score >= 0.0 AND readability_score <= 1.0),
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. 检查并创建分段模板表
DROP TABLE IF EXISTS segment_templates CASCADE;
CREATE TABLE segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 分段配置
    segment_method VARCHAR(50) NOT NULL CHECK (segment_method IN ('paragraph', 'sentence', 'fixed_length', 'semantic')),
    max_length INTEGER DEFAULT 500 CHECK (max_length >= 100 AND max_length <= 5000),
    overlap INTEGER DEFAULT 50 CHECK (overlap >= 0 AND overlap <= 500),
    preserve_formatting BOOLEAN DEFAULT true,
    
    -- 向量化配置
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536 CHECK (vector_dimension >= 512 AND vector_dimension <= 4096),
    chunk_size INTEGER DEFAULT 1000 CHECK (chunk_size >= 500 AND chunk_size <= 3000),
    
    -- 高级配置
    language VARCHAR(10) DEFAULT 'zh' CHECK (language IN ('zh', 'en', 'auto')),
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    
    -- 模板属性
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0 CHECK (usage_count >= 0),
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 4. 检查并创建向量索引表
DROP TABLE IF EXISTS vector_indexes CASCADE;
CREATE TABLE vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- 索引配置
    embedding_model VARCHAR(100) NOT NULL,
    vector_dimension INTEGER NOT NULL CHECK (vector_dimension >= 512 AND vector_dimension <= 4096),
    similarity_metric VARCHAR(20) DEFAULT 'cosine' CHECK (similarity_metric IN ('cosine', 'euclidean', 'dot_product')),
    
    -- 统计信息
    total_vectors INTEGER DEFAULT 0 CHECK (total_vectors >= 0),
    total_documents INTEGER DEFAULT 0 CHECK (total_documents >= 0),
    index_size_mb FLOAT DEFAULT 0.0 CHECK (index_size_mb >= 0.0),
    
    -- 状态信息
    is_active BOOLEAN DEFAULT true,
    last_updated_at TIMESTAMP WITH TIME ZONE,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. 创建性能优化索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_created_at ON document_segment_tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_segment_id ON document_segments(segment_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_content_hash ON document_segments(content_hash);
CREATE INDEX IF NOT EXISTS idx_document_segments_vectorize_status ON document_segments(vectorize_status);
CREATE INDEX IF NOT EXISTS idx_document_segments_created_at ON document_segments(created_at);

CREATE INDEX IF NOT EXISTS idx_segment_templates_template_name ON segment_templates(template_name);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_system ON segment_templates(is_system);

CREATE INDEX IF NOT EXISTS idx_vector_indexes_index_name ON vector_indexes(index_name);
CREATE INDEX IF NOT EXISTS idx_vector_indexes_is_active ON vector_indexes(is_active);

-- 6. 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 7. 为所有表添加更新时间触发器
DROP TRIGGER IF EXISTS update_document_segment_tasks_updated_at ON document_segment_tasks;
CREATE TRIGGER update_document_segment_tasks_updated_at
    BEFORE UPDATE ON document_segment_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_document_segments_updated_at ON document_segments;
CREATE TRIGGER update_document_segments_updated_at
    BEFORE UPDATE ON document_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_segment_templates_updated_at ON segment_templates;
CREATE TRIGGER update_segment_templates_updated_at
    BEFORE UPDATE ON segment_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_vector_indexes_updated_at ON vector_indexes;
CREATE TRIGGER update_vector_indexes_updated_at
    BEFORE UPDATE ON vector_indexes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. 插入默认分段模板
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES
(
    '通用文档分段',
    '适用于大多数文档的通用分段配置，平衡分段质量和处理效率',
    'paragraph', 500, 50,
    true, true, 'text-embedding-ada-002', 1536,
    1000, 'zh', false, true, true,
    true, true
),
(
    '长文档分段',
    '适用于长文档的分段配置，使用较大的分段长度以保持上下文完整性',
    'paragraph', 1000, 100,
    true, true, 'text-embedding-ada-002', 1536,
    1500, 'zh', false, true, true,
    false, true
),
(
    '精细分段',
    '适用于需要精细分段的文档，使用较小的分段长度以提高检索精度',
    'sentence', 200, 20,
    true, true, 'text-embedding-ada-002', 1536,
    500, 'zh', true, true, true,
    false, true
),
(
    '语义分段',
    '使用AI语义理解进行智能分段，适合复杂文档结构',
    'semantic', 800, 80,
    true, true, 'text-embedding-3-small', 1536,
    1200, 'zh', false, true, true,
    false, true
)
ON CONFLICT (template_name) DO NOTHING;

-- 9. 插入默认向量索引
INSERT INTO vector_indexes (
    index_name, description, embedding_model, vector_dimension,
    similarity_metric, is_active
) VALUES
(
    'default_index',
    '默认向量索引，使用OpenAI text-embedding-ada-002模型',
    'text-embedding-ada-002', 1536,
    'cosine', true
),
(
    'high_precision_index',
    '高精度向量索引，使用OpenAI text-embedding-3-large模型',
    'text-embedding-3-large', 3072,
    'cosine', true
)
ON CONFLICT (index_name) DO NOTHING;

-- 10. 验证表创建和数据插入
SELECT 'Tables created and data inserted successfully!' as status;

-- 显示创建的表和记录数
SELECT
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count,
    CASE
        WHEN table_name = 'document_segment_tasks' THEN (SELECT COUNT(*) FROM document_segment_tasks)
        WHEN table_name = 'document_segments' THEN (SELECT COUNT(*) FROM document_segments)
        WHEN table_name = 'segment_templates' THEN (SELECT COUNT(*) FROM segment_templates)
        WHEN table_name = 'vector_indexes' THEN (SELECT COUNT(*) FROM vector_indexes)
        ELSE 0
    END as record_count
FROM information_schema.tables t
WHERE table_schema = 'public'
AND table_name IN ('document_segment_tasks', 'document_segments', 'segment_templates', 'vector_indexes')
ORDER BY table_name;
