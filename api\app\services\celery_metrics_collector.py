"""
Celery指标收集器
"""
import asyncio
import json
import time
import os
from typing import Dict, Any
from datetime import datetime, timedelta
from loguru import logger

# 延迟导入避免循环依赖


class CeleryMetricsCollector:
    """Celery指标收集器"""
    
    def __init__(self):
        self.last_metrics = {}
        self.collection_interval = int(os.getenv('CELERY_METRICS_INTERVAL', '30'))  # 30秒收集一次
        self.is_running = False
        self.task = None
        self.enable_collection = os.getenv('CELERY_METRICS_ENABLED', 'true').lower() == 'true'  # 是否启用指标收集

    def _get_empty_metrics(self) -> Dict[str, Any]:
        """获取空的指标数据"""
        return {
            'timestamp': datetime.utcnow(),
            'active_workers': 0,
            'total_workers': 0,
            'active_tasks': 0,
            'processed_tasks': 0,
            'failed_tasks': 0,
            'retried_tasks': 0,
            'queue_lengths': {},
            'avg_task_runtime': 0.0,
            'task_throughput': 0.0,
            'worker_memory_usage': {},
            'redis_memory_usage': 0,
            'redis_connected_clients': 0
        }
    
    async def start_collection(self):
        """开始收集指标"""
        if not self.enable_collection:
            logger.info("指标收集已禁用，跳过启动")
            return

        if self.is_running:
            logger.warning("指标收集器已在运行")
            return

        self.is_running = True
        self.task = asyncio.create_task(self._collection_loop())
        logger.info("Celery指标收集器已启动")
    
    async def stop_collection(self):
        """停止收集指标"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        logger.info("Celery指标收集器已停止")
    
    async def _collection_loop(self):
        """指标收集循环"""
        while self.is_running:
            try:
                metrics = await self.collect_metrics()
                if metrics:  # 只有在成功收集到指标时才记录
                    logger.debug(f"收集到Celery指标: {metrics.get('active_workers', 0)} workers, {metrics.get('active_tasks', 0)} tasks")
                await asyncio.sleep(self.collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"指标收集循环异常: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def collect_metrics(self) -> Dict[str, Any]:
        """收集当前指标"""
        try:
            # 延迟导入Celery应用
            from app.core.celery_config import celery_app

            # 检查Celery服务是否运行
            from app.core.celery_manager import celery_manager
            celery_status = celery_manager.get_status()

            # 如果没有worker运行，返回空指标
            if not celery_status.get('services', {}).get('worker', {}).get('running', False):
                logger.debug("Celery Worker未运行，跳过指标收集")
                return self._get_empty_metrics()

            # 获取Celery inspect对象，设置超时时间
            inspect = celery_app.control.inspect(timeout=5.0)
            
            # 收集各种指标
            metrics = {
                'timestamp': datetime.utcnow(),
                'active_workers': 0,
                'total_workers': 0,
                'active_tasks': 0,
                'processed_tasks': 0,
                'failed_tasks': 0,
                'retried_tasks': 0,
                'queue_lengths': {},
                'avg_task_runtime': 0.0,
                'task_throughput': 0.0,
                'worker_memory_usage': {},
                'redis_memory_usage': 0,
                'redis_connected_clients': 0
            }
            
            # 获取活跃的workers
            try:
                active_workers = inspect.active()
                if active_workers:
                    metrics['total_workers'] = len(active_workers)
                    metrics['active_workers'] = len([w for w in active_workers.values() if w])

                    # 统计活跃任务
                    total_active_tasks = 0
                    for worker_tasks in active_workers.values():
                        if worker_tasks:
                            total_active_tasks += len(worker_tasks)
                    metrics['active_tasks'] = total_active_tasks
                else:
                    logger.debug("没有获取到活跃的workers")
            except Exception as e:
                logger.warning(f"获取活跃workers失败: {e}")
                # 使用默认值
            
            # 获取统计信息
            try:
                stats = inspect.stats()
                if stats:
                    total_processed = 0
                    total_failed = 0
                    total_retried = 0
                    worker_memory = {}

                    for worker_name, worker_stats in stats.items():
                        if worker_stats:
                            # 任务统计
                            total_processed += worker_stats.get('total', {}).get('tasks.processed', 0)
                            total_failed += worker_stats.get('total', {}).get('tasks.failed', 0)
                            total_retried += worker_stats.get('total', {}).get('tasks.retried', 0)

                            # 内存使用
                            rusage = worker_stats.get('rusage', {})
                            if rusage:
                                worker_memory[worker_name] = {
                                    'memory_rss': rusage.get('maxrss', 0),
                                    'memory_vms': rusage.get('ixrss', 0)
                                }

                    metrics['processed_tasks'] = total_processed
                    metrics['failed_tasks'] = total_failed
                    metrics['retried_tasks'] = total_retried
                    metrics['worker_memory_usage'] = worker_memory
                else:
                    logger.debug("没有获取到worker统计信息")
            except Exception as e:
                logger.warning(f"获取worker统计信息失败: {e}")
                # 使用默认值
            
            # 获取队列长度
            try:
                queue_lengths = await self._get_queue_lengths()
                metrics['queue_lengths'] = queue_lengths
            except Exception as e:
                logger.warning(f"获取队列长度失败: {e}")
            
            # 获取Redis指标
            try:
                redis_metrics = await self._get_redis_metrics()
                metrics.update(redis_metrics)
            except Exception as e:
                logger.warning(f"获取Redis指标失败: {e}")
            
            # 计算性能指标
            if self.last_metrics:
                metrics.update(self._calculate_performance_metrics(metrics))
            
            # 保存指标到数据库
            from app.services.celery_config_service import celery_config_service
            await celery_config_service.save_metrics(metrics)
            
            # 更新最后指标
            self.last_metrics = metrics.copy()
            
            return metrics
            
        except Exception as e:
            logger.warning(f"收集Celery指标失败: {e}")
            # 返回空指标而不是空字典，确保数据结构一致
            return self._get_empty_metrics()
    
    async def _get_queue_lengths(self) -> Dict[str, int]:
        """获取队列长度"""
        try:
            import redis
            from app.services.celery_config_service import celery_config_service
            
            # 获取Redis配置
            config = await celery_config_service.get_active_config()
            if not config:
                return {}
            
            # 连接Redis
            r = redis.Redis(
                host=config.redis_host,
                port=config.redis_port,
                db=config.redis_db,
                password=config.redis_password,
                decode_responses=True
            )
            
            # 获取队列长度
            queues = ['default', 'upload_queue', 'file_queue']
            queue_lengths = {}
            
            for queue in queues:
                try:
                    length = r.llen(queue)
                    queue_lengths[queue] = length
                except Exception as e:
                    logger.warning(f"获取队列 {queue} 长度失败: {e}")
                    queue_lengths[queue] = 0
            
            return queue_lengths
            
        except Exception as e:
            logger.error(f"获取队列长度失败: {e}")
            return {}
    
    async def _get_redis_metrics(self) -> Dict[str, Any]:
        """获取Redis指标"""
        try:
            import redis
            from app.services.celery_config_service import celery_config_service
            
            # 获取Redis配置
            config = await celery_config_service.get_active_config()
            if not config:
                return {}
            
            # 连接Redis
            r = redis.Redis(
                host=config.redis_host,
                port=config.redis_port,
                db=config.redis_db,
                password=config.redis_password
            )
            
            # 获取Redis信息
            info = r.info()
            
            return {
                'redis_memory_usage': int(info.get('used_memory', 0) / 1024 / 1024),  # MB
                'redis_connected_clients': info.get('connected_clients', 0)
            }
            
        except Exception as e:
            logger.error(f"获取Redis指标失败: {e}")
            return {
                'redis_memory_usage': 0,
                'redis_connected_clients': 0
            }
    
    def _calculate_performance_metrics(self, current_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """计算性能指标"""
        try:
            # 计算时间差
            current_time = current_metrics['timestamp']
            last_time = self.last_metrics.get('timestamp')
            
            if not last_time:
                return {'avg_task_runtime': 0.0, 'task_throughput': 0.0}
            
            time_diff = (current_time - last_time).total_seconds()
            if time_diff <= 0:
                return {'avg_task_runtime': 0.0, 'task_throughput': 0.0}
            
            # 计算任务吞吐量
            current_processed = current_metrics.get('processed_tasks', 0)
            last_processed = self.last_metrics.get('processed_tasks', 0)
            
            tasks_processed = current_processed - last_processed
            task_throughput = tasks_processed / time_diff if time_diff > 0 else 0.0
            
            # 估算平均任务运行时间（简化计算）
            active_tasks = current_metrics.get('active_tasks', 0)
            avg_task_runtime = time_diff / max(tasks_processed, 1) if tasks_processed > 0 else 0.0
            
            return {
                'avg_task_runtime': round(avg_task_runtime, 2),
                'task_throughput': round(task_throughput, 2)
            }
            
        except Exception as e:
            logger.warning(f"计算性能指标失败: {e}")
            return {'avg_task_runtime': 0.0, 'task_throughput': 0.0}
    
    async def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标（不保存到数据库）"""
        return await self.collect_metrics()
    
    async def get_historical_metrics(self, hours: int = 24) -> list:
        """获取历史指标"""
        try:
            from app.services.celery_config_service import celery_config_service

            # 计算时间范围
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(hours=hours)

            # 从数据库获取指标
            metrics_list = await celery_config_service.get_recent_metrics(limit=hours * 2)  # 每小时2个点
            
            # 过滤时间范围并转换为字典
            filtered_metrics = []
            for metrics in metrics_list:
                if metrics.timestamp >= start_time:
                    filtered_metrics.append(metrics.to_dict())
            
            return filtered_metrics
            
        except Exception as e:
            logger.error(f"获取历史指标失败: {e}")
            return []


# 全局收集器实例
metrics_collector = CeleryMetricsCollector()
