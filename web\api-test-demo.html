<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实数据API测试演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        .api-endpoint {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 10px;
        }
        .method-get {
            background: #dbeafe;
            color: #1e40af;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .result-area {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .instructions h4 {
            color: #92400e;
            margin-top: 0;
        }
        .code-block {
            background: #374151;
            color: #f9fafb;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">真实数据API测试演示</h1>
            <p class="subtitle">存储概览、系统状态、路径导航 - 完整的API测试工具</p>
        </div>

        <div class="instructions">
            <h4>🚀 启动说明</h4>
            <p>请先启动API服务器，然后使用下面的测试按钮验证API功能：</p>
            <div class="code-block">
# 方法1：启动完整API服务器
cd api
.venv\Scripts\python.exe -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 方法2：启动测试API服务器
cd api
.venv\Scripts\python.exe test_apis.py
            </div>
        </div>

        <!-- 存储概览API测试 -->
        <div class="test-section">
            <div class="section-title">
                <div class="section-icon" style="background: linear-gradient(135deg, #3b82f6, #2563eb);">📊</div>
                存储概览API测试
            </div>
            
            <div class="api-endpoint">
                <span class="method method-get">GET</span>
                <span>/api/v1/storage-overview/summary</span>
                <button class="test-button" onclick="testAPI('/api/v1/storage-overview/summary', 'storage-summary')">测试</button>
            </div>
            
            <div class="api-endpoint">
                <span class="method method-get">GET</span>
                <span>/api/v1/storage-overview/storage/1/details</span>
                <button class="test-button" onclick="testAPI('/api/v1/storage-overview/storage/1/details', 'storage-details')">测试</button>
            </div>
            
            <div class="api-endpoint">
                <span class="method method-get">GET</span>
                <span>/api/v1/storage-overview/file-types</span>
                <button class="test-button" onclick="testAPI('/api/v1/storage-overview/file-types', 'file-types')">测试</button>
            </div>
            
            <div class="result-area" id="storage-result">点击上方按钮测试存储概览API...</div>
        </div>

        <!-- 系统状态API测试 -->
        <div class="test-section">
            <div class="section-title">
                <div class="section-icon" style="background: linear-gradient(135deg, #10b981, #059669);">🖥️</div>
                系统状态API测试
            </div>
            
            <div class="api-endpoint">
                <span class="method method-get">GET</span>
                <span>/api/v1/system-status/overview</span>
                <button class="test-button" onclick="testAPI('/api/v1/system-status/overview', 'system-overview')">测试</button>
            </div>
            
            <div class="result-area" id="system-result">点击上方按钮测试系统状态API...</div>
        </div>

        <!-- 手动测试说明 -->
        <div class="test-section">
            <div class="section-title">
                <div class="section-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">🔧</div>
                手动测试说明
            </div>
            
            <p><strong>如果自动测试失败，可以手动测试：</strong></p>
            
            <h4>1. 使用浏览器直接访问：</h4>
            <ul>
                <li><a href="http://127.0.0.1:8000/api/v1/storage-overview/summary" target="_blank">存储概览摘要</a></li>
                <li><a href="http://127.0.0.1:8000/api/v1/system-status/overview" target="_blank">系统状态概览</a></li>
                <li><a href="http://127.0.0.1:8001/api/v1/storage-overview/summary" target="_blank">测试服务器 - 存储概览</a></li>
                <li><a href="http://127.0.0.1:8001/api/v1/system-status/overview" target="_blank">测试服务器 - 系统状态</a></li>
            </ul>
            
            <h4>2. 使用curl命令测试：</h4>
            <div class="code-block">
curl http://127.0.0.1:8000/api/v1/storage-overview/summary
curl http://127.0.0.1:8000/api/v1/system-status/overview
            </div>
            
            <h4>3. 预期响应格式：</h4>
            <div class="code-block">
{
  "success": true,
  "message": "获取存储概览成功",
  "data": {
    "total_capacity": 107374182400,
    "total_capacity_formatted": "100.0 GB",
    "total_files": 1247,
    "shared_files": 89,
    "recent_access": 23,
    "used_space": 48318382080,
    "used_space_formatted": "45.0 GB",
    "usage_percentage": 45.0,
    "storage_count": 1,
    "last_updated": "2024-01-01T12:00:00.000000"
  }
}
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000';
        const TEST_API_BASE_URL = 'http://127.0.0.1:8001';

        async function testAPI(endpoint, resultId) {
            const resultElement = document.getElementById(getResultElementId(resultId));
            resultElement.textContent = '正在测试API...';

            try {
                // 先尝试主API服务器
                let response = await fetch(API_BASE_URL + endpoint);
                
                if (!response.ok) {
                    // 如果主服务器失败，尝试测试服务器
                    response = await fetch(TEST_API_BASE_URL + endpoint);
                }

                if (response.ok) {
                    const data = await response.json();
                    resultElement.textContent = JSON.stringify(data, null, 2);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultElement.textContent = `测试失败: ${error.message}\n\n请确保API服务器已启动：\n1. 主服务器: http://127.0.0.1:8000\n2. 测试服务器: http://127.0.0.1:8001\n\n启动命令：\ncd api\n.venv\\Scripts\\python.exe test_apis.py`;
            }
        }

        function getResultElementId(resultId) {
            const mapping = {
                'storage-summary': 'storage-result',
                'storage-details': 'storage-result',
                'file-types': 'storage-result',
                'system-overview': 'system-result'
            };
            return mapping[resultId] || 'storage-result';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('API测试页面已加载');
            
            // 自动测试连接
            setTimeout(() => {
                testAPI('/api/v1/storage-overview/summary', 'storage-summary');
            }, 1000);
        });
    </script>
</body>
</html>
