<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库表创建解决方案</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        .status-badge {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 5px;
        }
        .solution-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }
        .solution-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .solution-icon {
            margin-right: 10px;
            font-size: 1.5rem;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            overflow-x: auto;
        }
        .sql-block {
            background: #0f172a;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            border-left: 4px solid #3b82f6;
        }
        .step-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .step-item {
            display: flex;
            align-items: flex-start;
            margin: 15px 0;
            padding: 12px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 3px solid #10b981;
        }
        .step-number {
            background: #10b981;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .warning-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #92400e;
        }
        .success-box {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #047857;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🗄️ 数据库表创建解决方案</h1>
            <p class="subtitle">解决 document_segment_tasks 表不存在的问题</p>
            <div>
                <span class="status-badge">✅ SQL脚本已准备</span>
                <span class="status-badge">✅ 手动创建方案</span>
                <span class="status-badge">✅ 自动化脚本</span>
                <span class="status-badge">✅ 验证方法</span>
            </div>
        </div>

        <!-- 问题说明 -->
        <div class="solution-card">
            <div class="solution-title">
                <span class="solution-icon">🚨</span>
                问题说明
            </div>
            <div class="warning-box">
                <strong>错误信息:</strong><br>
                <code>psycopg2.errors.UndefinedTable) relation "document_segment_tasks" does not exist</code>
                <br><br>
                <strong>原因:</strong> 批量分段功能需要的数据库表尚未创建，导致API调用失败。
            </div>
        </div>

        <!-- 解决方案1：手动执行SQL -->
        <div class="solution-card">
            <div class="solution-title">
                <span class="solution-icon">🛠️</span>
                解决方案1：手动执行SQL（推荐）
            </div>
            
            <ol class="step-list">
                <li class="step-item">
                    <div class="step-number">1</div>
                    <div>
                        <strong>连接数据库</strong><br>
                        使用 pgAdmin、DBeaver 或命令行工具连接到 PostgreSQL 数据库<br>
                        <code>Host: **************:5432, Database: xhc_rag, User: postgres</code>
                    </div>
                </li>
                <li class="step-item">
                    <div class="step-number">2</div>
                    <div>
                        <strong>执行SQL脚本</strong><br>
                        复制下面的SQL代码并在数据库中执行
                    </div>
                </li>
                <li class="step-item">
                    <div class="step-number">3</div>
                    <div>
                        <strong>验证表创建</strong><br>
                        检查是否成功创建了4个表和3个默认模板
                    </div>
                </li>
            </ol>

            <h4>完整SQL脚本:</h4>
            <div class="sql-block">
-- 创建文档分段任务表
CREATE TABLE IF NOT EXISTS document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    file_ids JSONB NOT NULL,
    total_files INTEGER DEFAULT 0,
    processed_files INTEGER DEFAULT 0,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'pending',
    progress FLOAT DEFAULT 0.0,
    error_message TEXT,
    total_segments INTEGER DEFAULT 0,
    total_vectors INTEGER DEFAULT 0,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档分段表
CREATE TABLE IF NOT EXISTS document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id VARCHAR(36) NOT NULL,
    file_id VARCHAR(500) NOT NULL,
    file_name VARCHAR(500),
    segment_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    char_count INTEGER DEFAULT 0,
    vectorize_status VARCHAR(20) DEFAULT 'pending',
    embedding_vector JSONB,
    keywords JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES document_segment_tasks(task_id) ON DELETE CASCADE
);

-- 创建分段模板表
CREATE TABLE IF NOT EXISTS segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(200) NOT NULL,
    description TEXT,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量索引表
CREATE TABLE IF NOT EXISTS vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(200) NOT NULL,
    description TEXT,
    embedding_model VARCHAR(100) NOT NULL,
    vector_dimension INTEGER NOT NULL,
    total_vectors INTEGER DEFAULT 0,
    index_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);

-- 插入默认分段模板
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
('通用文档分段', '适用于大多数文档的通用分段配置', 'paragraph', 500, 50,
 true, true, 'text-embedding-ada-002', 1536, 1000, 'zh', false, true, true, true, true),
('长文档分段', '适用于长文档的分段配置，较大的分段长度', 'paragraph', 1000, 100,
 true, true, 'text-embedding-ada-002', 1536, 1500, 'zh', false, true, true, false, true),
('精细分段', '适用于需要精细分段的文档，较小的分段长度', 'sentence', 200, 20,
 true, true, 'text-embedding-ada-002', 1536, 500, 'zh', true, true, true, false, true)
ON CONFLICT (template_name) DO NOTHING;
            </div>
        </div>

        <!-- 解决方案2：使用脚本 -->
        <div class="solution-card">
            <div class="solution-title">
                <span class="solution-icon">🤖</span>
                解决方案2：使用Python脚本
            </div>
            
            <p>如果您希望使用自动化脚本，可以执行以下命令：</p>
            
            <div class="code-block">
# 进入API目录
cd api

# 激活虚拟环境并执行脚本
.venv\Scripts\python.exe run_sql.py
            </div>
            
            <p>脚本文件已创建在 <code>api/run_sql.py</code> 和 <code>api/create_segment_tables.sql</code></p>
        </div>

        <!-- 验证方法 -->
        <div class="solution-card">
            <div class="solution-title">
                <span class="solution-icon">✅</span>
                验证表创建成功
            </div>
            
            <p>执行以下SQL查询验证表是否创建成功：</p>
            
            <div class="sql-block">
-- 检查创建的表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%segment%'
ORDER BY table_name;

-- 检查模板数量
SELECT COUNT(*) as template_count FROM segment_templates;

-- 查看默认模板
SELECT template_name, description, is_default 
FROM segment_templates 
ORDER BY is_default DESC, template_name;
            </div>
            
            <div class="success-box">
                <strong>预期结果:</strong><br>
                • 4个表：document_segment_tasks, document_segments, segment_templates, vector_indexes<br>
                • 3个默认模板：通用文档分段, 长文档分段, 精细分段<br>
                • 5个索引：提升查询性能
            </div>
        </div>

        <!-- 测试批量分段 -->
        <div class="solution-card">
            <div class="solution-title">
                <span class="solution-icon">🚀</span>
                测试批量分段功能
            </div>
            
            <ol class="step-list">
                <li class="step-item">
                    <div class="step-number">1</div>
                    <div>
                        <strong>确认表创建</strong><br>
                        验证所有必需的数据库表已成功创建
                    </div>
                </li>
                <li class="step-item">
                    <div class="step-number">2</div>
                    <div>
                        <strong>重启后端服务</strong><br>
                        重启FastAPI服务以确保连接到新创建的表
                    </div>
                </li>
                <li class="step-item">
                    <div class="step-number">3</div>
                    <div>
                        <strong>测试批量分段</strong><br>
                        访问文件管理页面，选择多个文件，点击"分段"按钮
                    </div>
                </li>
                <li class="step-item">
                    <div class="step-number">4</div>
                    <div>
                        <strong>验证功能</strong><br>
                        确认能正常进入批量分段配置页面，点击"开始AI分段"不再报错
                    </div>
                </li>
            </ol>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="action-button" onclick="copySQL()">
                📋 复制SQL脚本
            </button>
            <button class="action-button" onclick="showConnectionInfo()">
                🔗 数据库连接信息
            </button>
            <button class="action-button" onclick="showVerificationSteps()">
                ✅ 验证步骤
            </button>
            <button class="action-button" onclick="showTroubleshooting()">
                🛠️ 故障排除
            </button>
        </div>
    </div>

    <script>
        function copySQL() {
            const sqlText = `-- 创建文档分段任务表
CREATE TABLE IF NOT EXISTS document_segment_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    description TEXT,
    file_ids JSONB NOT NULL,
    total_files INTEGER DEFAULT 0,
    processed_files INTEGER DEFAULT 0,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'pending',
    progress FLOAT DEFAULT 0.0,
    error_message TEXT,
    total_segments INTEGER DEFAULT 0,
    total_vectors INTEGER DEFAULT 0,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档分段表
CREATE TABLE IF NOT EXISTS document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id VARCHAR(36) NOT NULL,
    file_id VARCHAR(500) NOT NULL,
    file_name VARCHAR(500),
    segment_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    char_count INTEGER DEFAULT 0,
    vectorize_status VARCHAR(20) DEFAULT 'pending',
    embedding_vector JSONB,
    keywords JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES document_segment_tasks(task_id) ON DELETE CASCADE
);

-- 创建分段模板表
CREATE TABLE IF NOT EXISTS segment_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(200) NOT NULL,
    description TEXT,
    segment_method VARCHAR(50) DEFAULT 'paragraph',
    max_length INTEGER DEFAULT 500,
    overlap INTEGER DEFAULT 50,
    preserve_formatting BOOLEAN DEFAULT true,
    enable_vectorization BOOLEAN DEFAULT true,
    embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    vector_dimension INTEGER DEFAULT 1536,
    chunk_size INTEGER DEFAULT 1000,
    language VARCHAR(10) DEFAULT 'zh',
    remove_stopwords BOOLEAN DEFAULT false,
    normalize_text BOOLEAN DEFAULT true,
    extract_keywords BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量索引表
CREATE TABLE IF NOT EXISTS vector_indexes (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(200) NOT NULL,
    description TEXT,
    embedding_model VARCHAR(100) NOT NULL,
    vector_dimension INTEGER NOT NULL,
    total_vectors INTEGER DEFAULT 0,
    index_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);

-- 插入默认分段模板
INSERT INTO segment_templates (
    template_name, description, segment_method, max_length, overlap,
    preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
    chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
    is_default, is_system
) VALUES 
('通用文档分段', '适用于大多数文档的通用分段配置', 'paragraph', 500, 50,
 true, true, 'text-embedding-ada-002', 1536, 1000, 'zh', false, true, true, true, true),
('长文档分段', '适用于长文档的分段配置，较大的分段长度', 'paragraph', 1000, 100,
 true, true, 'text-embedding-ada-002', 1536, 1500, 'zh', false, true, true, false, true),
('精细分段', '适用于需要精细分段的文档，较小的分段长度', 'sentence', 200, 20,
 true, true, 'text-embedding-ada-002', 1536, 500, 'zh', true, true, true, false, true)
ON CONFLICT (template_name) DO NOTHING;`;

            navigator.clipboard.writeText(sqlText).then(() => {
                alert('✅ SQL脚本已复制到剪贴板！\n\n请在数据库管理工具中执行此脚本。');
            }).catch(() => {
                alert('❌ 复制失败，请手动复制SQL脚本。');
            });
        }

        function showConnectionInfo() {
            alert(`🔗 数据库连接信息\n\n主机: **************\n端口: 5432\n数据库: xhc_rag\n用户名: postgres\n密码: XHC12345\n\n推荐工具:\n• pgAdmin (Web界面)\n• DBeaver (桌面应用)\n• psql (命令行)\n• DataGrip (JetBrains)\n\n连接字符串:\npostgresql://postgres:XHC12345@**************:5432/xhc_rag`);
        }

        function showVerificationSteps() {
            alert(`✅ 验证步骤\n\n1. 检查表是否创建:\nSELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE '%segment%';\n\n预期结果: 4个表\n• document_segment_tasks\n• document_segments\n• segment_templates\n• vector_indexes\n\n2. 检查模板数据:\nSELECT COUNT(*) FROM segment_templates;\n\n预期结果: 3个模板\n\n3. 测试API:\n访问: http://localhost:8000/api/v1/document-segment/templates\n\n预期结果: 返回模板列表JSON\n\n4. 测试批量分段:\n• 选择多个文件\n• 点击"分段"按钮\n• 进入配置页面\n• 点击"开始AI分段"\n• 不再出现表不存在错误`);
        }

        function showTroubleshooting() {
            alert(`🛠️ 故障排除\n\n常见问题:\n\n1. 连接被拒绝\n• 检查数据库服务是否运行\n• 验证IP地址和端口\n• 确认防火墙设置\n\n2. 认证失败\n• 检查用户名和密码\n• 确认用户权限\n• 验证数据库名称\n\n3. 表已存在错误\n• 使用 IF NOT EXISTS 子句\n• 或先删除现有表\n• 检查表结构是否正确\n\n4. 权限不足\n• 确保用户有CREATE权限\n• 检查schema权限\n• 验证表空间权限\n\n5. 脚本执行失败\n• 检查Python环境\n• 验证依赖包安装\n• 查看详细错误日志\n\n解决方案:\n• 优先使用手动SQL执行\n• 分步骤执行和验证\n• 检查数据库日志\n• 联系数据库管理员`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据库表创建解决方案页面已加载');
        });
    </script>
</body>
</html>
