<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端测试 - AI工具卡片</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 10px;
        }

        .test-info {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .test-info h2 {
            color: #1f2937;
            margin-bottom: 10px;
        }

        .test-info p {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .cards-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }

        .card:active::before {
            transform: scaleX(1);
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            flex-shrink: 0;
        }

        .card-content {
            flex: 1;
            min-width: 0;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .card-meta {
            font-size: 0.75rem;
            color: #9ca3af;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .card-company {
            font-weight: 500;
            color: #6366f1;
        }

        .card-creator {
            color: #8b5cf6;
        }

        .card-description {
            font-size: 0.85rem;
            color: #6b7280;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .device-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="device-info" id="deviceInfo"></div>
    
    <div class="test-info">
        <h2>📱 移动端布局测试</h2>
        <p>左侧图标 + 右侧内容的卡片布局展示</p>
    </div>

    <div class="cards-grid">
        <div class="card">
            <div class="card-icon">🤖</div>
            <div class="card-content">
                <div class="card-title">ChatGPT</div>
                <div class="card-meta">
                    <span class="card-company">OpenAI</span>
                    <span> • </span>
                    <span class="card-creator">Sam Altman</span>
                </div>
                <div class="card-description">OpenAI开发的强大对话AI，支持文本生成、问答、编程等多种任务</div>
            </div>
        </div>

        <div class="card">
            <div class="card-icon">🎨</div>
            <div class="card-content">
                <div class="card-title">Midjourney</div>
                <div class="card-meta">
                    <span class="card-company">Midjourney Inc</span>
                    <span> • </span>
                    <span class="card-creator">David Holz</span>
                </div>
                <div class="card-description">AI图像生成工具，通过文本描述创造惊艳的艺术作品和插图</div>
            </div>
        </div>

        <div class="card">
            <div class="card-icon">📝</div>
            <div class="card-content">
                <div class="card-title">Notion AI</div>
                <div class="card-meta">
                    <span class="card-company">Notion Labs</span>
                    <span> • </span>
                    <span class="card-creator">Ivan Zhao</span>
                </div>
                <div class="card-description">集成在Notion中的AI写作助手，帮助提升文档编写效率</div>
            </div>
        </div>

        <div class="card">
            <div class="card-icon">🎵</div>
            <div class="card-content">
                <div class="card-title">AIVA</div>
                <div class="card-description">AI音乐创作平台，自动生成各种风格的原创音乐作品</div>
            </div>
        </div>

        <div class="card">
            <div class="card-icon">🎬</div>
            <div class="card-content">
                <div class="card-title">Runway ML</div>
                <div class="card-description">AI视频编辑工具，提供视频生成、编辑和特效制作功能</div>
            </div>
        </div>

        <div class="card">
            <div class="card-icon">💻</div>
            <div class="card-content">
                <div class="card-title">GitHub Copilot</div>
                <div class="card-description">AI编程助手，实时提供代码建议和自动补全功能</div>
            </div>
        </div>

        <div class="card">
            <div class="card-icon">🔍</div>
            <div class="card-content">
                <div class="card-title">Perplexity AI</div>
                <div class="card-description">AI搜索引擎，提供准确的答案和可靠的信息来源</div>
            </div>
        </div>

        <div class="card">
            <div class="card-icon">📊</div>
            <div class="card-content">
                <div class="card-title">Tableau AI</div>
                <div class="card-description">智能数据分析平台，自动生成数据洞察和可视化图表</div>
            </div>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            const deviceInfo = document.getElementById('deviceInfo');
            const width = window.innerWidth;
            const height = window.innerHeight;
            const isMobile = width <= 768;
            const isTablet = width > 768 && width <= 1024;
            
            let deviceType = 'Desktop';
            if (isMobile) deviceType = 'Mobile';
            else if (isTablet) deviceType = 'Tablet';
            
            deviceInfo.textContent = `${deviceType} - ${width}×${height}`;
        }

        // 初始化和窗口大小变化时更新
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);

        // 卡片点击效果
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('.card-title').textContent;
                alert(`点击了: ${title}`);
            });
        });
    </script>
</body>
</html>
