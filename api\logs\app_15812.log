2025-06-17 17:36:20.832 | INFO     | app.core.logging:setup_logging:110 | Logging configured for development environment
2025-06-17 17:36:20.832 | INFO     | app.core.middleware:setup_middleware:136 | Middleware setup completed
2025-06-17 17:36:20.861 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: auth v1.0.0
2025-06-17 17:36:20.862 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: file_manager v1.0.0
2025-06-17 17:36:20.863 | INFO     | app.plugins.manager:_load_plugin:103 | Loaded plugin: rag_engine v1.0.0
2025-06-17 17:36:20.864 | INFO     | app.core.database:init_database:222 | Initializing database connection with URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:36:20.871 | INFO     | app.core.database:init_database:254 | Using async URL: postgresql+asyncpg://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:36:20.871 | INFO     | app.core.database:init_database:255 | Using sync URL: postgresql+psycopg2://postgres:XHC12345@192.168.50.142:5432/xhc_rag
2025-06-17 17:36:20.920 | INFO     | app.core.database:init_database:281 | Database connection initialized successfully for postgresql
2025-06-17 17:36:20.921 | INFO     | app.core.simple_migration:create_tables_directly:44 | Creating tables for database type: postgresql
2025-06-17 17:36:20.984 | INFO     | app.plugins.auth.plugin:initialize:36 | Auth plugin initialized
2025-06-17 17:36:20.985 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: auth
2025-06-17 17:36:20.985 | INFO     | app.plugins.file_manager.plugin:initialize:49 | File manager plugin initialized with upload dir: uploads
2025-06-17 17:36:20.985 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: file_manager
2025-06-17 17:36:20.986 | INFO     | app.plugins.rag_engine.plugin:initialize:64 | RAG engine plugin initialized
2025-06-17 17:36:20.986 | INFO     | app.plugins.manager:initialize_plugins:126 | Initialized plugin: rag_engine
2025-06-17 17:36:20.986 | INFO     | app.plugins.auth.plugin:startup:40 | Auth plugin started
2025-06-17 17:36:20.986 | INFO     | app.plugins.manager:startup:167 | Started plugin: auth
2025-06-17 17:36:20.987 | INFO     | app.plugins.file_manager.plugin:startup:57 | File manager plugin started
2025-06-17 17:36:20.987 | INFO     | app.plugins.manager:startup:167 | Started plugin: file_manager
2025-06-17 17:36:20.987 | INFO     | app.plugins.rag_engine.plugin:startup:69 | RAG engine plugin started
2025-06-17 17:36:20.987 | INFO     | app.plugins.manager:startup:167 | Started plugin: rag_engine
2025-06-17 17:36:20.988 | INFO     | app.services.upload_processor:start:34 | 上传处理器启动
2025-06-17 17:36:20.989 | INFO     | app.core.startup:startup_tasks:66 | 执行应用启动任务...
2025-06-17 17:36:20.989 | INFO     | app.core.startup:initialize_celery:30 | 自动启动Celery服务...
2025-06-17 17:36:20.990 | INFO     | app.core.startup:initialize_celery:55 | Celery指标收集器将在服务完全启动后手动启动
2025-06-17 17:36:20.990 | INFO     | app.core.startup:startup_tasks:71 | 应用启动任务完成
2025-06-17 17:36:21.034 | INFO     | app.core.celery_manager:_check_redis_connection:138 | Redis连接成功
2025-06-17 17:36:21.034 | INFO     | app.core.celery_manager:start_service:169 | 启动 worker 服务: F:\workspace\xhc-rag\api\.venv\Scripts\python.exe -m celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=4 --queues=default,upload_queue,file_queue --hostname=worker@%h --logfile=logs/celery_worker.log
