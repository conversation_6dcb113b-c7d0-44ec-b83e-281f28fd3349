'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ZoomIn,
  ZoomOut,
  RotateCw,
  RotateCcw,
  Download,
  Printer,
  Search,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  FileText,
  Maximize,
  Minimize,
  BookOpen,
  Grid3X3,
  Eye,
  Edit3,
  Highlighter,
  MessageSquare,
  Bookmark
} from 'lucide-react';

interface PDFViewerProps {
  fileId: string;
  fileName: string;
  pdfUrl: string;
  isReadOnly?: boolean;
  onClose?: () => void;
}

interface Annotation {
  id: string;
  page: number;
  x: number;
  y: number;
  width: number;
  height: number;
  type: 'highlight' | 'comment' | 'bookmark';
  content: string;
  color: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  fileId,
  fileName,
  pdfUrl,
  isReadOnly = false,
  onClose
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [viewMode, setViewMode] = useState<'single' | 'continuous' | 'facing'>('single');
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [selectedTool, setSelectedTool] = useState<'select' | 'highlight' | 'comment'>('select');
  const [showSidebar, setShowSidebar] = useState(false);

  const viewerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 模拟PDF加载
  useEffect(() => {
    // 这里应该集成真实的PDF.js库
    setTotalPages(10); // 模拟10页PDF
  }, [pdfUrl]);

  // 缩放控制
  const handleZoomIn = () => setZoom(Math.min(300, zoom + 25));
  const handleZoomOut = () => setZoom(Math.max(25, zoom - 25));
  const handleZoomFit = () => setZoom(100);

  // 页面导航
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(totalPages, page)));
  };

  const goToPrevPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);
  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(totalPages);

  // 旋转控制
  const rotateClockwise = () => setRotation((rotation + 90) % 360);
  const rotateCounterClockwise = () => setRotation((rotation - 90 + 360) % 360);

  // 搜索功能
  const handleSearch = () => {
    if (!searchTerm.trim()) return;
    
    // 模拟搜索结果
    const mockResults = [
      { page: 1, text: searchTerm, x: 100, y: 200 },
      { page: 3, text: searchTerm, x: 150, y: 300 },
    ];
    setSearchResults(mockResults);
  };

  // 添加注释
  const addAnnotation = (type: Annotation['type'], x: number, y: number) => {
    const newAnnotation: Annotation = {
      id: `annotation-${Date.now()}`,
      page: currentPage,
      x,
      y,
      width: 100,
      height: 20,
      type,
      content: type === 'highlight' ? '' : '新注释',
      color: type === 'highlight' ? '#ffff00' : '#ff6b6b'
    };
    
    setAnnotations([...annotations, newAnnotation]);
  };

  // 工具栏按钮组件
  const ToolbarButton: React.FC<{
    icon: React.ReactNode;
    title: string;
    active?: boolean;
    onClick: () => void;
    disabled?: boolean;
  }> = ({ icon, title, active, onClick, disabled }) => (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      disabled={disabled}
      className={`p-2 rounded-lg transition-all duration-200 ${
        active 
          ? 'bg-blue-500 text-white shadow-lg' 
          : disabled
          ? 'text-gray-400 cursor-not-allowed'
          : 'text-gray-700 hover:bg-gray-100'
      }`}
      title={title}
    >
      {icon}
    </motion.button>
  );

  return (
    <div className={`flex flex-col h-screen bg-gray-900 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* 顶部工具栏 */}
      <div className="bg-gray-800 text-white border-b border-gray-700">
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-red-400" />
              <span className="font-medium">{fileName}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <ToolbarButton
              icon={<Download className="w-4 h-4" />}
              title="下载"
              onClick={() => {
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.download = fileName;
                link.click();
              }}
            />
            
            <ToolbarButton
              icon={<Printer className="w-4 h-4" />}
              title="打印"
              onClick={() => window.print()}
            />

            <ToolbarButton
              icon={isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
              title={isFullscreen ? "退出全屏" : "全屏"}
              onClick={() => setIsFullscreen(!isFullscreen)}
            />

            {onClose && (
              <ToolbarButton
                icon={<Eye className="w-4 h-4" />}
                title="关闭"
                onClick={onClose}
              />
            )}
          </div>
        </div>

        {/* 导航和工具栏 */}
        <div className="flex items-center justify-between px-4 py-2 border-t border-gray-700">
          <div className="flex items-center space-x-2">
            {/* 页面导航 */}
            <ToolbarButton
              icon={<ChevronsLeft className="w-4 h-4" />}
              title="第一页"
              onClick={goToFirstPage}
              disabled={currentPage === 1}
            />
            <ToolbarButton
              icon={<ChevronLeft className="w-4 h-4" />}
              title="上一页"
              onClick={goToPrevPage}
              disabled={currentPage === 1}
            />
            
            <div className="flex items-center space-x-2 bg-gray-700 rounded px-2 py-1">
              <input
                type="number"
                value={currentPage}
                onChange={(e) => goToPage(parseInt(e.target.value) || 1)}
                className="w-12 bg-transparent text-center text-sm border-0 outline-none"
                min={1}
                max={totalPages}
              />
              <span className="text-sm text-gray-300">/ {totalPages}</span>
            </div>
            
            <ToolbarButton
              icon={<ChevronRight className="w-4 h-4" />}
              title="下一页"
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
            />
            <ToolbarButton
              icon={<ChevronsRight className="w-4 h-4" />}
              title="最后一页"
              onClick={goToLastPage}
              disabled={currentPage === totalPages}
            />

            <div className="w-px h-6 bg-gray-600 mx-2" />

            {/* 缩放控制 */}
            <ToolbarButton
              icon={<ZoomOut className="w-4 h-4" />}
              title="缩小"
              onClick={handleZoomOut}
            />
            <div className="bg-gray-700 rounded px-2 py-1">
              <span className="text-sm">{zoom}%</span>
            </div>
            <ToolbarButton
              icon={<ZoomIn className="w-4 h-4" />}
              title="放大"
              onClick={handleZoomIn}
            />
            <ToolbarButton
              icon={<Eye className="w-4 h-4" />}
              title="适合页面"
              onClick={handleZoomFit}
            />

            <div className="w-px h-6 bg-gray-600 mx-2" />

            {/* 旋转控制 */}
            <ToolbarButton
              icon={<RotateCcw className="w-4 h-4" />}
              title="逆时针旋转"
              onClick={rotateCounterClockwise}
            />
            <ToolbarButton
              icon={<RotateCw className="w-4 h-4" />}
              title="顺时针旋转"
              onClick={rotateClockwise}
            />

            <div className="w-px h-6 bg-gray-600 mx-2" />

            {/* 视图模式 */}
            <select
              value={viewMode}
              onChange={(e) => setViewMode(e.target.value as any)}
              className="bg-gray-700 text-white rounded px-2 py-1 text-sm border-0 outline-none"
            >
              <option value="single">单页</option>
              <option value="continuous">连续</option>
              <option value="facing">对页</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            {/* 搜索 */}
            <div className="flex items-center bg-gray-700 rounded">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                placeholder="搜索..."
                className="bg-transparent px-3 py-1 text-sm border-0 outline-none w-40"
              />
              <ToolbarButton
                icon={<Search className="w-4 h-4" />}
                title="搜索"
                onClick={handleSearch}
              />
            </div>

            {/* 注释工具 */}
            {!isReadOnly && (
              <>
                <ToolbarButton
                  icon={<Highlighter className="w-4 h-4" />}
                  title="高亮"
                  active={selectedTool === 'highlight'}
                  onClick={() => setSelectedTool('highlight')}
                />
                <ToolbarButton
                  icon={<MessageSquare className="w-4 h-4" />}
                  title="注释"
                  active={selectedTool === 'comment'}
                  onClick={() => setSelectedTool('comment')}
                />
              </>
            )}

            <ToolbarButton
              icon={<BookOpen className="w-4 h-4" />}
              title="侧边栏"
              active={showSidebar}
              onClick={() => setShowSidebar(!showSidebar)}
            />
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 侧边栏 */}
        {showSidebar && (
          <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
            <div className="p-4">
              <h3 className="text-white font-medium mb-4">文档大纲</h3>
              
              {/* 缩略图 */}
              <div className="space-y-2">
                {Array.from({ length: totalPages }, (_, i) => (
                  <div
                    key={i}
                    className={`p-2 border rounded cursor-pointer transition-colors ${
                      currentPage === i + 1 
                        ? 'border-blue-500 bg-blue-900' 
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                    onClick={() => goToPage(i + 1)}
                  >
                    <div className="aspect-[3/4] bg-white rounded mb-1 flex items-center justify-center">
                      <span className="text-gray-400 text-xs">页面 {i + 1}</span>
                    </div>
                    <div className="text-xs text-gray-300 text-center">第 {i + 1} 页</div>
                  </div>
                ))}
              </div>
            </div>

            {/* 注释列表 */}
            {annotations.length > 0 && (
              <div className="p-4 border-t border-gray-700">
                <h4 className="text-white font-medium mb-2">注释</h4>
                <div className="space-y-2">
                  {annotations.map((annotation) => (
                    <div
                      key={annotation.id}
                      className="p-2 bg-gray-700 rounded text-sm"
                      onClick={() => goToPage(annotation.page)}
                    >
                      <div className="text-gray-300">第 {annotation.page} 页</div>
                      <div className="text-white">{annotation.content}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* PDF查看区域 */}
        <div className="flex-1 overflow-auto bg-gray-900 p-4">
          <div className="flex justify-center">
            <div 
              ref={viewerRef}
              className="bg-white shadow-2xl rounded-lg overflow-hidden"
              style={{ 
                transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
                transformOrigin: 'center'
              }}
              onClick={(e) => {
                if (selectedTool !== 'select' && !isReadOnly) {
                  const rect = e.currentTarget.getBoundingClientRect();
                  const x = e.clientX - rect.left;
                  const y = e.clientY - rect.top;
                  addAnnotation(selectedTool as any, x, y);
                }
              }}
            >
              {/* 这里应该渲染实际的PDF页面 */}
              <div className="w-96 h-[500px] flex items-center justify-center relative">
                <div className="text-gray-400 text-center">
                  <FileText className="w-16 h-16 mx-auto mb-4" />
                  <div className="text-lg font-medium">PDF 页面 {currentPage}</div>
                  <div className="text-sm">这里将显示实际的PDF内容</div>
                  <div className="text-xs mt-2 text-gray-500">
                    需要集成 PDF.js 库来渲染真实PDF
                  </div>
                </div>

                {/* 渲染注释 */}
                {annotations
                  .filter(annotation => annotation.page === currentPage)
                  .map((annotation) => (
                    <div
                      key={annotation.id}
                      className="absolute border-2 border-dashed"
                      style={{
                        left: annotation.x,
                        top: annotation.y,
                        width: annotation.width,
                        height: annotation.height,
                        borderColor: annotation.color,
                        backgroundColor: annotation.type === 'highlight' 
                          ? `${annotation.color}40` 
                          : 'transparent'
                      }}
                    >
                      {annotation.type === 'comment' && (
                        <div className="absolute -top-8 left-0 bg-yellow-400 text-black text-xs px-2 py-1 rounded shadow">
                          {annotation.content}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <div className="bg-gray-800 text-white px-4 py-2 border-t border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span>页面 {currentPage} / {totalPages}</span>
            <span>缩放 {zoom}%</span>
            {rotation !== 0 && <span>旋转 {rotation}°</span>}
          </div>
          
          <div className="flex items-center space-x-4">
            {searchResults.length > 0 && (
              <span>{searchResults.length} 个搜索结果</span>
            )}
            {annotations.length > 0 && (
              <span>{annotations.length} 个注释</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;
