"""
Celery配置管理模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, Float
from sqlalchemy.sql import func
from app.core.database import Base


class CeleryConfiguration(Base):
    """Celery配置表"""
    __tablename__ = "celery_configurations"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Redis配置
    redis_host = Column(String(255), default="**************", comment="Redis主机")
    redis_port = Column(Integer, default=6379, comment="Redis端口")
    redis_db = Column(Integer, default=10, comment="Redis数据库")
    redis_password = Column(String(255), nullable=True, comment="Redis密码")
    
    # Worker配置
    worker_concurrency = Column(Integer, default=4, comment="Worker并发数")
    worker_prefetch_multiplier = Column(Integer, default=1, comment="Worker预取倍数")
    worker_max_tasks_per_child = Column(Integer, default=1000, comment="每个子进程最大任务数")
    worker_max_memory_per_child = Column(Integer, default=200000, comment="每个子进程最大内存(KB)")
    
    # 任务配置
    task_soft_time_limit = Column(Integer, default=300, comment="任务软超时(秒)")
    task_time_limit = Column(Integer, default=600, comment="任务硬超时(秒)")
    task_max_retries = Column(Integer, default=3, comment="最大重试次数")
    task_default_retry_delay = Column(Integer, default=60, comment="默认重试延迟(秒)")
    
    # 结果配置
    result_expires = Column(Integer, default=3600, comment="结果过期时间(秒)")
    result_backend_transport_options = Column(Text, nullable=True, comment="结果后端传输选项(JSON)")
    
    # 队列配置
    task_routes = Column(Text, nullable=True, comment="任务路由配置(JSON)")
    task_default_queue = Column(String(100), default="default", comment="默认队列")
    
    # 监控配置
    worker_send_task_events = Column(Boolean, default=True, comment="发送任务事件")
    task_send_sent_event = Column(Boolean, default=True, comment="发送已发送事件")
    
    # Flower配置
    flower_port = Column(Integer, default=5555, comment="Flower端口")
    flower_basic_auth = Column(String(255), default="admin:password", comment="Flower认证")
    
    # 其他配置
    timezone = Column(String(50), default="Asia/Shanghai", comment="时区")
    enable_utc = Column(Boolean, default=True, comment="启用UTC")
    
    # 状态字段
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "redis_host": self.redis_host,
            "redis_port": self.redis_port,
            "redis_db": self.redis_db,
            "redis_password": self.redis_password,
            "worker_concurrency": self.worker_concurrency,
            "worker_prefetch_multiplier": self.worker_prefetch_multiplier,
            "worker_max_tasks_per_child": self.worker_max_tasks_per_child,
            "worker_max_memory_per_child": self.worker_max_memory_per_child,
            "task_soft_time_limit": self.task_soft_time_limit,
            "task_time_limit": self.task_time_limit,
            "task_max_retries": self.task_max_retries,
            "task_default_retry_delay": self.task_default_retry_delay,
            "result_expires": self.result_expires,
            "result_backend_transport_options": self.result_backend_transport_options,
            "task_routes": self.task_routes,
            "task_default_queue": self.task_default_queue,
            "worker_send_task_events": self.worker_send_task_events,
            "task_send_sent_event": self.task_send_sent_event,
            "flower_port": self.flower_port,
            "flower_basic_auth": self.flower_basic_auth,
            "timezone": self.timezone,
            "enable_utc": self.enable_utc,
            "is_active": self.is_active,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }


class CeleryMetrics(Base):
    """Celery指标表"""
    __tablename__ = "celery_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), comment="记录时间")
    
    # Worker指标
    active_workers = Column(Integer, default=0, comment="活跃Worker数")
    total_workers = Column(Integer, default=0, comment="总Worker数")
    
    # 任务指标
    active_tasks = Column(Integer, default=0, comment="活跃任务数")
    processed_tasks = Column(Integer, default=0, comment="已处理任务数")
    failed_tasks = Column(Integer, default=0, comment="失败任务数")
    retried_tasks = Column(Integer, default=0, comment="重试任务数")
    
    # 队列指标
    queue_lengths = Column(Text, nullable=True, comment="队列长度(JSON)")
    
    # 性能指标
    avg_task_runtime = Column(Float, default=0.0, comment="平均任务运行时间(秒)")
    task_throughput = Column(Float, default=0.0, comment="任务吞吐量(任务/秒)")
    
    # 内存指标
    worker_memory_usage = Column(Text, nullable=True, comment="Worker内存使用(JSON)")
    
    # Redis指标
    redis_memory_usage = Column(Integer, default=0, comment="Redis内存使用(MB)")
    redis_connected_clients = Column(Integer, default=0, comment="Redis连接客户端数")
    
    def to_dict(self):
        """转换为字典"""
        import json
        
        return {
            "id": self.id,
            "timestamp": self.timestamp,
            "active_workers": self.active_workers,
            "total_workers": self.total_workers,
            "active_tasks": self.active_tasks,
            "processed_tasks": self.processed_tasks,
            "failed_tasks": self.failed_tasks,
            "retried_tasks": self.retried_tasks,
            "queue_lengths": json.loads(self.queue_lengths) if self.queue_lengths else {},
            "avg_task_runtime": self.avg_task_runtime,
            "task_throughput": self.task_throughput,
            "worker_memory_usage": json.loads(self.worker_memory_usage) if self.worker_memory_usage else {},
            "redis_memory_usage": self.redis_memory_usage,
            "redis_connected_clients": self.redis_connected_clients
        }
