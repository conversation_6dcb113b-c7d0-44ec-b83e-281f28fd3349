"""
简化的文件管理服务
"""
import os
import json
import urllib.parse
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.models.file_management import StorageConfig


class SimpleFileManagerService:
    """简化的文件管理服务"""
    
    async def list_files(
        self,
        storage_id: int,
        path: str = "/",
        page: int = 1,
        page_size: int = 50,
        sort_by: str = "name",
        sort_order: str = "asc",
        file_type: Optional[str] = None,
        search: Optional[str] = None,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """
        列出指定路径下的文件
        
        Args:
            storage_id: 存储ID
            path: 目录路径
            page: 页码
            page_size: 每页大小
            sort_by: 排序字段
            sort_order: 排序方向
            file_type: 文件类型过滤
            search: 搜索关键词
            session: 数据库会话
            
        Returns:
            Dict[str, Any]: 文件列表和分页信息
        """
        try:
            # 获取存储配置，只查询存在的列
            result = await session.execute(
                select(
                    StorageConfig.id,
                    StorageConfig.name,
                    StorageConfig.storage_type,
                    StorageConfig.is_default,
                    StorageConfig.is_active,
                    StorageConfig.config,
                    StorageConfig.created_at,
                    StorageConfig.updated_at
                ).where(StorageConfig.id == storage_id)
            )
            row = result.fetchone()

            if not row:
                raise Exception("存储配置不存在")

            # 手动构建存储配置对象
            storage_config = type('StorageConfig', (), {
                'id': row.id,
                'name': row.name,
                'storage_type': row.storage_type,
                'is_default': row.is_default,
                'is_active': row.is_active,
                'config': row.config,
                'created_at': row.created_at,
                'updated_at': row.updated_at
            })()

            
            if storage_config.storage_type.value != "LOCAL":
                # 暂时只支持本地存储
                return {
                    "files": [],
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": 0,
                        "pages": 0
                    },
                    "path": path,
                    "storage_id": storage_id,
                    "message": "暂时只支持本地存储"
                }
            
            # 解析存储配置
            config = json.loads(storage_config.config) if isinstance(storage_config.config, str) else storage_config.config
            base_path = config.get("base_path", "./storage")
            
            # 构建完整路径
            if path.startswith("/"):
                path = path[1:]  # 移除开头的斜杠
            
            full_path = os.path.join(base_path, path) if path else base_path
            full_path = os.path.normpath(full_path)
            
            # 确保路径在base_path内（安全检查）
            if not full_path.startswith(os.path.normpath(base_path)):
                raise Exception("路径不安全")
            
            # 检查目录是否存在
            if not os.path.exists(full_path):
                # 创建目录
                os.makedirs(full_path, exist_ok=True)
            
            if not os.path.isdir(full_path):
                raise Exception("路径不是目录")
            
            # 列出文件
            files = []
            try:
                for item in os.listdir(full_path):
                    item_path = os.path.join(full_path, item)
                    
                    # 跳过隐藏文件
                    if item.startswith('.'):
                        continue
                    
                    # 搜索过滤
                    if search and search.lower() not in item.lower():
                        continue
                    
                    try:
                        stat = os.stat(item_path)
                        is_directory = os.path.isdir(item_path)
                        
                        # 文件类型过滤
                        if file_type:
                            if file_type == "file" and is_directory:
                                continue
                            if file_type == "directory" and not is_directory:
                                continue
                        
                        # 构建相对路径
                        relative_path = os.path.join(path, item).replace("\\", "/")
                        if not relative_path.startswith("/"):
                            relative_path = "/" + relative_path
                        
                        # URL编码文件路径用于file_id，确保路径以/开头
                        normalized_path = relative_path if relative_path.startswith('/') else '/' + relative_path
                        encoded_path = urllib.parse.quote(normalized_path, safe='')

                        file_info = {
                            "file_id": f"local_{storage_id}_{encoded_path}",
                            "file_path": normalized_path,
                            "file_name": item,
                            "file_type": "directory" if is_directory else "file",
                            "file_size": 0 if is_directory else stat.st_size,
                            "file_size_formatted": self._format_file_size(0 if is_directory else stat.st_size),
                            "mime_type": self._get_mime_type(item) if not is_directory else None,
                            "file_extension": os.path.splitext(item)[1] if not is_directory else "",
                            "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                            "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            "is_directory": is_directory,
                            "is_image": self._is_image_file(item) if not is_directory else False,
                            "is_video": self._is_video_file(item) if not is_directory else False,
                            "is_document": self._is_document_file(item) if not is_directory else False,
                        }
                        
                        files.append(file_info)
                        
                    except (OSError, PermissionError) as e:
                        logger.warning("无法访问文件 {}: {}".format(item_path, str(e)))
                        continue
                        
            except PermissionError:
                raise Exception("没有权限访问目录")
            
            # 排序
            reverse = sort_order.lower() == "desc"
            if sort_by == "name":
                files.sort(key=lambda x: x["file_name"].lower(), reverse=reverse)
            elif sort_by == "size":
                files.sort(key=lambda x: x["file_size"], reverse=reverse)
            elif sort_by == "modified_at":
                files.sort(key=lambda x: x["modified_at"], reverse=reverse)
            else:
                files.sort(key=lambda x: x["file_name"].lower(), reverse=reverse)
            
            # 目录优先
            files.sort(key=lambda x: not x["is_directory"])
            
            # 分页
            total = len(files)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_files = files[start_idx:end_idx]
            
            return {
                "files": paginated_files,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "pages": (total + page_size - 1) // page_size if total > 0 else 0
                },
                "path": "/" + path if path else "/",
                "storage_id": storage_id
            }
            
        except Exception as e:
            logger.error("列出文件失败: {}".format(str(e)))
            raise Exception("列出文件失败: {}".format(str(e)))
    
    def _format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        if size == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return "{} {}".format(size, units[unit_index])
        else:
            return "{:.1f} {}".format(size, units[unit_index])
    
    def _get_mime_type(self, filename: str) -> str:
        """获取MIME类型"""
        ext = os.path.splitext(filename)[1].lower()
        mime_types = {
            '.txt': 'text/plain',
            '.pdf': 'application/pdf',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.mp4': 'video/mp4',
            '.avi': 'video/avi',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        }
        return mime_types.get(ext, 'application/octet-stream')
    
    def _is_image_file(self, filename: str) -> bool:
        """判断是否为图片文件"""
        ext = os.path.splitext(filename)[1].lower()
        return ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico']
    
    def _is_video_file(self, filename: str) -> bool:
        """判断是否为视频文件"""
        ext = os.path.splitext(filename)[1].lower()
        return ext in ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v']
    
    def _is_document_file(self, filename: str) -> bool:
        """判断是否为文档文件"""
        ext = os.path.splitext(filename)[1].lower()
        return ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf', '.odt']

    async def upload_file(
        self,
        storage_id: int,
        file_content: bytes,
        file_path: str,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """
        上传文件

        Args:
            storage_id: 存储ID
            file_content: 文件内容
            file_path: 文件路径
            session: 数据库会话

        Returns:
            Dict[str, Any]: 上传结果
        """
        try:
            # 获取存储配置，只查询存在的列
            result = await session.execute(
                select(
                    StorageConfig.id,
                    StorageConfig.name,
                    StorageConfig.storage_type,
                    StorageConfig.is_default,
                    StorageConfig.is_active,
                    StorageConfig.config,
                    StorageConfig.created_at,
                    StorageConfig.updated_at
                ).where(StorageConfig.id == storage_id)
            )
            row = result.fetchone()

            if not row:
                raise Exception("存储配置不存在")

            # 手动构建存储配置对象
            storage_config = type('StorageConfig', (), {
                'id': row.id,
                'name': row.name,
                'storage_type': row.storage_type,
                'is_default': row.is_default,
                'is_active': row.is_active,
                'config': row.config,
                'created_at': row.created_at,
                'updated_at': row.updated_at
            })()

            if storage_config.storage_type.value != "LOCAL":
                raise Exception("暂时只支持本地存储")

            # 解析存储配置
            config = json.loads(storage_config.config) if isinstance(storage_config.config, str) else storage_config.config
            base_path = config.get("base_path", "./storage")

            # 处理文件路径
            if file_path.startswith("/"):
                file_path = file_path[1:]  # 移除开头的斜杠

            full_path = os.path.join(base_path, file_path)
            full_path = os.path.normpath(full_path)

            # 确保路径在base_path内（安全检查）
            if not full_path.startswith(os.path.normpath(base_path)):
                raise Exception("路径不安全")

            # 确保目录存在
            dir_path = os.path.dirname(full_path)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)

            # 检查文件大小限制
            max_file_size = config.get("max_file_size", 100 * 1024 * 1024)  # 默认100MB
            if len(file_content) > max_file_size:
                raise Exception("文件大小超过限制: {} MB".format(max_file_size // (1024 * 1024)))

            # 写入文件
            with open(full_path, "wb") as f:
                f.write(file_content)

            # 获取文件信息
            stat = os.stat(full_path)
            filename = os.path.basename(full_path)

            return {
                "success": True,
                "message": "文件上传成功",
                "file_info": {
                    "file_path": "/" + file_path,
                    "file_name": filename,
                    "file_size": stat.st_size,
                    "file_size_formatted": self._format_file_size(stat.st_size),
                    "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                }
            }

        except Exception as e:
            logger.error("上传文件失败: {}".format(str(e)))
            raise Exception("上传文件失败: {}".format(str(e)))

    async def get_file_content(
        self,
        storage_id: int,
        file_path: str,
        session: AsyncSession = None
    ) -> bytes:
        """
        获取文件内容

        Args:
            storage_id: 存储ID
            file_path: 文件路径
            session: 数据库会话

        Returns:
            bytes: 文件内容
        """
        try:
            # 获取存储配置
            result = await session.execute(
                select(
                    StorageConfig.id,
                    StorageConfig.name,
                    StorageConfig.storage_type,
                    StorageConfig.config
                ).where(StorageConfig.id == storage_id)
            )
            row = result.fetchone()

            if not row:
                raise Exception("存储配置不存在")

            if row.storage_type.value != "LOCAL":
                raise Exception("暂时只支持本地存储")

            # 解析存储配置
            config = json.loads(row.config) if isinstance(row.config, str) else row.config
            base_path = config.get("base_path", "./storage")

            # 处理文件路径 - 移除开头的斜杠
            clean_path = file_path.lstrip('/')

            full_path = os.path.join(base_path, clean_path)
            full_path = os.path.normpath(full_path)

            logger.info(f"获取文件内容: original_path={file_path}, clean_path={clean_path}, full_path={full_path}")

            # 安全检查
            if not full_path.startswith(os.path.normpath(base_path)):
                raise Exception("路径不安全")

            # 检查文件是否存在
            if not os.path.exists(full_path) or not os.path.isfile(full_path):
                return None

            # 读取文件内容
            with open(full_path, "rb") as f:
                return f.read()

        except Exception as e:
            logger.error("获取文件内容失败: {}".format(str(e)))
            raise Exception("获取文件内容失败: {}".format(str(e)))

    async def get_file_info(
        self,
        storage_id: int,
        file_path: str,
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            storage_id: 存储ID
            file_path: 文件路径
            session: 数据库会话

        Returns:
            Dict[str, Any]: 文件信息
        """
        try:
            # 获取存储配置
            result = await session.execute(
                select(
                    StorageConfig.id,
                    StorageConfig.name,
                    StorageConfig.storage_type,
                    StorageConfig.config
                ).where(StorageConfig.id == storage_id)
            )
            row = result.fetchone()

            if not row:
                raise Exception("存储配置不存在")

            if row.storage_type.value != "LOCAL":
                raise Exception("暂时只支持本地存储")

            # 解析存储配置
            config = json.loads(row.config) if isinstance(row.config, str) else row.config
            base_path = config.get("base_path", "./storage")

            # 处理文件路径 - 移除开头的斜杠
            clean_path = file_path.lstrip('/')

            full_path = os.path.join(base_path, clean_path)
            full_path = os.path.normpath(full_path)

            logger.info(f"获取文件信息: original_path={file_path}, clean_path={clean_path}, full_path={full_path}")

            # 安全检查
            if not full_path.startswith(os.path.normpath(base_path)):
                raise Exception("路径不安全")

            # 检查文件是否存在
            if not os.path.exists(full_path):
                return None

            stat = os.stat(full_path)
            is_directory = os.path.isdir(full_path)
            filename = os.path.basename(full_path)

            # URL编码文件路径用于file_id
            normalized_path = "/" + file_path if not file_path.startswith("/") else file_path
            encoded_path = urllib.parse.quote(normalized_path, safe='')

            return {
                "file_id": f"local_{storage_id}_{encoded_path}",
                "file_path": normalized_path,
                "file_name": filename,
                "file_type": "directory" if is_directory else "file",
                "file_size": 0 if is_directory else stat.st_size,
                "file_size_formatted": self._format_file_size(0 if is_directory else stat.st_size),
                "mime_type": self._get_mime_type(filename) if not is_directory else None,
                "file_extension": os.path.splitext(filename)[1] if not is_directory else "",
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "is_directory": is_directory,
                "is_image": self._is_image_file(filename) if not is_directory else False,
                "is_video": self._is_video_file(filename) if not is_directory else False,
                "is_document": self._is_document_file(filename) if not is_directory else False,
            }

        except Exception as e:
            logger.error("获取文件信息失败: {}".format(str(e)))
            raise Exception("获取文件信息失败: {}".format(str(e)))

    async def create_file(
        self,
        storage_id: int,
        file_path: str,
        file_type: str,
        content: str = '',
        session: AsyncSession = None
    ) -> Dict[str, Any]:
        """
        创建新文件

        Args:
            storage_id: 存储ID
            file_path: 文件路径
            file_type: 文件类型
            content: 文件内容
            session: 数据库会话

        Returns:
            Dict[str, Any]: 创建的文件信息
        """
        try:
            # 获取存储配置
            result = await session.execute(
                select(
                    StorageConfig.id,
                    StorageConfig.name,
                    StorageConfig.storage_type,
                    StorageConfig.config
                ).where(StorageConfig.id == storage_id)
            )
            row = result.fetchone()

            if not row:
                raise Exception("存储配置不存在")

            if row.storage_type.value != "LOCAL":
                raise Exception("暂时只支持本地存储")

            # 解析存储配置
            config = json.loads(row.config) if isinstance(row.config, str) else row.config
            base_path = config.get("base_path", "./storage")

            # 处理文件路径 - 移除开头的斜杠
            clean_path = file_path.lstrip('/')

            full_path = os.path.join(base_path, clean_path)
            full_path = os.path.normpath(full_path)

            logger.info(f"创建文件: original_path={file_path}, clean_path={clean_path}, full_path={full_path}")

            # 安全检查
            if not full_path.startswith(os.path.normpath(base_path)):
                raise Exception("路径不安全")

            # 检查文件是否已存在
            if os.path.exists(full_path):
                raise Exception("文件已存在")

            # 确保目录存在
            dir_path = os.path.dirname(full_path)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)

            # 根据文件类型创建不同的默认内容
            if file_type in ['docx', 'doc']:
                # Word文档使用HTML格式
                if not content:
                    content = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>新建文档</title>
</head>
<body>
    <h1>新建文档</h1>
    <p>这是一个新创建的Word文档。</p>
</body>
</html>"""
            elif file_type in ['pptx', 'ppt']:
                # PowerPoint使用JSON格式
                if not content:
                    content = json.dumps([
                        {
                            "id": "slide-1",
                            "title": "标题幻灯片",
                            "content": "<h1>新建演示文稿</h1>",
                            "layout": "title",
                            "background": "#ffffff",
                            "animations": []
                        }
                    ])
            elif file_type in ['xlsx', 'xls']:
                # Excel使用JSON格式
                if not content:
                    content = json.dumps([
                        {
                            "id": "sheet-1",
                            "name": "Sheet1",
                            "data": [
                                [{"value": "列A"}, {"value": "列B"}, {"value": "列C"}],
                                [{"value": ""}, {"value": ""}, {"value": ""}]
                            ]
                        }
                    ])
            elif file_type == 'txt':
                # 纯文本
                if not content:
                    content = "这是一个新创建的文本文档。"
            elif file_type == 'md':
                # Markdown
                if not content:
                    content = "# 新建Markdown文档\n\n这是一个新创建的Markdown文档。"

            # 写入文件
            with open(full_path, "w", encoding="utf-8") as f:
                f.write(content)

            # 获取文件信息
            stat = os.stat(full_path)
            filename = os.path.basename(full_path)

            # 生成文件ID
            normalized_path = file_path if file_path.startswith('/') else '/' + file_path
            encoded_path = urllib.parse.quote(normalized_path, safe='')
            file_id = f"local_{storage_id}_{encoded_path}"

            return {
                "file_id": file_id,
                "file_path": normalized_path,
                "file_name": filename,
                "file_type": "file",
                "file_size": stat.st_size,
                "file_size_formatted": self._format_file_size(stat.st_size),
                "mime_type": self._get_mime_type(filename),
                "file_extension": os.path.splitext(filename)[1].lstrip('.'),
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "is_directory": False,
                "is_image": self._is_image_file(filename),
                "is_video": self._is_video_file(filename),
                "is_document": self._is_document_file(filename),
            }

        except Exception as e:
            logger.error("创建文件失败: {}".format(str(e)))
            raise Exception("创建文件失败: {}".format(str(e)))
