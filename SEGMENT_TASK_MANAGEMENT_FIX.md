# 分段任务管理功能修复指南

## 问题描述
前端页面出现模块导入错误：
```
Module not found: Can't resolve '@/lib/api-client'
```

## 问题原因
分段任务管理页面使用了错误的API客户端导入路径。正确的路径应该是 `@/lib/api` 而不是 `@/lib/api-client`。

## 修复内容

### 1. 修复API导入路径
**文件**: `web/app/file-manager/segment/task/page.tsx`
```typescript
// 修复前
import apiClient from '@/lib/api-client';

// 修复后
import apiClient from '@/lib/api';
```

### 2. 验证其他文件
检查了其他相关文件，确认都使用了正确的导入路径：
- `web/app/file-manager/segment/batch/page.tsx` ✅ 正确
- `web/lib/api.ts` ✅ 存在

## 测试步骤

### 1. 数据库准备
```sql
-- 在PostgreSQL中执行
\i api/sql_scripts/segment_task_management_fix.sql
```

### 2. 启动后端服务
```cmd
cd api
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 启动Celery服务
```cmd
cd api
celery -A app.core.celery_config:celery_app worker --loglevel=info --queues=segment_queue --concurrency=2 --pool=solo
```

### 4. 启动前端服务
```cmd
cd web
test_segment_task_management.bat
```

### 5. 功能验证
访问以下URL进行测试：

#### 5.1 文件管理页面
- URL: http://localhost:3000/file-manager
- 验证: 页面正常加载，能看到"分段任务管理"按钮

#### 5.2 分段任务管理页面
- URL: http://localhost:3000/file-manager/segment/task
- 验证: 
  - 左侧Celery状态面板正常显示
  - 右侧任务列表正常加载
  - 无模块导入错误

#### 5.3 AI批量分段页面
- URL: http://localhost:3000/file-manager/segment/batch
- 验证: 页面正常加载，功能正常

## 功能特性

### 左侧Celery监控面板
- **连接状态**: 显示Celery是否连接正常
- **Worker信息**: 显示活跃Worker数量和详细信息
- **性能指标**: CPU使用率、内存使用率等
- **队列状态**: 显示队列长度和消费者数量
- **实时刷新**: 每5秒自动刷新，支持手动刷新

### 右侧任务列表
- **任务统计**: 总任务数、已完成、处理中、失败
- **任务详情**: 显示任务状态、进度、配置信息
- **操作功能**: 查看详情、删除任务
- **新建任务**: 快速跳转到创建页面

### 任务详情查看
- 点击任务的"查看详情"按钮
- 跳转到AI批量分段页面，显示任务详细信息
- 支持查看任务配置、文件列表、处理进度

## API接口

### Celery状态监控
```
GET /api/v1/system/celery/status
```
返回Celery运行状态、Worker信息、性能指标等。

### 分段任务列表
```
GET /api/v1/document-segment/tasks
```
返回所有分段任务列表，支持分页和状态过滤。

### 任务详情
```
GET /api/v1/document-segment/tasks/{task_id}
```
返回指定任务的详细信息。

### 任务进度
```
GET /api/v1/document-segment/tasks/{task_id}/progress
```
返回任务的实时进度信息。

## 故障排除

### 1. 模块导入错误
- 检查导入路径是否正确
- 确认API客户端文件存在于 `web/lib/api.ts`
- 重启开发服务器

### 2. 页面加载失败
- 检查后端API服务是否启动
- 验证API端点是否可访问
- 查看浏览器控制台错误信息

### 3. Celery状态显示异常
- 检查Celery Worker是否启动
- 验证Redis连接是否正常
- 查看API日志

### 4. 任务列表为空
- 检查数据库连接
- 验证表结构是否正确
- 创建测试任务进行验证

## 性能优化

### 1. 前端优化
- 使用React.memo优化组件渲染
- 实现虚拟滚动处理大量任务
- 添加加载状态和错误边界

### 2. 后端优化
- 使用数据库索引优化查询
- 实现API响应缓存
- 优化Celery任务处理

### 3. 网络优化
- 减少API调用频率
- 使用WebSocket实现实时更新
- 压缩API响应数据

## 扩展功能

### 1. 任务管理增强
- 批量操作（批量删除、重启）
- 任务模板功能
- 任务调度和定时执行

### 2. 监控增强
- 添加图表和可视化
- 历史性能数据分析
- 告警和通知功能

### 3. 用户体验
- 任务搜索和过滤
- 自定义列显示
- 导出任务报告

## 总结

修复了API导入路径错误后，分段任务管理功能现在可以正常工作：

✅ **已修复**:
- 模块导入错误
- 页面加载问题
- API客户端路径

✅ **功能完整**:
- Celery状态监控
- 任务列表管理
- 任务详情查看
- 实时进度更新

✅ **用户体验**:
- 响应式设计
- 美观的界面
- 直观的操作

用户现在可以通过分段任务管理页面完整地管理所有分段任务，监控系统状态，并轻松查看任务详情。
