# AI知识库 - 开发环境配置
ENVIRONMENT="development"
DEBUG=true
HOST="127.0.0.1"
PORT=8000
LOG_LEVEL="DEBUG"

# PostgreSQL 数据库配置（开发环境默认）
DB_USERNAME=postgres
DB_PASSWORD=XHC12345
DB_HOST=**************
DB_PORT=5432
DB_DATABASE=xhc_rag
# DATABASE_URL will be dynamically generated to handle special characters in password
DATABASE_TYPE=postgresql
DATABASE_ECHO=true
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# Redis 配置
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=XHC12345
REDIS_USE_SSL=false
REDIS_DB=10

# 开发Celery
CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/11
CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/12

# 开发安全配置
SECRET_KEY="dev-secret-key-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# 开发CORS - 允许所有来源
CORS_ORIGINS="*"
CORS_ALLOW_CREDENTIALS=true

# 开发插件
ENABLED_PLUGINS="auth,file_manager,rag_engine,storage_management"

# 文件存储配置
UPLOAD_MAX_SIZE=104857600
UPLOAD_ALLOWED_EXTENSIONS="txt,pdf,doc,docx,xls,xlsx,ppt,pptx,md,json,csv,jpg,jpeg,png,gif,mp4,avi,mov"
DEFAULT_STORAGE_TYPE="local"
STORAGE_BASE_PATH="./storage"

# 开发监控
ENABLE_METRICS=false
METRICS_PORT=9090
