#!/usr/bin/env python3
"""
测试API路由是否正确注册
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from fastapi.testclient import TestClient
from loguru import logger


def test_api_routes():
    """测试API路由"""
    try:
        # 导入应用
        from app.core.app_factory import create_app
        
        logger.info("创建FastAPI应用...")
        app = create_app()
        
        # 创建测试客户端
        client = TestClient(app)
        
        # 测试根路径
        logger.info("测试根路径...")
        response = client.get("/")
        logger.info(f"根路径响应: {response.status_code}")
        
        # 测试API信息
        logger.info("测试API信息...")
        response = client.get("/api/")
        logger.info(f"API信息响应: {response.status_code}")
        if response.status_code == 200:
            logger.info(f"API信息: {response.json()}")
        
        # 测试Celery健康检查（无需认证）
        logger.info("测试Celery健康检查...")
        response = client.get("/api/v1/celery/health")
        logger.info(f"Celery健康检查响应: {response.status_code}")
        if response.status_code == 200:
            logger.info(f"健康检查结果: {response.json()}")
        else:
            logger.error(f"健康检查失败: {response.text}")
        
        # 测试需要认证的端点（应该返回401）
        logger.info("测试需要认证的端点...")
        response = client.get("/api/v1/celery/status")
        logger.info(f"状态端点响应: {response.status_code}")
        if response.status_code == 401:
            logger.info("状态端点正确要求认证")
        else:
            logger.warning(f"状态端点响应异常: {response.status_code} - {response.text}")
        
        # 获取所有路由
        logger.info("获取所有路由...")
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(f"{route.methods} {route.path}")
        
        logger.info("注册的路由:")
        for route in sorted(routes):
            if 'celery' in route.lower():
                logger.info(f"  {route}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    logger.info("开始API路由测试...")
    success = test_api_routes()
    logger.info("测试完成")
    sys.exit(0 if success else 1)
