'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  FileText,
  Presentation,
  FileSpreadsheet,
  File,
  Plus,
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react';
import apiClient from '@/lib/api';
import { useRouter } from 'next/navigation';

interface CreateFileDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath: string;
  storageId: number;
  onFileCreated?: (file: any) => void;
}

interface FileTemplate {
  type: string;
  name: string;
  extension: string;
  icon: React.ComponentType<any>;
  color: string;
  description: string;
  defaultContent: string;
}

const CreateFileDialog: React.FC<CreateFileDialogProps> = ({
  isOpen,
  onClose,
  currentPath,
  storageId,
  onFileCreated
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<FileTemplate | null>(null);
  const [fileName, setFileName] = useState('');
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // 检查是否为Office文件
  const isOfficeFile = (fileType: string) => {
    const officeTypes = ['docx', 'pptx', 'xlsx', 'pdf', 'txt', 'md'];
    return officeTypes.includes(fileType.toLowerCase());
  };

  const fileTemplates: FileTemplate[] = [
    {
      type: 'docx',
      name: 'Word 文档',
      extension: '.docx',
      icon: FileText,
      color: 'text-blue-600',
      description: '创建新的Word文档',
      defaultContent: `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>新建文档</title>
</head>
<body>
    <h1>新建文档</h1>
    <p>这是一个新创建的Word文档。您可以在这里开始编写内容。</p>
    <p>支持的功能包括：</p>
    <ul>
        <li>富文本格式</li>
        <li>段落设置</li>
        <li>表格插入</li>
        <li>图片插入</li>
    </ul>
</body>
</html>`
    },
    {
      type: 'pptx',
      name: 'PowerPoint 演示文稿',
      extension: '.pptx',
      icon: Presentation,
      color: 'text-orange-600',
      description: '创建新的PowerPoint演示文稿',
      defaultContent: JSON.stringify([
        {
          id: 'slide-1',
          title: '标题幻灯片',
          content: '<h1>新建演示文稿</h1><p>副标题</p>',
          layout: 'title',
          background: '#ffffff',
          animations: []
        },
        {
          id: 'slide-2',
          title: '内容幻灯片',
          content: '<h2>第一页内容</h2><p>在这里添加您的内容</p>',
          layout: 'content',
          background: '#ffffff',
          animations: []
        }
      ])
    },
    {
      type: 'xlsx',
      name: 'Excel 表格',
      extension: '.xlsx',
      icon: FileSpreadsheet,
      color: 'text-green-600',
      description: '创建新的Excel表格',
      defaultContent: JSON.stringify([
        {
          id: 'sheet-1',
          name: 'Sheet1',
          data: [
            [{ value: '列A' }, { value: '列B' }, { value: '列C' }],
            [{ value: '数据1' }, { value: '数据2' }, { value: '数据3' }],
            [{ value: '' }, { value: '' }, { value: '' }]
          ]
        }
      ])
    },
    {
      type: 'pdf',
      name: 'PDF 文档',
      extension: '.pdf',
      icon: File,
      color: 'text-red-600',
      description: '创建新的PDF文档',
      defaultContent: JSON.stringify({
        title: '新建PDF文档',
        content: '这是一个新建的PDF文档内容。',
        pages: [
          {
            content: '<h1>新建PDF文档</h1><p>这是第一页的内容。</p><p>您可以在这里添加更多内容。</p>'
          }
        ]
      })
    },
    {
      type: 'txt',
      name: '文本文档',
      extension: '.txt',
      icon: FileText,
      color: 'text-gray-600',
      description: '创建新的纯文本文档',
      defaultContent: '这是一个新创建的文本文档。\n\n您可以在这里编写纯文本内容。'
    },
    {
      type: 'md',
      name: 'Markdown 文档',
      extension: '.md',
      icon: FileText,
      color: 'text-purple-600',
      description: '创建新的Markdown文档',
      defaultContent: `# 新建Markdown文档

这是一个新创建的Markdown文档。

## 功能特性

- **粗体文本**
- *斜体文本*
- [链接](https://example.com)
- \`代码\`

## 代码块

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

## 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
`
    }
  ];

  const handleTemplateSelect = (template: FileTemplate) => {
    setSelectedTemplate(template);

    // 更新文件名，移除旧扩展名并添加新扩展名
    if (!fileName || fileName === `新建文档${selectedTemplate?.extension || ''}`) {
      // 如果是默认文件名或空，直接设置新的默认名
      setFileName(`新建文档${template.extension}`);
    } else {
      // 如果用户已经输入了自定义名称，只更新扩展名
      const nameWithoutExt = fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名
      setFileName(`${nameWithoutExt}${template.extension}`);
    }
  };

  const handleCreate = async () => {
    if (!selectedTemplate || !fileName.trim()) {
      setError('请选择文件类型并输入文件名');
      return;
    }

    try {
      setCreating(true);
      setError(null);

      // 确保文件名有正确的扩展名
      let finalFileName = fileName.trim();
      if (!finalFileName.endsWith(selectedTemplate.extension)) {
        finalFileName += selectedTemplate.extension;
      }

      // 构建文件路径
      const filePath = currentPath === '/' ? `/${finalFileName}` : `${currentPath}/${finalFileName}`;

      // 创建文件
      console.log('创建文件参数:', {
        storage_id: storageId,
        file_path: filePath,
        file_type: selectedTemplate.type,
        current_path: currentPath
      });

      const response = await apiClient.post('/api/v1/file-management/files/create', {
        storage_id: storageId,
        file_path: filePath,
        file_type: selectedTemplate.type,
        content: selectedTemplate.defaultContent
      });

      if (response.data.success) {
        const createdFile = response.data.file;

        // 通知父组件文件已创建
        if (onFileCreated) {
          onFileCreated(createdFile);
        }

        // 重置状态并关闭对话框
        setSelectedTemplate(null);
        setFileName('');
        onClose();

        // 自动在新窗口打开编辑器
        if (createdFile && createdFile.file_id) {
          const encodedFileId = btoa(createdFile.file_id);

          if (isOfficeFile(selectedTemplate.type)) {
            // Office文件使用专用编辑器
            window.open(`/file-manager/office/edit/${encodedFileId}`, '_blank');
          } else {
            // 其他文件使用通用编辑器
            window.open(`/file-manager/edit/${encodedFileId}`, '_blank');
          }
        }
      } else {
        setError(response.data.message || '创建文件失败');
      }
    } catch (err: any) {
      console.error('创建文件失败:', err);
      setError(err.response?.data?.detail || '创建文件失败');
    } finally {
      setCreating(false);
    }
  };

  const handleClose = () => {
    if (!creating) {
      setSelectedTemplate(null);
      setFileName('');
      setError(null);
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* 背景遮罩 - 透明效果 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-white bg-opacity-20 backdrop-blur-md"
            onClick={handleClose}
          />

          {/* 对话框 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden"
          >
            {/* 头部 */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Plus className="w-6 h-6" />
                  <h2 className="text-xl font-bold">新建文件 & Office 在线编辑</h2>
                </div>
                <button
                  onClick={handleClose}
                  disabled={creating}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors disabled:opacity-50"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <p className="text-blue-100 mt-2">创建文件并自动打开专业编辑器</p>
            </div>

            {/* 内容 */}
            <div className="p-6">
              {/* Office功能介绍 */}
              <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-blue-900">Office 在线编辑器</h3>
                </div>
                <p className="text-blue-700 text-sm mb-3">
                  类似 OnlyOffice 的完整编辑体验，支持富文本编辑、格式设置、表格插入等专业功能
                </p>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center space-x-1">
                    <Check className="w-3 h-3 text-green-500" />
                    <span className="text-blue-600">实时保存</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Check className="w-3 h-3 text-green-500" />
                    <span className="text-blue-600">格式化工具</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Check className="w-3 h-3 text-green-500" />
                    <span className="text-blue-600">协作编辑</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Check className="w-3 h-3 text-green-500" />
                    <span className="text-blue-600">云端同步</span>
                  </div>
                </div>
              </div>

              {/* 文件类型选择 */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">选择文件类型</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {fileTemplates.map((template) => (
                    <motion.button
                      key={template.type}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleTemplateSelect(template)}
                      className={`p-4 rounded-xl border-2 transition-all relative ${
                        selectedTemplate?.type === template.type
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <template.icon className={`w-8 h-8 ${template.color} mx-auto mb-2`} />
                      <div className="text-sm font-medium text-gray-900">{template.name}</div>
                      <div className="text-xs text-gray-500 mt-1">{template.description}</div>

                      {/* Office文件特殊标识 */}
                      {isOfficeFile(template.type) && (
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* 文件名输入 */}
              {selectedTemplate && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-6"
                >
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文件名
                  </label>
                  <input
                    type="text"
                    value={fileName}
                    onChange={(e) => setFileName(e.target.value)}
                    placeholder={`输入文件名${selectedTemplate.extension}`}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={creating}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    文件将保存到: {currentPath === '/' ? '/' : currentPath}/
                  </p>
                </motion.div>
              )}

              {/* 错误信息 */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2"
                >
                  <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                  <span className="text-red-700 text-sm">{error}</span>
                </motion.div>
              )}
            </div>

            {/* 底部操作 */}
            <div className="bg-gray-50 px-6 py-4 flex items-center justify-end space-x-3">
              <button
                onClick={handleClose}
                disabled={creating}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              >
                取消
              </button>
              <button
                onClick={handleCreate}
                disabled={!selectedTemplate || !fileName.trim() || creating}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {creating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>创建中...</span>
                  </>
                ) : (
                  <>
                    <Check className="w-4 h-4" />
                    <span>创建文件</span>
                  </>
                )}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default CreateFileDialog;
