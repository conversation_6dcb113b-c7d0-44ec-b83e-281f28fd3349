"""
文件处理相关的Celery任务
"""
import os
import asyncio
from typing import Dict, Any, List
from loguru import logger

from app.core.celery_config import celery_app
from app.core.database import get_async_session


@celery_app.task(queue='file_queue')
def calculate_storage_statistics(storage_id: int) -> Dict[str, Any]:
    """
    计算存储统计信息
    
    Args:
        storage_id: 存储配置ID
        
    Returns:
        Dict[str, Any]: 统计结果
    """
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(_calculate_storage_statistics_async(storage_id))
            return result
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"计算存储统计失败: {storage_id} - {e}")
        return {
            "status": "error",
            "storage_id": storage_id,
            "error": str(e),
            "total_files": 0,
            "total_size": 0
        }


async def _calculate_storage_statistics_async(storage_id: int) -> Dict[str, Any]:
    """异步计算存储统计信息"""
    async for session in get_async_session():
        try:
            from app.models.file_management import StorageConfig
            from sqlalchemy import select
            import json
            
            # 获取存储配置
            query = select(StorageConfig).where(StorageConfig.id == storage_id)
            result = await session.execute(query)
            storage = result.scalar_one_or_none()
            
            if not storage:
                raise Exception(f"存储配置不存在: {storage_id}")
            
            total_files = 0
            total_size = 0
            
            if storage.storage_type.value == "LOCAL":
                config_dict = json.loads(storage.config) if isinstance(storage.config, str) else storage.config
                base_path = config_dict.get("base_path", "./storage")
                
                if os.path.exists(base_path):
                    for root, dirs, files in os.walk(base_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                if os.path.isfile(file_path):
                                    total_files += 1
                                    total_size += os.path.getsize(file_path)
                            except (OSError, IOError):
                                continue
            
            # 更新存储配置的统计信息
            storage.total_files = total_files
            storage.total_size = total_size
            await session.commit()
            
            logger.info(f"存储统计完成: {storage_id} - {total_files} 文件, {total_size} 字节")
            
            return {
                "status": "success",
                "storage_id": storage_id,
                "total_files": total_files,
                "total_size": total_size
            }
            
        except Exception as e:
            logger.error(f"计算存储统计失败: {storage_id} - {e}")
            raise
        finally:
            break


@celery_app.task(queue='file_queue')
def batch_calculate_storage_statistics(storage_ids: List[int]) -> Dict[str, Any]:
    """
    批量计算存储统计信息
    
    Args:
        storage_ids: 存储配置ID列表
        
    Returns:
        Dict[str, Any]: 批量计算结果
    """
    results = []
    
    for storage_id in storage_ids:
        try:
            result = calculate_storage_statistics.delay(storage_id)
            results.append({
                "storage_id": storage_id,
                "celery_task_id": result.id,
                "status": "submitted"
            })
        except Exception as e:
            results.append({
                "storage_id": storage_id,
                "status": "failed",
                "error": str(e)
            })
    
    return {
        "total": len(storage_ids),
        "submitted": len([r for r in results if r["status"] == "submitted"]),
        "failed": len([r for r in results if r["status"] == "failed"]),
        "results": results
    }


@celery_app.task(queue='file_queue')
def cleanup_temp_files(temp_dir: str = None) -> Dict[str, Any]:
    """
    清理临时文件
    
    Args:
        temp_dir: 临时目录路径，如果为None则清理所有临时目录
        
    Returns:
        Dict[str, Any]: 清理结果
    """
    try:
        import tempfile
        import shutil
        import time
        
        cleaned_files = 0
        cleaned_size = 0
        
        if temp_dir and os.path.exists(temp_dir):
            # 清理指定目录
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        cleaned_files += 1
                        cleaned_size += file_size
                    except (OSError, IOError):
                        continue
            
            # 删除空目录
            try:
                if not os.listdir(temp_dir):
                    os.rmdir(temp_dir)
            except (OSError, IOError):
                pass
        else:
            # 清理系统临时目录中的旧文件
            temp_base = tempfile.gettempdir()
            current_time = time.time()
            
            for item in os.listdir(temp_base):
                if item.startswith('upload_'):
                    item_path = os.path.join(temp_base, item)
                    try:
                        # 删除超过1小时的临时文件
                        if current_time - os.path.getctime(item_path) > 3600:
                            if os.path.isfile(item_path):
                                file_size = os.path.getsize(item_path)
                                os.remove(item_path)
                                cleaned_files += 1
                                cleaned_size += file_size
                            elif os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                                cleaned_files += 1
                    except (OSError, IOError):
                        continue
        
        logger.info(f"临时文件清理完成: {cleaned_files} 文件, {cleaned_size} 字节")
        
        return {
            "status": "success",
            "cleaned_files": cleaned_files,
            "cleaned_size": cleaned_size
        }
        
    except Exception as e:
        logger.error(f"清理临时文件失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "cleaned_files": 0,
            "cleaned_size": 0
        }
