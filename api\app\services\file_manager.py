"""
文件管理服务
提供文件管理的核心业务逻辑
"""

import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.orm import selectinload

from app.core.database import get_async_session
from app.models.file_management import (
    StorageConfig, FileRecord, SyncLog, 
    StorageType, FileType, FileStatus
)
from app.services.storage.storage_factory import StorageFactory
from app.services.storage.base import StorageInterface, FileInfo, StorageException
from loguru import logger


class FileManagerService:
    """文件管理服务"""

    def __init__(self):
        self._storage_cache: Dict[int, StorageInterface] = {}
    
    async def get_storage(self, storage_id: int) -> StorageInterface:
        """获取存储实例"""
        if storage_id in self._storage_cache:
            storage = self._storage_cache[storage_id]
            if await storage.is_connected():
                return storage
            else:
                # 连接已断开，重新连接
                await storage.connect()
                return storage
        
        # 从数据库获取存储配置
        async with get_async_session() as session:
            result = await session.execute(
                select(StorageConfig).where(StorageConfig.id == storage_id)
            )
            storage_config = result.scalar_one_or_none()

            if not storage_config:
                raise StorageException(f"Storage config not found: {storage_id}")

            if not storage_config.is_active:
                raise StorageException(f"Storage is not active: {storage_id}")
        
        # 创建存储实例
        storage = StorageFactory.create_storage(
            storage_config.storage_type,
            storage_config.config
        )
        
        # 连接存储
        await storage.connect()
        
        # 缓存存储实例
        self._storage_cache[storage_id] = storage
        
        return storage
    
    async def list_files(
        self,
        storage_id: int,
        path: str = "/",
        page: int = 1,
        page_size: int = 50,
        sort_by: str = "name",
        sort_order: str = "asc",
        file_type: Optional[str] = None,
        search: Optional[str] = None,
        session: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """
        列出文件

        Args:
            storage_id: 存储ID
            path: 目录路径
            page: 页码
            page_size: 每页大小
            sort_by: 排序字段 (name, size, modified_at, created_at)
            sort_order: 排序方向 (asc, desc)
            file_type: 文件类型过滤 (file, directory)
            search: 搜索关键词
            session: 数据库会话

        Returns:
            Dict[str, Any]: 文件列表和分页信息
        """
        # 如果没有提供session，则创建一个临时的
        if session is None:
            from app.core.database import async_session_factory
            async with async_session_factory() as temp_session:
                return await self._list_files_impl(
                    temp_session, storage_id, path, page, page_size,
                    sort_by, sort_order, file_type, search
                )
        else:
            return await self._list_files_impl(
                session, storage_id, path, page, page_size,
                sort_by, sort_order, file_type, search
            )

    async def _list_files_impl(
        self,
        session: AsyncSession,
        storage_id: int,
        path: str,
        page: int,
        page_size: int,
        sort_by: str,
        sort_order: str,
        file_type: Optional[str],
        search: Optional[str]
    ) -> Dict[str, Any]:
        """实际的文件列表实现"""
        # 构建查询
        query = select(FileRecord).where(
            and_(
                FileRecord.storage_id == storage_id,
                FileRecord.parent_path == path,
                FileRecord.status == FileStatus.ACTIVE
            )
        )
            
            # 文件类型过滤
            if file_type:
                if file_type == "file":
                    query = query.where(FileRecord.file_type == FileType.FILE)
                elif file_type == "directory":
                    query = query.where(FileRecord.file_type == FileType.DIRECTORY)
            
            # 搜索过滤
            if search:
                search_pattern = f"%{search}%"
                query = query.where(
                    or_(
                        FileRecord.file_name.ilike(search_pattern),
                        FileRecord.file_path.ilike(search_pattern)
                    )
                )
            
            # 排序
            sort_column = getattr(FileRecord, sort_by, FileRecord.file_name)
            if sort_order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
            
            # 计算总数
            count_query = select(func.count()).select_from(query.subquery())
            total_result = await session.execute(count_query)
            total = total_result.scalar()
            
            # 分页
            offset = (page - 1) * page_size
            query = query.offset(offset).limit(page_size)
            
            # 执行查询
            result = await session.execute(query)
            files = result.scalars().all()
            
            # 转换为字典
            file_list = [file.to_dict() for file in files]
            
            return {
                "files": file_list,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "pages": (total + page_size - 1) // page_size
                },
                "path": path,
                "storage_id": storage_id
            }
    
    async def get_file_info(self, storage_id: int, file_path: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        async with get_async_session() as session:
            result = await session.execute(
                select(FileRecord).where(
                    and_(
                        FileRecord.storage_id == storage_id,
                        FileRecord.file_path == file_path,
                        FileRecord.status == FileStatus.ACTIVE
                    )
                )
            )
            file_record = result.scalar_one_or_none()
            
            if file_record:
                return file_record.to_dict()
            return None
    
    async def create_directory(self, storage_id: int, path: str) -> bool:
        """创建目录"""
        try:
            storage = await self.get_storage(storage_id)
            success = await storage.create_directory(path)
            
            if success:
                # 同步到数据库
                await self._sync_single_file(storage_id, path)
            
            return success
        except Exception as e:
            logger.error(f"Failed to create directory {path}: {e}")
            return False
    
    async def delete_file(self, storage_id: int, file_path: str) -> bool:
        """删除文件或目录"""
        try:
            storage = await self.get_storage(storage_id)
            success = await storage.delete_file(file_path)
            
            if success:
                # 更新数据库状态
                async with get_async_session() as session:
                    # 标记文件为已删除
                    result = await session.execute(
                        select(FileRecord).where(
                            and_(
                                FileRecord.storage_id == storage_id,
                                or_(
                                    FileRecord.file_path == file_path,
                                    FileRecord.file_path.like(f"{file_path}/%")
                                )
                            )
                        )
                    )
                    files = result.scalars().all()
                    
                    for file_record in files:
                        file_record.status = FileStatus.DELETED
                        file_record.updated_at = datetime.utcnow()
                    
                    await session.commit()
            
            return success
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    async def move_file(self, storage_id: int, source_path: str, target_path: str) -> bool:
        """移动/重命名文件"""
        try:
            storage = await self.get_storage(storage_id)
            success = await storage.move_file(source_path, target_path)
            
            if success:
                # 更新数据库
                async with get_async_session() as session:
                    # 更新源文件路径
                    result = await session.execute(
                        select(FileRecord).where(
                            and_(
                                FileRecord.storage_id == storage_id,
                                or_(
                                    FileRecord.file_path == source_path,
                                    FileRecord.file_path.like(f"{source_path}/%")
                                )
                            )
                        )
                    )
                    files = result.scalars().all()
                    
                    for file_record in files:
                        # 更新路径
                        old_path = file_record.file_path
                        new_path = old_path.replace(source_path, target_path, 1)
                        
                        file_record.file_path = new_path
                        file_record.file_name = new_path.split('/')[-1]
                        file_record.parent_path = '/'.join(new_path.split('/')[:-1]) or '/'
                        file_record.updated_at = datetime.utcnow()
                    
                    await session.commit()
            
            return success
        except Exception as e:
            logger.error(f"Failed to move file {source_path} to {target_path}: {e}")
            return False
    
    async def copy_file(self, storage_id: int, source_path: str, target_path: str) -> bool:
        """复制文件"""
        try:
            storage = await self.get_storage(storage_id)
            success = await storage.copy_file(source_path, target_path)
            
            if success:
                # 同步新文件到数据库
                await self._sync_single_file(storage_id, target_path)
            
            return success
        except Exception as e:
            logger.error(f"Failed to copy file {source_path} to {target_path}: {e}")
            return False
    
    async def upload_file(self, storage_id: int, file_content: bytes, remote_path: str) -> bool:
        """上传文件"""
        try:
            storage = await self.get_storage(storage_id)
            success = await storage.write_file(remote_path, file_content)
            
            if success:
                # 同步到数据库
                await self._sync_single_file(storage_id, remote_path)
            
            return success
        except Exception as e:
            logger.error(f"Failed to upload file to {remote_path}: {e}")
            return False
    
    async def download_file(self, storage_id: int, file_path: str) -> bytes:
        """下载文件"""
        storage = await self.get_storage(storage_id)
        return await storage.read_file(file_path)
    
    async def sync_storage(self, storage_id: int, full_sync: bool = False) -> Dict[str, Any]:
        """同步存储"""
        start_time = datetime.utcnow()
        
        # 创建同步日志
        async with get_async_session() as session:
            sync_log = SyncLog(
                storage_id=storage_id,
                sync_type="full" if full_sync else "incremental",
                status="running",
                started_at=start_time
            )
            session.add(sync_log)
            await session.commit()
            await session.refresh(sync_log)
            sync_log_id = sync_log.id
        
        try:
            storage = await self.get_storage(storage_id)
            
            # 获取存储中的所有文件
            storage_files = await storage.list_files("/", recursive=True)
            
            stats = {
                "files_scanned": len(storage_files),
                "files_added": 0,
                "files_updated": 0,
                "files_deleted": 0
            }
            
            # 同步文件到数据库
            async with get_async_session() as session:
                for file_info in storage_files:
                    await self._sync_file_to_db(session, storage_id, file_info, stats)
                
                # 如果是完全同步，标记不存在的文件为已删除
                if full_sync:
                    storage_paths = {f.path for f in storage_files}
                    
                    result = await session.execute(
                        select(FileRecord).where(
                            and_(
                                FileRecord.storage_id == storage_id,
                                FileRecord.status == FileStatus.ACTIVE
                            )
                        )
                    )
                    db_files = result.scalars().all()
                    
                    for db_file in db_files:
                        if db_file.file_path not in storage_paths:
                            db_file.status = FileStatus.DELETED
                            db_file.updated_at = datetime.utcnow()
                            stats["files_deleted"] += 1
                
                await session.commit()
            
            # 更新同步日志
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            async with get_async_session() as session:
                result = await session.execute(
                    select(SyncLog).where(SyncLog.id == sync_log_id)
                )
                sync_log = result.scalar_one()
                
                sync_log.status = "completed"
                sync_log.completed_at = end_time
                sync_log.duration_seconds = int(duration)
                sync_log.files_scanned = stats["files_scanned"]
                sync_log.files_added = stats["files_added"]
                sync_log.files_updated = stats["files_updated"]
                sync_log.files_deleted = stats["files_deleted"]
                
                await session.commit()
            
            return {
                "success": True,
                "stats": stats,
                "duration": duration
            }
        
        except Exception as e:
            # 更新同步日志为失败状态
            async with get_async_session() as session:
                result = await session.execute(
                    select(SyncLog).where(SyncLog.id == sync_log_id)
                )
                sync_log = result.scalar_one()
                
                sync_log.status = "failed"
                sync_log.error_message = str(e)
                sync_log.completed_at = datetime.utcnow()
                
                await session.commit()
            
            logger.error(f"Sync failed for storage {storage_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _sync_single_file(self, storage_id: int, file_path: str):
        """同步单个文件到数据库"""
        try:
            storage = await self.get_storage(storage_id)
            file_info = await storage.get_file_info(file_path)
            
            if file_info:
                async with get_async_session() as session:
                    stats = {"files_added": 0, "files_updated": 0, "files_deleted": 0}
                    await self._sync_file_to_db(session, storage_id, file_info, stats)
                    await session.commit()
        except Exception as e:
            logger.error(f"Failed to sync file {file_path}: {e}")
    
    async def _sync_file_to_db(
        self, 
        session: AsyncSession, 
        storage_id: int, 
        file_info: FileInfo, 
        stats: Dict[str, int]
    ):
        """同步文件信息到数据库"""
        # 查找现有记录
        result = await session.execute(
            select(FileRecord).where(
                and_(
                    FileRecord.storage_id == storage_id,
                    FileRecord.file_path == file_info.path
                )
            )
        )
        existing_file = result.scalar_one_or_none()
        
        if existing_file:
            # 更新现有记录
            if (existing_file.file_size != file_info.size or 
                existing_file.file_hash != file_info.hash_value or
                existing_file.status != FileStatus.ACTIVE):
                
                existing_file.file_size = file_info.size
                existing_file.file_hash = file_info.hash_value
                existing_file.file_modified_at = file_info.modified_at
                existing_file.status = FileStatus.ACTIVE
                existing_file.last_sync_at = datetime.utcnow()
                existing_file.updated_at = datetime.utcnow()
                
                stats["files_updated"] += 1
        else:
            # 创建新记录
            new_file = FileRecord(
                storage_id=storage_id,
                file_path=file_info.path,
                file_name=file_info.name,
                parent_path=file_info.parent_path,
                file_type=FileType.DIRECTORY if file_info.is_directory else FileType.FILE,
                file_size=file_info.size,
                mime_type=file_info.mime_type,
                file_extension=file_info.extension,
                file_hash=file_info.hash_value,
                file_created_at=file_info.created_at,
                file_modified_at=file_info.modified_at,
                status=FileStatus.ACTIVE,
                last_sync_at=datetime.utcnow()
            )
            session.add(new_file)
            stats["files_added"] += 1
