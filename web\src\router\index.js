import { createRouter, createWebHistory } from 'vue-router'
import { systemInitApi } from '@/api/systemInit'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/system-init',
    name: 'SystemInit',
    component: () => import('@/views/SystemInit.vue'),
    meta: {
      title: '系统初始化',
      requiresAuth: false,
      skipInitCheck: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: {
      title: '用户注册',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '控制台',
      requiresAuth: true
    }
  },
  {
    path: '/file-management',
    name: 'FileManagement',
    component: () => import('@/views/FileManagement.vue'),
    meta: {
      title: '文件管理',
      requiresAuth: true
    }
  },
  {
    path: '/storage-management',
    name: 'StorageManagement',
    component: () => import('@/views/StorageManagement.vue'),
    meta: {
      title: '存储管理',
      requiresAuth: true
    }
  },
  {
    path: '/user-management',
    name: 'UserManagement',
    component: () => import('@/views/UserManagement.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/system-settings',
    name: 'SystemSettings',
    component: () => import('@/views/SystemSettings.vue'),
    meta: {
      title: '系统设置',
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - AI知识库`
  }

  // 跳过初始化检查的页面
  if (to.meta.skipInitCheck) {
    next()
    return
  }

  try {
    // 检查系统是否需要初始化
    const response = await systemInitApi.checkNeedInit()
    const needInit = response.data.need_init

    if (needInit && to.name !== 'SystemInit') {
      // 系统需要初始化，跳转到初始化页面
      next('/system-init')
      return
    }

    if (!needInit && to.name === 'SystemInit') {
      // 系统已初始化，不允许访问初始化页面
      next('/login')
      return
    }
  } catch (error) {
    console.error('检查系统初始化状态失败:', error)
    // 如果检查失败，允许继续访问
  }

  // 检查认证状态
  const token = localStorage.getItem('access_token')
  const isAuthenticated = !!token

  if (to.meta.requiresAuth && !isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    next('/login')
    return
  }

  if ((to.name === 'Login' || to.name === 'Register') && isAuthenticated) {
    // 已登录用户访问登录/注册页，跳转到控制台
    next('/dashboard')
    return
  }

  // 检查管理员权限
  if (to.meta.requiresAdmin) {
    const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}')
    if (!userInfo.is_superuser) {
      // 需要管理员权限但不是管理员
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
