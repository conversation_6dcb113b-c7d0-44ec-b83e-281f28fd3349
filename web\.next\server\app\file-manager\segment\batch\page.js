/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/file-manager/segment/batch/page";
exports.ids = ["app/file-manager/segment/batch/page"];
exports.modules = {

/***/ "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$":
/*!**************************************************************!*\
  !*** ./lib/i18n/locales/ lazy ^\.\/.*\.ts$ namespace object ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.ts": [
		"(ssr)/./lib/i18n/locales/en.ts",
		"_ssr_lib_i18n_locales_en_ts"
	],
	"./ja.ts": [
		"(ssr)/./lib/i18n/locales/ja.ts",
		"_ssr_lib_i18n_locales_ja_ts"
	],
	"./zh-CN.ts": [
		"(ssr)/./lib/i18n/locales/zh-CN.ts",
		"_ssr_lib_i18n_locales_zh-CN_ts"
	],
	"./zh-TW.ts": [
		"(ssr)/./lib/i18n/locales/zh-TW.ts",
		"_ssr_lib_i18n_locales_zh-TW_ts"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(ssr)/./lib/i18n/locales lazy recursive ^\\.\\/.*\\.ts$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1aa5\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'file-manager',\n        {\n        children: [\n        'segment',\n        {\n        children: [\n        'batch',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/file-manager/segment/batch/page.tsx */ \"(rsc)/./app/file-manager/segment/batch/page.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/file-manager/segment/batch/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/file-manager/segment/batch/page\",\n        pathname: \"/file-manager/segment/batch\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cfile-manager%5C%5Csegment%5C%5Cbatch%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cfile-manager%5C%5Csegment%5C%5Cbatch%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/file-manager/segment/batch/page.tsx */ \"(ssr)/./app/file-manager/segment/batch/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJGJTNBJTVDJTVDd29ya3NwYWNlJTVDJTVDeGhjLXJhZyU1QyU1Q3dlYiU1QyU1Q2FwcCU1QyU1Q2ZpbGUtbWFuYWdlciU1QyU1Q3NlZ21lbnQlNUMlNUNiYXRjaCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBNkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvPzU1MmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJGOlxcXFx3b3Jrc3BhY2VcXFxceGhjLXJhZ1xcXFx3ZWJcXFxcYXBwXFxcXGZpbGUtbWFuYWdlclxcXFxzZWdtZW50XFxcXGJhdGNoXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cfile-manager%5C%5Csegment%5C%5Cbatch%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/LanguageContext.tsx */ \"(ssr)/./contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Creact-hot-toast%402.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5Cworkspace%5C%5Cxhc-rag%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/file-manager/segment/batch/page.tsx":
/*!*************************************************!*\
  !*** ./app/file-manager/segment/batch/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/.pnpm/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736/node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Brain,CheckCircle,FileText,FolderOpen,Loader2,Plus,Scissors,Search,Settings,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.300.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst BatchSegmentPage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // 解析URL参数中的文件ID列表\n    const fileIds = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        const filesParam = searchParams.get(\"files\");\n        if (!filesParam) return [];\n        // 分割文件ID并过滤空值\n        const ids = filesParam.split(\",\").filter(Boolean);\n        console.log(\"批量分段页面 - 接收到的URL参数:\", filesParam);\n        console.log(\"批量分段页面 - 解析的文件IDs:\", ids);\n        return ids;\n    }, [\n        searchParams\n    ]);\n    // 基础状态\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 任务状态\n    const [taskStatus, setTaskStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [taskId, setTaskId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [fileProgress, setFileProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 任务配置\n    const [taskName, setTaskName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        method: \"paragraph\",\n        max_length: 500,\n        overlap: 50,\n        preserve_formatting: true,\n        normalize_text: true,\n        extract_keywords: true,\n        remove_stopwords: false,\n        language: \"zh\"\n    });\n    // 文件选择相关状态\n    const [showFileSelector, setShowFileSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableFiles, setAvailableFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingAvailableFiles, setLoadingAvailableFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fileSearchQuery, setFileSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedStorage, setSelectedStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [storageList, setStorageList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fileIds.length > 0) {\n            loadFiles();\n            // 生成默认任务名称\n            const now = new Date();\n            const timestamp = now.toLocaleString(\"zh-CN\");\n            setTaskName(`批量分段任务 - ${timestamp}`);\n        }\n        loadStorageList();\n    }, [\n        fileIds\n    ]);\n    // 加载存储列表\n    const loadStorageList = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/storage-management\");\n            const data = response.data?.data || response.data || [];\n            setStorageList(data);\n            if (data.length > 0) {\n                setSelectedStorage(data[0].id);\n            }\n        } catch (err) {\n            console.error(\"Failed to load storage list:\", err);\n        }\n    };\n    // 加载文件信息\n    const loadFiles = async ()=>{\n        if (fileIds.length === 0) {\n            console.log(\"批量分段页面 - 没有文件ID，跳过加载\");\n            return;\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            console.log(\"批量分段页面 - 开始加载文件信息，文件IDs:\", fileIds);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/file-management/batch-info\", {\n                file_ids: fileIds\n            });\n            console.log(\"批量分段页面 - API响应:\", response.data);\n            const data = response.data?.data || response.data || [];\n            console.log(\"批量分段页面 - 解析的文件数据:\", data);\n            setFiles(data);\n            if (data.length === 0) {\n                setError(\"未找到指定的文件，请检查文件是否存在\");\n            }\n        } catch (err) {\n            console.error(\"批量分段页面 - 加载文件失败:\", err);\n            console.error(\"批量分段页面 - 错误详情:\", err.response?.data);\n            setError(err.response?.data?.detail || \"加载文件信息失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载可选文件列表\n    const loadAvailableFiles = async (storageId, search = \"\")=>{\n        try {\n            setLoadingAvailableFiles(true);\n            const params = new URLSearchParams({\n                storage_id: storageId,\n                page: \"1\",\n                page_size: \"50\"\n            });\n            if (search) {\n                params.append(\"search\", search);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`/api/v1/file-management?${params.toString()}`);\n            const data = response.data?.data || response.data || {};\n            setAvailableFiles(data.files || []);\n        } catch (err) {\n            console.error(\"Failed to load available files:\", err);\n        } finally{\n            setLoadingAvailableFiles(false);\n        }\n    };\n    // 添加文件到选择列表\n    const addFilesToSelection = (selectedFiles)=>{\n        const newFiles = selectedFiles.filter((newFile)=>!files.some((existingFile)=>existingFile.file_id === newFile.file_id));\n        if (newFiles.length > 0) {\n            setFiles((prev)=>[\n                    ...prev,\n                    ...newFiles\n                ]);\n            // 更新URL参数\n            const allFileIds = [\n                ...files,\n                ...newFiles\n            ].map((f)=>f.file_id);\n            const newUrl = `/file-manager/segment/batch?files=${allFileIds.join(\",\")}`;\n            router.replace(newUrl);\n        }\n        setShowFileSelector(false);\n    };\n    // 移除文件\n    const removeFile = (fileId)=>{\n        const newFiles = files.filter((f)=>f.file_id !== fileId);\n        setFiles(newFiles);\n        // 更新URL参数\n        if (newFiles.length > 0) {\n            const newUrl = `/file-manager/segment/batch?files=${newFiles.map((f)=>f.file_id).join(\",\")}`;\n            router.replace(newUrl);\n        } else {\n            router.push(\"/file-manager\");\n        }\n    };\n    // 开始分段任务\n    const startSegmentTask = async ()=>{\n        if (!taskName.trim()) {\n            setError(\"请输入任务名称\");\n            return;\n        }\n        if (files.length === 0) {\n            setError(\"请选择要分段的文件\");\n            return;\n        }\n        try {\n            setTaskStatus(\"running\");\n            setError(null);\n            const taskData = {\n                task_name: taskName,\n                description: \"\",\n                file_ids: files.map((f)=>f.file_id),\n                config: config\n            };\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/document-segment/tasks\", taskData);\n            const data = response.data?.data || response.data;\n            setTaskId(data.task_id);\n            // 初始化文件进度\n            const initialProgress = {};\n            files.forEach((file)=>{\n                initialProgress[file.file_id] = {\n                    status: \"pending\",\n                    progress: 0\n                };\n            });\n            setFileProgress(initialProgress);\n            // 开始轮询任务状态\n            startProgressPolling(data.task_id);\n        } catch (err) {\n            console.error(\"Failed to start segment task:\", err);\n            setError(err.response?.data?.detail || \"启动分段任务失败\");\n            setTaskStatus(\"idle\");\n        }\n    };\n    // 轮询任务进度\n    const startProgressPolling = (taskId)=>{\n        const pollInterval = setInterval(async ()=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`/api/v1/document-segment/tasks/${taskId}/progress`);\n                const data = response.data?.data || response.data;\n                console.log(\"轮询任务进度:\", data); // 调试日志\n                // 更新文件进度\n                if (data.file_progress) {\n                    setFileProgress(data.file_progress);\n                }\n                // 检查任务状态\n                if (data.task) {\n                    const taskStatus = data.task.status;\n                    const taskProgress = data.task.progress || 0;\n                    console.log(\"任务状态:\", taskStatus, \"进度:\", taskProgress); // 调试日志\n                    if (taskStatus === \"completed\") {\n                        clearInterval(pollInterval);\n                        setTaskStatus(\"completed\");\n                        toast.success(`AI分段处理完成！共生成 ${data.task.total_segments || 0} 个分段`);\n                        // 刷新文件列表\n                        await loadFiles();\n                    } else if (taskStatus === \"failed\") {\n                        clearInterval(pollInterval);\n                        setTaskStatus(\"idle\");\n                        const errorMsg = data.task.error_message || \"分段任务失败\";\n                        setError(errorMsg);\n                        toast.error(`分段处理失败: ${errorMsg}`);\n                    } else if (taskStatus === \"processing\") {\n                        // 任务正在处理中，保持运行状态\n                        setTaskStatus(\"running\");\n                    }\n                }\n            } catch (err) {\n                console.error(\"Failed to get task progress:\", err);\n                toast.error(\"获取任务进度失败，请检查网络连接\");\n            }\n        }, 2000);\n        // 10分钟后停止轮询\n        setTimeout(()=>{\n            clearInterval(pollInterval);\n            if (taskStatus === \"running\") {\n                setTaskStatus(\"idle\");\n                toast.warning(\"任务监控超时，请手动刷新页面查看结果\");\n            }\n        }, 600000);\n    };\n    // 返回文件管理\n    const handleBack = ()=>{\n        router.push(\"/file-manager\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"加载文件信息中...\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n            lineNumber: 333,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 计算统计指标\n    const getSegmentStats = ()=>{\n        const totalFiles = files.length;\n        const pendingFiles = Object.values(fileProgress).filter((p)=>p.status === \"pending\").length;\n        const processingFiles = Object.values(fileProgress).filter((p)=>p.status === \"processing\").length;\n        const completedFiles = Object.values(fileProgress).filter((p)=>p.status === \"completed\").length;\n        const failedFiles = Object.values(fileProgress).filter((p)=>p.status === \"error\").length;\n        const totalSegments = Object.values(fileProgress).reduce((sum, p)=>sum + (p.segments || 0), 0);\n        return {\n            totalFiles,\n            pendingFiles,\n            processingFiles,\n            completedFiles,\n            failedFiles,\n            totalSegments\n        };\n    };\n    const stats = getSegmentStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-96 flex-shrink-0 bg-white/60 backdrop-blur-lg border-r border-white/30 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleBack,\n                                            className: \"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                                            children: \"AI智能批量分段\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-3 h-3 text-purple-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        files.length,\n                                                                        \" 个文件待处理\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                taskStatus === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium\",\n                                                                            children: \"处理中...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                taskStatus === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"•\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 font-medium\",\n                                                                            children: \"已完成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-900\",\n                                                children: \"任务配置\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"任务名称 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: taskName,\n                                                    onChange: (e)=>setTaskName(e.target.value),\n                                                    disabled: taskStatus !== \"idle\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                    placeholder: \"请输入任务名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-gray-900\",\n                                                children: \"分段配置\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"分段方式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: config.method,\n                                                        onChange: (e)=>setConfig({\n                                                                ...config,\n                                                                method: e.target.value\n                                                            }),\n                                                        disabled: taskStatus !== \"idle\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"paragraph\",\n                                                                children: \"按段落分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"sentence\",\n                                                                children: \"按句子分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"fixed_length\",\n                                                                children: \"固定长度分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"semantic\",\n                                                                children: \"语义分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"最大长度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: config.max_length,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        max_length: parseInt(e.target.value) || 500\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                min: \"100\",\n                                                                max: \"2000\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                                placeholder: \"500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"重叠长度\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: config.overlap,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        overlap: parseInt(e.target.value) || 50\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                min: \"0\",\n                                                                max: \"500\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                                placeholder: \"50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"语言\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: config.language,\n                                                        onChange: (e)=>setConfig({\n                                                                ...config,\n                                                                language: e.target.value\n                                                            }),\n                                                        disabled: taskStatus !== \"idle\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"zh\",\n                                                                children: \"中文\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"en\",\n                                                                children: \"英文\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"auto\",\n                                                                children: \"自动检测\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.preserve_formatting,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        preserve_formatting: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"保留格式\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.normalize_text,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        normalize_text: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"文本标准化\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.extract_keywords,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        extract_keywords: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"提取关键词\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: config.remove_stopwords,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        remove_stopwords: e.target.checked\n                                                                    }),\n                                                                disabled: taskStatus !== \"idle\",\n                                                                className: \"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"移除停用词\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col bg-white/40 backdrop-blur-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: \"文件分段统计\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"实时监控分段进度和状态\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowFileSelector(true);\n                                                            if (selectedStorage) {\n                                                                loadAvailableFiles(selectedStorage);\n                                                            }\n                                                        },\n                                                        disabled: taskStatus === \"running\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"添加文件\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    taskStatus === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-blue-100/50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-blue-700\",\n                                                                children: \"AI处理中\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    taskStatus === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 px-4 py-2 bg-green-100/50 rounded-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-4 h-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-green-700\",\n                                                                children: \"处理完成\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    taskStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: startSegmentTask,\n                                                        disabled: files.length === 0 || !taskName.trim(),\n                                                        className: \"flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"开始AI分段\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-blue-600 uppercase tracking-wide\",\n                                                                    children: \"文件总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-blue-900\",\n                                                                    children: stats.totalFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-3 border border-purple-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-purple-600 uppercase tracking-wide\",\n                                                                    children: \"分段总数\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-purple-900\",\n                                                                    children: stats.totalSegments\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-purple-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-3 border border-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-gray-600 uppercase tracking-wide\",\n                                                                    children: \"待分段\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-gray-900\",\n                                                                    children: stats.pendingFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-gray-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-3 border border-yellow-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-yellow-600 uppercase tracking-wide\",\n                                                                    children: \"分段中\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-yellow-900\",\n                                                                    children: stats.processingFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-green-600 uppercase tracking-wide\",\n                                                                    children: \"已完成\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-green-900\",\n                                                                    children: stats.completedFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-green-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-red-50 to-rose-50 rounded-lg p-3 border border-red-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-red-600 uppercase tracking-wide\",\n                                                                    children: \"分段失败\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xl font-bold text-red-900\",\n                                                                    children: stats.failedFiles\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-7 h-7 bg-red-500 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-3.5 h-3.5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: files.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-lg font-medium mb-2\",\n                                                children: \"暂无文件\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: '请点击右上角的\"添加文件\"按钮来选择要分段的文件'\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: files.map((file)=>{\n                                        const progress = fileProgress[file.file_id];\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.95\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            className: \"bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-5 h-5 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                            title: file.file_name,\n                                                                            children: file.file_name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                file.file_size_formatted,\n                                                                                \" • \",\n                                                                                file.file_extension.toUpperCase()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        taskStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFile(file.file_id),\n                                                            className: \"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors\",\n                                                            title: \"移除文件\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"存储类型: \",\n                                                                    file.storage_type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: new Date(file.created_at).toLocaleDateString(\"zh-CN\")\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-200/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-700\",\n                                                                    children: [\n                                                                        progress.status === \"pending\" && \"等待处理\",\n                                                                        progress.status === \"processing\" && \"处理中...\",\n                                                                        progress.status === \"completed\" && `已完成 (${progress.segments || 0} 段)`,\n                                                                        progress.status === \"error\" && \"处理失败\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        progress.progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `h-2 rounded-full transition-all duration-300 ${progress.status === \"error\" ? \"bg-red-500\" : progress.status === \"completed\" ? \"bg-green-500\" : \"bg-blue-500\"}`,\n                                                                style: {\n                                                                    width: `${progress.progress}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        progress.error_message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-600 mt-1\",\n                                                            children: progress.error_message\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, file.file_id, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, undefined),\n            showFileSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    className: \"bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: \"选择文件\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowFileSelector(false),\n                                            className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 804,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: fileSearchQuery,\n                                                        onChange: (e)=>setFileSearchQuery(e.target.value),\n                                                        onKeyPress: (e)=>{\n                                                            if (e.key === \"Enter\") {\n                                                                loadAvailableFiles(selectedStorage, fileSearchQuery);\n                                                            }\n                                                        },\n                                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                        placeholder: \"搜索文件名...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedStorage,\n                                            onChange: (e)=>{\n                                                setSelectedStorage(e.target.value);\n                                                loadAvailableFiles(e.target.value, fileSearchQuery);\n                                            },\n                                            className: \"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: storageList.map((storage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: storage.id,\n                                                    children: storage.name\n                                                }, storage.id, false, {\n                                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>loadAvailableFiles(selectedStorage, fileSearchQuery),\n                                            disabled: loadingAvailableFiles,\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                                            children: loadingAvailableFiles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 21\n                                            }, undefined) : \"搜索\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 803,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 max-h-96 overflow-y-auto\",\n                            children: loadingAvailableFiles ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 animate-spin text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: \"加载文件中...\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 17\n                            }, undefined) : availableFiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-300 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"没有找到文件\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileSelector, {\n                                files: availableFiles,\n                                selectedFiles: [],\n                                onSelectionChange: (selected)=>addFilesToSelection(selected),\n                                excludeFileIds: files.map((f)=>f.file_id)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-5 h-5 text-red-500 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 897,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-700 text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 898,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                    lineNumber: 896,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 895,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n        lineNumber: 364,\n        columnNumber: 5\n    }, undefined);\n};\nconst FileSelector = ({ files, selectedFiles, onSelectionChange, excludeFileIds })=>{\n    const [selected, setSelected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedFiles);\n    const toggleFile = (file)=>{\n        const isSelected = selected.some((f)=>f.file_id === file.file_id);\n        if (isSelected) {\n            setSelected(selected.filter((f)=>f.file_id !== file.file_id));\n        } else {\n            setSelected([\n                ...selected,\n                file\n            ]);\n        }\n    };\n    const handleConfirm = ()=>{\n        onSelectionChange(selected);\n    };\n    const availableFiles = files.filter((file)=>!excludeFileIds.includes(file.file_id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4\",\n                children: availableFiles.map((file)=>{\n                    const isSelected = selected.some((f)=>f.file_id === file.file_id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>toggleFile(file),\n                        className: `p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 ${isSelected ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300 hover:bg-gray-50\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-4 h-4 border-2 rounded ${isSelected ? \"bg-blue-600 border-blue-600\" : \"border-gray-300\"}`,\n                                    children: isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Brain_CheckCircle_FileText_FolderOpen_Loader2_Plus_Scissors_Search_Settings_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: file.file_name\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                file.file_size_formatted,\n                                                \" • \",\n                                                file.file_extension.toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                            lineNumber: 953,\n                            columnNumber: 15\n                        }, undefined)\n                    }, file.file_id, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 944,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 939,\n                columnNumber: 7\n            }, undefined),\n            selected.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between pt-4 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"已选择 \",\n                            selected.length,\n                            \" 个文件\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 976,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleConfirm,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                        children: \"确认添加\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                        lineNumber: 977,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n                lineNumber: 975,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\file-manager\\\\segment\\\\batch\\\\page.tsx\",\n        lineNumber: 938,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BatchSegmentPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/file-manager/segment/batch/page.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst LanguageProvider = ({ children })=>{\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n    const [t, setTranslations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 初始化语言设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeLanguage = async ()=>{\n            setIsLoading(true);\n            try {\n                // 预加载所有语言\n                await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.preloadAllLanguages)();\n                // 从localStorage加载语言设置\n                const savedLanguage = localStorage.getItem(\"language\");\n                let targetLanguage;\n                if (savedLanguage && [\n                    \"zh-CN\",\n                    \"zh-TW\",\n                    \"en\",\n                    \"ja\"\n                ].includes(savedLanguage)) {\n                    targetLanguage = savedLanguage;\n                } else {\n                    // 检测浏览器语言\n                    targetLanguage = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.detectBrowserLanguage)();\n                }\n                setLanguageState(targetLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(targetLanguage);\n                setTranslations(translations);\n            } catch (error) {\n                console.error(\"Failed to initialize language:\", error);\n                // 回退到默认语言\n                setLanguageState(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLanguage);\n                setTranslations(translations);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeLanguage();\n    }, []);\n    const setLanguage = async (newLanguage)=>{\n        setIsLoading(true);\n        try {\n            setLanguageState(newLanguage);\n            const translations = await (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getTranslation)(newLanguage);\n            setTranslations(translations);\n            localStorage.setItem(\"language\", newLanguage);\n        } catch (error) {\n            console.error(\"Failed to set language:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage,\n            t,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\nconst useLanguage = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkHealth: () => (/* binding */ checkHealth),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   storageApi: () => (/* binding */ storageApi),\n/* harmony export */   testCors: () => (/* binding */ testCors)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.9.0/node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs\");\n/**\n * API客户端配置\n * 与后端FastAPI接口对接\n */ \n\n// API基础配置\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    },\n    withCredentials: false\n});\n// 添加调试日志\nconsole.log(\"API Client initialized with base URL:\", API_BASE_URL);\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 添加认证token\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    // 添加请求ID用于追踪\n    config.headers[\"X-Request-ID\"] = generateRequestId();\n    // 调试日志\n    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    console.log(`   Base URL: ${config.baseURL}`);\n    console.log(`   Full URL: ${config.baseURL}${config.url}`);\n    console.log(`   Timeout: ${config.timeout}ms`);\n    return config;\n}, (error)=>{\n    console.error(\"❌ Request interceptor error:\", error);\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);\n    return response;\n}, async (error)=>{\n    console.error(`❌ API Error: ${error.code} ${error.config?.method?.toUpperCase()} ${error.config?.url}`);\n    console.error(`   Message: ${error.message}`);\n    console.error(`   Status: ${error.response?.status}`);\n    const originalRequest = error.config;\n    // 处理401未授权错误\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        // 尝试刷新token\n        const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n        if (refreshToken) {\n            try {\n                const response = await refreshAccessToken(refreshToken);\n                const newToken = response.data.access_token;\n                // 更新token\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", newToken, {\n                    expires: 1\n                });\n                // 重试原请求\n                originalRequest.headers.Authorization = `Bearer ${newToken}`;\n                return apiClient(originalRequest);\n            } catch (refreshError) {\n                // 刷新失败，清除token并跳转到登录页\n                clearAuthTokens();\n                window.location.href = \"/login\";\n                return Promise.reject(refreshError);\n            }\n        } else {\n            // 没有刷新token，直接跳转到登录页\n            clearAuthTokens();\n            window.location.href = \"/login\";\n        }\n    }\n    return Promise.reject(error);\n});\n// 生成请求ID\nfunction generateRequestId() {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n}\n// 清除认证token\nfunction clearAuthTokens() {\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n}\n// 刷新访问token\nasync function refreshAccessToken(refreshToken) {\n    return axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${API_BASE_URL}/api/v1/auth/refresh`, {\n        refresh_token: refreshToken\n    });\n}\n// 登录\nconst login = async (credentials)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Attempting login with:\", {\n            username: credentials.username\n        });\n        console.log(\"\\uD83C\\uDF10 API Base URL:\", API_BASE_URL);\n        console.log(\"\\uD83D\\uDCE1 Full login URL:\", `${API_BASE_URL}/api/v1/auth/login`);\n        const response = await apiClient.post(\"/api/v1/auth/login\", credentials);\n        console.log(\"✅ Login response:\", response.data);\n        // 保存token到cookie\n        const { access_token, refresh_token, expires_in } = response.data;\n        // 设置cookie过期时间（转换为天数）\n        const expiresInDays = expires_in / (60 * 60 * 24);\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access_token, {\n            expires: expiresInDays\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", refresh_token, {\n            expires: 7\n        }); // 刷新token保存7天\n        // 同时保存到localStorage作为备份\n        localStorage.setItem(\"token\", access_token);\n        localStorage.setItem(\"refresh_token\", refresh_token);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ Login error details:\", error);\n        console.error(\"   Error code:\", error.code);\n        console.error(\"   Error message:\", error.message);\n        console.error(\"   Error response:\", error.response?.data);\n        console.error(\"   Error status:\", error.response?.status);\n        console.error(\"   Request config:\", error.config);\n        // 处理不同类型的错误响应\n        if (error.code === \"ECONNABORTED\") {\n            throw new Error(`请求超时，请检查网络连接或稍后重试。服务器地址：${API_BASE_URL}`);\n        } else if (error.response?.data?.detail) {\n            throw new Error(error.response.data.detail);\n        } else if (error.response?.data?.error?.message) {\n            throw new Error(error.response.data.error.message);\n        } else if (error.response?.status === 401) {\n            throw new Error(\"用户名或密码错误\");\n        } else if (error.response?.status >= 500) {\n            throw new Error(\"服务器错误，请稍后重试\");\n        } else if (error.code === \"ECONNREFUSED\" || error.message.includes(\"Network Error\")) {\n            throw new Error(`无法连接到服务器，请检查网络连接。服务器地址：${API_BASE_URL}`);\n        } else {\n            throw new Error(error.message || \"Login failed\");\n        }\n    }\n};\n// 登出\nconst logout = async ()=>{\n    try {\n        await apiClient.post(\"/api/v1/auth/logout\");\n    } catch (error) {\n        // 即使后端登出失败，也要清除本地token\n        console.error(\"Logout error:\", error);\n    } finally{\n        clearAuthTokens();\n    }\n};\n// 获取当前用户信息\nconst getCurrentUser = async ()=>{\n    try {\n        const response = await apiClient.get(\"/api/v1/auth/me\");\n        return response.data;\n    } catch (error) {\n        const apiError = error.response?.data;\n        throw new Error(apiError?.error?.message || \"Failed to get user info\");\n    }\n};\n// 检查健康状态\nconst checkHealth = async ()=>{\n    try {\n        const response = await apiClient.get(\"/health\");\n        return response.data;\n    } catch (error) {\n        throw new Error(\"API health check failed\");\n    }\n};\n// CORS测试\nconst testCors = async ()=>{\n    try {\n        console.log(\"\\uD83E\\uDDEA Testing CORS configuration...\");\n        const response = await apiClient.get(\"/api/v1/auth/test-cors\");\n        console.log(\"✅ CORS test successful:\", response.data);\n        return response.data;\n    } catch (error) {\n        console.error(\"❌ CORS test failed:\", error);\n        throw error;\n    }\n};\n// 存储管理API\nconst storageApi = {\n    // 获取存储列表\n    getStorages: async ()=>{\n        const response = await apiClient.get(\"/api/v1/storage/\");\n        return response.data;\n    },\n    // 创建存储\n    createStorage: async (data)=>{\n        const response = await apiClient.post(\"/api/v1/storage/\", data);\n        return response.data;\n    },\n    // 测试存储连接\n    testStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/test`);\n        return response.data;\n    },\n    // 删除存储\n    deleteStorage: async (storageId)=>{\n        const response = await apiClient.delete(`/api/v1/storage/${storageId}`);\n        return response.data;\n    },\n    // 同步存储\n    syncStorage: async (storageId)=>{\n        const response = await apiClient.post(`/api/v1/storage/${storageId}/sync`);\n        return response.data;\n    }\n};\n// 检查token是否有效\nconst isAuthenticated = ()=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    return !!token;\n};\n// 获取token\nconst getAccessToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatDate),\n/* harmony export */   formatNumber: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.formatNumber),\n/* harmony export */   getTranslation: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslation),\n/* harmony export */   getTranslationSync: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.getTranslationSync),\n/* harmony export */   languageConfig: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* reexport safe */ _i18n_index__WEBPACK_IMPORTED_MODULE_0__.preloadAllLanguages)\n/* harmony export */ });\n/* harmony import */ var _i18n_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./i18n/index */ \"(ssr)/./lib/i18n/index.ts\");\n/**\n * 多语言支持 - 兼容性导出文件\n * 重新导出新架构中的所有功能\n */ // 重新导出所有类型和函数\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvaTE4bi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFRCxjQUFjO0FBWVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9saWIvaTE4bi50cz80OWFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5aSa6K+t6KiA5pSv5oyBIC0g5YW85a655oCn5a+85Ye65paH5Lu2XG4gKiDph43mlrDlr7zlh7rmlrDmnrbmnoTkuK3nmoTmiYDmnInlip/og71cbiAqL1xuXG4vLyDph43mlrDlr7zlh7rmiYDmnInnsbvlnovlkozlh73mlbBcbmV4cG9ydCB0eXBlIHsgTGFuZ3VhZ2UsIFRyYW5zbGF0aW9ucyB9IGZyb20gJy4vaTE4bi9pbmRleCc7XG5cbmV4cG9ydCB7XG4gIGRlZmF1bHRMYW5ndWFnZSxcbiAgbGFuZ3VhZ2VDb25maWcsXG4gIGdldFRyYW5zbGF0aW9uLFxuICBnZXRUcmFuc2xhdGlvblN5bmMsXG4gIHByZWxvYWRBbGxMYW5ndWFnZXMsXG4gIGRldGVjdEJyb3dzZXJMYW5ndWFnZSxcbiAgZm9ybWF0TnVtYmVyLFxuICBmb3JtYXREYXRlXG59IGZyb20gJy4vaTE4bi9pbmRleCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdExhbmd1YWdlIiwibGFuZ3VhZ2VDb25maWciLCJnZXRUcmFuc2xhdGlvbiIsImdldFRyYW5zbGF0aW9uU3luYyIsInByZWxvYWRBbGxMYW5ndWFnZXMiLCJkZXRlY3RCcm93c2VyTGFuZ3VhZ2UiLCJmb3JtYXROdW1iZXIiLCJmb3JtYXREYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n/index.ts":
/*!***************************!*\
  !*** ./lib/i18n/index.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   detectBrowserLanguage: () => (/* binding */ detectBrowserLanguage),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   getTranslationSync: () => (/* binding */ getTranslationSync),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   preloadAllLanguages: () => (/* binding */ preloadAllLanguages)\n/* harmony export */ });\n/**\n * 国际化配置主文件\n * 支持动态加载语言文件\n */ const defaultLanguage = \"zh-CN\";\n// 语言配置\nconst languageConfig = {\n    \"zh-CN\": {\n        name: \"中文简体\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        direction: \"ltr\"\n    },\n    \"zh-TW\": {\n        name: \"中文繁體\",\n        flag: \"\\uD83C\\uDDF9\\uD83C\\uDDFC\",\n        direction: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        direction: \"ltr\"\n    },\n    \"ja\": {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        direction: \"ltr\"\n    }\n};\n// 动态导入语言文件\nconst loadTranslations = async (language)=>{\n    try {\n        const module = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${language}.ts`);\n        return module.default;\n    } catch (error) {\n        console.warn(`Failed to load translations for ${language}, falling back to default`);\n        const defaultModule = await __webpack_require__(\"(ssr)/./lib/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.ts$\")(`./${defaultLanguage}.ts`);\n        return defaultModule.default;\n    }\n};\n// 缓存已加载的翻译\nconst translationCache = new Map();\nconst getTranslation = async (language)=>{\n    if (translationCache.has(language)) {\n        return translationCache.get(language);\n    }\n    const translations = await loadTranslations(language);\n    translationCache.set(language, translations);\n    return translations;\n};\n// 同步获取翻译（用于已缓存的情况）\nconst getTranslationSync = (language)=>{\n    return translationCache.get(language) || null;\n};\n// 预加载所有语言\nconst preloadAllLanguages = async ()=>{\n    const languages = [\n        \"zh-CN\",\n        \"zh-TW\",\n        \"en\",\n        \"ja\"\n    ];\n    await Promise.all(languages.map(async (lang)=>{\n        try {\n            await getTranslation(lang);\n        } catch (error) {\n            console.warn(`Failed to preload language ${lang}:`, error);\n        }\n    }));\n};\n// 检测浏览器语言\nconst detectBrowserLanguage = ()=>{\n    if (true) {\n        return defaultLanguage;\n    }\n    const browserLanguage = navigator.language || navigator.languages?.[0];\n    if (browserLanguage?.startsWith(\"zh-CN\") || browserLanguage === \"zh\") {\n        return \"zh-CN\";\n    } else if (browserLanguage?.startsWith(\"zh-TW\") || browserLanguage === \"zh-Hant\") {\n        return \"zh-TW\";\n    } else if (browserLanguage?.startsWith(\"en\")) {\n        return \"en\";\n    } else if (browserLanguage?.startsWith(\"ja\")) {\n        return \"ja\";\n    }\n    return defaultLanguage;\n};\n// 格式化数字（根据语言环境）\nconst formatNumber = (number, language)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    return new Intl.NumberFormat(localeMap[language]).format(number);\n};\n// 格式化日期（根据语言环境）\nconst formatDate = (date, language, options)=>{\n    const localeMap = {\n        \"zh-CN\": \"zh-CN\",\n        \"zh-TW\": \"zh-TW\",\n        \"en\": \"en-US\",\n        \"ja\": \"ja-JP\"\n    };\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(localeMap[language], options || defaultOptions).format(date);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n/index.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0cc9297c17c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZ2xvYmFscy5jc3M/YWJhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBjYzkyOTdjMTdjNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/file-manager/segment/batch/page.tsx":
/*!*************************************************!*\
  !*** ./app/file-manager/segment/batch/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\app\file-manager\segment\batch\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./contexts/LanguageContext.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI知识库 - 智能文档管理平台\",\n    description: \"基于AI技术的智能文档管理和知识库系统\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#4ade80\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\workspace\\\\xhc-rag\\\\web\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./contexts/LanguageContext.tsx":
/*!**************************************!*\
  !*** ./contexts/LanguageContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\workspace\xhc-rag\web\contexts\LanguageContext.tsx#useLanguage`);


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1rbm93bGVkZ2UtYmFzZS13ZWIvLi9hcHAvZmF2aWNvbi5pY28/ZjUyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/framer-motion@10.18.0_react_39718d184a546e7029955c4644d61736","vendor-chunks/mime-db@1.52.0","vendor-chunks/axios@1.9.0","vendor-chunks/lucide-react@0.300.0_react@18.3.1","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/debug@4.4.1","vendor-chunks/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d","vendor-chunks/form-data@4.0.3","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/asynckit@0.4.0","vendor-chunks/combined-stream@1.0.8","vendor-chunks/mime-types@2.1.35","vendor-chunks/js-cookie@3.0.5","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-symbols@1.1.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/function-bind@1.1.2","vendor-chunks/es-set-tostringtag@2.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/es-errors@1.3.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/has-tostringtag@1.0.2","vendor-chunks/es-object-atoms@1.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&page=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&appPaths=%2Ffile-manager%2Fsegment%2Fbatch%2Fpage&pagePath=private-next-app-dir%2Ffile-manager%2Fsegment%2Fbatch%2Fpage.tsx&appDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5Cworkspace%5Cxhc-rag%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();