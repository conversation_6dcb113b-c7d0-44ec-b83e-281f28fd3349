/**
 * 系统初始化相关API
 */

import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 响应拦截器
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

export interface SystemInitStatus {
  is_initialized: boolean;
  steps: {
    [key: string]: {
      completed: boolean;
      completed_at: string | null;
      description: string;
    };
  };
  next_step: string | null;
}

export interface AdminInitData {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface StorageConfigData {
  type: string;
  basePath: string;
}

export const systemInitApi = {
  /**
   * 检查系统是否需要初始化
   */
  async checkNeedInit(): Promise<{ need_init: boolean; is_initialized: boolean }> {
    try {
      const response = await api.get('/api/v1/system-init/check');
      return response.data.data;
    } catch (error) {
      console.error('检查系统初始化状态失败:', error);
      // 如果API调用失败，假设需要初始化
      return { need_init: true, is_initialized: false };
    }
  },

  /**
   * 获取系统初始化状态
   */
  async getStatus(): Promise<SystemInitStatus> {
    const response = await api.get('/api/v1/system-init/status');
    return response.data.data;
  },

  /**
   * 初始化管理员账户
   */
  async initializeAdmin(data: AdminInitData): Promise<{ admin_id: number }> {
    const response = await api.post('/api/v1/system-init/admin', data);
    return response.data.data;
  },

  /**
   * 初始化字典数据
   */
  async initializeDictionaries(): Promise<void> {
    await api.post('/api/v1/system-init/dictionaries');
  },

  /**
   * 初始化存储配置
   */
  async initializeStorage(config?: StorageConfigData): Promise<void> {
    await api.post('/api/v1/system-init/storage', config);
  },

  /**
   * 初始化系统配置
   */
  async initializeSystemConfig(): Promise<void> {
    await api.post('/api/v1/system-init/system-config');
  },

  /**
   * 自动完成所有初始化步骤
   */
  async autoInitialize(): Promise<void> {
    await api.post('/api/v1/system-init/auto-init');
  },
};
