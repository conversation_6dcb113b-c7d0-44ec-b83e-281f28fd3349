<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏iframe演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-container {
            text-align: center;
            color: white;
        }

        .demo-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .demo-subtitle {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .demo-button {
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* 全屏弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f8fafc;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .modal-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            padding: 12px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 68px;
        }

        .modal-header-left {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
        }

        .modal-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 20px;
            color: white;
            flex-shrink: 0;
        }

        .modal-info {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        .modal-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2px;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-meta {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.1;
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 2px;
        }

        .modal-company {
            font-weight: 600;
            color: #6366f1;
        }

        .modal-creator {
            color: #8b5cf6;
            font-weight: 500;
        }

        .modal-description-short {
            font-size: 0.7rem;
            color: #9ca3af;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .modal-iframe-container {
            position: absolute;
            top: 68px;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: calc(100vh - 68px);
            overflow: hidden;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            background: white;
            display: block;
        }

        .close {
            width: 32px;
            height: 32px;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #ef4444;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(239, 68, 68, 0.2);
            flex-shrink: 0;
            margin-left: 12px;
        }

        .close:hover {
            background: rgba(239, 68, 68, 0.2);
            color: #dc2626;
            transform: scale(1.05);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-button {
                width: 200px;
            }
            
            .modal-header {
                padding: 10px 15px;
                height: 58px;
            }
            
            .modal-icon {
                width: 36px;
                height: 36px;
                font-size: 18px;
                margin-right: 10px;
            }
            
            .modal-title {
                font-size: 1rem;
            }
            
            .modal-meta {
                font-size: 0.7rem;
                gap: 4px;
            }
            
            .modal-description-short {
                font-size: 0.65rem;
            }
            
            .modal-iframe-container {
                top: 58px;
                height: calc(100vh - 58px);
            }
            
            .close {
                width: 28px;
                height: 28px;
                font-size: 14px;
                margin-left: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🚀 全屏iframe演示</h1>
        <p class="demo-subtitle">点击下方按钮体验无边框全屏iframe效果</p>
        
        <div class="demo-buttons">
            <button class="demo-button" onclick="openModal('baidu')">
                🔍 百度搜索
            </button>
            <button class="demo-button" onclick="openModal('github')">
                💻 GitHub
            </button>
            <button class="demo-button" onclick="openModal('youtube')">
                🎬 YouTube
            </button>
        </div>
    </div>

    <!-- 全屏弹窗 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-header-left">
                    <div class="modal-icon" id="modalIcon"></div>
                    <div class="modal-info">
                        <div class="modal-title" id="modalTitle"></div>
                        <div class="modal-meta">
                            <span class="modal-company" id="modalCompany"></span>
                            <span>•</span>
                            <span class="modal-creator" id="modalCreator"></span>
                        </div>
                        <div class="modal-description-short" id="modalDescriptionShort"></div>
                    </div>
                </div>
                <button class="close" onclick="closeModal()" title="关闭">&times;</button>
            </div>
            <div class="modal-iframe-container">
                <iframe id="modalIframe" class="modal-iframe" src="" frameborder="0" border="0" allowfullscreen seamless></iframe>
            </div>
        </div>
    </div>

    <script>
        const sites = {
            baidu: {
                icon: '🔍',
                title: '百度搜索',
                company: '百度',
                creator: '李彦宏',
                description: '中国最大的搜索引擎，提供网页、图片、视频等多种搜索服务',
                url: 'https://www.baidu.com'
            },
            github: {
                icon: '💻',
                title: 'GitHub',
                company: 'Microsoft',
                creator: 'Tom Preston-Werner',
                description: '全球最大的代码托管平台，开发者协作和版本控制的首选工具',
                url: 'https://github.com'
            },
            youtube: {
                icon: '🎬',
                title: 'YouTube',
                company: 'Google',
                creator: 'Chad Hurley',
                description: '全球最大的视频分享平台，用户可以上传、观看和分享视频内容',
                url: 'https://www.youtube.com'
            }
        };

        function openModal(siteKey) {
            const site = sites[siteKey];
            const modal = document.getElementById('modal');
            const modalIcon = document.getElementById('modalIcon');
            const modalTitle = document.getElementById('modalTitle');
            const modalCompany = document.getElementById('modalCompany');
            const modalCreator = document.getElementById('modalCreator');
            const modalDescriptionShort = document.getElementById('modalDescriptionShort');
            const modalIframe = document.getElementById('modalIframe');
            
            // 设置头部信息
            modalIcon.textContent = site.icon;
            modalTitle.textContent = site.title;
            modalTitle.title = site.title;
            modalCompany.textContent = site.company;
            modalCreator.textContent = site.creator;
            modalDescriptionShort.textContent = site.description;
            
            // 加载第三方页面
            modalIframe.src = site.url;
            
            // 显示弹窗
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            const modalIframe = document.getElementById('modalIframe');
            
            // 清空iframe内容以停止加载
            modalIframe.src = 'about:blank';
            
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
