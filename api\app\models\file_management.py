"""
文件管理数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Boolean, BigInteger, Text, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel, get_text_type, get_json_type


class StorageType(str, Enum):
    """存储类型枚举"""
    LOCAL = "LOCAL"
    MINIO = "MINIO"
    FTP = "FTP"
    SFTP = "SFTP"


class FileType(str, Enum):
    """文件类型枚举"""
    FILE = "file"
    DIRECTORY = "directory"


class FileStatus(str, Enum):
    """文件状态枚举"""
    ACTIVE = "active"
    DELETED = "deleted"
    SYNCING = "syncing"
    ERROR = "error"


class StorageConfig(BaseModel):
    """存储配置表"""
    __tablename__ = "storage_configs"
    
    name = Column(String(100), unique=True, nullable=False, comment="存储配置名称")
    storage_type = Column(SQLEnum(StorageType), nullable=False, comment="存储类型")
    is_default = Column(Boolean, default=False, comment="是否为默认存储")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 存储配置参数 (JSON格式)
    config = Column(get_json_type(), comment="存储配置参数")
    
    # 统计信息
    total_files = Column(Integer, default=0, comment="总文件数")
    total_size = Column(BigInteger, default=0, comment="总大小(字节)")
    last_sync_at = Column(DateTime(timezone=True), comment="最后同步时间")

    # 关系
    stats = relationship("StorageStats", back_populates="storage", lazy="dynamic")
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典，排除敏感配置信息"""
        exclude_fields = exclude_fields or set()
        exclude_fields.add('config')  # 默认排除配置信息
        
        result = super().to_dict(exclude_fields)
        
        # 只返回非敏感的配置信息
        if self.config:
            safe_config = {}
            if self.storage_type == StorageType.LOCAL:
                safe_config = {
                    'base_path': self.config.get('base_path', ''),
                    'max_file_size': self.config.get('max_file_size', 0)
                }
            elif self.storage_type == StorageType.MINIO:
                safe_config = {
                    'endpoint': self.config.get('endpoint', ''),
                    'bucket_name': self.config.get('bucket_name', ''),
                    'secure': self.config.get('secure', True)
                }
            elif self.storage_type in [StorageType.FTP, StorageType.SFTP]:
                safe_config = {
                    'host': self.config.get('host', ''),
                    'port': self.config.get('port', 21),
                    'base_path': self.config.get('base_path', '/')
                }
            
            result['config'] = safe_config
        
        return result


class FileRecord(BaseModel):
    """文件记录表"""
    __tablename__ = "file_records"
    
    # 基本信息
    file_id = Column(String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()), comment="文件唯一ID")
    storage_id = Column(Integer, nullable=True, comment="存储配置ID（不使用外键约束）")
    
    # 文件路径信息
    file_path = Column(get_text_type(1000), nullable=False, comment="文件完整路径")
    file_name = Column(String(255), nullable=False, comment="文件名")
    parent_path = Column(get_text_type(1000), comment="父目录路径")
    
    # 文件属性
    file_type = Column(SQLEnum(FileType), nullable=False, comment="文件类型")
    file_size = Column(BigInteger, default=0, comment="文件大小(字节)")
    mime_type = Column(String(100), comment="MIME类型")
    file_extension = Column(String(20), comment="文件扩展名")
    
    # 文件哈希值(用于检测文件变化)
    file_hash = Column(String(64), comment="文件MD5哈希值")
    
    # 时间信息
    file_created_at = Column(DateTime(timezone=True), comment="文件创建时间")
    file_modified_at = Column(DateTime(timezone=True), comment="文件修改时间")
    
    # 状态信息
    status = Column(SQLEnum(FileStatus), default=FileStatus.ACTIVE, comment="文件状态")
    
    # 元数据信息
    file_metadata = Column(get_json_type(), comment="文件元数据")
    
    # 缩略图信息
    thumbnail_path = Column(String(500), comment="缩略图路径")
    has_thumbnail = Column(Boolean, default=False, comment="是否有缩略图")
    
    # 同步信息
    last_sync_at = Column(DateTime(timezone=True), server_default=func.now(), comment="最后同步时间")
    sync_version = Column(Integer, default=1, comment="同步版本号")
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        
        # 添加计算字段
        result['file_size_formatted'] = self.format_file_size()
        result['is_directory'] = self.file_type == FileType.DIRECTORY
        result['is_image'] = self.is_image_file()
        result['is_video'] = self.is_video_file()
        result['is_document'] = self.is_document_file()
        
        return result
    
    def format_file_size(self) -> str:
        """格式化文件大小"""
        if self.file_type == FileType.DIRECTORY:
            return "-"
        
        if not self.file_size:
            return "0 B"
        
        size = self.file_size
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{size} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"
    
    def is_image_file(self) -> bool:
        """判断是否为图片文件"""
        if self.file_type == FileType.DIRECTORY:
            return False
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico'}
        return self.file_extension and self.file_extension.lower() in image_extensions
    
    def is_video_file(self) -> bool:
        """判断是否为视频文件"""
        if self.file_type == FileType.DIRECTORY:
            return False
        
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
        return self.file_extension and self.file_extension.lower() in video_extensions
    
    def is_document_file(self) -> bool:
        """判断是否为文档文件"""
        if self.file_type == FileType.DIRECTORY:
            return False
        
        doc_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf', '.odt'}
        return self.file_extension and self.file_extension.lower() in doc_extensions


class SyncLog(BaseModel):
    """同步日志表"""
    __tablename__ = "sync_logs"
    
    storage_id = Column(Integer, nullable=True, comment="存储配置ID（不使用外键约束）")
    sync_type = Column(String(20), nullable=False, comment="同步类型: full/incremental")
    
    # 同步统计
    files_scanned = Column(Integer, default=0, comment="扫描文件数")
    files_added = Column(Integer, default=0, comment="新增文件数")
    files_updated = Column(Integer, default=0, comment="更新文件数")
    files_deleted = Column(Integer, default=0, comment="删除文件数")
    
    # 同步状态
    status = Column(String(20), default="running", comment="同步状态: running/completed/failed")
    error_message = Column(Text, comment="错误信息")
    
    # 时间信息
    started_at = Column(DateTime(timezone=True), server_default=func.now(), comment="开始时间")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
    duration_seconds = Column(Integer, comment="耗时(秒)")
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """转换为字典"""
        result = super().to_dict(exclude_fields)
        
        # 添加计算字段
        if self.started_at and self.completed_at:
            duration = (self.completed_at - self.started_at).total_seconds()
            result['duration_formatted'] = self.format_duration(duration)
        
        return result
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化持续时间"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"
