'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  Language,
  Translations,
  getTranslation,
  getTranslationSync,
  defaultLanguage,
  detectBrowserLanguage,
  preloadAllLanguages
} from '@/lib/i18n';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: Translations | null;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>(defaultLanguage);
  const [t, setTranslations] = useState<Translations | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化语言设置
  useEffect(() => {
    const initializeLanguage = async () => {
      setIsLoading(true);

      try {
        // 预加载所有语言
        await preloadAllLanguages();

        // 从localStorage加载语言设置
        const savedLanguage = localStorage.getItem('language') as Language;
        let targetLanguage: Language;

        if (savedLanguage && ['zh-CN', 'zh-TW', 'en', 'ja'].includes(savedLanguage)) {
          targetLanguage = savedLanguage;
        } else {
          // 检测浏览器语言
          targetLanguage = detectBrowserLanguage();
        }

        setLanguageState(targetLanguage);
        const translations = await getTranslation(targetLanguage);
        setTranslations(translations);

      } catch (error) {
        console.error('Failed to initialize language:', error);
        // 回退到默认语言
        setLanguageState(defaultLanguage);
        const translations = await getTranslation(defaultLanguage);
        setTranslations(translations);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, []);

  const setLanguage = async (newLanguage: Language) => {
    setIsLoading(true);

    try {
      setLanguageState(newLanguage);
      const translations = await getTranslation(newLanguage);
      setTranslations(translations);
      localStorage.setItem('language', newLanguage);
    } catch (error) {
      console.error('Failed to set language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isLoading }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
