@echo off
chcp 65001 >nul
title AI知识库 - Celery服务管理

echo ========================================
echo    AI知识库 Celery服务启动器
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Python环境，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

REM 检查是否在正确的目录
if not exist "app" (
    echo [错误] 请在API项目根目录下运行此脚本
    echo 当前目录: %cd%
    pause
    exit /b 1
)

REM 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo [信息] 激活虚拟环境...
    call .venv\Scripts\activate.bat
) else (
    echo [警告] 未找到虚拟环境，使用系统Python
)

REM 检查Celery是否安装
python -c "import celery" >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Celery未安装，正在尝试安装...
    pip install celery redis kombu flower msgpack
    if %errorlevel% neq 0 (
        echo [错误] Celery安装失败
        pause
        exit /b 1
    )
)

REM 检查Redis连接
echo [检查] Redis连接状态...
python -c "
import redis, os
try:
    r = redis.Redis(host=os.getenv('REDIS_HOST', '**************'), port=int(os.getenv('REDIS_PORT', '6379')), db=int(os.getenv('REDIS_DB', '10')))
    r.ping()
    print('[成功] Redis连接正常')
except Exception as e:
    print(f'[失败] Redis连接失败: {e}')
    exit(1)
"
if %errorlevel% neq 0 (
    echo.
    echo [提示] 请检查Redis服务是否启动
    echo        默认配置: **************:6379/10
    pause
    exit /b 1
)

echo.
echo [启动] 正在启动Celery服务...
echo.

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "temp" mkdir temp

REM 设置环境变量
set PYTHONPATH=%PYTHONPATH%;%cd%

REM 启动服务
echo [1/3] 启动Celery Worker...
start "Celery-Worker" cmd /k "title Celery Worker && celery -A app.core.celery_config:celery_app worker --loglevel=info --concurrency=2 --queues=default,upload_queue,file_queue"

timeout /t 2 /nobreak >nul

echo [2/3] 启动Celery Beat...
start "Celery-Beat" cmd /k "title Celery Beat && celery -A app.core.celery_config:celery_app beat --loglevel=info"

timeout /t 2 /nobreak >nul

echo [3/3] 启动Flower监控...
start "Celery-Flower" cmd /k "title Celery Flower && celery -A app.core.celery_config:celery_app flower --port=5555 --basic_auth=admin:password"

echo.
echo ========================================
echo           启动完成！
echo ========================================
echo.
echo 服务状态:
echo   ✓ Celery Worker  - 任务处理器
echo   ✓ Celery Beat    - 定时任务调度
echo   ✓ Flower Monitor - Web监控界面
echo.
echo 访问地址:
echo   Flower监控: http://localhost:5555
echo   用户名/密码: admin/password
echo.
echo 管理命令:
echo   停止服务: stop_celery.bat
echo   查看日志: 在对应的窗口中查看
echo.
echo 注意: 请保持此窗口打开以维持服务运行
echo       关闭窗口不会停止后台服务
echo.
pause
