#!/usr/bin/env python3
"""
直接创建文档分段相关数据库表
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from app.core.config import get_settings
from loguru import logger


async def create_segment_tables_direct():
    """直接创建文档分段相关表"""
    try:
        logger.info("开始创建文档分段数据库表...")
        
        # 获取设置
        settings = get_settings()
        database_url = settings.database_url
        
        # 创建异步引擎
        engine = create_async_engine(database_url, echo=True)
        
        # 创建表的SQL语句
        create_tables_sql = """
        -- 创建文档分段任务表
        CREATE TABLE IF NOT EXISTS document_segment_tasks (
            id SERIAL PRIMARY KEY,
            task_id VARCHAR(36) UNIQUE NOT NULL,
            task_name VARCHAR(200) NOT NULL,
            description TEXT,
            file_ids JSONB NOT NULL,
            total_files INTEGER DEFAULT 0,
            processed_files INTEGER DEFAULT 0,
            segment_method VARCHAR(50) DEFAULT 'paragraph',
            max_length INTEGER DEFAULT 500,
            overlap INTEGER DEFAULT 50,
            preserve_formatting BOOLEAN DEFAULT true,
            enable_vectorization BOOLEAN DEFAULT true,
            embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
            vector_dimension INTEGER DEFAULT 1536,
            chunk_size INTEGER DEFAULT 1000,
            language VARCHAR(10) DEFAULT 'zh',
            remove_stopwords BOOLEAN DEFAULT false,
            normalize_text BOOLEAN DEFAULT true,
            extract_keywords BOOLEAN DEFAULT true,
            status VARCHAR(20) DEFAULT 'pending',
            progress FLOAT DEFAULT 0.0,
            error_message TEXT,
            total_segments INTEGER DEFAULT 0,
            total_vectors INTEGER DEFAULT 0,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 创建文档分段表
        CREATE TABLE IF NOT EXISTS document_segments (
            id SERIAL PRIMARY KEY,
            segment_id VARCHAR(36) UNIQUE NOT NULL,
            task_id VARCHAR(36) NOT NULL,
            file_id VARCHAR(500) NOT NULL,
            file_name VARCHAR(500),
            segment_index INTEGER NOT NULL,
            content TEXT NOT NULL,
            word_count INTEGER DEFAULT 0,
            char_count INTEGER DEFAULT 0,
            vectorize_status VARCHAR(20) DEFAULT 'pending',
            embedding_vector JSONB,
            keywords JSONB,
            metadata JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (task_id) REFERENCES document_segment_tasks(task_id) ON DELETE CASCADE
        );

        -- 创建分段模板表
        CREATE TABLE IF NOT EXISTS segment_templates (
            id SERIAL PRIMARY KEY,
            template_name VARCHAR(200) NOT NULL,
            description TEXT,
            segment_method VARCHAR(50) DEFAULT 'paragraph',
            max_length INTEGER DEFAULT 500,
            overlap INTEGER DEFAULT 50,
            preserve_formatting BOOLEAN DEFAULT true,
            enable_vectorization BOOLEAN DEFAULT true,
            embedding_model VARCHAR(100) DEFAULT 'text-embedding-ada-002',
            vector_dimension INTEGER DEFAULT 1536,
            chunk_size INTEGER DEFAULT 1000,
            language VARCHAR(10) DEFAULT 'zh',
            remove_stopwords BOOLEAN DEFAULT false,
            normalize_text BOOLEAN DEFAULT true,
            extract_keywords BOOLEAN DEFAULT true,
            is_default BOOLEAN DEFAULT false,
            is_system BOOLEAN DEFAULT false,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 创建向量索引表
        CREATE TABLE IF NOT EXISTS vector_indexes (
            id SERIAL PRIMARY KEY,
            index_name VARCHAR(200) NOT NULL,
            description TEXT,
            embedding_model VARCHAR(100) NOT NULL,
            vector_dimension INTEGER NOT NULL,
            total_vectors INTEGER DEFAULT 0,
            index_config JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_task_id ON document_segment_tasks(task_id);
        CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_status ON document_segment_tasks(status);
        CREATE INDEX IF NOT EXISTS idx_document_segments_task_id ON document_segments(task_id);
        CREATE INDEX IF NOT EXISTS idx_document_segments_file_id ON document_segments(file_id);
        CREATE INDEX IF NOT EXISTS idx_segment_templates_is_default ON segment_templates(is_default);
        """
        
        # 执行创建表的SQL
        async with engine.begin() as conn:
            logger.info("执行创建表SQL...")
            await conn.execute(text(create_tables_sql))
            logger.info("数据库表创建成功")
        
        # 插入默认模板
        await insert_default_templates(engine)
        
        # 关闭引擎
        await engine.dispose()
        
        logger.info("✅ 文档分段数据库表创建完成")
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        raise


async def insert_default_templates(engine):
    """插入默认分段模板"""
    try:
        logger.info("插入默认分段模板...")
        
        insert_templates_sql = """
        INSERT INTO segment_templates (
            template_name, description, segment_method, max_length, overlap,
            preserve_formatting, enable_vectorization, embedding_model, vector_dimension,
            chunk_size, language, remove_stopwords, normalize_text, extract_keywords,
            is_default, is_system
        ) VALUES 
        (
            '通用文档分段', '适用于大多数文档的通用分段配置', 'paragraph', 500, 50,
            true, true, 'text-embedding-ada-002', 1536,
            1000, 'zh', false, true, true,
            true, true
        ),
        (
            '长文档分段', '适用于长文档的分段配置，较大的分段长度', 'paragraph', 1000, 100,
            true, true, 'text-embedding-ada-002', 1536,
            1500, 'zh', false, true, true,
            false, true
        ),
        (
            '精细分段', '适用于需要精细分段的文档，较小的分段长度', 'sentence', 200, 20,
            true, true, 'text-embedding-ada-002', 1536,
            500, 'zh', true, true, true,
            false, true
        )
        ON CONFLICT (template_name) DO NOTHING;
        """
        
        async with engine.begin() as conn:
            await conn.execute(text(insert_templates_sql))
            logger.info("默认模板插入成功")
            
    except Exception as e:
        logger.error(f"插入默认模板失败: {e}")
        # 不抛出异常，因为模板插入失败不应该影响表创建


async def verify_tables(engine):
    """验证表是否创建成功"""
    try:
        logger.info("验证数据库表...")
        
        verify_sql = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('document_segment_tasks', 'document_segments', 'segment_templates', 'vector_indexes')
        ORDER BY table_name;
        """
        
        async with engine.begin() as conn:
            result = await conn.execute(text(verify_sql))
            tables = [row[0] for row in result.fetchall()]
            
            expected_tables = ['document_segment_tasks', 'document_segments', 'segment_templates', 'vector_indexes']
            
            for table in expected_tables:
                if table in tables:
                    logger.info(f"✅ 表 {table} 创建成功")
                else:
                    logger.error(f"❌ 表 {table} 创建失败")
            
            # 检查模板数量
            template_count_sql = "SELECT COUNT(*) FROM segment_templates;"
            result = await conn.execute(text(template_count_sql))
            count = result.scalar()
            logger.info(f"✅ 分段模板表包含 {count} 个模板")
            
    except Exception as e:
        logger.error(f"验证表失败: {e}")


async def main():
    """主函数"""
    try:
        logger.info("🚀 开始创建文档分段数据库表")
        
        # 创建表
        await create_segment_tables_direct()
        
        logger.info("🎉 文档分段数据库表创建完成！")
        
    except Exception as e:
        logger.error(f"❌ 操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
