"""
中间件配置模块
设置各种中间件以增强应用功能
"""

import time
import uuid
from typing import Callable
from fastapi import FastAP<PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.sessions import SessionMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from app.core.config import Settings


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始
        start_time = time.time()
        
        logger.info(
            f"Request started: {request.method} {request.url}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "client_ip": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent"),
            }
        )
        
        # 处理请求
        try:
            response = await call_next(request)
        except Exception as exc:
            # 记录异常
            process_time = time.time() - start_time
            logger.error(
                f"Request failed: {request.method} {request.url}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "url": str(request.url),
                    "process_time": process_time,
                    "error": str(exc),
                }
            )
            raise
        
        # 记录请求完成
        process_time = time.time() - start_time
        logger.info(
            f"Request completed: {request.method} {request.url}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "status_code": response.status_code,
                "process_time": process_time,
            }
        )
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data:; "
            "connect-src 'self';"
        )
        
        return response


def setup_middleware(app: FastAPI, settings: Settings) -> None:
    """
    设置应用中间件

    Args:
        app: FastAPI应用实例
        settings: 应用配置
    """

    # CORS中间件已在app_factory中添加，这里不再重复添加
    
    # 会话中间件
    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.SECRET_KEY,
        max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        same_site="lax",
        https_only=settings.is_production,
    )
    
    # 安全头中间件
    if settings.is_production:
        app.add_middleware(SecurityHeadersMiddleware)
        
        # 信任主机中间件（生产环境）
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1"],
        )
    
    # 请求日志中间件
    if settings.LOG_LEVEL in ["DEBUG", "INFO"]:
        app.add_middleware(RequestLoggingMiddleware)
    
    logger.info("Middleware setup completed")
