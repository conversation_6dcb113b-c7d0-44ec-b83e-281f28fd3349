/**
 * API客户端配置
 * 与后端FastAPI接口对接
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

// API基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 增加到30秒
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: false, // 明确设置跨域凭证
});

// 添加调试日志
console.log('API Client initialized with base URL:', API_BASE_URL);

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = Cookies.get('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId();

    // 调试日志
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    console.log(`   Base URL: ${config.baseURL}`);
    console.log(`   Full URL: ${config.baseURL}${config.url}`);
    console.log(`   Timeout: ${config.timeout}ms`);

    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
    return response;
  },
  async (error) => {
    console.error(`❌ API Error: ${error.code} ${error.config?.method?.toUpperCase()} ${error.config?.url}`);
    console.error(`   Message: ${error.message}`);
    console.error(`   Status: ${error.response?.status}`);

    const originalRequest = error.config;

    // 处理401未授权错误
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // 尝试刷新token
      const refreshToken = Cookies.get('refresh_token');
      if (refreshToken) {
        try {
          const response = await refreshAccessToken(refreshToken);
          const newToken = response.data.access_token;

          // 更新token
          Cookies.set('access_token', newToken, { expires: 1 });

          // 重试原请求
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return apiClient(originalRequest);
        } catch (refreshError) {
          // 刷新失败，清除token并跳转到登录页
          clearAuthTokens();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        // 没有刷新token，直接跳转到登录页
        clearAuthTokens();
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 清除认证token
function clearAuthTokens(): void {
  Cookies.remove('access_token');
  Cookies.remove('refresh_token');
}

// 刷新访问token
async function refreshAccessToken(refreshToken: string) {
  return axios.post(`${API_BASE_URL}/api/v1/auth/refresh`, {
    refresh_token: refreshToken,
  });
}

// 登录接口
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_superuser: boolean;
  avatar_url?: string;
  bio?: string;
  last_login_at?: string;
  login_count: number;
  created_at: string;
  updated_at: string;
}

export interface ApiError {
  error: {
    code: string;
    message: string;
    details?: any;
  };
  path: string;
  method: string;
}

// 登录
export const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
  try {
    console.log('🔐 Attempting login with:', { username: credentials.username });
    console.log('🌐 API Base URL:', API_BASE_URL);
    console.log('📡 Full login URL:', `${API_BASE_URL}/api/v1/auth/login`);

    const response = await apiClient.post<LoginResponse>('/api/v1/auth/login', credentials);

    console.log('✅ Login response:', response.data);

    // 保存token到cookie
    const { access_token, refresh_token, expires_in } = response.data;

    // 设置cookie过期时间（转换为天数）
    const expiresInDays = expires_in / (60 * 60 * 24);

    Cookies.set('access_token', access_token, { expires: expiresInDays });
    Cookies.set('refresh_token', refresh_token, { expires: 7 }); // 刷新token保存7天

    // 同时保存到localStorage作为备份
    localStorage.setItem('token', access_token);
    localStorage.setItem('refresh_token', refresh_token);

    return response.data;
  } catch (error: any) {
    console.error('❌ Login error details:', error);
    console.error('   Error code:', error.code);
    console.error('   Error message:', error.message);
    console.error('   Error response:', error.response?.data);
    console.error('   Error status:', error.response?.status);
    console.error('   Request config:', error.config);

    // 处理不同类型的错误响应
    if (error.code === 'ECONNABORTED') {
      throw new Error(`请求超时，请检查网络连接或稍后重试。服务器地址：${API_BASE_URL}`);
    } else if (error.response?.data?.detail) {
      throw new Error(error.response.data.detail);
    } else if (error.response?.data?.error?.message) {
      throw new Error(error.response.data.error.message);
    } else if (error.response?.status === 401) {
      throw new Error('用户名或密码错误');
    } else if (error.response?.status >= 500) {
      throw new Error('服务器错误，请稍后重试');
    } else if (error.code === 'ECONNREFUSED' || error.message.includes('Network Error')) {
      throw new Error(`无法连接到服务器，请检查网络连接。服务器地址：${API_BASE_URL}`);
    } else {
      throw new Error(error.message || 'Login failed');
    }
  }
};

// 登出
export const logout = async (): Promise<void> => {
  try {
    await apiClient.post('/api/v1/auth/logout');
  } catch (error) {
    // 即使后端登出失败，也要清除本地token
    console.error('Logout error:', error);
  } finally {
    clearAuthTokens();
  }
};

// 获取当前用户信息
export const getCurrentUser = async (): Promise<User> => {
  try {
    const response = await apiClient.get<User>('/api/v1/auth/me');
    return response.data;
  } catch (error: any) {
    const apiError = error.response?.data as ApiError;
    throw new Error(apiError?.error?.message || 'Failed to get user info');
  }
};

// 检查健康状态
export const checkHealth = async () => {
  try {
    const response = await apiClient.get('/health');
    return response.data;
  } catch (error) {
    throw new Error('API health check failed');
  }
};

// CORS测试
export const testCors = async (): Promise<any> => {
  try {
    console.log('🧪 Testing CORS configuration...');
    const response = await apiClient.get('/api/v1/auth/test-cors');
    console.log('✅ CORS test successful:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ CORS test failed:', error);
    throw error;
  }
};

// 存储管理API
export const storageApi = {
  // 获取存储列表
  getStorages: async () => {
    const response = await apiClient.get('/api/v1/storage/');
    return response.data;
  },

  // 创建存储
  createStorage: async (data: any) => {
    const response = await apiClient.post('/api/v1/storage/', data);
    return response.data;
  },

  // 测试存储连接
  testStorage: async (storageId: number) => {
    const response = await apiClient.post(`/api/v1/storage/${storageId}/test`);
    return response.data;
  },

  // 删除存储
  deleteStorage: async (storageId: number) => {
    const response = await apiClient.delete(`/api/v1/storage/${storageId}`);
    return response.data;
  },

  // 同步存储
  syncStorage: async (storageId: number) => {
    const response = await apiClient.post(`/api/v1/storage/${storageId}/sync`);
    return response.data;
  }
};

// 检查token是否有效
export const isAuthenticated = (): boolean => {
  const token = Cookies.get('access_token');
  return !!token;
};

// 获取token
export const getAccessToken = (): string | undefined => {
  return Cookies.get('access_token');
};

export default apiClient;
