"""
初始化分段模板
"""

from sqlalchemy.orm import Session
from app.models.document_segment import SegmentTemplate, SegmentMethod


def init_default_segment_templates(db: Session):
    """初始化默认分段模板"""
    
    # 检查是否已经有模板
    existing_count = db.query(SegmentTemplate).count()
    if existing_count > 0:
        print("分段模板已存在，跳过初始化")
        return
    
    templates = [
        {
            "template_name": "通用文档分段",
            "description": "适用于大多数文档的通用分段配置，按段落分段，保留格式",
            "segment_method": SegmentMethod.PARAGRAPH,
            "max_length": 500,
            "overlap": 50,
            "preserve_formatting": True,
            "enable_vectorization": True,
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 1000,
            "language": "zh",
            "remove_stopwords": False,
            "normalize_text": True,
            "extract_keywords": True,
            "is_default": True,
            "is_system": True
        },
        {
            "template_name": "长文档分段",
            "description": "适用于长文档的分段配置，较大的分段长度，适合学术论文等",
            "segment_method": SegmentMethod.PARAGRAPH,
            "max_length": 1000,
            "overlap": 100,
            "preserve_formatting": True,
            "enable_vectorization": True,
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 1500,
            "language": "zh",
            "remove_stopwords": False,
            "normalize_text": True,
            "extract_keywords": True,
            "is_default": False,
            "is_system": True
        },
        {
            "template_name": "精细分段",
            "description": "精细化分段配置，按句子分段，适合需要精确检索的场景",
            "segment_method": SegmentMethod.SENTENCE,
            "max_length": 200,
            "overlap": 20,
            "preserve_formatting": False,
            "enable_vectorization": True,
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 500,
            "language": "zh",
            "remove_stopwords": True,
            "normalize_text": True,
            "extract_keywords": True,
            "is_default": False,
            "is_system": True
        },
        {
            "template_name": "固定长度分段",
            "description": "固定长度分段配置，适合结构化文档处理",
            "segment_method": SegmentMethod.FIXED_LENGTH,
            "max_length": 300,
            "overlap": 30,
            "preserve_formatting": False,
            "enable_vectorization": True,
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 800,
            "language": "zh",
            "remove_stopwords": False,
            "normalize_text": True,
            "extract_keywords": False,
            "is_default": False,
            "is_system": True
        },
        {
            "template_name": "AI语义分段",
            "description": "基于AI的语义分段，智能识别语义边界，适合复杂文档",
            "segment_method": SegmentMethod.SEMANTIC,
            "max_length": 800,
            "overlap": 80,
            "preserve_formatting": True,
            "enable_vectorization": True,
            "embedding_model": "text-embedding-3-large",
            "vector_dimension": 3072,
            "chunk_size": 1200,
            "language": "auto",
            "remove_stopwords": False,
            "normalize_text": True,
            "extract_keywords": True,
            "is_default": False,
            "is_system": True
        },
        {
            "template_name": "英文文档分段",
            "description": "专门针对英文文档优化的分段配置",
            "segment_method": SegmentMethod.PARAGRAPH,
            "max_length": 600,
            "overlap": 60,
            "preserve_formatting": True,
            "enable_vectorization": True,
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 1000,
            "language": "en",
            "remove_stopwords": True,
            "normalize_text": True,
            "extract_keywords": True,
            "is_default": False,
            "is_system": True
        },
        {
            "template_name": "快速分段",
            "description": "快速分段配置，不启用向量化，适合快速预览",
            "segment_method": SegmentMethod.PARAGRAPH,
            "max_length": 400,
            "overlap": 40,
            "preserve_formatting": True,
            "enable_vectorization": False,
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 1000,
            "language": "zh",
            "remove_stopwords": False,
            "normalize_text": False,
            "extract_keywords": False,
            "is_default": False,
            "is_system": True
        }
    ]
    
    try:
        for template_data in templates:
            template = SegmentTemplate(**template_data)
            db.add(template)
        
        db.commit()
        print(f"成功初始化 {len(templates)} 个分段模板")
        
    except Exception as e:
        db.rollback()
        print(f"初始化分段模板失败: {str(e)}")
        raise


def get_default_template(db: Session) -> SegmentTemplate:
    """获取默认分段模板"""
    template = db.query(SegmentTemplate).filter(
        SegmentTemplate.is_default == True
    ).first()
    
    if not template:
        # 如果没有默认模板，创建一个
        template = SegmentTemplate(
            template_name="默认分段模板",
            description="系统默认分段配置",
            segment_method=SegmentMethod.PARAGRAPH,
            max_length=500,
            overlap=50,
            preserve_formatting=True,
            enable_vectorization=True,
            embedding_model="text-embedding-ada-002",
            vector_dimension=1536,
            chunk_size=1000,
            language="zh",
            remove_stopwords=False,
            normalize_text=True,
            extract_keywords=True,
            is_default=True,
            is_system=True
        )
        db.add(template)
        db.commit()
        db.refresh(template)
    
    return template


if __name__ == "__main__":
    # 用于测试的代码
    from app.core.database import get_sync_session

    db = next(get_sync_session())
    try:
        init_default_segment_templates(db)
        print("分段模板初始化完成")
    finally:
        db.close()
