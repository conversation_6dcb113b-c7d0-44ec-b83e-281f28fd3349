#!/usr/bin/env python3
"""
创建文档分段相关数据表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.core.config import get_settings
from app.core.database import Base
from app.models.document_segment import DocumentSegmentTask, DocumentSegment, SegmentTemplate, VectorIndex
from app.core.init_segment_templates import init_default_segment_templates
from app.core.database import get_sync_session


def create_segment_tables():
    """创建文档分段相关表"""
    print("🔧 开始创建文档分段相关数据表...")
    
    try:
        settings = get_settings()
        
        # 创建数据库引擎
        engine = create_engine(
            settings.DATABASE_URL,
            echo=True  # 显示SQL语句
        )
        
        print("📊 检查现有表...")
        
        # 检查表是否已存在
        with engine.connect() as conn:
            # 检查 document_segment_tasks 表
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'document_segment_tasks'
                );
            """))
            tasks_table_exists = result.scalar()
            
            # 检查 document_segments 表
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'document_segments'
                );
            """))
            segments_table_exists = result.scalar()
            
            # 检查 segment_templates 表
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'segment_templates'
                );
            """))
            templates_table_exists = result.scalar()
            
            # 检查 vector_indexes 表
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'vector_indexes'
                );
            """))
            indexes_table_exists = result.scalar()
        
        print(f"📋 表状态检查:")
        print(f"   - document_segment_tasks: {'存在' if tasks_table_exists else '不存在'}")
        print(f"   - document_segments: {'存在' if segments_table_exists else '不存在'}")
        print(f"   - segment_templates: {'存在' if templates_table_exists else '不存在'}")
        print(f"   - vector_indexes: {'存在' if indexes_table_exists else '不存在'}")
        
        # 如果表不存在，创建它们
        if not all([tasks_table_exists, segments_table_exists, templates_table_exists, indexes_table_exists]):
            print("\n🔨 创建缺失的表...")
            
            # 创建表
            Base.metadata.create_all(engine, tables=[
                DocumentSegmentTask.__table__,
                DocumentSegment.__table__,
                SegmentTemplate.__table__,
                VectorIndex.__table__
            ])
            
            print("✅ 数据表创建完成")
        else:
            print("✅ 所有表已存在，跳过创建")
        
        # 初始化默认模板
        print("\n📋 初始化默认分段模板...")
        db = next(get_sync_session())
        try:
            init_default_segment_templates(db)
            print("✅ 默认模板初始化完成")
        finally:
            db.close()
        
        print("\n🎉 文档分段功能数据库初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建数据表失败: {str(e)}")
        return False


def verify_tables():
    """验证表创建结果"""
    print("\n🔍 验证表创建结果...")
    
    try:
        settings = get_settings()
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as conn:
            # 查询所有相关表
            result = conn.execute(text("""
                SELECT table_name, 
                       (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = t.table_name AND table_schema = 'public') as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'public' 
                AND table_name IN ('document_segment_tasks', 'document_segments', 'segment_templates', 'vector_indexes')
                ORDER BY table_name;
            """))
            
            tables = result.fetchall()
            
            print("📊 表结构验证:")
            for table_name, column_count in tables:
                print(f"   - {table_name}: {column_count} 列")
            
            # 查询模板数量
            result = conn.execute(text("SELECT COUNT(*) FROM segment_templates;"))
            template_count = result.scalar()
            print(f"   - 默认模板数量: {template_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("🚀 文档分段功能数据库初始化")
    print("=" * 50)
    
    # 创建表
    if create_segment_tables():
        # 验证表
        if verify_tables():
            print("\n✅ 所有操作完成！")
            print("\n📋 下一步:")
            print("   1. 启动后端服务: python -m uvicorn main:app --reload")
            print("   2. 启动前端服务: cd web && pnpm dev")
            print("   3. 测试文档分段功能")
        else:
            print("\n❌ 验证失败，请检查数据库连接")
    else:
        print("\n❌ 初始化失败，请检查错误信息")


if __name__ == "__main__":
    main()
