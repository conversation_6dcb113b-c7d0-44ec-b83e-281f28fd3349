'use client';

import React from 'react';
import {
  File,
  FileText,
  Image,
  Video,
  Music,
  Code,
  Sheet,
  Presentation,
  Archive,
  Folder,
  FolderOpen,
  Download,
  Settings,
  Database,
  Globe,
  Zap
} from 'lucide-react';

interface FileTypeIconProps {
  fileName: string;
  fileType?: string;
  isDirectory?: boolean;
  isOpen?: boolean;
  className?: string;
  size?: number;
}

const FileTypeIcon: React.FC<FileTypeIconProps> = ({
  fileName,
  fileType,
  isDirectory = false,
  isOpen = false,
  className = '',
  size = 20
}) => {
  // 如果是目录，返回文件夹图标
  if (isDirectory) {
    return isOpen ? (
      <FolderOpen 
        className={`text-blue-500 ${className}`} 
        size={size} 
      />
    ) : (
      <Folder 
        className={`text-blue-500 ${className}`} 
        size={size} 
      />
    );
  }

  // 获取文件扩展名
  const getFileExtension = (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
  };

  const extension = getFileExtension(fileName);

  // 根据文件扩展名返回对应图标和颜色
  const getIconAndColor = (ext: string) => {
    // 图片文件
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'raw'].includes(ext)) {
      return {
        icon: Image,
        color: 'text-green-500',
        bgColor: 'bg-green-50'
      };
    }

    // 视频文件
    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'ogv'].includes(ext)) {
      return {
        icon: Video,
        color: 'text-red-500',
        bgColor: 'bg-red-50'
      };
    }

    // 音频文件
    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus'].includes(ext)) {
      return {
        icon: Music,
        color: 'text-purple-500',
        bgColor: 'bg-purple-50'
      };
    }

    // 文档文件
    if (['pdf', 'doc', 'docx', 'rtf', 'odt', 'pages'].includes(ext)) {
      return { 
        icon: FileText, 
        color: 'text-blue-600',
        bgColor: 'bg-blue-50'
      };
    }

    // 表格文件
    if (['xls', 'xlsx', 'csv', 'ods', 'numbers'].includes(ext)) {
      return {
        icon: Sheet,
        color: 'text-green-600',
        bgColor: 'bg-green-50'
      };
    }

    // 演示文件
    if (['ppt', 'pptx', 'odp', 'key'].includes(ext)) {
      return {
        icon: Presentation,
        color: 'text-orange-500',
        bgColor: 'bg-orange-50'
      };
    }

    // 代码文件
    if (['js', 'ts', 'jsx', 'tsx', 'html', 'css', 'scss', 'sass', 'less', 'py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'dart', 'vue', 'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf'].includes(ext)) {
      return {
        icon: Code,
        color: 'text-indigo-500',
        bgColor: 'bg-indigo-50'
      };
    }

    // 压缩文件
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'dmg', 'iso'].includes(ext)) {
      return { 
        icon: Archive, 
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50'
      };
    }

    // 可执行文件
    if (['exe', 'msi', 'deb', 'rpm', 'pkg', 'dmg', 'app'].includes(ext)) {
      return { 
        icon: Settings, 
        color: 'text-gray-600',
        bgColor: 'bg-gray-50'
      };
    }

    // 数据库文件
    if (['db', 'sqlite', 'sql', 'mdb', 'accdb'].includes(ext)) {
      return { 
        icon: Database, 
        color: 'text-teal-500',
        bgColor: 'bg-teal-50'
      };
    }

    // 网页文件
    if (['html', 'htm', 'xhtml'].includes(ext)) {
      return { 
        icon: Globe, 
        color: 'text-blue-500',
        bgColor: 'bg-blue-50'
      };
    }

    // 字体文件
    if (['ttf', 'otf', 'woff', 'woff2', 'eot'].includes(ext)) {
      return { 
        icon: FileText, 
        color: 'text-pink-500',
        bgColor: 'bg-pink-50'
      };
    }

    // 3D文件
    if (['obj', 'fbx', 'dae', 'blend', 'max', 'maya'].includes(ext)) {
      return { 
        icon: Zap, 
        color: 'text-cyan-500',
        bgColor: 'bg-cyan-50'
      };
    }

    // 默认文件图标
    return { 
      icon: File, 
      color: 'text-gray-500',
      bgColor: 'bg-gray-50'
    };
  };

  const { icon: IconComponent, color } = getIconAndColor(extension);

  return (
    <IconComponent 
      className={`${color} ${className}`} 
      size={size} 
    />
  );
};

// 获取文件类型描述
export const getFileTypeDescription = (fileName: string, isDirectory: boolean = false): string => {
  if (isDirectory) {
    return '文件夹';
  }

  const getFileExtension = (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
  };

  const extension = getFileExtension(fileName);

  const typeMap: { [key: string]: string } = {
    // 图片
    'jpg': 'JPEG 图片', 'jpeg': 'JPEG 图片', 'png': 'PNG 图片', 'gif': 'GIF 图片',
    'bmp': 'BMP 图片', 'webp': 'WebP 图片', 'svg': 'SVG 图片', 'ico': '图标文件',
    
    // 视频
    'mp4': 'MP4 视频', 'avi': 'AVI 视频', 'mkv': 'MKV 视频', 'mov': 'QuickTime 视频',
    'wmv': 'WMV 视频', 'flv': 'Flash 视频', 'webm': 'WebM 视频',
    
    // 音频
    'mp3': 'MP3 音频', 'wav': 'WAV 音频', 'flac': 'FLAC 音频', 'aac': 'AAC 音频',
    'ogg': 'OGG 音频', 'wma': 'WMA 音频',
    
    // 文档
    'pdf': 'PDF 文档', 'doc': 'Word 文档', 'docx': 'Word 文档', 'rtf': 'RTF 文档',
    'txt': '文本文件', 'md': 'Markdown 文档',
    
    // 表格
    'xls': 'Excel 表格', 'xlsx': 'Excel 表格', 'csv': 'CSV 文件',
    
    // 演示
    'ppt': 'PowerPoint 演示', 'pptx': 'PowerPoint 演示',
    
    // 代码
    'js': 'JavaScript 文件', 'ts': 'TypeScript 文件', 'html': 'HTML 文件',
    'css': 'CSS 文件', 'py': 'Python 文件', 'java': 'Java 文件',
    'cpp': 'C++ 文件', 'c': 'C 文件', 'php': 'PHP 文件',
    'json': 'JSON 文件', 'xml': 'XML 文件', 'yaml': 'YAML 文件',
    
    // 压缩
    'zip': 'ZIP 压缩包', 'rar': 'RAR 压缩包', '7z': '7-Zip 压缩包',
    'tar': 'TAR 归档', 'gz': 'GZip 压缩包',
    
    // 其他
    'exe': '可执行文件', 'msi': 'Windows 安装包', 'deb': 'Debian 包',
    'rpm': 'RPM 包', 'dmg': 'macOS 磁盘映像'
  };

  return typeMap[extension] || `${extension.toUpperCase()} 文件` || '未知文件';
};

// 获取文件类型类别
export const getFileCategory = (fileName: string, isDirectory: boolean = false): string => {
  if (isDirectory) {
    return 'directory';
  }

  const getFileExtension = (filename: string): string => {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
  };

  const extension = getFileExtension(fileName);

  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(extension)) {
    return 'image';
  }
  if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(extension)) {
    return 'video';
  }
  if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'].includes(extension)) {
    return 'audio';
  }
  if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'md'].includes(extension)) {
    return 'document';
  }
  if (['xls', 'xlsx', 'csv'].includes(extension)) {
    return 'spreadsheet';
  }
  if (['ppt', 'pptx'].includes(extension)) {
    return 'presentation';
  }
  if (['js', 'ts', 'html', 'css', 'py', 'java', 'cpp', 'c', 'php', 'json', 'xml'].includes(extension)) {
    return 'code';
  }
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return 'archive';
  }

  return 'file';
};

export default FileTypeIcon;
