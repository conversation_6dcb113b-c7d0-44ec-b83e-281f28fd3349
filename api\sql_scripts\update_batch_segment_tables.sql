-- 更新批量分段功能表结构
-- 手动执行此SQL脚本来更新现有表结构

-- 为 document_segment_tasks 表添加新字段
ALTER TABLE document_segment_tasks 
ADD COLUMN IF NOT EXISTS processing_files INTEGER DEFAULT 0;

ALTER TABLE document_segment_tasks 
ADD COLUMN IF NOT EXISTS pending_files INTEGER DEFAULT 0;

ALTER TABLE document_segment_tasks 
ADD COLUMN IF NOT EXISTS total_segments INTEGER DEFAULT 0;

-- 为 document_segment_task_files 表添加新字段
ALTER TABLE document_segment_task_files 
ADD COLUMN IF NOT EXISTS file_size BIGINT DEFAULT 0;

ALTER TABLE document_segment_task_files 
ADD COLUMN IF NOT EXISTS file_extension VARCHAR(50);

ALTER TABLE document_segment_task_files 
ADD COLUMN IF NOT EXISTS storage_type VARCHAR(50);

ALTER TABLE document_segment_task_files 
ADD COLUMN IF NOT EXISTS started_at TIMESTAMP;

ALTER TABLE document_segment_task_files 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP;

-- 创建分段统计表（如果不存在）
CREATE TABLE IF NOT EXISTS document_segment_statistics (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    total_files INTEGER DEFAULT 0,
    pending_files INTEGER DEFAULT 0,
    processing_files INTEGER DEFAULT 0,
    completed_files INTEGER DEFAULT 0,
    failed_files INTEGER DEFAULT 0,
    total_segments INTEGER DEFAULT 0,
    total_words INTEGER DEFAULT 0,
    average_segments_per_file DECIMAL(10,2) DEFAULT 0,
    processing_time_seconds INTEGER DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加新的索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_created_at ON document_segment_tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_document_segment_task_files_file_id ON document_segment_task_files(file_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_segment_index ON document_segments(segment_index);
CREATE INDEX IF NOT EXISTS idx_document_segment_statistics_task_id ON document_segment_statistics(task_id);
CREATE INDEX IF NOT EXISTS idx_document_segment_statistics_updated_at ON document_segment_statistics(updated_at);

-- 添加注释
COMMENT ON COLUMN document_segment_tasks.processing_files IS '正在处理的文件数量';
COMMENT ON COLUMN document_segment_tasks.pending_files IS '等待处理的文件数量';
COMMENT ON COLUMN document_segment_tasks.total_segments IS '总分段数量';

COMMENT ON COLUMN document_segment_task_files.file_size IS '文件大小（字节）';
COMMENT ON COLUMN document_segment_task_files.file_extension IS '文件扩展名';
COMMENT ON COLUMN document_segment_task_files.storage_type IS '存储类型';
COMMENT ON COLUMN document_segment_task_files.started_at IS '开始处理时间';
COMMENT ON COLUMN document_segment_task_files.completed_at IS '完成处理时间';

COMMENT ON TABLE document_segment_statistics IS '分段统计表，用于实时统计显示';
COMMENT ON COLUMN document_segment_statistics.total_words IS '总词数';
COMMENT ON COLUMN document_segment_statistics.average_segments_per_file IS '平均每个文件的分段数';
COMMENT ON COLUMN document_segment_statistics.processing_time_seconds IS '处理耗时（秒）';

-- 创建触发器函数来自动更新统计信息
CREATE OR REPLACE FUNCTION update_segment_statistics()
RETURNS TRIGGER AS $$
BEGIN
    -- 当文件状态更新时，更新对应任务的统计信息
    IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        INSERT INTO document_segment_statistics (
            task_id,
            total_files,
            pending_files,
            processing_files,
            completed_files,
            failed_files,
            total_segments,
            updated_at
        )
        SELECT 
            NEW.task_id,
            COUNT(*) as total_files,
            COUNT(*) FILTER (WHERE status = 'pending') as pending_files,
            COUNT(*) FILTER (WHERE status = 'processing') as processing_files,
            COUNT(*) FILTER (WHERE status = 'completed') as completed_files,
            COUNT(*) FILTER (WHERE status = 'error') as failed_files,
            COALESCE(SUM(segments_count), 0) as total_segments,
            CURRENT_TIMESTAMP
        FROM document_segment_task_files 
        WHERE task_id = NEW.task_id
        ON CONFLICT (task_id) DO UPDATE SET
            total_files = EXCLUDED.total_files,
            pending_files = EXCLUDED.pending_files,
            processing_files = EXCLUDED.processing_files,
            completed_files = EXCLUDED.completed_files,
            failed_files = EXCLUDED.failed_files,
            total_segments = EXCLUDED.total_segments,
            updated_at = EXCLUDED.updated_at;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_update_segment_statistics ON document_segment_task_files;
CREATE TRIGGER trigger_update_segment_statistics
    AFTER UPDATE ON document_segment_task_files
    FOR EACH ROW
    EXECUTE FUNCTION update_segment_statistics();

-- 为统计表添加唯一约束
ALTER TABLE document_segment_statistics 
ADD CONSTRAINT IF NOT EXISTS unique_task_statistics 
UNIQUE (task_id);

-- 初始化现有任务的统计信息
INSERT INTO document_segment_statistics (
    task_id,
    total_files,
    pending_files,
    processing_files,
    completed_files,
    failed_files,
    total_segments,
    updated_at
)
SELECT 
    t.id as task_id,
    COALESCE(f.total_files, 0) as total_files,
    COALESCE(f.pending_files, 0) as pending_files,
    COALESCE(f.processing_files, 0) as processing_files,
    COALESCE(f.completed_files, 0) as completed_files,
    COALESCE(f.failed_files, 0) as failed_files,
    COALESCE(f.total_segments, 0) as total_segments,
    CURRENT_TIMESTAMP
FROM document_segment_tasks t
LEFT JOIN (
    SELECT 
        task_id,
        COUNT(*) as total_files,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_files,
        COUNT(*) FILTER (WHERE status = 'processing') as processing_files,
        COUNT(*) FILTER (WHERE status = 'completed') as completed_files,
        COUNT(*) FILTER (WHERE status = 'error') as failed_files,
        COALESCE(SUM(segments_count), 0) as total_segments
    FROM document_segment_task_files 
    GROUP BY task_id
) f ON t.id = f.task_id
ON CONFLICT (task_id) DO NOTHING;

-- 验证表结构
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN (
    'document_segment_tasks',
    'document_segment_task_files', 
    'document_segments',
    'document_segment_statistics'
)
ORDER BY table_name, ordinal_position;
