"""
命令行工具
提供项目管理和维护命令
"""

import typer
import asyncio
from pathlib import Path
from typing import Optional

from app.core.config import get_settings
from app.plugins.manager import PluginManager

app = typer.Typer(help="AI知识库 API CLI Tools")


@app.command()
def info():
    """显示应用信息"""
    settings = get_settings()
    
    typer.echo(f"🚀 {settings.APP_NAME} v{settings.APP_VERSION}")
    typer.echo(f"📍 Environment: {settings.ENVIRONMENT}")
    typer.echo(f"🌐 Host: {settings.HOST}:{settings.PORT}")
    typer.echo(f"📊 Debug: {settings.DEBUG}")
    typer.echo(f"📝 Log Level: {settings.LOG_LEVEL}")


@app.command()
def plugins():
    """显示插件信息"""
    settings = get_settings()
    plugin_manager = PluginManager(settings)
    plugin_manager.load_plugins()
    
    typer.echo("📦 Loaded Plugins:")
    for name, plugin in plugin_manager.plugins.items():
        metadata = plugin.metadata
        status = "✅ Enabled" if plugin.is_enabled else "❌ Disabled"
        typer.echo(f"  • {metadata.name} v{metadata.version} - {status}")
        typer.echo(f"    {metadata.description}")


@app.command()
def health():
    """检查系统健康状态"""
    async def check_health():
        settings = get_settings()
        plugin_manager = PluginManager(settings)
        plugin_manager.load_plugins()
        await plugin_manager.initialize_plugins()
        
        typer.echo("🏥 System Health Check:")
        
        # 检查插件健康状态
        health_status = await plugin_manager.get_plugins_health()
        
        for plugin_name, status in health_status.items():
            if status.get("status") == "healthy":
                typer.echo(f"  ✅ {plugin_name}: {status['status']}")
            else:
                typer.echo(f"  ❌ {plugin_name}: {status['status']}")
                if "error" in status:
                    typer.echo(f"     Error: {status['error']}")
    
    asyncio.run(check_health())


@app.command()
def create_env(
    environment: str = typer.Argument(..., help="Environment name (development, testing, production)"),
    force: bool = typer.Option(False, "--force", "-f", help="Overwrite existing file")
):
    """创建环境配置文件"""
    env_file = Path(f".env.{environment}")
    
    if env_file.exists() and not force:
        typer.echo(f"❌ Environment file {env_file} already exists. Use --force to overwrite.")
        raise typer.Exit(1)
    
    # 基础配置模板
    config_template = f"""# {environment.title()} Environment Configuration
ENVIRONMENT="{environment}"
DEBUG={"true" if environment == "development" else "false"}
HOST="127.0.0.1"
PORT=8000
LOG_LEVEL="{"DEBUG" if environment == "development" else "INFO"}"

# Security
SECRET_KEY="change-me-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/xhc_rag_{environment}"
DATABASE_ECHO={"true" if environment == "development" else "false"}

# Redis
REDIS_URL="redis://localhost:6379/0"

# Plugins
ENABLED_PLUGINS="auth,file_manager,rag_engine"

# CORS
CORS_ORIGINS="{"*" if environment == "development" else "https://yourdomain.com"}"
"""
    
    env_file.write_text(config_template)
    typer.echo(f"✅ Created environment file: {env_file}")


@app.command()
def run(
    host: Optional[str] = typer.Option(None, "--host", "-h", help="Host to bind to"),
    port: Optional[int] = typer.Option(None, "--port", "-p", help="Port to bind to"),
    reload: Optional[bool] = typer.Option(None, "--reload", "-r", help="Enable auto-reload"),
    log_level: Optional[str] = typer.Option(None, "--log-level", "-l", help="Log level")
):
    """运行应用服务器"""
    import uvicorn
    
    settings = get_settings()
    
    # 使用命令行参数覆盖配置
    run_host = host or settings.HOST
    run_port = port or settings.PORT
    run_reload = reload if reload is not None else settings.DEBUG
    run_log_level = log_level or settings.LOG_LEVEL
    
    typer.echo(f"🚀 Starting {settings.APP_NAME} v{settings.APP_VERSION}")
    typer.echo(f"📍 Environment: {settings.ENVIRONMENT}")
    typer.echo(f"🌐 Server: http://{run_host}:{run_port}")
    
    uvicorn.run(
        "main:app",
        host=run_host,
        port=run_port,
        reload=run_reload,
        log_level=run_log_level.lower()
    )


@app.command()
def init_db():
    """初始化数据库"""
    import subprocess
    import sys

    typer.echo("🗄️ Initializing database...")

    try:
        # 运行Alembic初始化
        result = subprocess.run([
            sys.executable, "-m", "alembic", "upgrade", "head"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            typer.echo("✅ Database initialized successfully")
            typer.echo(result.stdout)
        else:
            typer.echo("❌ Database initialization failed")
            typer.echo(result.stderr)
            raise typer.Exit(1)

    except Exception as e:
        typer.echo(f"❌ Error initializing database: {e}")
        raise typer.Exit(1)


@app.command()
def migrate(
    message: str = typer.Option("Auto migration", "--message", "-m", help="Migration message")
):
    """创建数据库迁移"""
    import subprocess
    import sys

    typer.echo(f"📝 Creating migration: {message}")

    try:
        result = subprocess.run([
            sys.executable, "-m", "alembic", "revision", "--autogenerate", "-m", message
        ], capture_output=True, text=True)

        if result.returncode == 0:
            typer.echo("✅ Migration created successfully")
            typer.echo(result.stdout)
        else:
            typer.echo("❌ Migration creation failed")
            typer.echo(result.stderr)
            raise typer.Exit(1)

    except Exception as e:
        typer.echo(f"❌ Error creating migration: {e}")
        raise typer.Exit(1)


@app.command()
def upgrade_db():
    """升级数据库到最新版本"""
    import subprocess
    import sys

    typer.echo("⬆️ Upgrading database...")

    try:
        result = subprocess.run([
            sys.executable, "-m", "alembic", "upgrade", "head"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            typer.echo("✅ Database upgraded successfully")
            typer.echo(result.stdout)
        else:
            typer.echo("❌ Database upgrade failed")
            typer.echo(result.stderr)
            raise typer.Exit(1)

    except Exception as e:
        typer.echo(f"❌ Error upgrading database: {e}")
        raise typer.Exit(1)


@app.command()
def reset_db(
    confirm: bool = typer.Option(False, "--yes", "-y", help="Skip confirmation")
):
    """重置数据库"""
    if not confirm:
        confirm = typer.confirm("⚠️ This will delete all data. Are you sure?")

    if not confirm:
        typer.echo("❌ Operation cancelled")
        raise typer.Exit(1)

    import subprocess
    import sys

    typer.echo("🗄️ Resetting database...")

    try:
        # 降级到base版本
        result = subprocess.run([
            sys.executable, "-m", "alembic", "downgrade", "base"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            # 重新升级到最新版本
            result = subprocess.run([
                sys.executable, "-m", "alembic", "upgrade", "head"
            ], capture_output=True, text=True)

            if result.returncode == 0:
                typer.echo("✅ Database reset successfully")
            else:
                typer.echo("❌ Database reset failed during upgrade")
                typer.echo(result.stderr)
                raise typer.Exit(1)
        else:
            typer.echo("❌ Database reset failed during downgrade")
            typer.echo(result.stderr)
            raise typer.Exit(1)

    except Exception as e:
        typer.echo(f"❌ Error resetting database: {e}")
        raise typer.Exit(1)


def main():
    """CLI入口点"""
    app()


if __name__ == "__main__":
    main()
