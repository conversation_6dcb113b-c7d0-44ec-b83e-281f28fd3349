# 知识库智能平台 - 前端

基于Next.js开发的AI风格登录页面，支持多语言切换，与FastAPI后端无缝对接。

## 特性

- 🎨 **AI风格设计** - 现代化的AI主题界面设计
- 🌍 **多语言支持** - 支持中文简体、中文繁体、英文、日文
- 🔐 **安全认证** - 与FastAPI后端JWT认证集成
- ⚡ **响应式设计** - 适配各种设备屏幕
- 🎭 **动画效果** - 使用Framer Motion实现流畅动画
- 🔄 **状态管理** - React Hook Form表单管理
- 📱 **移动优先** - 移动设备优化体验

## 技术栈

- **框架**: Next.js 15.3.3
- **语言**: TypeScript
- **样式**: Tailwind CSS 4.0
- **动画**: Framer Motion
- **表单**: React Hook Form
- **HTTP客户端**: Axios
- **通知**: React Hot Toast
- **图标**: Lucide React

## 快速开始

### 1. 安装依赖

```bash
cd web

# 使用pnpm（推荐）
pnpm install

# 或使用npm
npm install

# 或使用yarn
yarn install
```

### 2. 环境配置

复制环境变量文件：

```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件：

```env
# API基础URL - 指向FastAPI后端
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=知识库智能平台
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 3. 启动开发服务器

```bash
# 使用pnpm（推荐）
pnpm dev

# 或使用npm
npm run dev

# 或使用yarn
yarn dev
```

### 4. 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 项目结构

```
web/
├── app/                    # Next.js App Router
│   ├── login/             # 登录页面
│   ├── dashboard/         # 仪表板页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 可复用组件
│   ├── AIBackground.tsx   # AI背景组件
│   ├── LanguageSwitcher.tsx # 语言切换器
│   └── LoginForm.tsx      # 登录表单
├── contexts/              # React上下文
│   └── LanguageContext.tsx # 语言上下文
├── lib/                   # 工具库
│   ├── api.ts            # API客户端
│   └── i18n.ts           # 国际化配置
├── public/               # 静态资源
├── .env.local           # 环境变量
├── package.json         # 项目配置
└── README.md           # 项目文档
```

## 页面说明

### 首页 (/)
- 展示产品介绍
- 提供登录入口
- AI风格设计

### 登录页面 (/login)
- AI风格登录界面
- 多语言切换功能
- 表单验证
- 与后端API对接
- 动画效果

### 仪表板 (/dashboard)
- 登录后的主界面
- 用户信息展示
- 系统统计
- 快速操作

## 多语言支持

支持以下语言：

- 🇨🇳 中文简体 (zh-CN)
- 🇹🇼 中文繁体 (zh-TW)
- 🇺🇸 英文 (en)
- 🇯🇵 日文 (ja)

语言切换功能：
- 自动检测浏览器语言
- 本地存储用户选择
- 实时切换界面语言

## API集成

与FastAPI后端的集成功能：

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/me` - 获取用户信息

### 功能特性
- JWT令牌管理
- 自动令牌刷新
- 请求拦截器
- 错误处理
- Cookie存储

## 开发指南

### 添加新语言

1. 在 `lib/i18n.ts` 中添加新语言类型
2. 添加翻译内容
3. 在 `LanguageSwitcher.tsx` 中添加语言选项

### 自定义主题

修改 `tailwind.config.js` 中的颜色配置：

```js
module.exports = {
  theme: {
    extend: {
      colors: {
        // 自定义颜色
      }
    }
  }
}
```

### 添加新页面

1. 在 `app/` 目录下创建新文件夹
2. 添加 `page.tsx` 文件
3. 实现页面组件

## 构建和部署

### 构建生产版本

```bash
pnpm build
```

### 启动生产服务器

```bash
pnpm start
```

### 部署到Vercel

1. 推送代码到GitHub
2. 在Vercel中导入项目
3. 设置环境变量
4. 部署

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查 `.env.local` 中的API URL
   - 确保后端服务正在运行

2. **依赖安装失败**
   - 清除缓存：`pnpm store prune`
   - 重新安装：`rm -rf node_modules && pnpm install`

3. **类型错误**
   - 运行类型检查：`pnpm type-check`
   - 更新类型定义

### 开发工具

```bash
# 类型检查
pnpm type-check

# 代码检查
pnpm lint

# 代码格式化
pnpm format
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
