# PowerShell脚本启动Celery服务
# 使用方法: powershell -ExecutionPolicy Bypass -File start_celery.ps1

Write-Host "启动AI知识库Celery服务..." -ForegroundColor Green

# 检查Redis连接
Write-Host "检查Redis连接..." -ForegroundColor Yellow
try {
    $redisCheck = python -c @"
import redis
import os
redis_host = os.getenv('REDIS_HOST', '**************')
redis_port = int(os.getenv('REDIS_PORT', '6379'))
redis_db = int(os.getenv('REDIS_DB', '10'))
try:
    r = redis.Redis(host=redis_host, port=redis_port, db=redis_db)
    r.ping()
    print('Redis连接成功')
except Exception as e:
    print(f'Redis连接失败: {e}')
    exit(1)
"@
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Redis连接失败，请检查Redis服务" -ForegroundColor Red
        Read-Host "按Enter键退出"
        exit 1
    }
    Write-Host "Redis连接成功" -ForegroundColor Green
} catch {
    Write-Host "Redis连接检查失败: $_" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 设置环境变量
$env:PYTHONPATH = "$env:PYTHONPATH;$PWD"

# 创建日志目录
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
}

# 创建PID目录
if (!(Test-Path "pids")) {
    New-Item -ItemType Directory -Path "pids" | Out-Null
}

# 启动Celery Worker
Write-Host "启动Celery Worker..." -ForegroundColor Yellow
$workerJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    $env:PYTHONPATH = $using:env:PYTHONPATH
    celery -A app.core.celery_config:celery_app worker `
        --loglevel=info `
        --concurrency=4 `
        --queues=default,upload_queue,file_queue `
        --hostname=worker@%h `
        --logfile=logs/celery_worker.log
} -Name "CeleryWorker"

# 等待Worker启动
Start-Sleep -Seconds 3

# 启动Celery Beat
Write-Host "启动Celery Beat..." -ForegroundColor Yellow
$beatJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    $env:PYTHONPATH = $using:env:PYTHONPATH
    celery -A app.core.celery_config:celery_app beat `
        --loglevel=info `
        --logfile=logs/celery_beat.log
} -Name "CeleryBeat"

# 启动Flower监控
Write-Host "启动Flower监控..." -ForegroundColor Yellow
$flowerJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    $env:PYTHONPATH = $using:env:PYTHONPATH
    celery -A app.core.celery_config:celery_app flower `
        --port=5555 `
        --basic_auth=admin:password `
        --logfile=logs/celery_flower.log
} -Name "CeleryFlower"

# 保存Job ID到文件
$workerJob.Id | Out-File -FilePath "pids/worker.pid" -Encoding utf8
$beatJob.Id | Out-File -FilePath "pids/beat.pid" -Encoding utf8
$flowerJob.Id | Out-File -FilePath "pids/flower.pid" -Encoding utf8

Write-Host ""
Write-Host "Celery服务启动完成!" -ForegroundColor Green
Write-Host "- Worker Job ID: $($workerJob.Id)" -ForegroundColor Cyan
Write-Host "- Beat Job ID: $($beatJob.Id)" -ForegroundColor Cyan
Write-Host "- Flower Job ID: $($flowerJob.Id)" -ForegroundColor Cyan
Write-Host "- Flower监控地址: http://localhost:5555 (admin/password)" -ForegroundColor Cyan
Write-Host ""
Write-Host "查看日志:" -ForegroundColor Yellow
Write-Host "- Worker日志: Get-Content logs\celery_worker.log -Wait" -ForegroundColor Gray
Write-Host "- Beat日志: Get-Content logs\celery_beat.log -Wait" -ForegroundColor Gray
Write-Host "- Flower日志: Get-Content logs\celery_flower.log -Wait" -ForegroundColor Gray
Write-Host ""
Write-Host "停止服务: powershell -ExecutionPolicy Bypass -File stop_celery.ps1" -ForegroundColor Yellow
Write-Host ""

# 监控服务状态
Write-Host "监控服务状态 (按Ctrl+C退出监控)..." -ForegroundColor Yellow
try {
    while ($true) {
        $workerState = (Get-Job -Id $workerJob.Id).State
        $beatState = (Get-Job -Id $beatJob.Id).State
        $flowerState = (Get-Job -Id $flowerJob.Id).State
        
        Write-Host "$(Get-Date -Format 'HH:mm:ss') - Worker: $workerState, Beat: $beatState, Flower: $flowerState" -ForegroundColor Gray
        
        # 检查是否有服务停止
        if ($workerState -eq "Failed" -or $beatState -eq "Failed" -or $flowerState -eq "Failed") {
            Write-Host "检测到服务异常，请检查日志" -ForegroundColor Red
        }
        
        Start-Sleep -Seconds 10
    }
} catch {
    Write-Host "监控已停止" -ForegroundColor Yellow
}

Read-Host "按Enter键退出"
