#!/usr/bin/env python3
"""
Python版本的Celery启动脚本
通过Python代码管理Celery服务，支持跨平台
"""
import os
import sys
import time
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.celery_manager import celery_manager
from loguru import logger


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Celery服务管理器')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status'], 
                       help='操作类型')
    parser.add_argument('--service', choices=['worker', 'beat', 'flower', 'all'], 
                       default='all', help='服务类型')
    parser.add_argument('--daemon', action='store_true', 
                       help='后台运行模式')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("    AI知识库 Celery服务管理器")
    print("=" * 60)
    print()
    
    try:
        if args.action == 'start':
            print(f"🚀 启动 {args.service} 服务...")
            if args.service == 'all':
                results = celery_manager.start_all()
            else:
                results = {args.service: celery_manager.start_service(args.service)}
            
            # 显示结果
            for service, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"   {service}: {status}")
            
            if args.daemon:
                print("\n🔄 后台运行模式，按Ctrl+C退出监控...")
                try:
                    while True:
                        time.sleep(10)
                        status = celery_manager.get_status()
                        running_count = sum(1 for s in status["services"].values() if s["running"])
                        print(f"📊 运行中的服务: {running_count}/3", end='\r')
                except KeyboardInterrupt:
                    print("\n👋 退出监控模式")
            
        elif args.action == 'stop':
            print(f"🛑 停止 {args.service} 服务...")
            if args.service == 'all':
                results = celery_manager.stop_all()
            else:
                results = {args.service: celery_manager.stop_service(args.service)}
            
            # 显示结果
            for service, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"   {service}: {status}")
        
        elif args.action == 'restart':
            print(f"🔄 重启 {args.service} 服务...")
            if args.service == 'all':
                results = celery_manager.restart_all()
            else:
                results = {args.service: celery_manager.restart_service(args.service)}
            
            # 显示结果
            for service, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"   {service}: {status}")
        
        elif args.action == 'status':
            print("📊 服务状态:")
            status = celery_manager.get_status()
            
            print(f"   Redis连接: {'✅ 正常' if status['redis_connected'] else '❌ 断开'}")
            print()
            
            for service_name, service_status in status["services"].items():
                running = service_status["running"]
                pid = service_status["pid"]
                status_text = "✅ 运行中" if running else "❌ 已停止"
                pid_text = f" (PID: {pid})" if pid else ""
                print(f"   {service_name.capitalize()}: {status_text}{pid_text}")
            
            # 整体状态
            running_services = sum(1 for s in status["services"].values() if s["running"])
            total_services = len(status["services"])
            
            if running_services == total_services:
                overall = "✅ 全部运行"
            elif running_services > 0:
                overall = f"⚠️  部分运行 ({running_services}/{total_services})"
            else:
                overall = "❌ 全部停止"
            
            print(f"\n   整体状态: {overall}")
            
            # 显示访问地址
            if status["services"]["flower"]["running"]:
                print(f"\n🌸 Flower监控: http://localhost:5555 (admin/password)")
    
    except KeyboardInterrupt:
        print("\n👋 操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("操作完成!")
    print("=" * 60)


if __name__ == "__main__":
    main()
