#!/usr/bin/env python3
"""
简化的Celery管理脚本
使用方法:
  python celery_manager.py start    # 启动所有服务
  python celery_manager.py stop     # 停止所有服务
  python celery_manager.py restart  # 重启所有服务
  python celery_manager.py status   # 查看状态
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.celery_manager import celery_manager


def main():
    if len(sys.argv) < 2:
        print("使用方法: python celery_manager.py [start|stop|restart|status]")
        sys.exit(1)
    
    action = sys.argv[1].lower()
    
    if action == 'start':
        print("启动Celery服务...")
        results = celery_manager.start_all()
        for service, success in results.items():
            print(f"{service}: {'成功' if success else '失败'}")
    
    elif action == 'stop':
        print("停止Celery服务...")
        results = celery_manager.stop_all()
        for service, success in results.items():
            print(f"{service}: {'成功' if success else '失败'}")
    
    elif action == 'restart':
        print("重启Celery服务...")
        results = celery_manager.restart_all()
        for service, success in results.items():
            print(f"{service}: {'成功' if success else '失败'}")
    
    elif action == 'status':
        status = celery_manager.get_status()
        print(f"Redis: {'连接' if status['redis_connected'] else '断开'}")
        for service, info in status['services'].items():
            print(f"{service}: {'运行' if info['running'] else '停止'}")
    
    else:
        print("无效的操作，请使用: start, stop, restart, status")
        sys.exit(1)


if __name__ == "__main__":
    main()
