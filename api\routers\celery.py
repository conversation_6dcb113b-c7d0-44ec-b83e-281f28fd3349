"""
Celery相关API路由
"""
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.celery_control_service import CeleryControlService

router = APIRouter(prefix="/api/v1/celery", tags=["celery"])
celery_service = CeleryControlService()


class CeleryControlRequest(BaseModel):
    """Celery控制请求"""
    action: str  # start, stop, restart
    service: str = "all"  # all, worker, beat, flower
    timestamp: str = None


@router.get("/status")
async def get_celery_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取Celery状态"""
    try:
        status = celery_service.get_celery_status()
        
        return {
            "success": True,
            "message": "获取Celery状态成功",
            "data": status
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Celery状态失败: {str(e)}")


@router.post("/control")
async def control_celery_service(
    request: CeleryControlRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """控制Celery服务"""
    try:
        action = request.action.lower()
        service = request.service.lower()
        
        if action not in ['start', 'stop', 'restart']:
            raise HTTPException(status_code=400, detail="无效的操作类型")
        
        if service not in ['all', 'worker', 'beat', 'flower']:
            raise HTTPException(status_code=400, detail="无效的服务类型")
        
        results = {}
        
        if action == 'start':
            if service == 'all':
                results = celery_service.start_all_services()
            elif service == 'worker':
                results['worker'] = celery_service.start_celery_worker()
            elif service == 'beat':
                results['beat'] = celery_service.start_celery_beat()
            elif service == 'flower':
                results['flower'] = celery_service.start_flower()
                
        elif action == 'stop':
            if service == 'all':
                results = celery_service.stop_all_services()
            else:
                results[service] = celery_service.stop_celery_service(service)
                
        elif action == 'restart':
            if service == 'all':
                results = celery_service.restart_all_services()
            else:
                # 单个服务重启
                stop_result = celery_service.stop_celery_service(service)
                if stop_result[0]:
                    if service == 'worker':
                        start_result = celery_service.start_celery_worker()
                    elif service == 'beat':
                        start_result = celery_service.start_celery_beat()
                    elif service == 'flower':
                        start_result = celery_service.start_flower()
                    
                    results[service] = start_result
                else:
                    results[service] = stop_result
        
        # 检查操作结果
        success_count = sum(1 for success, _ in results.values() if success)
        total_count = len(results)
        
        if success_count == total_count:
            message = f"{action}操作执行成功"
            status_code = 200
        elif success_count > 0:
            message = f"{action}操作部分成功"
            status_code = 200
        else:
            message = f"{action}操作失败"
            status_code = 500
        
        # 获取最新状态
        current_status = celery_service.get_celery_status()
        
        response_data = {
            "success": success_count > 0,
            "message": message,
            "data": {
                "action": action,
                "service": service,
                "results": {k: {"success": v[0], "message": v[1]} for k, v in results.items()},
                "status": current_status
            }
        }
        
        if status_code == 500:
            raise HTTPException(status_code=500, detail=response_data)
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"控制Celery服务失败: {str(e)}")


@router.get("/config")
async def get_celery_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取Celery配置"""
    try:
        from app.core.config import settings
        
        config = {
            "redis_host": settings.REDIS_HOST,
            "redis_port": settings.REDIS_PORT,
            "redis_db": settings.REDIS_DB,
            "redis_password": "***" if settings.REDIS_PASSWORD else "",
            "worker_concurrency": 4,
            "worker_prefetch_multiplier": 1,
            "task_soft_time_limit": 300,
            "task_time_limit": 600,
            "task_max_retries": 3,
            "flower_port": 5555,
            "flower_basic_auth": "admin:password"
        }
        
        return {
            "success": True,
            "message": "获取Celery配置成功",
            "data": config
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Celery配置失败: {str(e)}")


@router.get("/metrics")
async def get_celery_metrics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取Celery性能指标"""
    try:
        # 获取Redis连接
        redis_client = celery_service._get_redis_client()
        
        # 获取基本指标
        metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "active_workers": 0,
            "total_workers": 0,
            "active_tasks": 0,
            "processed_tasks": 0,
            "failed_tasks": 0,
            "queue_lengths": {},
            "avg_task_runtime": 0.0,
            "task_throughput": 0.0,
            "redis_memory_usage": 0.0,
            "redis_connected_clients": 0
        }
        
        try:
            # 获取Redis信息
            redis_info = redis_client.info()
            metrics["redis_memory_usage"] = redis_info.get("used_memory", 0) / (1024 * 1024)  # MB
            metrics["redis_connected_clients"] = redis_info.get("connected_clients", 0)
            
            # 获取队列长度
            queue_names = ["default", "high_priority", "low_priority"]
            for queue_name in queue_names:
                try:
                    length = redis_client.llen(queue_name)
                    metrics["queue_lengths"][queue_name] = length
                except:
                    metrics["queue_lengths"][queue_name] = 0
            
        except Exception as e:
            print(f"获取Redis指标失败: {e}")
        
        # 获取进程信息
        processes = celery_service.get_celery_processes()
        metrics["total_workers"] = 1 if processes["worker"]["running"] else 0
        metrics["active_workers"] = 1 if processes["worker"]["running"] else 0
        
        return {
            "success": True,
            "message": "获取Celery指标成功",
            "data": metrics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Celery指标失败: {str(e)}")


@router.get("/process-status")
async def get_celery_process_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取Celery进程状态"""
    try:
        redis_connected = celery_service.check_redis_connection()
        services = celery_service.get_celery_processes()
        
        # 计算整体状态
        running_services = sum(1 for service in services.values() if service['running'])
        if running_services == 3:
            overall_status = 'running'
        elif running_services == 0:
            overall_status = 'stopped'
        else:
            overall_status = 'partial'
        
        return {
            "success": True,
            "message": "获取进程状态成功",
            "data": {
                "redis_connected": redis_connected,
                "services": services,
                "overall_status": overall_status
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取进程状态失败: {str(e)}")


@router.get("/redis-check")
async def check_redis_connection(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """检查Redis连接"""
    try:
        connected = celery_service.check_redis_connection()
        
        return {
            "success": True,
            "message": "Redis连接检查完成",
            "data": {
                "connected": connected
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis连接检查失败: {str(e)}")


@router.get("/auto-start")
async def get_auto_start_setting(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取自动启动设置"""
    try:
        # TODO: 从数据库或配置文件读取自动启动设置
        return {
            "success": True,
            "message": "获取自动启动设置成功",
            "data": {
                "enabled": False
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取自动启动设置失败: {str(e)}")


@router.post("/auto-start")
async def set_auto_start_setting(
    request: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """设置自动启动"""
    try:
        enabled = request.get("enabled", False)
        
        # TODO: 保存自动启动设置到数据库或配置文件
        
        return {
            "success": True,
            "message": f"自动启动已{'开启' if enabled else '关闭'}",
            "data": {
                "enabled": enabled
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置自动启动失败: {str(e)}")
