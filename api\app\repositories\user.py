"""
用户Repository
处理用户相关的数据访问操作
"""

from typing import Optional, List, Dict, Any
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.models.user import User
from app.repositories.base import BaseRepository
from app.schemas.user import UserCreate, UserUpdate


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """用户Repository"""
    
    def __init__(self, session: AsyncSession):
        super().__init__(User, session)
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            result = await self.session.execute(
                select(User).where(User.username == username)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting user by username {username}: {e}")
            return None
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        try:
            result = await self.session.execute(
                select(User).where(User.email == email)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            return None
    
    async def get_by_username_or_email(self, identifier: str) -> Optional[User]:
        """根据用户名或邮箱获取用户"""
        try:
            result = await self.session.execute(
                select(User).where(
                    or_(User.username == identifier, User.email == identifier)
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting user by username or email {identifier}: {e}")
            return None
    
    async def check_username_exists(self, username: str, exclude_id: int = None) -> bool:
        """检查用户名是否已存在"""
        try:
            query = select(func.count(User.id)).where(User.username == username)
            if exclude_id:
                query = query.where(User.id != exclude_id)
            
            result = await self.session.execute(query)
            return result.scalar() > 0
        except Exception as e:
            logger.error(f"Error checking username existence {username}: {e}")
            return False
    
    async def check_email_exists(self, email: str, exclude_id: int = None) -> bool:
        """检查邮箱是否已存在"""
        try:
            query = select(func.count(User.id)).where(User.email == email)
            if exclude_id:
                query = query.where(User.id != exclude_id)
            
            result = await self.session.execute(query)
            return result.scalar() > 0
        except Exception as e:
            logger.error(f"Error checking email existence {email}: {e}")
            return False
    
    async def get_active_users(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[User]:
        """获取活跃用户列表"""
        return await self.get_multi(
            skip=skip,
            limit=limit,
            filters={"is_active": True},
            order_by="created_at",
            desc=True
        )
    
    async def get_superusers(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[User]:
        """获取超级用户列表"""
        return await self.get_multi(
            skip=skip,
            limit=limit,
            filters={"is_superuser": True},
            order_by="created_at",
            desc=True
        )
    
    async def search_users(
        self, 
        query: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[User]:
        """搜索用户"""
        try:
            search_query = select(User).where(
                or_(
                    User.username.ilike(f"%{query}%"),
                    User.email.ilike(f"%{query}%"),
                    User.full_name.ilike(f"%{query}%")
                )
            ).offset(skip).limit(limit).order_by(User.created_at.desc())
            
            result = await self.session.execute(search_query)
            return result.scalars().all()
        except Exception as e:
            logger.error("Error searching users with query {}: {}".format(query, str(e)))
            return []
    
    async def update_last_login(self, user_id: int) -> bool:
        """更新用户最后登录时间"""
        try:
            from datetime import datetime
            
            user = await self.get(user_id)
            if user:
                user.last_login_at = datetime.utcnow()
                user.login_count = (user.login_count or 0) + 1
                await self.session.commit()
                logger.info("Updated last login for user {}".format(user_id))
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error("Error updating last login for user {}: {}".format(user_id, str(e)))
            return False

    async def activate_user(self, user_id: int) -> bool:
        """激活用户"""
        try:
            user = await self.get(user_id)
            if user:
                user.is_active = True
                await self.session.commit()
                logger.info("Activated user {}".format(user_id))
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error("Error activating user {}: {}".format(user_id, str(e)))
            return False

    async def deactivate_user(self, user_id: int) -> bool:
        """停用用户"""
        try:
            user = await self.get(user_id)
            if user:
                user.is_active = False
                await self.session.commit()
                logger.info("Deactivated user {}".format(user_id))
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error("Error deactivating user {}: {}".format(user_id, str(e)))
            return False

    async def change_password(self, user_id: int, hashed_password: str) -> bool:
        """修改用户密码"""
        try:
            user = await self.get(user_id)
            if user:
                user.hashed_password = hashed_password
                await self.session.commit()
                logger.info("Changed password for user {}".format(user_id))
                return True
            return False
        except Exception as e:
            await self.session.rollback()
            logger.error("Error changing password for user {}: {}".format(user_id, str(e)))
            return False
    
    async def get_user_stats(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            # 总用户数
            total_users = await self.count()
            
            # 活跃用户数
            active_users = await self.count({"is_active": True})
            
            # 超级用户数
            superusers = await self.count({"is_superuser": True})
            
            # 最近30天注册用户数
            from datetime import datetime, timedelta
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            recent_query = select(func.count(User.id)).where(
                User.created_at >= thirty_days_ago
            )
            result = await self.session.execute(recent_query)
            recent_users = result.scalar() or 0
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "inactive_users": total_users - active_users,
                "superusers": superusers,
                "recent_users": recent_users
            }
        except Exception as e:
            logger.error("Error getting user stats: {}".format(str(e)))
            return {
                "total_users": 0,
                "active_users": 0,
                "inactive_users": 0,
                "superusers": 0,
                "recent_users": 0
            }
