"""
认证相关的Pydantic Schema
"""

from typing import Optional
from pydantic import BaseModel, Field


class Token(BaseModel):
    """令牌基础Schema"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")


class TokenData(BaseModel):
    """令牌数据Schema"""
    username: Optional[str] = Field(None, description="用户名")
    user_id: Optional[int] = Field(None, description="用户ID")


class TokenResponse(BaseModel):
    """令牌响应Schema"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")


class PasswordResetRequest(BaseModel):
    """密码重置请求Schema"""
    email: str = Field(..., description="邮箱地址")


class PasswordResetConfirm(BaseModel):
    """密码重置确认Schema"""
    token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")


class EmailVerificationRequest(BaseModel):
    """邮箱验证请求Schema"""
    email: str = Field(..., description="邮箱地址")


class EmailVerificationConfirm(BaseModel):
    """邮箱验证确认Schema"""
    token: str = Field(..., description="验证令牌")


class ApiKeyCreate(BaseModel):
    """API密钥创建Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="API密钥名称")
    expires_days: Optional[int] = Field(365, ge=1, le=3650, description="过期天数")


class ApiKeyResponse(BaseModel):
    """API密钥响应Schema"""
    id: int = Field(..., description="密钥ID")
    name: str = Field(..., description="密钥名称")
    key: str = Field(..., description="API密钥")
    created_at: str = Field(..., description="创建时间")
    expires_at: str = Field(..., description="过期时间")
    is_active: bool = Field(..., description="是否激活")


class PasswordStrengthResponse(BaseModel):
    """密码强度响应Schema"""
    is_strong: bool = Field(..., description="是否强密码")
    score: int = Field(..., description="强度评分")
    issues: list[str] = Field(..., description="问题列表")
