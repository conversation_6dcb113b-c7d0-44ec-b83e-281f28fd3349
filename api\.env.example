# 环境配置示例文件
# 复制此文件为 .env.local 并根据需要修改配置

# 应用基础配置
APP_NAME="AI知识库 API"
APP_VERSION="0.1.0"
APP_DESCRIPTION="AI知识库 - 基于人工智能的智能知识管理与检索系统"
ENVIRONMENT="development"
DEBUG=true
HOST="0.0.0.0"
PORT=8000
LOG_LEVEL="INFO"

# 安全配置
SECRET_KEY="your-secret-key-here-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM="HS256"

# 数据库配置 - PostgreSQL
DB_USERNAME=postgres
DB_PASSWORD=XHC12345
DB_HOST=**************
DB_PORT=5432
DB_DATABASE=xhc_rag
DATABASE_URL=postgresql+asyncpg://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}

# 备用数据库配置
# SQLite (开发环境)
# DATABASE_URL="sqlite+aiosqlite:///./ai_knowledge_base.db"
# MySQL
# DATABASE_URL=mysql+aiomysql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}

DATABASE_TYPE=postgresql
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_TIMEOUT=30
DATABASE_RETRY_ATTEMPTS=3

# Redis配置
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=XHC12345
REDIS_USE_SSL=false
REDIS_DB=10
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# Celery配置
CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/11
CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/12

# 文件存储配置
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES="jpg,jpeg,png,gif,pdf,txt,doc,docx"

# 邮件配置
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_TLS=true

# 第三方服务配置
OPENAI_API_KEY=""
OPENAI_BASE_URL="https://api.openai.com/v1"

# 插件配置
PLUGINS_DIR="plugins"
ENABLED_PLUGINS="auth,file_manager,rag_engine"

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# CORS配置
CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_ALLOW_HEADERS="*"
