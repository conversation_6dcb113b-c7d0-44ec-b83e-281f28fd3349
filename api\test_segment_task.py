#!/usr/bin/env python3
"""
测试文档分段任务的脚本
用于调试分段功能问题
"""

import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_sync_session
from app.models.document_segment import DocumentSegmentTask, DocumentSegment, SegmentStatus, SegmentMethod
from app.tasks.segment_tasks import process_batch_segment_task, process_single_file_segment


def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        db = next(get_sync_session())
        print("✅ 数据库连接成功")
        
        # 测试查询
        tasks = db.query(DocumentSegmentTask).limit(5).all()
        print(f"✅ 查询到 {len(tasks)} 个分段任务")
        
        segments = db.query(DocumentSegment).limit(5).all()
        print(f"✅ 查询到 {len(segments)} 个分段记录")
        
        db.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


def test_create_segment_task():
    """测试创建分段任务"""
    print("\n=== 测试创建分段任务 ===")
    try:
        db = next(get_sync_session())
        
        # 创建测试任务
        task = DocumentSegmentTask(
            task_name="测试分段任务",
            description="用于测试的分段任务",
            file_ids=["test_file_1", "test_file_2"],
            total_files=2,
            segment_method=SegmentMethod.PARAGRAPH,
            max_length=1000,
            overlap=100,
            enable_vectorization=False,  # 暂时禁用向量化
            status=SegmentStatus.PENDING
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✅ 创建分段任务成功: ID={task.id}, task_id={task.task_id}")
        
        db.close()
        return task.id
    except Exception as e:
        print(f"❌ 创建分段任务失败: {e}")
        return None


def test_celery_task(task_id: int):
    """测试Celery任务执行"""
    print(f"\n=== 测试Celery任务执行 (task_id={task_id}) ===")
    try:
        # 直接调用任务函数（同步测试）
        result = process_batch_segment_task(task_id)
        print(f"✅ Celery任务执行结果: {result}")
        return True
    except Exception as e:
        print(f"❌ Celery任务执行失败: {e}")
        return False


def test_single_file_segment(task_id: int, file_id: str):
    """测试单文件分段"""
    print(f"\n=== 测试单文件分段 (task_id={task_id}, file_id={file_id}) ===")
    try:
        # 直接调用单文件分段函数
        result = process_single_file_segment(task_id, file_id, 0)
        print(f"✅ 单文件分段结果: {result}")
        
        # 检查数据库中的分段记录
        db = next(get_sync_session())
        segments = db.query(DocumentSegment).filter(
            DocumentSegment.task_id == task_id,
            DocumentSegment.file_id == file_id
        ).all()
        print(f"✅ 数据库中创建了 {len(segments)} 个分段记录")
        
        for i, segment in enumerate(segments[:3]):  # 只显示前3个
            print(f"  分段 {i+1}: {segment.content[:50]}...")
        
        db.close()
        return True
    except Exception as e:
        print(f"❌ 单文件分段失败: {e}")
        return False


def check_task_status(task_id: int):
    """检查任务状态"""
    print(f"\n=== 检查任务状态 (task_id={task_id}) ===")
    try:
        db = next(get_sync_session())
        task = db.query(DocumentSegmentTask).filter(DocumentSegmentTask.id == task_id).first()
        
        if task:
            print(f"任务状态: {task.status}")
            print(f"进度: {task.progress}%")
            print(f"已处理文件: {task.processed_files}/{task.total_files}")
            print(f"总分段数: {task.total_segments}")
            print(f"错误信息: {task.error_message or '无'}")
            
            # 检查分段记录
            segments = db.query(DocumentSegment).filter(DocumentSegment.task_id == task_id).all()
            print(f"分段记录数: {len(segments)}")
            
            if segments:
                print("分段详情:")
                for segment in segments[:5]:  # 只显示前5个
                    print(f"  - 文件: {segment.file_id}, 索引: {segment.segment_index}, 长度: {len(segment.content)}")
        else:
            print("❌ 任务不存在")
        
        db.close()
    except Exception as e:
        print(f"❌ 检查任务状态失败: {e}")


def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    try:
        db = next(get_sync_session())
        
        # 删除测试分段记录
        segments_deleted = db.query(DocumentSegment).filter(
            DocumentSegment.file_id.like('test_file_%')
        ).delete(synchronize_session=False)
        
        # 删除测试任务
        tasks_deleted = db.query(DocumentSegmentTask).filter(
            DocumentSegmentTask.task_name.like('测试%')
        ).delete(synchronize_session=False)
        
        db.commit()
        print(f"✅ 清理完成: 删除 {tasks_deleted} 个任务, {segments_deleted} 个分段记录")
        
        db.close()
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")


def main():
    """主测试函数"""
    print("🚀 开始文档分段功能测试")
    print("=" * 50)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("❌ 数据库连接失败，退出测试")
        return
    
    # 2. 创建测试任务
    task_id = test_create_segment_task()
    if not task_id:
        print("❌ 创建测试任务失败，退出测试")
        return
    
    # 3. 测试单文件分段
    if test_single_file_segment(task_id, "test_file_1"):
        print("✅ 单文件分段测试通过")
    
    # 4. 检查任务状态
    check_task_status(task_id)
    
    # 5. 测试Celery任务（可选）
    print("\n是否测试完整的Celery任务流程？(y/n): ", end="")
    if input().lower() == 'y':
        test_celery_task(task_id)
        check_task_status(task_id)
    
    # 6. 清理测试数据
    print("\n是否清理测试数据？(y/n): ", end="")
    if input().lower() == 'y':
        cleanup_test_data()
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    main()
