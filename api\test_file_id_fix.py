#!/usr/bin/env python3
"""
测试修复后的文件ID编码解码
"""
import urllib.parse

def test_file_id_encoding():
    # 测试文件路径
    original_path = "/1-深圳市政务云服务申请表-民生警务平台汇总20250605.docx"
    storage_id = 1
    
    print("=== 文件ID编码测试 ===")
    print(f"原始路径: {original_path}")
    
    # 生成file_id (模拟SimpleFileManagerService的逻辑)
    normalized_path = original_path if original_path.startswith('/') else '/' + original_path
    encoded_path = urllib.parse.quote(normalized_path, safe='')
    file_id = f"local_{storage_id}_{encoded_path}"
    
    print(f"标准化路径: {normalized_path}")
    print(f"编码后路径: {encoded_path}")
    print(f"生成的file_id: {file_id}")
    
    print("\n=== 文件ID解码测试 ===")
    
    # 解析file_id (模拟API端点的逻辑)
    parts = file_id.split('_', 2)
    if len(parts) >= 3:
        storage_type = parts[0]
        storage_id_parsed = int(parts[1])
        encoded_path_parsed = parts[2]
        
        # URL解码文件路径
        decoded_path = urllib.parse.unquote(encoded_path_parsed)
        
        # 确保路径格式正确
        if not decoded_path.startswith('/'):
            decoded_path = '/' + decoded_path
        
        print(f"存储类型: {storage_type}")
        print(f"存储ID: {storage_id_parsed}")
        print(f"编码路径: {encoded_path_parsed}")
        print(f"解码路径: {decoded_path}")
        print(f"路径匹配: {original_path == decoded_path}")
        
        # 模拟文件系统路径处理
        clean_path = decoded_path.lstrip('/')
        print(f"清理后路径: {clean_path}")
        
        import os
        base_path = "./storage"
        full_path = os.path.join(base_path, clean_path)
        full_path = os.path.normpath(full_path)
        print(f"完整文件路径: {full_path}")
    
    print("\n=== 前端编码测试 ===")
    
    # 模拟前端的base64编码
    import base64
    frontend_encoded = base64.b64encode(urllib.parse.quote(file_id, safe='').encode('utf-8')).decode('ascii')
    print(f"前端编码: {frontend_encoded}")
    
    # 模拟前端解码
    frontend_decoded = urllib.parse.unquote(base64.b64decode(frontend_encoded.encode('ascii')).decode('utf-8'))
    print(f"前端解码: {frontend_decoded}")
    print(f"前端编码匹配: {file_id == frontend_decoded}")

if __name__ == '__main__':
    test_file_id_encoding()
