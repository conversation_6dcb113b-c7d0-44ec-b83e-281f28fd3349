"""
数据模型包
包含所有数据库模型定义
"""

from app.core.database import Base
from .base import BaseModel
from .user import User
from .file_management import StorageConfig
from .storage_stats import StorageStats, StorageStatsHistory
from .upload_task import UploadTask
from .system import SystemConfig, SystemInitStatus, Dictionary, OperationLog
from .document_segment import DocumentSegmentTask, DocumentSegment, SegmentTemplate, VectorIndex
from .knowledge_base import KnowledgeBase, KnowledgeBaseDocument, KnowledgeBaseVector, KnowledgeBaseConfig, KnowledgeBaseAccessLog

# 所有模型已导入，关系已在模型中定义

# 导出所有模型
__all__ = [
    "Base",
    "BaseModel",
    "User",
    "StorageConfig",
    "StorageStats",
    "StorageStatsHistory",
    "UploadTask",
    "SystemConfig",
    "SystemInitStatus",
    "Dictionary",
    "OperationLog",
    "DocumentSegmentTask",
    "DocumentSegment",
    "SegmentTemplate",
    "VectorIndex",
    "KnowledgeBase",
    "KnowledgeBaseDocument",
    "KnowledgeBaseVector",
    "KnowledgeBaseConfig",
    "KnowledgeBaseAccessLog",
]
