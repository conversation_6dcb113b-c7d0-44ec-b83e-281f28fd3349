'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CheckCircle,
  AlertCircle,
  Settings,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import apiClient from '@/lib/api';

interface SystemStatus {
  needs_setup: boolean;
  tables: {
    all_exist: boolean;
    existing: string[];
    missing: string[];
  };
  has_admin_user: boolean;
  has_storage_config: boolean;
  setup_steps: string[];
}

interface SetupFormData {
  username: string;
  password: string;
  confirmPassword: string;
  email: string;
  fullName: string;
  storagePath: string;
}

const FirstTimeSetupPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [setupLoading, setSetupLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<SetupFormData>({
    username: 'admin',
    password: 'password',
    confirmPassword: 'password',
    email: '<EMAIL>',
    fullName: '系统管理员',
    storagePath: './storage'
  });

  // 检查系统状态
  const checkSystemStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get('/api/v1/setup/status');

      // 检查响应结构
      if (!response.data || !response.data.success) {
        throw new Error('API响应格式错误');
      }

      const status = response.data.data;

      // 验证状态数据结构
      if (!status || typeof status.needs_setup === 'undefined') {
        throw new Error('系统状态数据格式错误');
      }

      setSystemStatus(status);

      // 如果不需要设置，跳转到登录页
      if (!status.needs_setup) {
        router.push('/login');
        return;
      }

    } catch (err: any) {
      console.error('检查系统状态失败:', err);
      const errorMessage = err.response?.data?.detail || err.message || '检查系统状态失败';
      setError(errorMessage);

      // 设置默认状态，允许用户继续设置
      setSystemStatus({
        needs_setup: true,
        tables: { all_exist: false, existing: [], missing: [] },
        has_admin_user: false,
        has_storage_config: false,
        setup_steps: ['数据库初始化', '管理员创建', '存储配置']
      });
    } finally {
      setLoading(false);
    }
  };

  // 执行系统初始化
  const handleSetup = async () => {
    if (formData.password !== formData.confirmPassword) {
      setError('密码确认不匹配');
      return;
    }

    try {
      setSetupLoading(true);
      setError(null);

      const response = await apiClient.post('/api/v1/setup/initialize', {
        admin_user: {
          username: formData.username,
          password: formData.password,
          email: formData.email,
          full_name: formData.fullName
        },
        storage_path: formData.storagePath
      });

      if (response.data.success) {
        // 设置成功，跳转到登录页
        router.push('/login?message=setup_complete');
      }

    } catch (err: any) {
      console.error('系统初始化失败:', err);
      setError(err.response?.data?.detail?.message || err.response?.data?.detail || '系统初始化失败');
    } finally {
      setSetupLoading(false);
    }
  };

  useEffect(() => {
    checkSystemStatus();
  }, []);

  // 渲染加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">检查系统状态中...</p>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (error && !systemStatus) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">系统检查失败</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={checkSystemStatus}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full mb-6 shadow-lg"
          >
            <Settings className="w-10 h-10" />
          </motion.div>
          <h1 className="text-4xl font-bold text-gray-900 mb-3">AI知识库 - 首次设置</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            欢迎使用AI知识库！请完成以下设置来初始化您的系统
          </p>
        </div>



        {/* 主要内容 */}
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-xl p-8 border border-gray-100"
          >
            {/* 系统状态 */}
            {systemStatus && (
              <div className="mb-8">
                <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    系统状态检查
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center space-x-3">
                      {systemStatus.tables?.all_exist ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-yellow-500" />
                      )}
                      <span className="text-sm text-gray-700">数据库表结构</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      {systemStatus.has_admin_user ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-yellow-500" />
                      )}
                      <span className="text-sm text-gray-700">管理员账户</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      {systemStatus.has_storage_config ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-yellow-500" />
                      )}
                      <span className="text-sm text-gray-700">存储配置</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 设置表单 */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">管理员账户设置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      用户名 *
                    </label>
                    <input
                      type="text"
                      value={formData.username}
                      onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱 *
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      密码 *
                    </label>
                    <input
                      type="password"
                      value={formData.password}
                      onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      确认密码 *
                    </label>
                    <input
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      姓名
                    </label>
                    <input
                      type="text"
                      value={formData.fullName}
                      onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">存储配置</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    默认存储路径
                  </label>
                  <input
                    type="text"
                    value={formData.storagePath}
                    onChange={(e) => setFormData(prev => ({ ...prev, storagePath: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="./storage"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    文件将存储在此目录中，如果目录不存在将自动创建
                  </p>
                </div>
              </div>

              {/* 错误信息 */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="flex items-center">
                    <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                    <span className="text-red-700">{error}</span>
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                <button
                  onClick={checkSystemStatus}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  重新检查
                </button>
                
                <button
                  onClick={handleSetup}
                  disabled={setupLoading}
                  className="flex items-center px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {setupLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      初始化中...
                    </>
                  ) : (
                    <>
                      开始初始化
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default FirstTimeSetupPage;
