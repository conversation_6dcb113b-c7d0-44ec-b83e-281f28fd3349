"""
多数据库支持配置
支持PostgreSQL、MySQL、Oracle、SQLite等数据库
"""

import os
from typing import AsyncGenerator, Optional, Dict, Any
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import StaticPool
from loguru import logger

from app.core.config import Settings

# 创建基础模型类
Base = declarative_base()

# 数据库引擎实例
async_engine = None
sync_engine = None
async_session_factory = None
sync_session_factory = None


class DatabaseConfig:
    """数据库配置类"""
    
    # 支持的数据库类型
    SUPPORTED_DATABASES = {
        'postgresql': {
            'async_driver': 'postgresql+asyncpg',
            'sync_driver': 'postgresql+psycopg2',
            'fallback_sync_driver': 'postgresql+asyncpg',  # 备用驱动
            'default_port': 5432,
        },
        'mysql': {
            'async_driver': 'mysql+aiomysql',
            'sync_driver': 'mysql+pymysql',
            'default_port': 3306,
        },
        'oracle': {
            'async_driver': 'oracle+oracledb',
            'sync_driver': 'oracle+oracledb',  # 优先使用现代驱动
            'fallback_sync_driver': 'oracle+cx_oracle',  # 备用传统驱动
            'default_port': 1521,
        },
        'sqlite': {
            'async_driver': 'sqlite+aiosqlite',
            'sync_driver': 'sqlite',
            'default_port': None,
        }
    }
    
    @classmethod
    def get_database_type(cls, database_url: str) -> str:
        """从数据库URL获取数据库类型"""
        # 清理URL，移除可能的多余部分
        url_lower = database_url.lower().strip()

        if url_lower.startswith('postgresql'):
            return 'postgresql'
        elif url_lower.startswith('mysql'):
            return 'mysql'
        elif url_lower.startswith('oracle'):
            return 'oracle'
        elif url_lower.startswith('sqlite'):
            return 'sqlite'
        else:
            raise ValueError(f"Unsupported database type in URL: {database_url}")
    
    @classmethod
    def get_async_url(cls, database_url: str) -> str:
        """将同步数据库URL转换为异步URL"""
        db_type = cls.get_database_type(database_url)
        config = cls.SUPPORTED_DATABASES[db_type]

        # 如果URL已经包含驱动信息，直接返回
        if '+' in database_url.split('://')[0]:
            return database_url

        if db_type == 'postgresql':
            return database_url.replace('postgresql://', f"{config['async_driver']}://")
        elif db_type == 'mysql':
            return database_url.replace('mysql://', f"{config['async_driver']}://")
        elif db_type == 'oracle':
            return database_url.replace('oracle://', f"{config['async_driver']}://")
        elif db_type == 'sqlite':
            return database_url.replace('sqlite://', f"{config['async_driver']}:///")

        return database_url
    
    @classmethod
    def get_sync_url(cls, database_url: str) -> str:
        """将异步数据库URL转换为同步URL"""
        db_type = cls.get_database_type(database_url)
        config = cls.SUPPORTED_DATABASES[db_type]

        # 如果URL已经是同步格式，检查驱动可用性
        if '+' in database_url.split('://')[0]:
            driver_part = database_url.split('://')[0]
            if 'asyncpg' in driver_part or 'aiomysql' in driver_part or 'aiosqlite' in driver_part or 'oracledb' in driver_part:
                # 需要转换为同步驱动
                pass
            else:
                # 已经是同步驱动，直接返回
                return database_url

        if db_type == 'postgresql':
            # 尝试使用可用的 PostgreSQL 驱动
            try:
                sync_driver = cls._get_available_postgresql_driver()
                return database_url.replace('postgresql+asyncpg://', f"{sync_driver}://").replace('postgresql://', f"{sync_driver}://")
            except ImportError:
                # 如果没有同步驱动，使用异步驱动
                return database_url.replace('postgresql://', 'postgresql+asyncpg://')
        elif db_type == 'mysql':
            return database_url.replace('mysql+aiomysql://', f"{config['sync_driver']}://").replace('mysql://', f"{config['sync_driver']}://")
        elif db_type == 'oracle':
            try:
                sync_driver = cls._get_available_oracle_driver()
                return database_url.replace('oracle+oracledb://', f"{sync_driver}://").replace('oracle://', f"{sync_driver}://")
            except ImportError:
                return database_url.replace('oracle://', 'oracle+oracledb://')
        elif db_type == 'sqlite':
            return database_url.replace('sqlite+aiosqlite://', f"{config['sync_driver']}://").replace('sqlite://', f"{config['sync_driver']}://")

        return database_url

    @classmethod
    def _get_available_oracle_driver(cls) -> str:
        """获取可用的 Oracle 驱动"""
        try:
            import oracledb
            return 'oracle+oracledb'
        except ImportError:
            try:
                import cx_Oracle
                return 'oracle+cx_oracle'
            except ImportError:
                raise ImportError(
                    "No Oracle driver available. Install either 'oracledb' or 'cx-oracle'.\n"
                    "For oracledb: uv add --optional oracle oracledb\n"
                    "For cx-oracle: uv add --optional oracle cx-oracle"
                )

    @classmethod
    def _get_available_postgresql_driver(cls) -> str:
        """获取可用的 PostgreSQL 同步驱动"""
        try:
            import psycopg2
            return 'postgresql+psycopg2'
        except ImportError:
            try:
                import asyncpg
                logger.warning("psycopg2 not available, using asyncpg for sync operations")
                return 'postgresql+asyncpg'
            except ImportError:
                raise ImportError(
                    "No PostgreSQL driver available. Install 'psycopg2-binary' or 'asyncpg'.\n"
                    "For psycopg2: uv add psycopg2-binary\n"
                    "For asyncpg: uv add asyncpg"
                )
    
    @classmethod
    def get_engine_kwargs(cls, database_url: str, is_async: bool = True) -> Dict[str, Any]:
        """获取数据库引擎配置参数"""
        db_type = cls.get_database_type(database_url)
        
        base_kwargs = {
            'echo': False,  # 将在settings中控制
            'future': True,
        }
        
        if db_type == 'sqlite':
            # SQLite特殊配置
            base_kwargs.update({
                'poolclass': StaticPool,
                'connect_args': {
                    'check_same_thread': False,
                    'timeout': 20,
                }
            })
        elif db_type == 'postgresql':
            # PostgreSQL配置
            base_kwargs.update({
                'pool_size': 10,
                'max_overflow': 20,
                'pool_timeout': 30,
                'pool_recycle': 3600,
            })
        elif db_type == 'mysql':
            # MySQL配置
            base_kwargs.update({
                'pool_size': 10,
                'max_overflow': 20,
                'pool_timeout': 30,
                'pool_recycle': 3600,
                'connect_args': {
                    'charset': 'utf8mb4',
                }
            })
        elif db_type == 'oracle':
            # Oracle配置
            base_kwargs.update({
                'pool_size': 10,
                'max_overflow': 20,
                'pool_timeout': 30,
                'pool_recycle': 3600,
            })
        
        return base_kwargs


async def init_database(settings: Settings) -> None:
    """初始化数据库连接"""
    global async_engine, sync_engine, async_session_factory, sync_session_factory

    try:
        # 获取数据库配置
        database_url = settings.database_url  # 使用动态生成的URL
        logger.info(f"Initializing database connection with URL: {database_url}")

        # 简化URL处理，确保格式正确
        if database_url.startswith('sqlite'):
            # SQLite 特殊处理
            if not database_url.startswith('sqlite+aiosqlite'):
                async_url = database_url.replace('sqlite://', 'sqlite+aiosqlite:///')
            else:
                async_url = database_url

            sync_url = async_url.replace('sqlite+aiosqlite://', 'sqlite:///')
            db_type = 'sqlite'

        elif database_url.startswith('postgresql'):
            if '+' not in database_url.split('://')[0]:
                async_url = database_url.replace('postgresql://', 'postgresql+asyncpg://')
            else:
                async_url = database_url

            try:
                sync_url = DatabaseConfig.get_sync_url(async_url)
            except ImportError:
                sync_url = async_url  # 使用异步驱动作为备用

            db_type = 'postgresql'

        else:
            # 其他数据库类型
            db_type = DatabaseConfig.get_database_type(database_url)
            async_url = DatabaseConfig.get_async_url(database_url)
            sync_url = DatabaseConfig.get_sync_url(database_url)

        logger.info(f"Using async URL: {async_url}")
        logger.info(f"Using sync URL: {sync_url}")

        # 创建异步引擎
        async_kwargs = DatabaseConfig.get_engine_kwargs(async_url, is_async=True)
        async_kwargs['echo'] = settings.DATABASE_ECHO

        async_engine = create_async_engine(async_url, **async_kwargs)

        # 创建同步引擎（用于Alembic迁移）
        sync_kwargs = DatabaseConfig.get_engine_kwargs(sync_url, is_async=False)
        sync_kwargs['echo'] = settings.DATABASE_ECHO

        sync_engine = create_engine(sync_url, **sync_kwargs)

        # 创建会话工厂
        async_session_factory = async_sessionmaker(
            async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )

        sync_session_factory = sessionmaker(
            sync_engine,
            expire_on_commit=False
        )

        logger.info(f"Database connection initialized successfully for {db_type}")

    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        logger.error(f"Database URL: {database_url}")
        raise


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    if async_session_factory is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    async with async_session_factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


def get_sync_session():
    """获取同步数据库会话"""
    if sync_session_factory is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    with sync_session_factory() as session:
        try:
            yield session
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()


async def create_tables() -> None:
    """创建数据库表"""
    try:
        # 使用简化的迁移方法
        from app.core.simple_migration import create_tables_directly

        logger.info("Creating database tables...")
        result = await create_tables_directly()

        if result["status"] == "error":
            raise RuntimeError(f"Table creation failed: {result.get('errors', ['Unknown error'])}")

        logger.success("✅ Database tables created successfully")

        # 显示创建结果
        for action in result["actions"]:
            logger.info(f"  {action}")

    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        raise


async def close_database() -> None:
    """关闭数据库连接"""
    global async_engine, sync_engine

    if async_engine:
        await async_engine.dispose()
        logger.info("Async database connection closed")

    if sync_engine:
        sync_engine.dispose()
        logger.info("Sync database connection closed")


# 数据库健康检查
async def check_database_health() -> Dict[str, Any]:
    """检查数据库连接健康状态"""
    if async_engine is None:
        return {
            "status": "error",
            "message": "Database not initialized"
        }

    if async_session_factory is None:
        return {
            "status": "error",
            "message": "Database session factory not initialized"
        }

    try:
        # 使用更简单的连接测试方法
        async with async_engine.begin() as conn:
            # 执行简单查询测试连接
            result = await conn.execute(text("SELECT 1"))
            row = result.fetchone()

            # 获取数据库类型
            db_url = str(async_engine.url)
            db_type = DatabaseConfig.get_database_type(db_url)

            # 获取连接池信息
            pool_info = {}
            try:
                if hasattr(async_engine.pool, 'size'):
                    pool_info["pool_size"] = async_engine.pool.size()
                if hasattr(async_engine.pool, 'checkedout'):
                    pool_info["checked_out"] = async_engine.pool.checkedout()
                if hasattr(async_engine.pool, 'checked_in'):
                    pool_info["checked_in"] = async_engine.pool.checked_in()
            except Exception:
                # 忽略连接池信息获取错误
                pass

            return {
                "status": "healthy",
                "database_type": db_type,
                "connection_test": "passed",
                **pool_info
            }
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "error",
            "message": str(e)
        }
