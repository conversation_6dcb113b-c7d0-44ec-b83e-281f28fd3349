<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能批量分段页面优化测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f1f5f9 0%, #e0f2fe 50%, #e8eaf6 100%);
        }
        .card-gradient {
            background: linear-gradient(135deg, rgba(255,255,255,0.6) 0%, rgba(255,255,255,0.4) 100%);
            backdrop-filter: blur(10px);
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="h-screen flex flex-col">
        <!-- 头部导航 - 占满整行 -->
        <div class="card-gradient shadow-sm border-b border-white/30 flex-shrink-0">
            <div class="w-full px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50/50 rounded-lg transition-all duration-200">
                            ← 返回
                        </button>

                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                                ✂️
                            </div>
                            <div>
                                <h1 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                                    AI智能批量分段
                                </h1>
                                <p class="text-sm text-gray-500 flex items-center space-x-1">
                                    🧠 <span id="fileCount">5 个文件待处理</span>
                                    <span>•</span>
                                    <span class="text-blue-600 font-medium" id="taskStatus">准备就绪</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3">
                        <!-- 添加文件按钮 - 移至头部 -->
                        <button class="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                            ➕ <span>添加文件</span>
                        </button>

                        <!-- 开始AI分段按钮 -->
                        <button id="startBtn" class="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                            ⚡ <span>开始AI分段</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex-1 flex overflow-hidden">
            <!-- 左侧：任务配置和分段配置 -->
            <div class="w-96 flex-shrink-0 card-gradient border-r border-white/30 overflow-y-auto">
                <!-- 任务配置区域 -->
                <div class="p-6 border-b border-gray-200/50">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                            ⚙️
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">任务配置</h3>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">任务名称 *</label>
                            <input type="text" value="批量分段任务 - 2024/6/17 15:30" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" placeholder="请输入任务名称">
                        </div>
                    </div>
                </div>

                <!-- 分段配置区域 -->
                <div class="p-6">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                            ✂️
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">分段配置</h3>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">分段方式</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                <option value="paragraph">按段落分段</option>
                                <option value="sentence">按句子分段</option>
                                <option value="fixed_length">固定长度分段</option>
                                <option value="semantic">语义分段</option>
                            </select>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">最大长度</label>
                                <input type="number" value="500" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">重叠长度</label>
                                <input type="number" value="50" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            </div>
                        </div>

                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">保留格式</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" checked class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">提取关键词</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：文件列表 -->
            <div class="flex-1 flex flex-col card-gradient overflow-hidden">
                <!-- 文件列表头部 - 带统计指标 -->
                <div class="p-6 border-b border-gray-200/50">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                📄
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900">文件分段统计</h3>
                                <p class="text-sm text-gray-500">实时监控分段进度和状态</p>
                            </div>
                        </div>
                    </div>

                    <!-- 统计指标卡片 -->
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        <!-- 文件总数 -->
                        <div class="stat-card bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs font-medium text-blue-600 uppercase tracking-wide">文件总数</p>
                                    <p class="text-2xl font-bold text-blue-900" id="totalFiles">5</p>
                                </div>
                                <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                    📄
                                </div>
                            </div>
                        </div>

                        <!-- 分段总数 -->
                        <div class="stat-card bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs font-medium text-purple-600 uppercase tracking-wide">分段总数</p>
                                    <p class="text-2xl font-bold text-purple-900" id="totalSegments">127</p>
                                </div>
                                <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                                    ✂️
                                </div>
                            </div>
                        </div>

                        <!-- 待分段 -->
                        <div class="stat-card bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-4 border border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs font-medium text-gray-600 uppercase tracking-wide">待分段</p>
                                    <p class="text-2xl font-bold text-gray-900" id="pendingFiles">3</p>
                                </div>
                                <div class="w-8 h-8 bg-gray-500 rounded-lg flex items-center justify-center">
                                    ⏳
                                </div>
                            </div>
                        </div>

                        <!-- 分段中 -->
                        <div class="stat-card bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-4 border border-yellow-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs font-medium text-yellow-600 uppercase tracking-wide">分段中</p>
                                    <p class="text-2xl font-bold text-yellow-900" id="processingFiles">1</p>
                                </div>
                                <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                                    🔄
                                </div>
                            </div>
                        </div>

                        <!-- 已完成 -->
                        <div class="stat-card bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs font-medium text-green-600 uppercase tracking-wide">已完成</p>
                                    <p class="text-2xl font-bold text-green-900" id="completedFiles">1</p>
                                </div>
                                <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                                    ✅
                                </div>
                            </div>
                        </div>

                        <!-- 分段失败 -->
                        <div class="stat-card bg-gradient-to-r from-red-50 to-rose-50 rounded-xl p-4 border border-red-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-xs font-medium text-red-600 uppercase tracking-wide">分段失败</p>
                                    <p class="text-2xl font-bold text-red-900" id="failedFiles">0</p>
                                </div>
                                <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                                    ❌
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件列表内容 -->
                <div class="flex-1 overflow-y-auto p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- 示例文件卡片 -->
                        <div class="bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center space-x-3 flex-1 min-w-0">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                        📄
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm font-medium text-gray-900 truncate">产品需求文档.docx</p>
                                        <p class="text-xs text-gray-500">2.5 MB • DOCX</p>
                                    </div>
                                </div>
                                <button class="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors">❌</button>
                            </div>
                            <div class="mb-3">
                                <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                                    <span>存储类型: LOCAL</span>
                                    <span>2024/6/17</span>
                                </div>
                            </div>
                            <!-- 进度条 -->
                            <div class="mt-3 pt-3 border-t border-gray-200/50">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-xs font-medium text-gray-700">已完成 (25 段)</span>
                                    <span class="text-xs text-gray-500">100%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-green-500 transition-all duration-300" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 更多示例文件... -->
                        <div class="bg-white/70 backdrop-blur-sm rounded-xl border border-gray-200/50 p-4 hover:shadow-md transition-all duration-200">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center space-x-3 flex-1 min-w-0">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                        📄
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm font-medium text-gray-900 truncate">技术方案.pdf</p>
                                        <p class="text-xs text-gray-500">1.8 MB • PDF</p>
                                    </div>
                                </div>
                                <button class="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors">❌</button>
                            </div>
                            <div class="mb-3">
                                <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                                    <span>存储类型: LOCAL</span>
                                    <span>2024/6/17</span>
                                </div>
                            </div>
                            <!-- 进度条 -->
                            <div class="mt-3 pt-3 border-t border-gray-200/50">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-xs font-medium text-gray-700">处理中...</span>
                                    <span class="text-xs text-gray-500">65%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-blue-500 transition-all duration-300" style="width: 65%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟实时统计更新
        function updateStats() {
            const stats = {
                totalFiles: Math.floor(Math.random() * 10) + 5,
                totalSegments: Math.floor(Math.random() * 200) + 100,
                pendingFiles: Math.floor(Math.random() * 5),
                processingFiles: Math.floor(Math.random() * 3),
                completedFiles: Math.floor(Math.random() * 8) + 1,
                failedFiles: Math.floor(Math.random() * 2)
            };

            document.getElementById('totalFiles').textContent = stats.totalFiles;
            document.getElementById('totalSegments').textContent = stats.totalSegments;
            document.getElementById('pendingFiles').textContent = stats.pendingFiles;
            document.getElementById('processingFiles').textContent = stats.processingFiles;
            document.getElementById('completedFiles').textContent = stats.completedFiles;
            document.getElementById('failedFiles').textContent = stats.failedFiles;
            document.getElementById('fileCount').textContent = `${stats.totalFiles} 个文件待处理`;
        }

        // 每3秒更新一次统计
        setInterval(updateStats, 3000);

        // 初始化
        updateStats();

        console.log('AI智能批量分段页面优化完成！');
        console.log('主要改进：');
        console.log('1. 头部导航占满整行');
        console.log('2. 添加文件按钮移至头部');
        console.log('3. 文件列表头部显示实时统计指标');
        console.log('4. 新增数据库表支持统计功能');
    </script>
</body>
</html>
