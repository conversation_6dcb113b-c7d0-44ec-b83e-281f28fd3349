"""
修复登录问题
解决SQLAlchemy模型关系导致的登录失败问题
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("=" * 60)
    print("修复登录问题")
    print("=" * 60)
    
    try:
        print("1. 测试模型导入...")
        
        # 测试基础导入
        from app.core.database import Base, engine
        print("   ✓ 数据库模块导入成功")
        
        from app.models.base import BaseModel
        print("   ✓ BaseModel 导入成功")
        
        from app.models.user import User
        print("   ✓ User 模型导入成功")
        
        from app.models.file_management import StorageConfig
        print("   ✓ StorageConfig 模型导入成功")
        
        from app.models.storage_stats import StorageStats, StorageStatsHistory
        print("   ✓ StorageStats 模型导入成功")
        
        print("2. 重新创建数据库表...")
        
        # 重新创建所有表
        Base.metadata.create_all(bind=engine)
        print("   ✓ 数据库表创建成功")
        
        print("3. 测试用户查询...")
        
        # 测试用户查询（登录时的关键操作）
        from app.core.database import get_db
        
        db = next(get_db())
        try:
            # 尝试查询用户表
            users = db.query(User).limit(1).all()
            print(f"   ✓ 用户查询成功，找到 {len(users)} 个用户")
            
            # 如果没有用户，创建默认管理员用户
            if len(users) == 0:
                print("   创建默认管理员用户...")
                from app.core.security import get_password_hash
                
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    hashed_password=get_password_hash("password"),
                    is_active=True,
                    is_superuser=True
                )
                
                db.add(admin_user)
                db.commit()
                print("   ✓ 默认管理员用户创建成功 (用户名: admin, 密码: password)")
            
        finally:
            db.close()
        
        print("4. 测试存储配置查询...")
        
        db = next(get_db())
        try:
            # 测试存储配置查询
            storages = db.query(StorageConfig).limit(1).all()
            print(f"   ✓ 存储配置查询成功，找到 {len(storages)} 个存储配置")
            
        finally:
            db.close()
        
        print("\n🎉 登录问题修复完成！")
        print("\n可以使用以下凭据登录：")
        print("用户名: admin")
        print("密码: password")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 修复成功！现在可以正常登录了。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")
    
    sys.exit(0 if success else 1)
