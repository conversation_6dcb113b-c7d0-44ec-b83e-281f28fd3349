<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库字段缺失问题修复完成</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
        }
        .status-badge {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 5px;
        }
        .error-badge {
            display: inline-block;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 10px 5px;
        }
        .fix-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #10b981;
            margin: 20px 0;
        }
        .fix-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .fix-icon {
            margin-right: 10px;
            font-size: 1.5rem;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            overflow-x: auto;
        }
        .error-block {
            background: #7f1d1d;
            color: #fecaca;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            border-left: 4px solid #ef4444;
        }
        .sql-block {
            background: #0f172a;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            border-left: 4px solid #3b82f6;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .success-box {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #047857;
        }
        .fix-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .fix-item {
            display: flex;
            align-items: flex-start;
            margin: 10px 0;
            padding: 8px 0;
        }
        .fix-item-icon {
            margin-right: 10px;
            margin-top: 2px;
            font-size: 1rem;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
        .comparison-card.before {
            border-left: 4px solid #ef4444;
        }
        .comparison-card.after {
            border-left: 4px solid #10b981;
        }
        .card-header {
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .card-icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 数据库字段缺失问题修复完成</h1>
            <p class="subtitle">content_hash 字段缺失和数据库表结构不匹配问题已解决</p>
            <div>
                <span class="error-badge">❌ content_hash 字段缺失</span>
                <span class="status-badge">✅ SQL脚本更新</span>
                <span class="status-badge">✅ API代码修复</span>
                <span class="status-badge">✅ 字段完整性</span>
            </div>
        </div>

        <!-- 错误详情 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🚨</span>
                错误详情
            </div>
            
            <div class="error-block">
sqlalchemy.exc.PendingRollbackError: This Session's transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (psycopg2.errors.UndefinedColumn) column "content_hash" of relation "document_segments" does not exist
LINE 1: ..._segments (segment_id, task_id, file_id, content, content_ha...
                                                             ^
            </div>
            
            <p><strong>问题原因：</strong></p>
            <ul class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">1️⃣</span>
                    <span>数据库模型中定义了 <code>content_hash</code> 字段，但SQL创建脚本中缺少此字段</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">2️⃣</span>
                    <span>API代码尝试插入 <code>content_hash</code> 值，但数据库表中不存在该列</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">3️⃣</span>
                    <span>模型定义与实际数据库表结构不匹配</span>
                </li>
            </ul>
        </div>

        <!-- 修复对比 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🔄</span>
                修复对比
            </div>
            
            <div class="comparison-grid">
                <div class="comparison-card before">
                    <div class="card-header">
                        <span class="card-icon">❌</span>
                        修复前（缺少字段）
                    </div>
                    <div class="sql-block">
-- 原始SQL（缺少字段）
CREATE TABLE document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id VARCHAR(36) NOT NULL,
    file_id VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    -- 缺少 content_hash 字段
    segment_index INTEGER NOT NULL,
    word_count INTEGER DEFAULT 0,
    -- 缺少其他字段...
);
                    </div>
                    <p><strong>问题：</strong>表结构不完整，缺少模型中定义的多个字段</p>
                </div>

                <div class="comparison-card after">
                    <div class="card-header">
                        <span class="card-icon">✅</span>
                        修复后（完整字段）
                    </div>
                    <div class="sql-block">
-- 修复后SQL（包含所有字段）
CREATE TABLE document_segments (
    id SERIAL PRIMARY KEY,
    segment_id VARCHAR(36) UNIQUE NOT NULL,
    task_id INTEGER NOT NULL,
    file_id VARCHAR(36) NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64),  -- 添加缺少的字段
    segment_index INTEGER NOT NULL,
    start_position INTEGER DEFAULT 0,
    end_position INTEGER DEFAULT 0,
    word_count INTEGER DEFAULT 0,
    sentence_count INTEGER DEFAULT 0,
    segment_metadata JSONB,
    keywords JSONB,
    vectorize_status VARCHAR(20) DEFAULT 'pending',
    vector_id VARCHAR(100),
    embedding_vector JSONB,
    quality_score FLOAT DEFAULT 0.0,
    readability_score FLOAT DEFAULT 0.0,
    -- 包含所有模型定义的字段
);
                    </div>
                    <p><strong>优势：</strong>完整的表结构，与模型定义完全匹配</p>
                </div>
            </div>
        </div>

        <!-- 修复内容 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🔧</span>
                具体修复内容
            </div>
            
            <ul class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">1️⃣</span>
                    <div>
                        <strong>创建完整SQL脚本</strong><br>
                        新建 <code>api/create_segment_tables_complete.sql</code>，包含所有模型字段
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">2️⃣</span>
                    <div>
                        <strong>修复API代码</strong><br>
                        在 <code>api/app/api/v1/document_segment.py</code> 中添加 content_hash 生成逻辑
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">3️⃣</span>
                    <div>
                        <strong>添加缺失字段</strong><br>
                        包含 content_hash, start_position, end_position, sentence_count 等字段
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">4️⃣</span>
                    <div>
                        <strong>优化数据类型</strong><br>
                        修正 task_id 为 INTEGER 类型，与外键关系匹配
                    </div>
                </li>
            </ul>
        </div>

        <!-- API代码修复 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">💻</span>
                API代码修复
            </div>
            
            <p>在 <code>api/app/api/v1/document_segment.py</code> 中添加了 content_hash 生成：</p>
            
            <div class="code-block">
# 添加 hashlib 导入
import hashlib

# 修复 DocumentSegment 创建
for j in range(segments_count):
    content = f"这是文件 {file_id} 的第 {j+1} 个分段内容..."
    content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()  # 生成内容哈希
    
    segment = DocumentSegment(
        task_id=task.id,
        file_id=file_id,
        content=content,
        content_hash=content_hash,  # 添加缺失的字段
        segment_index=j,
        start_position=j * 100,
        end_position=(j + 1) * 100,
        word_count=100,
        sentence_count=5,
        vectorize_status=VectorizeStatus.COMPLETED if task.enable_vectorization else VectorizeStatus.PENDING,
        quality_score=0.8,
        readability_score=0.7,
        keywords=["关键词1", "关键词2"] if task.extract_keywords else None
    )
            </div>
        </div>

        <!-- 完整SQL脚本 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">📝</span>
                完整SQL脚本特性
            </div>
            
            <p>新的 <code>create_segment_tables_complete.sql</code> 包含：</p>
            
            <ul class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">✅</span>
                    <span><strong>完整字段定义</strong> - 包含所有模型中定义的字段</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">✅</span>
                    <span><strong>正确数据类型</strong> - task_id 使用 INTEGER 类型</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">✅</span>
                    <span><strong>性能优化索引</strong> - 为关键字段创建索引</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">✅</span>
                    <span><strong>默认模板数据</strong> - 包含4个预设分段模板</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">✅</span>
                    <span><strong>向量索引配置</strong> - 预设向量索引配置</span>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">✅</span>
                    <span><strong>验证查询</strong> - 自动验证表创建结果</span>
                </li>
            </ul>
        </div>

        <!-- 执行步骤 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">🚀</span>
                执行步骤
            </div>
            
            <ol class="fix-list">
                <li class="fix-item">
                    <span class="fix-item-icon">1️⃣</span>
                    <div>
                        <strong>删除旧表（如果存在）</strong><br>
                        <code>DROP TABLE IF EXISTS document_segments CASCADE;</code>
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">2️⃣</span>
                    <div>
                        <strong>执行完整SQL脚本</strong><br>
                        运行 <code>api/create_segment_tables_complete.sql</code>
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">3️⃣</span>
                    <div>
                        <strong>验证表结构</strong><br>
                        检查所有字段是否正确创建
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">4️⃣</span>
                    <div>
                        <strong>重启后端服务</strong><br>
                        重启FastAPI服务以应用代码修复
                    </div>
                </li>
                <li class="fix-item">
                    <span class="fix-item-icon">5️⃣</span>
                    <div>
                        <strong>测试批量分段</strong><br>
                        验证功能是否正常工作
                    </div>
                </li>
            </ol>
        </div>

        <!-- 验证方法 -->
        <div class="fix-card">
            <div class="fix-title">
                <span class="fix-icon">✅</span>
                验证方法
            </div>
            
            <div class="sql-block">
-- 检查表结构
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'document_segments' 
ORDER BY ordinal_position;

-- 验证 content_hash 字段存在
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'document_segments' 
AND column_name = 'content_hash';

-- 检查模板数据
SELECT COUNT(*) as template_count FROM segment_templates;

-- 检查索引数量
SELECT COUNT(*) as index_count FROM vector_indexes;
            </div>
            
            <div class="success-box">
                <strong>预期结果：</strong><br>
                • document_segments 表包含所有字段（约17个字段）<br>
                • content_hash 字段存在且类型为 VARCHAR(64)<br>
                • 4个默认分段模板<br>
                • 2个默认向量索引<br>
                • 所有索引正确创建
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="action-button" onclick="copyCompleteSQL()">
                📋 复制完整SQL
            </button>
            <button class="action-button" onclick="showFieldComparison()">
                🔍 字段对比
            </button>
            <button class="action-button" onclick="showVerificationSteps()">
                ✅ 验证步骤
            </button>
            <button class="action-button" onclick="showTroubleshooting()">
                🛠️ 故障排除
            </button>
        </div>
    </div>

    <script>
        function copyCompleteSQL() {
            // 这里应该包含完整的SQL脚本内容
            alert('📋 完整SQL脚本内容较长，请直接使用文件：\n\napi/create_segment_tables_complete.sql\n\n该文件包含：\n• 完整的表结构定义\n• 所有必需字段\n• 性能优化索引\n• 默认模板和索引数据\n• 验证查询\n\n请在数据库管理工具中执行此文件。');
        }

        function showFieldComparison() {
            alert(`🔍 字段对比详情\n\n原始表缺少的字段：\n• content_hash VARCHAR(64) - 内容哈希值\n• start_position INTEGER - 开始位置\n• end_position INTEGER - 结束位置\n• sentence_count INTEGER - 句子数\n• segment_metadata JSONB - 分段元数据\n• vector_id VARCHAR(100) - 向量ID\n• embedding_vector JSONB - 嵌入向量\n• quality_score FLOAT - 质量评分\n• readability_score FLOAT - 可读性评分\n\n数据类型修正：\n• task_id: VARCHAR(36) → INTEGER\n• file_id: VARCHAR(500) → VARCHAR(36)\n\n新增索引：\n• idx_document_segments_content_hash\n• idx_document_segments_vectorize_status\n• 其他性能优化索引\n\n现在表结构与模型定义完全匹配！`);
        }

        function showVerificationSteps() {
            alert(`✅ 验证步骤详情\n\n1. 检查表结构\nSELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'document_segments';\n\n预期：约17个字段\n\n2. 验证关键字段\nSELECT column_name FROM information_schema.columns WHERE table_name = 'document_segments' AND column_name IN ('content_hash', 'start_position', 'end_position');\n\n预期：3个字段都存在\n\n3. 测试插入数据\nINSERT INTO document_segments (segment_id, task_id, file_id, content, content_hash, segment_index) VALUES ('test-123', 1, 'file-123', 'test content', 'hash123', 0);\n\n预期：插入成功\n\n4. 测试API调用\nPOST /api/v1/document-segment/tasks\n\n预期：不再出现字段缺失错误\n\n5. 检查模板数据\nSELECT COUNT(*) FROM segment_templates;\n\n预期：4个模板`);
        }

        function showTroubleshooting() {
            alert(`🛠️ 故障排除\n\n常见问题：\n\n1. 表已存在错误\n解决：先删除旧表\nDROP TABLE IF EXISTS document_segments CASCADE;\n\n2. 外键约束错误\n解决：按顺序创建表，先创建主表\n\n3. 字段类型不匹配\n解决：检查模型定义，确保SQL类型匹配\n\n4. 插入数据失败\n解决：检查必需字段是否都有值\n\n5. API仍然报错\n解决：\n• 重启后端服务\n• 检查数据库连接\n• 验证表结构\n• 查看详细错误日志\n\n6. 性能问题\n解决：确保索引已创建\nSELECT indexname FROM pg_indexes WHERE tablename = 'document_segments';\n\n调试技巧：\n• 使用 \\d document_segments 查看表结构\n• 检查 SQLAlchemy 日志\n• 使用数据库查询日志\n• 验证模型与表的一致性\n\n如果问题持续，请提供具体错误信息！`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据库字段缺失问题修复完成页面已加载');
        });
    </script>
</body>
</html>
