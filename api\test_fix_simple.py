#!/usr/bin/env python3
"""
简单的修复验证测试
"""
import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("开始验证模型修复...")
    
    try:
        # 测试基础模型导入
        print("1. 测试基础模型导入...")
        from app.models.base import BaseModel
        print("   ✓ BaseModel 导入成功")
        
        # 测试用户模型
        print("2. 测试用户模型导入...")
        from app.models.user import User
        print("   ✓ User 模型导入成功")
        
        # 测试存储配置模型
        print("3. 测试存储配置模型导入...")
        from app.models.file_management import StorageConfig
        print("   ✓ StorageConfig 模型导入成功")
        
        # 测试存储统计模型
        print("4. 测试存储统计模型导入...")
        from app.models.storage_stats import StorageStats, StorageStatsHistory
        print("   ✓ StorageStats 模型导入成功")
        
        # 测试模型初始化
        print("5. 测试模型初始化...")
        from app.models import *
        print("   ✓ 所有模型初始化成功")
        
        # 测试关系配置
        print("6. 测试关系配置...")
        if hasattr(StorageConfig, 'stats'):
            print("   ✓ StorageConfig.stats 关系已配置")
        else:
            print("   ⚠ StorageConfig.stats 关系未配置")
            
        if hasattr(StorageStats, 'storage'):
            print("   ✓ StorageStats.storage 关系已配置")
        else:
            print("   ⚠ StorageStats.storage 关系未配置")
        
        print("\n🎉 所有测试通过！模型修复成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
