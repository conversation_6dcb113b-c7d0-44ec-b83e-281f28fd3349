/**
 * 中文简体翻译文件
 */

import { Translations } from '../index';

const zhCN: Translations = {
  common: {
    loading: '加载中...',
    error: '错误',
    success: '成功',
    cancel: '取消',
    confirm: '确认',
    back: '返回',
    next: '下一步',
    submit: '提交',
    retry: '重试',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    filter: '筛选',
    export: '导出',
    import: '导入',
    refresh: '刷新',
  },
  
  login: {
    title: 'AI知识库',
    subtitle: '基于人工智能的智能知识管理与检索系统',
    welcomeBack: '欢迎回来',
    username: '用户名',
    password: '密码',
    rememberMe: '记住我',
    forgotPassword: '忘记密码？',
    loginButton: '登录',
    noAccount: '还没有账户？',
    signUp: '立即注册',
    loginSuccess: '登录成功！',
    loginError: '登录失败',
    invalidCredentials: '用户名或密码错误',
    networkError: '网络连接错误，请稍后重试',
    usernameRequired: '请输入用户名',
    passwordRequired: '请输入密码',
    usernameMinLength: '用户名至少需要3个字符',
    passwordMinLength: '密码至少需要6个字符',
  },
  
  ai: {
    poweredBy: '由AI驱动',
    intelligentSystem: '智能系统',
    secureLogin: '安全登录',
    aiAssistant: 'AI助手',
    smartAnalysis: '智能分析',
    dataProtection: '数据保护',
    knowledgeBase: '知识库',
    intelligentRetrieval: '智能检索',
    documentProcessing: '文档处理',
  },
  
  language: {
    current: '中文简体',
    switch: '切换语言',
    chinese: '中文简体',
    traditionalChinese: '中文繁体',
    english: 'English',
    japanese: '日本語',
  },
  
  navigation: {
    home: '首页',
    dashboard: '仪表板',
    documents: '文件管理',
    knowledge: '知识库',
    search: '智能搜索',
    analytics: '数据分析',
    settings: '系统设置',
    profile: '个人资料',
    logout: '退出登录',
    admin: '系统管理',
  },
  
  dashboard: {
    welcome: '欢迎回来',
    subtitle: '管理您的AI驱动的文档处理工作流',
    overview: '概览',
    statistics: '统计信息',
    recentActivity: '最近活动',
    quickActions: '快速操作',
    systemStatus: '系统状态',
    userInfo: '用户信息',
    totalDocuments: '文档总数',
    totalQueries: '查询总数',
    totalUsers: '用户总数',
    systemSettings: '系统设置',
    stats: {
      documents: '文档',
      queries: '查询',
      users: '用户',
      settings: '设置'
    },
    fileManager: {
      description: '上传、组织和管理您的文件库',
      stats: '125 个文件'
    },
    knowledge: {
      description: '构建和查询您的知识库',
      stats: '89 个条目'
    },
    search: {
      description: '使用AI进行智能内容搜索',
      stats: '1,234 次查询'
    },
    activities: {
      documentUploaded: '文档已上传',
      queryProcessed: '查询已处理',
      userLoggedIn: '用户已登录',
      settingsUpdated: '设置已更新'
    },
    actions: {
      uploadDocument: '上传文档',
      runQuery: '运行查询',
      manageUsers: '管理用户',
      systemSettings: '系统设置'
    }
  },
  
  documents: {
    title: '文档管理',
    upload: '上传文档',
    download: '下载',
    delete: '删除',
    preview: '预览',
    details: '详情',
    fileName: '文件名',
    fileSize: '文件大小',
    uploadTime: '上传时间',
    fileType: '文件类型',
    status: '状态',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    uploadSuccess: '上传成功',
    uploadError: '上传失败',
    deleteConfirm: '确定要删除这个文档吗？',
  },
  
  search: {
    title: '智能搜索',
    placeholder: '请输入搜索关键词...',
    results: '搜索结果',
    noResults: '未找到相关结果',
    searching: '搜索中...',
    advanced: '高级搜索',
    filters: '筛选条件',
    sortBy: '排序方式',
    relevance: '相关性',
    date: '日期',
    size: '大小',
  },
  
  settings: {
    title: '系统设置',
    general: '常规设置',
    account: '账户设置',
    security: '安全设置',
    notifications: '通知设置',
    language: '语言设置',
    theme: '主题设置',
    privacy: '隐私设置',
    about: '关于系统',
  },
  
  errors: {
    pageNotFound: '页面未找到',
    serverError: '服务器内部错误',
    networkError: '网络连接错误',
    unauthorized: '未授权访问',
    forbidden: '访问被禁止',
    validationError: '数据验证错误',
    unknownError: '未知错误',
  },
};

export default zhCN;
