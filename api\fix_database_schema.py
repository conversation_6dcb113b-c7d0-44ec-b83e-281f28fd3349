"""
修复数据库表结构
解决StorageStats外键约束问题
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def fix_database_schema():
    """修复数据库表结构"""
    try:
        print("开始修复数据库表结构...")
        
        # 导入必要的模块
        from app.core.database import engine, Base
        from app.models import *  # 导入所有模型
        
        print("✓ 模型导入成功")
        
        # 删除现有的storage_stats表（如果存在）
        from sqlalchemy import text
        
        with engine.connect() as conn:
            try:
                # 检查表是否存在
                result = conn.execute(text("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='storage_stats'
                """))
                
                if result.fetchone():
                    print("删除现有的storage_stats表...")
                    conn.execute(text("DROP TABLE IF EXISTS storage_stats"))
                    
                result = conn.execute(text("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='storage_stats_history'
                """))
                
                if result.fetchone():
                    print("删除现有的storage_stats_history表...")
                    conn.execute(text("DROP TABLE IF EXISTS storage_stats_history"))
                    
                conn.commit()
                print("✓ 旧表删除成功")
                
            except Exception as e:
                print(f"删除旧表时出错（可能表不存在）: {e}")
        
        # 重新创建所有表
        print("重新创建数据库表...")
        Base.metadata.create_all(bind=engine)
        print("✓ 数据库表创建成功")
        
        # 验证表结构
        print("验证表结构...")
        from sqlalchemy import inspect
        
        inspector = inspect(engine)
        
        # 检查storage_stats表
        if 'storage_stats' in inspector.get_table_names():
            columns = inspector.get_columns('storage_stats')
            foreign_keys = inspector.get_foreign_keys('storage_stats')
            
            print(f"✓ storage_stats表存在，包含 {len(columns)} 列")
            print(f"✓ storage_stats表包含 {len(foreign_keys)} 个外键")
            
            # 检查外键是否正确
            storage_id_fk = any(fk['constrained_columns'] == ['storage_id'] for fk in foreign_keys)
            if storage_id_fk:
                print("✓ storage_id外键约束正确")
            else:
                print("✗ storage_id外键约束缺失")
        else:
            print("✗ storage_stats表不存在")
        
        # 检查storage_stats_history表
        if 'storage_stats_history' in inspector.get_table_names():
            columns = inspector.get_columns('storage_stats_history')
            foreign_keys = inspector.get_foreign_keys('storage_stats_history')
            
            print(f"✓ storage_stats_history表存在，包含 {len(columns)} 列")
            print(f"✓ storage_stats_history表包含 {len(foreign_keys)} 个外键")
        else:
            print("✗ storage_stats_history表不存在")
        
        print("\n🎉 数据库表结构修复完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 数据库表结构修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_relationships():
    """测试模型关系"""
    try:
        print("\n测试模型关系...")
        
        from app.models.file_management import StorageConfig
        from app.models.storage_stats import StorageStats
        
        # 检查关系是否正确配置
        if hasattr(StorageConfig, 'stats'):
            print("✓ StorageConfig.stats 关系已配置")
        else:
            print("✗ StorageConfig.stats 关系未配置")
            
        if hasattr(StorageStats, 'storage'):
            print("✓ StorageStats.storage 关系已配置")
        else:
            print("✗ StorageStats.storage 关系未配置")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型关系测试失败: {e}")
        return False

def test_database_query():
    """测试数据库查询"""
    try:
        print("\n测试数据库查询...")
        
        from app.core.database import get_db
        from app.models.user import User
        
        # 获取数据库会话
        db = next(get_db())
        
        # 尝试查询用户（这是登录时会执行的查询）
        users = db.query(User).limit(1).all()
        print(f"✓ 用户查询成功，找到 {len(users)} 个用户")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库查询测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("数据库表结构修复工具")
    print("=" * 60)
    
    success = True
    
    # 1. 修复数据库表结构
    if not fix_database_schema():
        success = False
    
    # 2. 测试模型关系
    if not test_model_relationships():
        success = False
    
    # 3. 测试数据库查询
    if not test_database_query():
        success = False
    
    if success:
        print("\n🎉 所有修复和测试都成功完成！")
        print("现在可以正常登录系统了。")
        return 0
    else:
        print("\n❌ 部分修复或测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
