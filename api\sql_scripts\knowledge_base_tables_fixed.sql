-- 知识库管理相关表结构
-- 注意：所有表都不创建外键约束
-- 适用于PostgreSQL数据库

-- 1. 知识库表
CREATE TABLE IF NOT EXISTS knowledge_bases (
    id SERIAL PRIMARY KEY,
    kb_id VARCHAR(255) UNIQUE NOT NULL,  -- 知识库唯一标识
    name VARCHAR(255) NOT NULL,          -- 知识库名称
    description TEXT,                    -- 知识库描述
    avatar_url VARCHAR(500),             -- 知识库头像URL
    status VARCHAR(50) DEFAULT 'active', -- 状态：active, inactive, deleted
    visibility VARCHAR(50) DEFAULT 'private', -- 可见性：private, public, shared
    owner_id VARCHAR(255),               -- 所有者ID（关联用户表，但不创建外键）
    
    -- 统计信息
    document_count INTEGER DEFAULT 0,    -- 文档数量
    segment_count INTEGER DEFAULT 0,     -- 分段数量
    vector_count INTEGER DEFAULT 0,      -- 向量数量
    total_tokens INTEGER DEFAULT 0,      -- 总token数
    
    -- 配置信息
    embedding_model VARCHAR(255),        -- 嵌入模型
    vector_dimension INTEGER,            -- 向量维度
    chunk_size INTEGER DEFAULT 1000,     -- 分块大小
    chunk_overlap INTEGER DEFAULT 200,   -- 分块重叠
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_indexed_at TIMESTAMP            -- 最后索引时间
);

-- 为knowledge_bases表创建索引
CREATE INDEX IF NOT EXISTS idx_kb_id ON knowledge_bases(kb_id);
CREATE INDEX IF NOT EXISTS idx_kb_owner_id ON knowledge_bases(owner_id);
CREATE INDEX IF NOT EXISTS idx_kb_status ON knowledge_bases(status);
CREATE INDEX IF NOT EXISTS idx_kb_created_at ON knowledge_bases(created_at);

-- 2. 知识库文档关联表
CREATE TABLE IF NOT EXISTS knowledge_base_documents (
    id SERIAL PRIMARY KEY,
    kb_id VARCHAR(255) NOT NULL,         -- 知识库ID
    file_id VARCHAR(255) NOT NULL,       -- 文件ID（关联文件管理表，但不创建外键）
    document_name VARCHAR(255),          -- 文档名称
    document_path VARCHAR(500),          -- 文档路径
    file_size BIGINT,                    -- 文件大小
    file_type VARCHAR(100),              -- 文件类型
    
    -- 处理状态
    status VARCHAR(50) DEFAULT 'pending', -- 状态：pending, processing, completed, failed
    segment_status VARCHAR(50) DEFAULT 'pending', -- 分段状态
    vector_status VARCHAR(50) DEFAULT 'pending',  -- 向量化状态
    
    -- 统计信息
    segment_count INTEGER DEFAULT 0,     -- 分段数量
    vector_count INTEGER DEFAULT 0,      -- 向量数量
    token_count INTEGER DEFAULT 0,       -- token数量
    
    -- 处理信息
    error_message TEXT,                  -- 错误信息
    processing_log TEXT,                 -- 处理日志
    
    -- 时间戳
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,              -- 处理完成时间
    
    -- 唯一约束
    CONSTRAINT uk_kb_file UNIQUE (kb_id, file_id)
);

-- 为knowledge_base_documents表创建索引
CREATE INDEX IF NOT EXISTS idx_kb_documents_kb_id ON knowledge_base_documents(kb_id);
CREATE INDEX IF NOT EXISTS idx_kb_documents_file_id ON knowledge_base_documents(file_id);
CREATE INDEX IF NOT EXISTS idx_kb_documents_status ON knowledge_base_documents(status);

-- 3. 知识库向量表
CREATE TABLE IF NOT EXISTS knowledge_base_vectors (
    id SERIAL PRIMARY KEY,
    kb_id VARCHAR(255) NOT NULL,         -- 知识库ID
    document_id INTEGER,                 -- 文档关联ID（关联knowledge_base_documents表，但不创建外键）
    segment_id VARCHAR(255),             -- 分段ID（关联document_segments表，但不创建外键）
    
    -- 向量信息
    vector_id VARCHAR(255) UNIQUE NOT NULL, -- 向量唯一标识
    embedding_model VARCHAR(255),        -- 嵌入模型
    vector_dimension INTEGER,            -- 向量维度
    vector_data TEXT,                    -- 向量数据（JSON格式存储）
    
    -- 内容信息
    content TEXT,                        -- 原始内容
    content_hash VARCHAR(255),           -- 内容哈希
    meta_data JSONB,                     -- 元数据（使用JSONB类型，避免与metadata关键字冲突）
    
    -- 统计信息
    token_count INTEGER DEFAULT 0,       -- token数量
    quality_score DECIMAL(5,4),          -- 质量分数
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为knowledge_base_vectors表创建索引
CREATE INDEX IF NOT EXISTS idx_kb_vectors_kb_id ON knowledge_base_vectors(kb_id);
CREATE INDEX IF NOT EXISTS idx_kb_vectors_document_id ON knowledge_base_vectors(document_id);
CREATE INDEX IF NOT EXISTS idx_kb_vectors_segment_id ON knowledge_base_vectors(segment_id);
CREATE INDEX IF NOT EXISTS idx_kb_vectors_vector_id ON knowledge_base_vectors(vector_id);
CREATE INDEX IF NOT EXISTS idx_kb_vectors_model ON knowledge_base_vectors(embedding_model);
CREATE INDEX IF NOT EXISTS idx_kb_vectors_hash ON knowledge_base_vectors(content_hash);

-- 4. 知识库配置表
CREATE TABLE IF NOT EXISTS knowledge_base_configs (
    id SERIAL PRIMARY KEY,
    kb_id VARCHAR(255) NOT NULL,         -- 知识库ID
    config_type VARCHAR(100) NOT NULL,   -- 配置类型：embedding, retrieval, generation
    config_key VARCHAR(255) NOT NULL,    -- 配置键
    config_value TEXT,                   -- 配置值
    config_description TEXT,             -- 配置描述
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一约束
    CONSTRAINT uk_kb_config UNIQUE (kb_id, config_type, config_key)
);

-- 为knowledge_base_configs表创建索引
CREATE INDEX IF NOT EXISTS idx_kb_configs_kb_id ON knowledge_base_configs(kb_id);
CREATE INDEX IF NOT EXISTS idx_kb_configs_type ON knowledge_base_configs(config_type);

-- 5. 知识库访问日志表
CREATE TABLE IF NOT EXISTS knowledge_base_access_logs (
    id SERIAL PRIMARY KEY,
    kb_id VARCHAR(255) NOT NULL,         -- 知识库ID
    user_id VARCHAR(255),                -- 用户ID
    action VARCHAR(100),                 -- 操作类型：view, search, query, update
    query_text TEXT,                     -- 查询文本
    result_count INTEGER,                -- 结果数量
    response_time INTEGER,               -- 响应时间（毫秒）
    ip_address VARCHAR(45),              -- IP地址
    user_agent TEXT,                     -- 用户代理
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为knowledge_base_access_logs表创建索引
CREATE INDEX IF NOT EXISTS idx_kb_access_kb_id ON knowledge_base_access_logs(kb_id);
CREATE INDEX IF NOT EXISTS idx_kb_access_user_id ON knowledge_base_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_kb_access_action ON knowledge_base_access_logs(action);
CREATE INDEX IF NOT EXISTS idx_kb_access_created_at ON knowledge_base_access_logs(created_at);

-- 6. 更新document_segments表，添加知识库相关字段
ALTER TABLE document_segments 
ADD COLUMN IF NOT EXISTS kb_id VARCHAR(255),           -- 关联的知识库ID
ADD COLUMN IF NOT EXISTS vector_status VARCHAR(50) DEFAULT 'pending', -- 向量化状态
ADD COLUMN IF NOT EXISTS vector_id VARCHAR(255),       -- 向量ID
ADD COLUMN IF NOT EXISTS embedding_model VARCHAR(255), -- 嵌入模型
ADD COLUMN IF NOT EXISTS vector_dimension INTEGER;     -- 向量维度

-- 为document_segments表添加索引
CREATE INDEX IF NOT EXISTS idx_document_segments_kb_id ON document_segments(kb_id);
CREATE INDEX IF NOT EXISTS idx_document_segments_vector_status ON document_segments(vector_status);
CREATE INDEX IF NOT EXISTS idx_document_segments_vector_id ON document_segments(vector_id);

-- 7. 更新document_segment_tasks表，添加知识库相关字段
ALTER TABLE document_segment_tasks 
ADD COLUMN IF NOT EXISTS kb_id VARCHAR(255),           -- 关联的知识库ID
ADD COLUMN IF NOT EXISTS enable_vectorization BOOLEAN DEFAULT false; -- 是否启用向量化

-- 为document_segment_tasks表添加索引
CREATE INDEX IF NOT EXISTS idx_document_segment_tasks_kb_id ON document_segment_tasks(kb_id);

-- 插入默认的嵌入模型配置
INSERT INTO knowledge_base_configs (kb_id, config_type, config_key, config_value, config_description) VALUES
('default', 'embedding', 'available_models', '["text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large", "bge-large-zh-v1.5", "m3e-base"]', '可用的嵌入模型列表'),
('default', 'embedding', 'default_model', 'text-embedding-ada-002', '默认嵌入模型'),
('default', 'embedding', 'default_dimension', '1536', '默认向量维度'),
('default', 'retrieval', 'default_top_k', '10', '默认检索数量'),
('default', 'retrieval', 'similarity_threshold', '0.7', '相似度阈值')
ON CONFLICT (kb_id, config_type, config_key) DO NOTHING;

-- 创建知识库统计视图
CREATE OR REPLACE VIEW knowledge_base_stats AS
SELECT 
    kb.kb_id,
    kb.name,
    kb.status,
    kb.document_count,
    kb.segment_count,
    kb.vector_count,
    kb.created_at,
    kb.updated_at,
    COALESCE(doc_stats.actual_document_count, 0) as actual_document_count,
    COALESCE(seg_stats.actual_segment_count, 0) as actual_segment_count,
    COALESCE(vec_stats.actual_vector_count, 0) as actual_vector_count
FROM knowledge_bases kb
LEFT JOIN (
    SELECT kb_id, COUNT(*) as actual_document_count
    FROM knowledge_base_documents 
    WHERE status != 'deleted'
    GROUP BY kb_id
) doc_stats ON kb.kb_id = doc_stats.kb_id
LEFT JOIN (
    SELECT kb_id, COUNT(*) as actual_segment_count
    FROM document_segments 
    WHERE kb_id IS NOT NULL
    GROUP BY kb_id
) seg_stats ON kb.kb_id = seg_stats.kb_id
LEFT JOIN (
    SELECT kb_id, COUNT(*) as actual_vector_count
    FROM knowledge_base_vectors
    GROUP BY kb_id
) vec_stats ON kb.kb_id = vec_stats.kb_id;

-- 添加表注释
COMMENT ON TABLE knowledge_bases IS '知识库主表';
COMMENT ON TABLE knowledge_base_documents IS '知识库文档关联表';
COMMENT ON TABLE knowledge_base_vectors IS '知识库向量表';
COMMENT ON TABLE knowledge_base_configs IS '知识库配置表';
COMMENT ON TABLE knowledge_base_access_logs IS '知识库访问日志表';

-- 添加列注释
COMMENT ON COLUMN knowledge_bases.kb_id IS '知识库唯一标识';
COMMENT ON COLUMN knowledge_bases.name IS '知识库名称';
COMMENT ON COLUMN knowledge_bases.description IS '知识库描述';
COMMENT ON COLUMN knowledge_bases.status IS '状态：active, inactive, deleted';
COMMENT ON COLUMN knowledge_bases.visibility IS '可见性：private, public, shared';
COMMENT ON COLUMN knowledge_bases.embedding_model IS '嵌入模型';
COMMENT ON COLUMN knowledge_bases.vector_dimension IS '向量维度';

COMMENT ON COLUMN knowledge_base_documents.kb_id IS '知识库ID';
COMMENT ON COLUMN knowledge_base_documents.file_id IS '文件ID';
COMMENT ON COLUMN knowledge_base_documents.status IS '状态：pending, processing, completed, failed';
COMMENT ON COLUMN knowledge_base_documents.segment_status IS '分段状态';
COMMENT ON COLUMN knowledge_base_documents.vector_status IS '向量化状态';

COMMENT ON COLUMN knowledge_base_vectors.vector_id IS '向量唯一标识';
COMMENT ON COLUMN knowledge_base_vectors.embedding_model IS '嵌入模型';
COMMENT ON COLUMN knowledge_base_vectors.vector_data IS '向量数据（JSON格式存储）';
COMMENT ON COLUMN knowledge_base_vectors.content IS '原始内容';
COMMENT ON COLUMN knowledge_base_vectors.meta_data IS '元数据';

COMMENT ON COLUMN knowledge_base_configs.config_type IS '配置类型：embedding, retrieval, generation';
COMMENT ON COLUMN knowledge_base_configs.config_key IS '配置键';
COMMENT ON COLUMN knowledge_base_configs.config_value IS '配置值';

COMMENT ON COLUMN knowledge_base_access_logs.action IS '操作类型：view, search, query, update';
COMMENT ON COLUMN knowledge_base_access_logs.query_text IS '查询文本';
COMMENT ON COLUMN knowledge_base_access_logs.response_time IS '响应时间（毫秒）';
