/* 文档编辑器样式 */

/* 通用编辑器样式 */
.document-editor {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.document-editor [contenteditable="true"] {
  outline: none;
  border: none;
  min-height: 200px;
  padding: 20px;
  line-height: 1.6;
  font-size: 14px;
  color: #374151;
}

.document-editor [contenteditable="true"]:focus {
  box-shadow: inset 0 0 0 2px #3b82f6;
  border-radius: 4px;
}

/* Word文档样式 */
.word-editor {
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin: 20px auto;
  max-width: 8.5in;
  min-height: 11in;
  padding: 1in;
}

.word-editor h1, .word-editor h2, .word-editor h3, 
.word-editor h4, .word-editor h5, .word-editor h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #1f2937;
}

.word-editor h1 { font-size: 2em; }
.word-editor h2 { font-size: 1.5em; }
.word-editor h3 { font-size: 1.25em; }
.word-editor h4 { font-size: 1.125em; }
.word-editor h5 { font-size: 1em; }
.word-editor h6 { font-size: 0.875em; }

.word-editor p {
  margin-bottom: 1em;
  text-align: justify;
}

.word-editor table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
  border: 1px solid #d1d5db;
}

.word-editor table th,
.word-editor table td {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  text-align: left;
}

.word-editor table th {
  background-color: #f9fafb;
  font-weight: 600;
}

.word-editor ul, .word-editor ol {
  margin: 1em 0;
  padding-left: 2em;
}

.word-editor li {
  margin-bottom: 0.5em;
}

/* PowerPoint编辑器样式 */
.powerpoint-editor {
  background: #f3f4f6;
}

.slide-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  aspect-ratio: 16/9;
  overflow: hidden;
}

.slide-thumbnail {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.slide-thumbnail:hover {
  border-color: #3b82f6;
  transform: scale(1.02);
}

.slide-thumbnail.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Excel编辑器样式 */
.excel-editor {
  background: #f9fafb;
}

.excel-cell {
  border: 1px solid #d1d5db;
  padding: 0;
  position: relative;
  min-width: 80px;
  height: 32px;
}

.excel-cell:hover {
  background-color: #f3f4f6;
}

.excel-cell.selected {
  border: 2px solid #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

.excel-cell input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 4px 8px;
  font-size: 12px;
  background: transparent;
}

.excel-header {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  padding: 4px;
  color: #374151;
}

.excel-row-header {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
  padding: 4px;
  color: #374151;
  min-width: 48px;
}

/* PDF查看器样式 */
.pdf-viewer {
  background: #1f2937;
  color: white;
}

.pdf-page {
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  margin: 20px auto;
}

.pdf-annotation {
  position: absolute;
  border: 2px dashed;
  pointer-events: none;
}

.pdf-annotation.highlight {
  background-color: rgba(255, 255, 0, 0.3);
  border-color: #fbbf24;
}

.pdf-annotation.comment {
  border-color: #ef4444;
}

.pdf-annotation.bookmark {
  border-color: #10b981;
}

/* 工具栏样式 */
.toolbar {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  border-right: 1px solid #e5e7eb;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-button {
  padding: 6px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  color: #374151;
}

.toolbar-button:hover {
  background-color: #f3f4f6;
}

.toolbar-button.active {
  background-color: #3b82f6;
  color: white;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  color: #374151;
}

.toolbar-input {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 12px;
  color: #374151;
  width: 60px;
}

/* 状态栏样式 */
.status-bar {
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

/* 侧边栏样式 */
.sidebar {
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  width: 250px;
  overflow-y: auto;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
}

.sidebar-content {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .word-editor {
    margin: 10px;
    padding: 0.5in;
    max-width: none;
  }
  
  .toolbar {
    padding: 4px 8px;
    gap: 4px;
  }
  
  .toolbar-group {
    padding: 0 4px;
  }
  
  .sidebar {
    width: 200px;
  }
  
  .excel-cell {
    min-width: 60px;
    height: 28px;
  }
  
  .slide-container {
    margin: 10px;
  }
}

@media (max-width: 480px) {
  .word-editor {
    padding: 0.25in;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-group {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
    padding: 4px 0;
  }
  
  .sidebar {
    width: 100%;
    position: absolute;
    z-index: 10;
    height: 100%;
  }
}

/* 打印样式 */
@media print {
  .toolbar,
  .status-bar,
  .sidebar {
    display: none !important;
  }
  
  .word-editor {
    box-shadow: none;
    margin: 0;
    padding: 0;
    max-width: none;
  }
  
  .slide-container {
    page-break-after: always;
    box-shadow: none;
  }
  
  .excel-editor {
    background: white;
  }
  
  .pdf-viewer {
    background: white;
    color: black;
  }
}
