#!/usr/bin/env python3
"""
添加存储配置表缺失字段的迁移脚本
"""
import asyncio
import asyncpg
import os
from loguru import logger


async def add_storage_fields():
    """添加存储配置表的缺失字段"""
    
    # 从环境变量获取数据库连接信息
    db_host = os.getenv('DB_HOST', '**************')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'xhc_rag')
    db_user = os.getenv('DB_USERNAME', 'postgres')
    db_password = os.getenv('DB_PASSWORD', 'XHC12345')
    
    # 构建连接字符串
    dsn = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(dsn)
        logger.info("数据库连接成功")
        
        # 检查字段是否存在
        fields_to_add = [
            ("total_files", "INTEGER DEFAULT 0"),
            ("total_size", "BIGINT DEFAULT 0"),
            ("last_sync_at", "TIMESTAMP WITH TIME ZONE")
        ]
        
        for field_name, field_type in fields_to_add:
            # 检查字段是否存在
            exists = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'storage_configs'
                    AND column_name = $1
                );
            """, field_name)
            
            if not exists:
                # 添加字段
                alter_sql = f"ALTER TABLE storage_configs ADD COLUMN {field_name} {field_type};"
                await conn.execute(alter_sql)
                logger.info(f"✅ 添加字段成功: {field_name}")
            else:
                logger.info(f"✅ 字段已存在: {field_name}")
        
        # 关闭连接
        await conn.close()
        logger.info("✅ 存储配置表字段迁移完成！")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(add_storage_fields())
