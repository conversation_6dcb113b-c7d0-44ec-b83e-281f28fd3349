'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Check,
  Download,
  Eye,
  MoreHorizontal,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { getFileTypeDescription } from './FileTypeIcon';

interface FileItem {
  file_id: string;
  file_path: string;
  file_name: string;
  file_type: 'file' | 'directory';
  file_size: number;
  file_size_formatted: string;
  mime_type?: string;
  file_extension: string;
  created_at: string;
  modified_at: string;
  is_directory: boolean;
  is_image: boolean;
  is_video: boolean;
  is_document: boolean;
}

interface FileListViewProps {
  files: FileItem[];
  selectedFiles: string[];
  onFileSelect: (file: FileItem, isSelected: boolean) => void;
  onFileDoubleClick: (file: FileItem) => void;
  onSelectAll: () => void;
  getFileIcon: (file: FileItem) => React.ReactNode;
  onFileDownload: (file: FileItem) => void;
  onFilePreview: (file: FileItem) => void;
}

const FileListView: React.FC<FileListViewProps> = ({
  files,
  selectedFiles,
  onFileSelect,
  onFileDoubleClick,
  onSelectAll,
  getFileIcon,
  onFileDownload,
  onFilePreview
}) => {
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    file: FileItem;
  } | null>(null);

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, file: FileItem) => {
    e.preventDefault();
    e.stopPropagation();

    // 获取鼠标位置
    const mouseX = e.clientX;
    const mouseY = e.clientY;

    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = 192; // 菜单实际宽度 (min-w-48 = 12rem = 192px)
    const menuHeight = 200; // 菜单预估高度

    // 计算菜单位置，确保不超出视口
    let x = mouseX;
    let y = mouseY;

    // 右侧边界检查
    if (x + menuWidth > viewportWidth) {
      x = mouseX - menuWidth; // 显示在鼠标左侧
    }

    // 底部边界检查
    if (y + menuHeight > viewportHeight) {
      y = mouseY - menuHeight; // 显示在鼠标上方
    }

    // 确保菜单不会超出屏幕边界
    x = Math.max(8, Math.min(x, viewportWidth - menuWidth - 8));
    y = Math.max(8, Math.min(y, viewportHeight - menuHeight - 8));

    setContextMenu({
      x,
      y,
      file
    });
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // 处理文件选择
  const handleFileClick = (file: FileItem, e: React.MouseEvent) => {
    const isSelected = selectedFiles.includes(file.file_path);
    
    if (e.ctrlKey || e.metaKey) {
      // Ctrl/Cmd + 点击：切换选择状态
      onFileSelect(file, !isSelected);
    } else if (e.shiftKey && selectedFiles.length > 0) {
      // Shift + 点击：范围选择
      // 这里可以实现范围选择逻辑
      onFileSelect(file, !isSelected);
    } else {
      // 普通点击：单选
      if (!isSelected) {
        onFileSelect(file, true);
      }
    }
  };

  const allSelected = files.length > 0 && selectedFiles.length === files.length;
  const someSelected = selectedFiles.length > 0 && selectedFiles.length < files.length;

  return (
    <>
      <div className="bg-white">
        {/* 表头 */}
        <div className="border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-12 gap-4 px-4 py-3 text-sm font-medium text-gray-700">
            <div className="col-span-1 flex items-center">
              <button
                onClick={onSelectAll}
                className="flex items-center justify-center w-5 h-5 border border-gray-300 rounded"
              >
                {allSelected && <Check className="w-3 h-3 text-blue-600" />}
                {someSelected && !allSelected && (
                  <div className="w-2 h-2 bg-blue-600 rounded-sm" />
                )}
              </button>
            </div>
            <div className="col-span-5">名称</div>
            <div className="col-span-2">大小</div>
            <div className="col-span-2">修改时间</div>
            <div className="col-span-2">类型</div>
          </div>
        </div>

        {/* 文件列表 */}
        <div className="divide-y divide-gray-200">
          {files.map((file) => {
            const isSelected = selectedFiles.includes(file.file_path);
            
            return (
              <motion.div
                key={file.file_id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`grid grid-cols-12 gap-4 px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                  isSelected ? 'bg-blue-50 border-blue-200' : ''
                }`}
                onClick={(e) => handleFileClick(file, e)}
                onDoubleClick={() => onFileDoubleClick(file)}
                onContextMenu={(e) => handleContextMenu(e, file)}
              >
                {/* 选择框 */}
                <div className="col-span-1 flex items-center">
                  <div
                    className={`flex items-center justify-center w-5 h-5 border rounded transition-colors ${
                      isSelected 
                        ? 'bg-blue-600 border-blue-600' 
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    {isSelected && <Check className="w-3 h-3 text-white" />}
                  </div>
                </div>

                {/* 文件名 */}
                <div className="col-span-5 flex items-center space-x-3 min-w-0">
                  {getFileIcon(file)}
                  <span className="truncate text-sm text-gray-900">
                    {file.file_name}
                  </span>
                </div>

                {/* 文件大小 */}
                <div className="col-span-2 flex items-center">
                  <span className="text-sm text-gray-600">
                    {file.is_directory ? '-' : file.file_size_formatted}
                  </span>
                </div>

                {/* 修改时间 */}
                <div className="col-span-2 flex items-center">
                  <span className="text-sm text-gray-600">
                    {formatDate(file.modified_at)}
                  </span>
                </div>

                {/* 文件类型 */}
                <div className="col-span-2 flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {getFileTypeDescription(file.file_name, file.is_directory)}
                  </span>
                  
                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {!file.is_directory && (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onFilePreview(file);
                          }}
                          className="p-1 text-gray-400 hover:text-gray-600 rounded"
                          title="预览"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onFileDownload(file);
                          }}
                          className="p-1 text-gray-400 hover:text-gray-600 rounded"
                          title="下载"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleContextMenu(e, file);
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600 rounded"
                      title="更多操作"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* 右键菜单 */}
      {contextMenu && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={closeContextMenu}
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-48"
            style={{
              left: contextMenu.x,
              top: contextMenu.y
            }}
          >
            {!contextMenu.file.is_directory && (
              <>
                <button
                  onClick={() => {
                    onFilePreview(contextMenu.file);
                    closeContextMenu();
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Eye className="w-4 h-4" />
                  <span>预览</span>
                </button>
                
                <button
                  onClick={() => {
                    onFileDownload(contextMenu.file);
                    closeContextMenu();
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>下载</span>
                </button>
                
                <div className="border-t border-gray-200 my-1"></div>
              </>
            )}
            
            <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
              重命名
            </button>
            
            <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
              复制
            </button>
            
            <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
              移动
            </button>
            
            <div className="border-t border-gray-200 my-1"></div>
            
            <button className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
              删除
            </button>
          </motion.div>
        </>
      )}
    </>
  );
};

export default FileListView;
