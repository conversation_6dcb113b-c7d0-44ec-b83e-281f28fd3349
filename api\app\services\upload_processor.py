"""
后台上传处理器
负责自动处理上传队列中的任务
"""
import os
import asyncio
import shutil
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from loguru import logger

from app.core.database import get_async_session
from app.models.upload_task import UploadTask
from app.services.upload_task_service import upload_task_service
from app.services.simple_file_manager import SimpleFileManagerService


class UploadProcessor:
    """上传处理器"""
    
    def __init__(self):
        self.is_running = False
        self.max_concurrent = 3
        self.active_uploads = set()
        self.file_service = SimpleFileManagerService()
    
    async def start(self):
        """启动上传处理器"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("上传处理器启动")
        
        # 启动处理循环
        asyncio.create_task(self._process_loop())
    
    async def stop(self):
        """停止上传处理器"""
        self.is_running = False
        logger.info("上传处理器停止")
    
    async def _process_loop(self):
        """处理循环"""
        while self.is_running:
            try:
                await self._process_pending_tasks()
                await asyncio.sleep(2)  # 每2秒检查一次
            except Exception as e:
                logger.error(f"处理循环出错: {str(e)}")
                await asyncio.sleep(5)  # 出错时等待5秒
    
    async def _process_pending_tasks(self):
        """处理待处理的任务"""
        if len(self.active_uploads) >= self.max_concurrent:
            return

        async for session in get_async_session():
            try:
                # 获取待处理的任务（排除暂停的任务）
                query = select(UploadTask).where(
                    UploadTask.status == "pending"
                ).order_by(UploadTask.created_at).limit(
                    self.max_concurrent - len(self.active_uploads)
                )

                result = await session.execute(query)
                tasks = result.scalars().all()

                # 处理每个任务
                for task in tasks:
                    if task.task_id not in self.active_uploads:
                        asyncio.create_task(self._process_task(task))

                break  # 退出async for循环

            except Exception as e:
                logger.error(f"获取待处理任务失败: {str(e)}")
    
    async def _process_task(self, task: UploadTask):
        """处理单个任务"""
        task_id = task.task_id
        self.active_uploads.add(task_id)

        try:
            logger.info(f"开始处理任务: {task_id} - {task.file_name}")

            # 在开始前检查任务是否被暂停
            async for session in get_async_session():
                current_task = await upload_task_service.get_task_by_id(task_id, session)
                if current_task and current_task.status == "paused":
                    logger.info(f"任务已被暂停，跳过处理: {task_id}")
                    return
                break

            # 更新任务状态为上传中
            await upload_task_service.update_task_status(
                task_id, "uploading", progress=0
            )

            # 检查文件是否存在
            if not os.path.exists(task.file_path):
                raise Exception("源文件不存在")

            # 执行上传
            await self._upload_file(task)

            # 上传成功
            await upload_task_service.update_task_status(
                task_id, "success", progress=100
            )

            # 清理临时文件
            await self._cleanup_temp_file(task.file_path)

            logger.info(f"任务完成: {task_id} - {task.file_name}")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"任务失败: {task_id} - {error_msg}")

            # 更新任务状态为失败
            await upload_task_service.update_task_status(
                task_id, "error", error_message=error_msg
            )

        finally:
            self.active_uploads.discard(task_id)
    
    async def _upload_file(self, task: UploadTask):
        """上传文件到目标存储"""
        try:
            # 模拟分块上传以显示进度
            file_size = task.file_size
            chunk_size = max(1024 * 1024, file_size // 10)  # 至少1MB，最多分10块
            uploaded_bytes = 0

            # 更新进度为10%（开始读取文件）
            await upload_task_service.update_task_status(
                task.task_id, "uploading", progress=10, uploaded_bytes=uploaded_bytes
            )

            # 读取文件内容
            with open(task.file_path, 'rb') as f:
                file_content = f.read()

            # 更新进度为30%（文件读取完成）
            await upload_task_service.update_task_status(
                task.task_id, "uploading", progress=30, uploaded_bytes=uploaded_bytes
            )

            # 模拟上传过程中的进度更新
            for i in range(3, 10):  # 从30%到90%
                await asyncio.sleep(0.5)  # 模拟上传时间
                progress = 30 + (i - 2) * 10
                uploaded_bytes = int(file_size * progress / 100)

                await upload_task_service.update_task_status(
                    task.task_id, "uploading", progress=progress, uploaded_bytes=uploaded_bytes
                )

            # 获取数据库会话并调用文件管理服务上传文件
            async for session in get_async_session():
                result = await self.file_service.upload_file(
                    storage_id=task.storage_id,
                    file_content=file_content,
                    file_path=task.upload_path,
                    session=session
                )

                if not result.get("success"):
                    raise Exception(result.get("message", "上传失败"))

                logger.info(f"文件上传成功: {task.file_name} -> {task.upload_path}")
                break  # 退出async for循环

        except Exception as e:
            logger.error(f"文件上传失败: {task.file_name} - {str(e)}")
            raise
    
    async def _cleanup_temp_file(self, file_path: str):
        """清理临时文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                
                # 如果目录为空，也删除目录
                temp_dir = os.path.dirname(file_path)
                if os.path.exists(temp_dir) and not os.listdir(temp_dir):
                    os.rmdir(temp_dir)
                    
                logger.debug(f"临时文件清理完成: {file_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {file_path} - {str(e)}")


# 全局上传处理器实例
upload_processor = UploadProcessor()
